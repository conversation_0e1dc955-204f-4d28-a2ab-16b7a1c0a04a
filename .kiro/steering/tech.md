---
inclusion: always
---

# Technology Stack & Development Conventions

## Core Technology Requirements

### React Native + Expo SDK 52
- **ALWAYS** use functional components with TypeScript
- **NEVER** use class components or JavaScript files
- Use Expo Router file-based routing with proper route groups
- Implement proper error boundaries for all feature components

### TypeScript Standards
- **STRICT MODE**: No `any` types in production code
- Use proper interface definitions with `I` prefix (`IUser`, `IChatMessage`)
- Export both types and implementations from component files
- Leverage type inference where possible, explicit types for public APIs

## State Management Patterns

### Zustand Store Architecture
**REQUIRED PATTERN**:
```typescript
export const useStoreNameStore = create<StoreNameStore>()(
  persist(
    (set, get) => ({
      // state properties
      // action methods
    }),
    { name: 'store-name' }
  )
);
```

### Context Usage Rules
- **Authentication**: Use `AuthContext` for user state and permissions
- **Knowledge Cards**: Use `KnowledgeCardsContext` for card management
- **Permission Errors**: Use `PermissionErrorContext` for error handling
- **AVOID** prop drilling - use contexts for deeply nested state

### Data Fetching Standards
- **PRIMARY**: Use SWR for all remote API calls
- **PATTERN**: Implement optimistic updates for user actions
- **CACHING**: Cache-first strategy with stale-while-revalidate
- **ERROR HANDLING**: Always provide fallback UI states

## UI Development Rules

### GluestackUI Component Patterns
**MANDATORY STRUCTURE**:
```typescript
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';

// Use compound pattern for all UI components
<Button variant="solid" size="md">
  <ButtonIcon as={IconName} />
  <ButtonText>Action</ButtonText>
</Button>
```

### Styling Conventions
- **PRIMARY**: NativeWind classes for all styling
- **TOKENS**: Use design tokens from `tailwind.config.js`
- **RESPONSIVE**: Implement proper breakpoints for tablet/desktop
- **ANIMATIONS**: Use React Native Reanimated for smooth 60fps animations

### Component Architecture
- Each component in own folder with `index.tsx`
- Export both component and types from index file
- Use `@/` alias for all imports outside current directory
- Implement loading states and error boundaries

## Backend Integration Standards

### Appwrite Service Layer
- **LOCATION**: All Appwrite logic in `lib/api/appwrite.ts`
- **AUTHENTICATION**: Use GitHub OAuth as primary method
- **PERMISSIONS**: Implement proper document-level permissions
- **OFFLINE**: Design for offline-first with sync capabilities

### AI Provider Integration
- **MULTI-PROVIDER**: Support OpenAI, Anthropic, Perplexity, Google, Mistral, xAI
- **FALLBACKS**: Implement graceful degradation when providers fail
- **RATE LIMITING**: Respect API limits with intelligent queuing
- **COST MANAGEMENT**: Monitor token usage and implement caps

## Development Workflow

### Code Quality Requirements
- **TESTING**: Write tests for all business logic and components
- **LINTING**: Fix all ESLint errors before committing
- **TYPES**: Ensure strict TypeScript compliance
- **DOCUMENTATION**: Update TypeDoc comments for public APIs

### Performance Standards
- **60FPS**: All animations must maintain 60fps on mid-range devices
- **BUNDLE SIZE**: Monitor and optimize bundle size regularly
- **MEMORY**: Implement proper cleanup for subscriptions and timers
- **BATTERY**: Minimize background processing and network requests

### Error Handling Patterns
- **BOUNDARIES**: Use `PermissionErrorBoundary` for feature-level errors
- **FALLBACKS**: Provide meaningful fallback UI for all error states
- **LOGGING**: Log errors appropriately without exposing sensitive data
- **USER FEEDBACK**: Show user-friendly error messages with recovery actions

## Critical Development Commands

### Essential Commands
```bash
# Development with proper logging
npm run start-with-logging

# Run tests before committing
npm test

# Setup backend schema (run once)
npm run setup:appwrite:v2

# Build for production testing
npm run build:android
npm run build:ios
```

### Environment Setup
1. Copy `.env.example` to `.env.local`
2. Configure all AI provider API keys
3. Set up Appwrite project credentials
4. Configure GitHub OAuth application
5. Run `npm run setup:appwrite:v2` to initialize database schema