---
inclusion: always
---

# Product Context & Development Guidelines

## Pocket Manus - AI-Powered Document Scanning & Knowledge Management

Pocket Manus is a React Native mobile app combining document scanning, AI text recognition, and knowledge management. When implementing features, prioritize user experience, performance, and accessibility.

## Core Feature Implementation Rules

### Document Processing Features
**ALWAYS implement these patterns:**
- Use `CameraInterface` or `SmartCameraInterface` for document capture
- Implement edge detection with visual feedback for document boundaries
- Show confidence scores for OCR results using `ConfidenceScore` component
- Store documents in Appwrite with offline-first sync capabilities
- Provide export options: PDF, text, and social sharing workflows

### Knowledge Management Features
**REQUIRED components and patterns:**
- Use `KnowledgeCard` component for structured information display
- Implement graph visualization with D3.js in `app/(knowledge)/graph.tsx`
- Use `KnowledgeCardsContext` for state management across knowledge features
- Enable full-text search with AI-enhanced relevance scoring
- Auto-categorize content using AI provider integration

### AI Integration Requirements
**MANDATORY patterns:**
- Support multiple providers: OpenAI, Anthropic, Perplexity, Google, Mistral, xAI
- Use `ChatSessionStore` for persistent conversation management
- Implement provider fallbacks with graceful degradation
- Generate knowledge cards from chat interactions automatically
- Provide context-aware suggestions based on user activity

## UI/UX Implementation Standards

### Design System Requirements
**ALWAYS use these patterns:**
- Apply candy color palette from `tailwind.config.js` design tokens
- Implement glass morphism effects with `backdrop-blur` and transparency
- Use React Native Reanimated for all animations (target 60fps)
- Ensure responsive design with proper breakpoints for tablets

### User Interaction Patterns
**REQUIRED behaviors:**
- Implement gesture-first interactions (swipe, pinch, tap)
- Use progressive disclosure for complex features
- Show contextual actions based on current user state
- Provide offline-first experience with sync indicators

## Development Implementation Rules

### Feature Access Control
**MANDATORY for all premium features:**
```typescript
<FeatureGate requiredTier="premium" feature="advanced-ocr">
  <AdvancedOCRComponent />
</FeatureGate>
```

### Error Handling Requirements
**ALWAYS implement:**
- Wrap feature components in `PermissionErrorBoundary`
- Show loading states with skeleton screens during data fetching
- Provide user-friendly error messages with recovery actions
- Implement WCAG 2.1 AA compliance for accessibility

### Data Management Patterns
**REQUIRED for all data operations:**
- Use optimistic updates for immediate UI feedback
- Implement SWR with cache-first strategy for API calls
- Persist critical state using Zustand with AsyncStorage
- Use Appwrite realtime subscriptions for collaborative features

## Mobile Platform Requirements

### Performance Standards
**MANDATORY optimizations:**
- Maintain 60fps for all animations and scrolling
- Use minimum 44px touch targets for accessibility
- Respect device safe areas and notches in layouts
- Minimize background processing to preserve battery life

### Cross-Platform Compatibility
**REQUIRED patterns:**
- Use Expo Router file-based routing with typed navigation
- Leverage Expo SDK APIs for consistent cross-platform behavior
- Minimize native module dependencies for maintainability
- Implement graceful web fallbacks where applicable

## Business Logic Implementation

### Authentication Requirements
**MANDATORY flow:**
- Use GitHub OAuth as primary authentication method
- Implement secure token storage with automatic refresh
- Apply permission levels: Free, Premium, Enterprise with feature gates
- Provide progressive onboarding with tutorial overlays

### Content Management Rules
**REQUIRED data structure:**
- Knowledge cards must include: title, content, tags, relationships, metadata
- Document metadata must capture: creation date, source, confidence scores, processing status
- Implement version control for knowledge cards and documents
- Enable sharing with proper permission controls

### AI Integration Standards
**MANDATORY practices:**
- Implement provider fallbacks for reliability
- Respect API rate limits with intelligent queuing
- Monitor and cap token usage for cost management
- Process locally when possible, secure transmission for cloud AI
- Cache AI responses to reduce redundant API calls