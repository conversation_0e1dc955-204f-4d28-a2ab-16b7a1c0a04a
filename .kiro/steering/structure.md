---
inclusion: always
---

# Project Structure & Architecture Guidelines

## File Organization Rules

### Expo Router Structure (`app/`)
**CRITICAL**: Use file-based routing with proper route groups:
- `app/(tabs)/` - Main navigation: home, scan, search, profile
- `app/(knowledge)/` - Knowledge management: cards, graph, share
- `app/(document-stack)/` - Document workflows: editor, viewer, text-recognition
- `app/(workflows)/` - Specialized flows: social-share, text-review
- `app/(drawer)/` - Settings and secondary navigation
- `app/auth/` - Authentication screens

**Route Naming**: Use kebab-case for route files (e.g., `document-editor.tsx`)

### Component Architecture

#### UI Components (`components/ui/`)
**MANDATORY PATTERNS**:
- Each component in own folder with `index.tsx`
- Use GluestackUI compound pattern: `Button`, `ButtonText`, `ButtonIcon`
- Apply candy colors and glass morphism variants
- Export both component and types from index file

#### Feature Components
**LOCATION RULES**:
- `components/learni-scan/` - Document scanning UI
- `components/camera/` - Camera interfaces
- `components/auth/` - Authentication UI (FeatureGate, AuthGuard, PermissionErrorBoundary)
- `components/home/<USER>

## State Management Architecture

### Zustand Store Pattern
**REQUIRED STRUCTURE**:
```typescript
// Export both hook and store object
export const useUIStore = create<UIStore>()(
  persist(
    (set, get) => ({
      // state and actions
    }),
    { name: 'ui-store' }
  )
);
export const uiStore = useUIStore.getState;
```

**Store Locations**:
- `UIStore.ts` - UI preferences, theme, page states
- `ChatSessionStore.ts` - Chat session management
- `BenchmarkStore.ts` - Model benchmark data

## Import & Export Conventions

### Import Rules
**MANDATORY**:
- Use `@/` alias for root imports: `import { Button } from '@/components/ui'`
- Relative imports for same directory: `import './styles.css'`
- Barrel exports in `index.ts` files

### File Naming Standards
**STRICT RULES**:
- Components: PascalCase (`Button.tsx`, `CameraInterface.tsx`)
- Utilities: camelCase (`formatBytes.ts`, `timeAgo.ts`)
- Routes: kebab-case (`document-editor.tsx`, `ai-search-cards.tsx`)
- Types: PascalCase with prefix (`IUser.ts`, `TTheme.ts`)

## Code Organization Patterns

### Type Definitions
**LOCATION & NAMING**:
- `types/` - Shared TypeScript types (prefix with `T`)
- `interfaces/` - Interface definitions (prefix with `I`)
- Co-locate types with components when component-specific

### Utility Structure
**REQUIRED LOCATIONS**:
- `lib/api/` - API clients and authentication
- `lib/contexts/` - React contexts (AuthContext, KnowledgeCardsContext)
- `lib/helpers/` - Pure utility functions
- `lib/services/` - Business logic services
- `lib/utils.ts` - Common utilities (cn function for class merging)

### Asset Management
**ORGANIZATION RULES**:
- `assets/images/` - Static images, icons, splash screens
- `assets/fonts/` - Custom fonts (SpaceMono)
- `design/` - Design system docs and HTML prototypes

## Development Standards

### Component Creation Rules
1. **Always** use functional components with TypeScript
2. **Always** implement proper error boundaries for feature components
3. **Always** use custom hooks for reusable logic
4. **Always** implement loading and error states

### Styling Requirements
- **Primary**: NativeWind classes for styling
- **Components**: GluestackUI with custom variants
- **Tokens**: Use design tokens from `tailwind.config.js`
- **Responsive**: Implement Tailwind breakpoints

### Testing Structure
**MIRROR SOURCE STRUCTURE**:
- `__tests__/screens/` - Screen component tests
- `__tests__/services/` - Service layer tests  
- `__tests__/store/` - State management tests
- Use React Native Testing Library patterns

## Critical Architecture Rules

### Permission System Integration
**ALWAYS USE**:
- `FeatureGate` component for premium features
- `PermissionErrorBoundary` for error handling
- `AuthGuard` for protected routes

### Performance Requirements
- Implement optimistic updates for user actions
- Use SWR for data fetching with cache-first strategy
- Persist critical state with AsyncStorage
- Minimize re-renders with proper memoization

### Error Handling Standards
- Implement error boundaries at feature level
- Use consistent error message patterns
- Provide fallback UI for failed states
- Log errors appropriately for debugging