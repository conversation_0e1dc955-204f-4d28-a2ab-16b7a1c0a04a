{"enabled": true, "name": "Source to Docs Sync", "description": "Monitors all TypeScript source files, React Native components, configuration files, and other relevant project files. When changes are detected, triggers documentation updates in README.md or the docs folder to keep documentation in sync with code changes.", "version": "1", "when": {"type": "fileEdited", "patterns": ["*.ts", "*.tsx", "*.js", "*.jsx", "app/**/*.ts", "app/**/*.tsx", "components/**/*.ts", "components/**/*.tsx", "lib/**/*.ts", "lib/**/*.tsx", "store/**/*.ts", "hooks/**/*.ts", "interfaces/**/*.ts", "types/**/*.ts", "utils/**/*.ts", "scripts/**/*.ts", "package.json", "tsconfig.json", "app.config.js", "tailwind.config.js", "metro.config.js", "babel.config.js"]}, "then": {"type": "askAgent", "prompt": "Source code files have been modified in this React Native TypeScript project. Please analyze the changes and update the documentation accordingly. Focus on:\n\n1. Update README.md if there are significant architectural changes, new features, or setup instructions\n2. Update documentation in the /docs folder if it exists for more detailed technical documentation\n3. Ensure API documentation reflects any interface or service changes\n4. Update component documentation for any new or modified React components\n5. Reflect changes in configuration files (package.json, tsconfig.json, etc.) in setup guides\n6. Update any workflow documentation if new scripts or processes are added\n\nKeep documentation clear, accurate, and aligned with the current codebase. Focus on user-facing changes and developer setup instructions."}}