#!/bin/bash
EXPO_PUBLIC_API_BASE_URL=http://localhost:8081
EXPO_PUBLIC_FIREBASE_FUNCTIONS_URL=http://localhost:1001 # Firebase fake URL

# AppWrite Configuration
EXPO_PUBLIC_APPWRITE_ENDPOINT=https://nyc.cloud.appwrite.io/v1
EXPO_PUBLIC_APPWRITE_PROJECT_ID=6864bf85002a5c148683
EXPO_PUBLIC_APPWRITE_DATABASE_ID=learni-scan-db

# Server API Key (for setup scripts only - fill this out)
APPWRITE_API_KEY=standard_a9edc484d4215534ddc4c7ad3e056ea4572da32cb55b05d4439596b452b274af967c7eda221c1fff5c859025e528b7e127466821bbd958053f171fa5f51e77a7bc157f5d36b9e527fae9f0840faecece230b38f1bceb57e53bed9b2d761def788d7f2d17e621c34fb0e34d10ecaad4dead7c06e17e7d2e96decbd41f98031c76