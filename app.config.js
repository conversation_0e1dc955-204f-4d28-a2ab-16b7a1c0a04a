require('dotenv').config();

// 补充 app.json，增加一些动态配置
module.exports = ({ config }) => {
  return {
    ...config,
    extra: {
      githubClientId: process.env.GITHUB_CLIENT_ID,
      githubClientSecret: process.env.GITHUB_CLIENT_SECRET,
      // AppWrite Configuration
      appwriteEndpoint: process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
      appwriteProjectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID,
      appwriteDatabaseId: process.env.EXPO_PUBLIC_APPWRITE_DATABASE_ID || 'learni-scan-db',
      router: {
        origin: false
      },
      eas:{
        projectId: '645b3690-b352-4d76-af69-33bd241b5ddf',
      }
    }
  };
};
