#!/usr/bin/env node

/**
 * Knowledge Base Seeding Script - Programming Content (Node.js version)
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 12: Comprehensive error handling
 * - Rule 1: Interactive feedback protocol
 * 
 * Usage:
 * node scripts/seed-programming-knowledge-node.js
 */

const { Client, Databases, ID } = require('node-appwrite');

// AppWrite configuration from environment
const APPWRITE_CONFIG = {
  endpoint: process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
  projectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID || 'learni-scan',
  databaseId: process.env.EXPO_PUBLIC_APPWRITE_DATABASE_ID || 'learni-scan-db',
  collections: {
    knowledgeCardsV2: process.env.EXPO_PUBLIC_APPWRITE_KNOWLEDGE_CARDS_V2_COLLECTION_ID || 'knowledge-cards-v2',
  },
};

// Initialize AppWrite client
const client = new Client()
  .setEndpoint(APPWRITE_CONFIG.endpoint)
  .setProject(APPWRITE_CONFIG.projectId)
  .setKey(process.env.APPWRITE_API_KEY || ''); // Server API key needed for Node.js

const databases = new Databases(client);

// Programming knowledge cards data
const PROGRAMMING_KNOWLEDGE_CARDS = [
  // JavaScript Fundamentals
  {
    title: 'JavaScript Variables and Data Types',
    content: `# JavaScript Variables and Data Types

## Variable Declarations
- **var**: Function-scoped, can be redeclared
- **let**: Block-scoped, cannot be redeclared
- **const**: Block-scoped, cannot be reassigned

## Primitive Data Types
1. **Number**: 42, 3.14, NaN, Infinity
2. **String**: "Hello", 'World', \`Template\`
3. **Boolean**: true, false
4. **Undefined**: Variable declared but not assigned
5. **Null**: Intentional absence of value
6. **Symbol**: Unique identifier (ES6+)
7. **BigInt**: Large integers (ES2020+)

## Examples
\`\`\`javascript
let age = 25;           // Number
const name = "Alice";   // String
let isActive = true;    // Boolean
let data;               // Undefined
let result = null;      // Null
\`\`\`

## Key Concepts
- **Type coercion**: JavaScript automatically converts types
- **Hoisting**: var declarations are moved to top of scope
- **Temporal dead zone**: let/const cannot be used before declaration`,
    category: 'programming',
    tags: ['javascript', 'variables', 'data-types', 'fundamentals'],
    difficulty: 'beginner',
    studyCount: 0,
    lastStudied: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  // Python Fundamentals
  {
    title: 'Python Functions and Scope',
    content: `# Python Functions and Scope

## Function Definition
\`\`\`python
def greet(name, greeting="Hello"):
    return f"{greeting}, {name}!"

# Function call
message = greet("Alice")
print(message)  # Output: Hello, Alice!
\`\`\`

## Scope Rules (LEGB)
1. **Local**: Inside function
2. **Enclosing**: In enclosing function
3. **Global**: At module level
4. **Built-in**: Built-in names

## Lambda Functions
\`\`\`python
# Lambda function
square = lambda x: x ** 2
print(square(5))  # Output: 25

# With map()
numbers = [1, 2, 3, 4, 5]
squared = list(map(lambda x: x ** 2, numbers))
\`\`\`

## Decorators
\`\`\`python
def timer(func):
    def wrapper(*args, **kwargs):
        import time
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} took {end - start:.2f} seconds")
        return result
    return wrapper

@timer
def slow_function():
    time.sleep(1)
    return "Done"
\`\`\``,
    category: 'programming',
    tags: ['python', 'functions', 'scope', 'decorators', 'lambda'],
    difficulty: 'intermediate',
    studyCount: 0,
    lastStudied: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  // Data Structures
  {
    title: 'Arrays and Lists - Core Operations',
    content: `# Arrays and Lists - Core Operations

## JavaScript Arrays
\`\`\`javascript
// Creation
let fruits = ['apple', 'banana', 'orange'];
let numbers = new Array(1, 2, 3, 4, 5);

// Common Methods
fruits.push('grape');        // Add to end
fruits.unshift('mango');     // Add to beginning
let last = fruits.pop();     // Remove from end
let first = fruits.shift();  // Remove from beginning

// Iteration
fruits.forEach(fruit => console.log(fruit));
let upperFruits = fruits.map(fruit => fruit.toUpperCase());
let longFruits = fruits.filter(fruit => fruit.length > 5);
\`\`\`

## Python Lists
\`\`\`python
# Creation
fruits = ['apple', 'banana', 'orange']
numbers = list(range(1, 6))

# Common Methods
fruits.append('grape')       # Add to end
fruits.insert(0, 'mango')   # Insert at index
last = fruits.pop()         # Remove from end
fruits.remove('banana')     # Remove by value

# List Comprehensions
squares = [x**2 for x in range(10)]
even_squares = [x**2 for x in range(10) if x % 2 == 0]
\`\`\`

## Time Complexity
- **Access**: O(1)
- **Search**: O(n)
- **Insertion**: O(1) at end, O(n) at beginning
- **Deletion**: O(1) at end, O(n) at beginning`,
    category: 'programming',
    tags: ['data-structures', 'arrays', 'lists', 'javascript', 'python'],
    difficulty: 'beginner',
    studyCount: 0,
    lastStudied: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  // React Concepts
  {
    title: 'React Hooks - useState and useEffect',
    content: `# React Hooks - useState and useEffect

## useState Hook
\`\`\`jsx
import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);
  const [name, setName] = useState('');

  const increment = () => setCount(count + 1);
  const decrement = () => setCount(prev => prev - 1);

  return (
    <div>
      <h2>Count: {count}</h2>
      <button onClick={increment}>+</button>
      <button onClick={decrement}>-</button>
      
      <input 
        value={name}
        onChange={(e) => setName(e.target.value)}
        placeholder="Enter name"
      />
      <p>Hello, {name}!</p>
    </div>
  );
}
\`\`\`

## useEffect Hook
\`\`\`jsx
import React, { useState, useEffect } from 'react';

function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Effect with dependency array
  useEffect(() => {
    const fetchUser = async () => {
      setLoading(true);
      try {
        const response = await fetch(\`/api/users/\${userId}\`);
        const userData = await response.json();
        setUser(userData);
      } catch (error) {
        console.error('Failed to fetch user:', error);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchUser();
    }
  }, [userId]); // Re-run when userId changes

  if (loading) return <div>Loading...</div>;
  if (!user) return <div>User not found</div>;

  return (
    <div>
      <h1>{user.name}</h1>
      <p>{user.email}</p>
    </div>
  );
}
\`\`\`

## Hook Rules
1. Only call hooks at the top level
2. Only call hooks from React functions
3. Use dependency arrays correctly
4. Clean up side effects`,
    category: 'programming',
    tags: ['react', 'hooks', 'useState', 'useEffect', 'javascript'],
    difficulty: 'intermediate',
    studyCount: 0,
    lastStudied: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  // Algorithms
  {
    title: 'Sorting Algorithms Comparison',
    content: `# Sorting Algorithms Comparison

## Bubble Sort
\`\`\`python
def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n - i - 1):
            if arr[j] > arr[j + 1]:
                arr[j], arr[j + 1] = arr[j + 1], arr[j]
    return arr

# Time: O(n²), Space: O(1)
\`\`\`

## Quick Sort
\`\`\`python
def quick_sort(arr):
    if len(arr) <= 1:
        return arr
    
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    
    return quick_sort(left) + middle + quick_sort(right)

# Average: O(n log n), Worst: O(n²), Space: O(log n)
\`\`\`

## Merge Sort
\`\`\`python
def merge_sort(arr):
    if len(arr) <= 1:
        return arr
    
    mid = len(arr) // 2
    left = merge_sort(arr[:mid])
    right = merge_sort(arr[mid:])
    
    return merge(left, right)

def merge(left, right):
    result = []
    i = j = 0
    
    while i < len(left) and j < len(right):
        if left[i] <= right[j]:
            result.append(left[i])
            i += 1
        else:
            result.append(right[j])
            j += 1
    
    result.extend(left[i:])
    result.extend(right[j:])
    return result

# Time: O(n log n), Space: O(n)
\`\`\`

## Algorithm Comparison
| Algorithm | Best | Average | Worst | Space | Stable |
|-----------|------|---------|-------|-------|--------|
| Bubble    | O(n) | O(n²)   | O(n²) | O(1)  | Yes    |
| Quick     | O(n log n) | O(n log n) | O(n²) | O(log n) | No |
| Merge     | O(n log n) | O(n log n) | O(n log n) | O(n) | Yes |`,
    category: 'programming',
    tags: ['algorithms', 'sorting', 'time-complexity', 'python'],
    difficulty: 'intermediate',
    studyCount: 0,
    lastStudied: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

async function seedProgrammingKnowledge() {
  console.log('🌱 Seeding Programming Knowledge Cards...\n');
  
  try {
    // Validate configuration (Rule 12: Error handling)
    if (!APPWRITE_CONFIG.databaseId) {
      throw new Error('Database ID not configured in APPWRITE_CONFIG');
    }
    
    if (!APPWRITE_CONFIG.collections.knowledgeCardsV2) {
      throw new Error('Knowledge Cards V2 collection not configured');
    }
    
    console.log('📋 Configuration:');
    console.log(`   Endpoint: ${APPWRITE_CONFIG.endpoint}`);
    console.log(`   Project ID: ${APPWRITE_CONFIG.projectId}`);
    console.log(`   Database ID: ${APPWRITE_CONFIG.databaseId}`);
    console.log(`   Collection ID: ${APPWRITE_CONFIG.collections.knowledgeCardsV2}`);
    console.log(`   Cards to create: ${PROGRAMMING_KNOWLEDGE_CARDS.length}`);
    
    let successCount = 0;
    let errorCount = 0;
    const errors = [];
    
    for (const cardData of PROGRAMMING_KNOWLEDGE_CARDS) {
      try {
        console.log(`   Creating: "${cardData.title}"`);
        
        const documentId = ID.unique();
        await databases.createDocument(
          APPWRITE_CONFIG.databaseId,
          APPWRITE_CONFIG.collections.knowledgeCardsV2,
          documentId,
          cardData
        );
        
        successCount++;
        console.log(`   ✅ Created successfully (ID: ${documentId})`);
        
      } catch (error) {
        errorCount++;
        const errorMessage = error.message || 'Unknown error';
        errors.push(`"${cardData.title}": ${errorMessage}`);
        console.error(`   ❌ Failed to create "${cardData.title}":`, errorMessage);
      }
    }
    
    console.log('\n📈 Seeding Summary:');
    console.log(`   ✅ Successfully created: ${successCount} cards`);
    console.log(`   ❌ Failed to create: ${errorCount} cards`);
    
    if (errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      errors.forEach(error => console.log(`   - ${error}`));
    }
    
    if (successCount > 0) {
      console.log('\n🎯 Programming Knowledge Categories Added:');
      console.log('   • JavaScript fundamentals (variables, data types)');
      console.log('   • Python functions and scope');
      console.log('   • Data structures (arrays, lists)');
      console.log('   • React hooks (useState, useEffect)');
      console.log('   • Sorting algorithms comparison');
      
      console.log('\n🔍 AI Search Testing Benefits:');
      console.log('   • Review Intent Test: Now has programming content to find');
      console.log('   • Semantic Search: Can match programming concepts');
      console.log('   • Intent Recognition: Better training data for AI');
      console.log('   • Knowledge Graph: Programming relationships');
      
      console.log('\n🧪 Test Scenarios Enhanced:');
      console.log('   • Test #1: Learning Intent - Can find JS/Python basics');
      console.log('   • Test #2: Review Intent - Programming concepts available');
      console.log('   • Test #3: Discovery Intent - Algorithm comparisons');
      console.log('   • Test #4: Semantic Understanding - Cross-language concepts');
      console.log('   • Test #5: Personal Knowledge Base - Expanded content');
    }
    
    return {
      success: successCount > 0,
      successCount,
      errorCount,
      errors,
    };
    
  } catch (error) {
    console.error('💥 Seeding failed:', error);
    return {
      success: false,
      successCount: 0,
      errorCount: PROGRAMMING_KNOWLEDGE_CARDS.length,
      errors: [error.message || 'Unknown error'],
    };
  }
}

// Run the seeding
seedProgrammingKnowledge()
  .then((result) => {
    console.log(`\n🏁 Seeding ${result.success ? 'completed successfully! 🎉' : 'failed! ❌'}`);
    
    if (result.success) {
      console.log('\n🚀 Next Steps:');
      console.log('   1. Test AI search with programming queries');
      console.log('   2. Run all 5 test scenarios in user testing interface');
      console.log('   3. Verify Review Intent Test finds programming content');
      console.log('   4. Check semantic search across different languages');
    } else {
      console.log('\n🔧 Troubleshooting:');
      console.log('   1. Check AppWrite configuration and API key');
      console.log('   2. Verify database and collection IDs');
      console.log('   3. Ensure proper permissions are set');
    }
    
    process.exit(result.success ? 0 : 1);
  })
  .catch((error) => {
    console.error('\n💥 Seeding crashed:', error);
    process.exit(1);
  });