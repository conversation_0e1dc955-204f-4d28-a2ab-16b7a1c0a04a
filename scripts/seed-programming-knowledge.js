#!/usr/bin/env node

/**
 * Knowledge Base Seeding Script - Programming Content (JavaScript version)
 * 
 * This script adds comprehensive programming knowledge cards to the database
 * to support AI search testing, especially for review intent scenarios.
 * 
 * Usage:
 * node scripts/seed-programming-knowledge.js
 */

const { Client, Databases, ID } = require('appwrite');

// AppWrite configuration
const client = new Client()
  .setEndpoint(process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1')
  .setProject(process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID || 'learni-scan');

const databases = new Databases(client);
const DATABASE_ID = process.env.EXPO_PUBLIC_APPWRITE_DATABASE_ID || 'learni-scan-db';
const KNOWLEDGE_CARDS_COLLECTION_ID = process.env.EXPO_PUBLIC_APPWRITE_KNOWLEDGE_CARDS_V2_COLLECTION_ID || 'knowledge-cards-v2';

// Programming knowledge cards data
const PROGRAMMING_KNOWLEDGE_CARDS = [
  // JavaScript Fundamentals
  {
    title: 'JavaScript Variables and Data Types',
    content: `# JavaScript Variables and Data Types

## Variable Declarations
- **var**: Function-scoped, can be redeclared
- **let**: Block-scoped, cannot be redeclared
- **const**: Block-scoped, cannot be reassigned

## Primitive Data Types
1. **Number**: 42, 3.14, NaN, Infinity
2. **String**: "Hello", 'World', \`Template\`
3. **Boolean**: true, false
4. **Undefined**: Variable declared but not assigned
5. **Null**: Intentional absence of value
6. **Symbol**: Unique identifier (ES6+)
7. **BigInt**: Large integers (ES2020+)

## Examples
\`\`\`javascript
let age = 25;           // Number
const name = "Alice";   // String
let isActive = true;    // Boolean
let data;               // Undefined
let result = null;      // Null
\`\`\``,
    category: 'programming',
    tags: ['javascript', 'variables', 'data-types', 'fundamentals'],
    difficulty: 'beginner',
    studyCount: 0,
    lastStudied: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  // Python Fundamentals
  {
    title: 'Python Functions and Scope',
    content: `# Python Functions and Scope

## Function Definition
\`\`\`python
def greet(name, greeting="Hello"):
    return f"{greeting}, {name}!"

# Function call
message = greet("Alice")
print(message)  # Output: Hello, Alice!
\`\`\`

## Scope Rules (LEGB)
1. **Local**: Inside function
2. **Enclosing**: In enclosing function
3. **Global**: At module level
4. **Built-in**: Built-in names

## Lambda Functions
\`\`\`python
# Lambda function
square = lambda x: x ** 2
print(square(5))  # Output: 25

# With map()
numbers = [1, 2, 3, 4, 5]
squared = list(map(lambda x: x ** 2, numbers))
\`\`\``,
    category: 'programming',
    tags: ['python', 'functions', 'scope', 'lambda'],
    difficulty: 'intermediate',
    studyCount: 0,
    lastStudied: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  // Data Structures
  {
    title: 'Arrays and Lists - Core Operations',
    content: `# Arrays and Lists - Core Operations

## JavaScript Arrays
\`\`\`javascript
// Creation
let fruits = ['apple', 'banana', 'orange'];
let numbers = new Array(1, 2, 3, 4, 5);

// Common Methods
fruits.push('grape');        // Add to end
fruits.unshift('mango');     // Add to beginning
let last = fruits.pop();     // Remove from end
let first = fruits.shift();  // Remove from beginning

// Iteration
fruits.forEach(fruit => console.log(fruit));
let upperFruits = fruits.map(fruit => fruit.toUpperCase());
let longFruits = fruits.filter(fruit => fruit.length > 5);
\`\`\`

## Python Lists
\`\`\`python
# Creation
fruits = ['apple', 'banana', 'orange']
numbers = list(range(1, 6))

# Common Methods
fruits.append('grape')       # Add to end
fruits.insert(0, 'mango')   # Insert at index
last = fruits.pop()         # Remove from end
fruits.remove('banana')     # Remove by value

# List Comprehensions
squares = [x**2 for x in range(10)]
even_squares = [x**2 for x in range(10) if x % 2 == 0]
\`\`\`

## Time Complexity
- **Access**: O(1)
- **Search**: O(n)
- **Insertion**: O(1) at end, O(n) at beginning
- **Deletion**: O(1) at end, O(n) at beginning`,
    category: 'programming',
    tags: ['data-structures', 'arrays', 'lists', 'javascript', 'python'],
    difficulty: 'beginner',
    studyCount: 0,
    lastStudied: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  // React Concepts
  {
    title: 'React Hooks - useState and useEffect',
    content: `# React Hooks - useState and useEffect

## useState Hook
\`\`\`jsx
import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);
  const [name, setName] = useState('');

  const increment = () => setCount(count + 1);
  const decrement = () => setCount(prev => prev - 1);

  return (
    <div>
      <h2>Count: {count}</h2>
      <button onClick={increment}>+</button>
      <button onClick={decrement}>-</button>
      
      <input 
        value={name}
        onChange={(e) => setName(e.target.value)}
        placeholder="Enter name"
      />
      <p>Hello, {name}!</p>
    </div>
  );
}
\`\`\`

## useEffect Hook
\`\`\`jsx
import React, { useState, useEffect } from 'react';

function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Effect with dependency array
  useEffect(() => {
    const fetchUser = async () => {
      setLoading(true);
      try {
        const response = await fetch(\`/api/users/\${userId}\`);
        const userData = await response.json();
        setUser(userData);
      } catch (error) {
        console.error('Failed to fetch user:', error);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchUser();
    }
  }, [userId]); // Re-run when userId changes

  if (loading) return <div>Loading...</div>;
  if (!user) return <div>User not found</div>;

  return (
    <div>
      <h1>{user.name}</h1>
      <p>{user.email}</p>
    </div>
  );
}
\`\`\`

## Hook Rules
1. Only call hooks at the top level
2. Only call hooks from React functions
3. Use dependency arrays correctly
4. Clean up side effects`,
    category: 'programming',
    tags: ['react', 'hooks', 'useState', 'useEffect', 'javascript'],
    difficulty: 'intermediate',
    studyCount: 0,
    lastStudied: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

async function seedProgrammingKnowledge() {
  console.log('🌱 Seeding Programming Knowledge Cards...\n');
  
  try {
    console.log('📊 Cards to create:', PROGRAMMING_KNOWLEDGE_CARDS.length);
    console.log('📋 Configuration:');
    console.log(`   Database ID: ${DATABASE_ID}`);
    console.log(`   Collection ID: ${KNOWLEDGE_CARDS_COLLECTION_ID}`);
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const card of PROGRAMMING_KNOWLEDGE_CARDS) {
      try {
        console.log(`   Creating: "${card.title}"`);
        
        const documentId = ID.unique();
        await databases.createDocument(
          DATABASE_ID,
          KNOWLEDGE_CARDS_COLLECTION_ID,
          documentId,
          card
        );
        
        successCount++;
        console.log(`   ✅ Created successfully (ID: ${documentId})`);
        
      } catch (error) {
        errorCount++;
        console.error(`   ❌ Failed to create "${card.title}":`, error.message);
      }
    }
    
    console.log('\n📈 Seeding Summary:');
    console.log(`   ✅ Successfully created: ${successCount} cards`);
    console.log(`   ❌ Failed to create: ${errorCount} cards`);
    console.log(`   📚 Total programming cards: ${successCount}`);
    
    if (successCount > 0) {
      console.log('\n🎯 Programming Knowledge Categories Added:');
      console.log('   • JavaScript fundamentals (variables, data types)');
      console.log('   • Python functions and scope');
      console.log('   • Data structures (arrays, lists)');
      console.log('   • React hooks (useState, useEffect)');
      
      console.log('\n🔍 AI Search Testing Benefits:');
      console.log('   • Review Intent Test: Now has programming content to find');
      console.log('   • Semantic Search: Can match programming concepts');
      console.log('   • Intent Recognition: Better training data for AI');
      console.log('   • Knowledge Graph: Programming relationships');
    }
    
    return successCount > 0;
    
  } catch (error) {
    console.error('💥 Seeding failed:', error);
    return false;
  }
}

// Run the seeding
seedProgrammingKnowledge()
  .then((success) => {
    console.log(`\n🏁 Seeding ${success ? 'completed successfully! 🎉' : 'failed! ❌'}`);
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('\n💥 Seeding crashed:', error);
    process.exit(1);
  });
