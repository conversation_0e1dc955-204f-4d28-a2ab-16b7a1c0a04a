#!/usr/bin/env tsx

/**
 * AppWrite Schema Setup Script for LearniScan
 * 
 * This script automatically creates the database schema, collections,
 * attributes, indexes, and storage buckets for the LearniScan application.
 * 
 * Prerequisites:
 * 1. AppWrite project created
 * 2. Server API key with full permissions
 * 3. Environment variables configured
 * 
 * Usage:
 * npm run setup:appwrite
 * or
 * npx tsx scripts/setup-appwrite-schema.ts
 */

import { Client, Databases, Storage, Permission, Role, IndexType } from 'node-appwrite';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Configuration
const config = {
  endpoint: process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
  projectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID!,
  apiKey: process.env.APPWRITE_API_KEY!, // Server-side API key
  databaseId: process.env.EXPO_PUBLIC_APPWRITE_DATABASE_ID || 'learni-scan-db',
};

// Validate configuration
if (!config.projectId) {
  console.error('❌ EXPO_PUBLIC_APPWRITE_PROJECT_ID is required');
  process.exit(1);
}

if (!config.apiKey) {
  console.error('❌ APPWRITE_API_KEY is required (server-side API key)');
  process.exit(1);
}

// Initialize AppWrite client
const client = new Client()
  .setEndpoint(config.endpoint)
  .setProject(config.projectId)
  .setKey(config.apiKey);

const databases = new Databases(client);
const storage = new Storage(client);

// Utility function to handle errors gracefully
async function safeExecute<T>(
  operation: () => Promise<T>,
  description: string,
  skipIfExists = true
): Promise<T | null> {
  try {
    const result = await operation();
    console.log(`✅ ${description}`);
    return result;
  } catch (error: any) {
    if (skipIfExists && (error.code === 409 || error.message?.includes('already exists'))) {
      console.log(`⚠️  ${description} (already exists, skipping)`);
      return null;
    }
    console.error(`❌ ${description}:`, error.message);
    return null;
  }
}

// Main setup function
async function setupAppwriteSchema() {
  console.log('🚀 Starting AppWrite schema setup for LearniScan...\n');

  try {
    // Step 1: Create database
    await safeExecute(
      () => databases.create(config.databaseId, 'LearniScan Database'),
      'Creating database'
    );

    // Step 2: Create collections
    await createUsersCollection();
    await createKnowledgeCardsCollection();
    await createLearningSessionsCollection();
    await createScanHistoryCollection();

    // Step 3: Create storage buckets
    await createStorageBuckets();

    console.log('\n🎉 AppWrite schema setup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Configure OAuth providers in AppWrite console');
    console.log('2. Customize email templates');
    console.log('3. Set up rate limiting and security settings');
    console.log('4. Test the authentication and database operations');

  } catch (error) {
    console.error('\n💥 Schema setup failed:', error);
    process.exit(1);
  }
}

// Create Users collection
async function createUsersCollection() {
  const collectionId = 'users';
  console.log('\n📝 Setting up Users collection...');

  // Create collection
  await safeExecute(
    () => databases.createCollection(config.databaseId, collectionId, 'Users'),
    'Creating Users collection'
  );

  // Add attributes
  await safeExecute(
    () => databases.createStringAttribute(config.databaseId, collectionId, 'email', 255, true),
    'Adding email attribute'
  );

  await safeExecute(
    () => databases.createStringAttribute(config.databaseId, collectionId, 'name', 100, true),
    'Adding name attribute'
  );

  await safeExecute(
    () => databases.createStringAttribute(config.databaseId, collectionId, 'avatar', 500, false),
    'Adding avatar attribute'
  );

  await safeExecute(
    () => databases.createStringAttribute(config.databaseId, collectionId, 'preferences', 2000, false),
    'Adding preferences attribute'
  );

  await safeExecute(
    () => databases.createStringAttribute(config.databaseId, collectionId, 'learningStats', 1000, false),
    'Adding learningStats attribute'
  );

  await safeExecute(
    () => databases.createStringAttribute(config.databaseId, collectionId, 'subscription', 500, false),
    'Adding subscription attribute'
  );

  // Create indexes (wait for attributes to be ready)
  await new Promise(resolve => setTimeout(resolve, 2000));

  await safeExecute(
    () => databases.createIndex(config.databaseId, collectionId, 'email_unique', IndexType.Unique, ['email']),
    'Creating email unique index'
  );

  await safeExecute(
    () => databases.createIndex(config.databaseId, collectionId, 'name_index', IndexType.Key, ['name']),
    'Creating name index'
  );

  await safeExecute(
    () => databases.createIndex(config.databaseId, collectionId, 'created_at_index', IndexType.Key, ['$createdAt'], ['desc']),
    'Creating created_at index'
  );
}

// Create Knowledge Cards collection
async function createKnowledgeCardsCollection() {
  const collectionId = 'knowledge_cards';
  console.log('\n📚 Setting up Knowledge Cards collection...');

  // Create collection
  await safeExecute(
    () => databases.createCollection(config.databaseId, collectionId, 'Knowledge Cards'),
    'Creating Knowledge Cards collection'
  );

  // Add attributes
  const attributes = [
    { key: 'userId', size: 50, required: true },
    { key: 'title', size: 200, required: true },
    { key: 'content', size: 10000, required: true },
    { key: 'sourceType', size: 50, required: true },
    { key: 'sourceData', size: 5000, required: false },
    { key: 'tags', size: 1000, required: false },
    { key: 'difficulty', size: 20, required: true },
    { key: 'category', size: 100, required: true },
    { key: 'reviewData', size: 1000, required: true },
  ];

  for (const attr of attributes) {
    await safeExecute(
      () => databases.createStringAttribute(config.databaseId, collectionId, attr.key, attr.size, attr.required),
      `Adding ${attr.key} attribute`
    );
  }

  // Boolean attributes
  await safeExecute(
    () => databases.createBooleanAttribute(config.databaseId, collectionId, 'aiEnhanced', true),
    'Adding aiEnhanced attribute'
  );

  await safeExecute(
    () => databases.createBooleanAttribute(config.databaseId, collectionId, 'isPublic', true),
    'Adding isPublic attribute'
  );

  // Create indexes
  await new Promise(resolve => setTimeout(resolve, 3000));

  const indexes = [
    { name: 'userId_index', type: IndexType.Key, attributes: ['userId'] },
    { name: 'category_index', type: IndexType.Key, attributes: ['category'] },
    { name: 'difficulty_index', type: IndexType.Key, attributes: ['difficulty'] },
    { name: 'created_at_index', type: IndexType.Key, attributes: ['$createdAt'], orders: ['desc'] },
    { name: 'isPublic_index', type: IndexType.Key, attributes: ['isPublic'] },
    { name: 'title_fulltext', type: IndexType.Fulltext, attributes: ['title'] },
    { name: 'content_fulltext', type: IndexType.Fulltext, attributes: ['content'] },
  ];

  for (const index of indexes) {
    await safeExecute(
      () => databases.createIndex(config.databaseId, collectionId, index.name, index.type, index.attributes, index.orders),
      `Creating ${index.name} index`
    );
  }
}

// Create Learning Sessions collection
async function createLearningSessionsCollection() {
  const collectionId = 'learning_sessions';
  console.log('\n🎯 Setting up Learning Sessions collection...');

  // Create collection
  await safeExecute(
    () => databases.createCollection(config.databaseId, collectionId, 'Learning Sessions'),
    'Creating Learning Sessions collection'
  );

  // Add attributes
  const stringAttributes = [
    { key: 'userId', size: 50, required: true },
    { key: 'sessionType', size: 20, required: true },
    { key: 'cardsReviewed', size: 2000, required: true },
    { key: 'performance', size: 1000, required: true },
  ];

  for (const attr of stringAttributes) {
    await safeExecute(
      () => databases.createStringAttribute(config.databaseId, collectionId, attr.key, attr.size, attr.required),
      `Adding ${attr.key} attribute`
    );
  }

  // Integer and datetime attributes
  await safeExecute(
    () => databases.createIntegerAttribute(config.databaseId, collectionId, 'duration', true),
    'Adding duration attribute'
  );

  await safeExecute(
    () => databases.createDatetimeAttribute(config.databaseId, collectionId, 'startedAt', true),
    'Adding startedAt attribute'
  );

  await safeExecute(
    () => databases.createDatetimeAttribute(config.databaseId, collectionId, 'completedAt', true),
    'Adding completedAt attribute'
  );

  // Create indexes
  await new Promise(resolve => setTimeout(resolve, 2000));

  const indexes = [
    { name: 'userId_index', type: IndexType.Key, attributes: ['userId'] },
    { name: 'sessionType_index', type: IndexType.Key, attributes: ['sessionType'] },
    { name: 'startedAt_index', type: IndexType.Key, attributes: ['startedAt'], orders: ['desc'] },
    { name: 'completedAt_index', type: IndexType.Key, attributes: ['completedAt'], orders: ['desc'] },
  ];

  for (const index of indexes) {
    await safeExecute(
      () => databases.createIndex(config.databaseId, collectionId, index.name, index.type, index.attributes, index.orders),
      `Creating ${index.name} index`
    );
  }
}

// Create Scan History collection
async function createScanHistoryCollection() {
  const collectionId = 'scan_history';
  console.log('\n📸 Setting up Scan History collection...');

  // Create collection
  await safeExecute(
    () => databases.createCollection(config.databaseId, collectionId, 'Scan History'),
    'Creating Scan History collection'
  );

  // Add string attributes
  const stringAttributes = [
    { key: 'userId', size: 50, required: true },
    { key: 'originalImageId', size: 50, required: true },
    { key: 'extractedText', size: 20000, required: true },
    { key: 'processedContent', size: 20000, required: false },
    { key: 'knowledgeCardId', size: 50, required: false },
    { key: 'scanType', size: 20, required: true },
    { key: 'language', size: 10, required: true },
    { key: 'metadata', size: 1000, required: true },
  ];

  for (const attr of stringAttributes) {
    await safeExecute(
      () => databases.createStringAttribute(config.databaseId, collectionId, attr.key, attr.size, attr.required),
      `Adding ${attr.key} attribute`
    );
  }

  // Float attribute
  await safeExecute(
    () => databases.createFloatAttribute(config.databaseId, collectionId, 'confidence', true),
    'Adding confidence attribute'
  );

  // Create indexes
  await new Promise(resolve => setTimeout(resolve, 2000));

  const indexes = [
    { name: 'userId_index', type: IndexType.Key, attributes: ['userId'] },
    { name: 'scanType_index', type: IndexType.Key, attributes: ['scanType'] },
    { name: 'created_at_index', type: IndexType.Key, attributes: ['$createdAt'], orders: ['desc'] },
    { name: 'knowledgeCardId_index', type: IndexType.Key, attributes: ['knowledgeCardId'] },
  ];

  for (const index of indexes) {
    await safeExecute(
      () => databases.createIndex(config.databaseId, collectionId, index.name, index.type, index.attributes, index.orders),
      `Creating ${index.name} index`
    );
  }
}

// Create storage buckets
async function createStorageBuckets() {
  console.log('\n💾 Setting up Storage buckets...');

  const buckets = [
    {
      id: 'user-avatars',
      name: 'User Profile Pictures',
      permissions: [Permission.create(Role.users()), Permission.read(Role.users()), Permission.update(Role.users()), Permission.delete(Role.users())],
      fileSecurity: true,
      enabled: true,
      maximumFileSize: 5 * 1024 * 1024, // 5MB
      allowedFileExtensions: ['jpg', 'jpeg', 'png', 'webp'],
      compression: 'gzip',
      encryption: true,
      antivirus: true,
    },
    {
      id: 'scan-images',
      name: 'Scanned Documents and Images',
      permissions: [Permission.create(Role.users()), Permission.read(Role.users()), Permission.update(Role.users()), Permission.delete(Role.users())],
      fileSecurity: true,
      enabled: true,
      maximumFileSize: 20 * 1024 * 1024, // 20MB
      allowedFileExtensions: ['jpg', 'jpeg', 'png', 'pdf'],
      compression: 'none', // Preserve quality for OCR
      encryption: true,
      antivirus: true,
    },
    {
      id: 'card-assets',
      name: 'Knowledge Card Images and Assets',
      permissions: [Permission.create(Role.users()), Permission.read(Role.any()), Permission.update(Role.users()), Permission.delete(Role.users())],
      fileSecurity: true,
      enabled: true,
      maximumFileSize: 10 * 1024 * 1024, // 10MB
      allowedFileExtensions: ['jpg', 'jpeg', 'png', 'svg', 'gif'],
      compression: 'gzip',
      encryption: true,
      antivirus: true,
    },
    {
      id: 'user-exports',
      name: 'User Data Exports',
      permissions: [Permission.create(Role.users()), Permission.read(Role.users()), Permission.update(Role.users()), Permission.delete(Role.users())],
      fileSecurity: true,
      enabled: true,
      maximumFileSize: 50 * 1024 * 1024, // 50MB
      allowedFileExtensions: ['pdf', 'json', 'csv', 'zip'],
      compression: 'gzip',
      encryption: true,
      antivirus: true,
    },
  ];

  for (const bucket of buckets) {
    await safeExecute(
      () => storage.createBucket(
        bucket.id,
        bucket.name,
        bucket.permissions,
        bucket.fileSecurity,
        bucket.enabled,
        bucket.maximumFileSize,
        bucket.allowedFileExtensions,
        bucket.compression,
        bucket.encryption,
        bucket.antivirus
      ),
      `Creating ${bucket.name} bucket`
    );
  }
}

// Run the setup
if (require.main === module) {
  setupAppwriteSchema().catch(console.error);
}

export { setupAppwriteSchema };
