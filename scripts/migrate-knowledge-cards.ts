#!/usr/bin/env tsx

/**
 * Knowledge Cards Migration Script
 * 
 * This script migrates existing knowledge cards from the old single-collection
 * schema to the new split-collection architecture.
 * 
 * Migration Process:
 * 1. Read existing knowledge_cards collection
 * 2. Create corresponding records in new collections:
 *    - knowledge_cards_v2 (core metadata)
 *    - knowledge_content (content and source data)
 *    - knowledge_reviews (review data)
 *    - knowledge_tags (tags)
 * 3. Verify migration integrity
 * 4. Optionally backup/archive old collection
 * 
 * Usage:
 * npm run migrate:knowledge-cards
 * or
 * npx tsx scripts/migrate-knowledge-cards.ts
 */

import { Client, Databases, Query } from 'node-appwrite';
import * as dotenv from 'dotenv';
import type { LegacyKnowledgeCard } from '../types/appwrite-v2-fixed';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Configuration
const config = {
  endpoint: process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
  projectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID!,
  apiKey: process.env.APPWRITE_API_KEY!,
  databaseId: process.env.EXPO_PUBLIC_APPWRITE_DATABASE_ID || 'learni-scan-db',
};

// Collection IDs
const OLD_COLLECTION = 'knowledge_cards';
const NEW_COLLECTIONS = {
  cardsV2: 'knowledge_cards_v2',
  content: 'knowledge_content',
  reviews: 'knowledge_reviews',
  tags: 'knowledge_tags',
};

// Initialize AppWrite client
const client = new Client()
  .setEndpoint(config.endpoint)
  .setProject(config.projectId)
  .setKey(config.apiKey);

const databases = new Databases(client);

interface MigrationStats {
  totalCards: number;
  migratedCards: number;
  failedCards: number;
  errors: string[];
  startTime: Date;
  endTime?: Date;
}

// Main migration function
async function migrateKnowledgeCards() {
  console.log('🚀 Starting Knowledge Cards migration...\n');

  const stats: MigrationStats = {
    totalCards: 0,
    migratedCards: 0,
    failedCards: 0,
    errors: [],
    startTime: new Date(),
  };

  try {
    // Step 1: Check if new collections exist
    await verifyNewCollections();

    // Step 2: Get all existing knowledge cards
    console.log('📊 Fetching existing knowledge cards...');
    const legacyCards = await getAllLegacyCards();
    stats.totalCards = legacyCards.length;
    
    console.log(`Found ${stats.totalCards} cards to migrate\n`);

    if (stats.totalCards === 0) {
      console.log('✅ No cards to migrate. Migration completed.');
      return;
    }

    // Step 3: Migrate each card
    console.log('🔄 Starting migration process...\n');
    
    for (let i = 0; i < legacyCards.length; i++) {
      const card = legacyCards[i];
      const progress = `[${i + 1}/${stats.totalCards}]`;
      
      try {
        console.log(`${progress} Migrating: "${card.title}"`);
        await migrateCard(card);
        stats.migratedCards++;
        console.log(`${progress} ✅ Success`);
      } catch (error: any) {
        stats.failedCards++;
        const errorMsg = `${progress} ❌ Failed: ${error.message}`;
        console.error(errorMsg);
        stats.errors.push(`Card "${card.title}" (${card.$id}): ${error.message}`);
      }
    }

    // Step 4: Verify migration
    console.log('\n🔍 Verifying migration...');
    await verifyMigration(stats);

    // Step 5: Summary
    stats.endTime = new Date();
    printMigrationSummary(stats);

  } catch (error: any) {
    console.error('\n💥 Migration failed:', error.message);
    process.exit(1);
  }
}

// Verify that new collections exist
async function verifyNewCollections() {
  console.log('🔍 Verifying new collections exist...');
  
  for (const [name, collectionId] of Object.entries(NEW_COLLECTIONS)) {
    try {
      await databases.getCollection(config.databaseId, collectionId);
      console.log(`✅ ${name}: ${collectionId}`);
    } catch (error) {
      throw new Error(`Collection ${collectionId} does not exist. Please run setup:appwrite:v2 first.`);
    }
  }
  console.log('');
}

// Get all legacy knowledge cards
async function getAllLegacyCards(): Promise<LegacyKnowledgeCard[]> {
  const allCards: LegacyKnowledgeCard[] = [];
  let offset = 0;
  const limit = 100;

  try {
    while (true) {
      const response = await databases.listDocuments(
        config.databaseId,
        OLD_COLLECTION,
        [
          Query.limit(limit),
          Query.offset(offset),
          Query.orderAsc('$createdAt'),
        ]
      );

      if (response.documents.length === 0) break;

      allCards.push(...(response.documents as LegacyKnowledgeCard[]));
      offset += limit;

      console.log(`Fetched ${allCards.length} cards...`);
    }
  } catch (error: any) {
    if (error.code === 404) {
      console.log('⚠️  Legacy knowledge_cards collection not found. Nothing to migrate.');
      return [];
    }
    throw error;
  }

  return allCards;
}

// Migrate a single card
async function migrateCard(legacyCard: LegacyKnowledgeCard) {
  const cardId = legacyCard.$id;

  // 1. Create core card record
  const coreCardData = {
    userId: legacyCard.userId,
    title: legacyCard.title,
    difficulty: legacyCard.difficulty,
    category: legacyCard.category,
    sourceType: legacyCard.sourceType,
    status: 'active',
    aiEnhanced: legacyCard.aiEnhanced,
    isPublic: legacyCard.isPublic,
  };

  const newCard = await databases.createDocument(
    config.databaseId,
    NEW_COLLECTIONS.cardsV2,
    cardId, // Use same ID for easier tracking
    coreCardData
  );

  // 2. Create content record
  const contentData = {
    cardId: newCard.$id,
    content: legacyCard.content,
    sourceData: legacyCard.sourceData ? JSON.stringify(legacyCard.sourceData) : undefined,
  };

  await databases.createDocument(
    config.databaseId,
    NEW_COLLECTIONS.content,
    `${cardId}_content`,
    contentData
  );

  // 3. Create reviews record
  const reviewsData = {
    cardId: newCard.$id,
    nextReview: legacyCard.reviewData.nextReview,
    interval: legacyCard.reviewData.interval,
    easeFactor: legacyCard.reviewData.easeFactor,
    reviewCount: legacyCard.reviewData.reviewCount,
    correctCount: legacyCard.reviewData.correctCount,
    reviewHistory: JSON.stringify([]), // Start with empty history
  };

  await databases.createDocument(
    config.databaseId,
    NEW_COLLECTIONS.reviews,
    `${cardId}_reviews`,
    reviewsData
  );

  // 4. Create tag records
  if (legacyCard.tags && legacyCard.tags.length > 0) {
    for (let i = 0; i < legacyCard.tags.length; i++) {
      const tag = legacyCard.tags[i];
      const tagData = {
        cardId: newCard.$id,
        tag: tag.toLowerCase().trim(),
        tagType: 'user',
      };

      try {
        await databases.createDocument(
          config.databaseId,
          NEW_COLLECTIONS.tags,
          `${cardId}_tag_${i}`,
          tagData
        );
      } catch (error: any) {
        // Skip duplicate tags
        if (!error.message?.includes('already exists')) {
          throw error;
        }
      }
    }
  }
}

// Verify migration integrity
async function verifyMigration(stats: MigrationStats) {
  try {
    // Count records in new collections
    const [cardsCount, contentCount, reviewsCount, tagsCount] = await Promise.all([
      countDocuments(NEW_COLLECTIONS.cardsV2),
      countDocuments(NEW_COLLECTIONS.content),
      countDocuments(NEW_COLLECTIONS.reviews),
      countDocuments(NEW_COLLECTIONS.tags),
    ]);

    console.log(`📊 Migration verification:`);
    console.log(`   Cards v2: ${cardsCount}`);
    console.log(`   Content: ${contentCount}`);
    console.log(`   Reviews: ${reviewsCount}`);
    console.log(`   Tags: ${tagsCount}`);

    // Basic integrity checks
    if (cardsCount !== stats.migratedCards) {
      throw new Error(`Card count mismatch: expected ${stats.migratedCards}, got ${cardsCount}`);
    }

    if (contentCount !== stats.migratedCards) {
      throw new Error(`Content count mismatch: expected ${stats.migratedCards}, got ${contentCount}`);
    }

    if (reviewsCount !== stats.migratedCards) {
      throw new Error(`Reviews count mismatch: expected ${stats.migratedCards}, got ${reviewsCount}`);
    }

    console.log('✅ Migration verification passed');

  } catch (error: any) {
    console.error('❌ Migration verification failed:', error.message);
    throw error;
  }
}

// Count documents in a collection
async function countDocuments(collectionId: string): Promise<number> {
  const response = await databases.listDocuments(
    config.databaseId,
    collectionId,
    [Query.limit(1)]
  );
  return response.total;
}

// Print migration summary
function printMigrationSummary(stats: MigrationStats) {
  const duration = stats.endTime 
    ? Math.round((stats.endTime.getTime() - stats.startTime.getTime()) / 1000)
    : 0;

  console.log('\n' + '='.repeat(50));
  console.log('📋 MIGRATION SUMMARY');
  console.log('='.repeat(50));
  console.log(`Total cards: ${stats.totalCards}`);
  console.log(`Successfully migrated: ${stats.migratedCards}`);
  console.log(`Failed: ${stats.failedCards}`);
  console.log(`Duration: ${duration} seconds`);
  console.log(`Success rate: ${((stats.migratedCards / stats.totalCards) * 100).toFixed(1)}%`);

  if (stats.errors.length > 0) {
    console.log('\n❌ ERRORS:');
    stats.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }

  if (stats.migratedCards === stats.totalCards) {
    console.log('\n🎉 Migration completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Test the new schema with your application');
    console.log('2. Update your code to use the new database service');
    console.log('3. Consider archiving the old knowledge_cards collection');
  } else {
    console.log('\n⚠️  Migration completed with errors.');
    console.log('Please review the errors above and consider re-running for failed cards.');
  }

  console.log('='.repeat(50));
}

// Archive old collection (optional)
async function archiveOldCollection() {
  console.log('\n🗄️  Archiving old collection...');
  
  try {
    // Rename the old collection to indicate it's archived
    // Note: AppWrite doesn't support renaming, so we'll just add a note
    console.log('⚠️  Manual step required: Consider renaming or documenting the old collection as archived');
    console.log('   Collection ID: knowledge_cards');
    console.log('   Suggestion: Add "ARCHIVED_" prefix in your documentation');
  } catch (error: any) {
    console.error('Error archiving old collection:', error.message);
  }
}

// Add script to package.json
function addMigrationScript() {
  console.log('\n📝 Add this script to your package.json:');
  console.log('"migrate:knowledge-cards": "npx tsx scripts/migrate-knowledge-cards.ts"');
}

// Run migration if called directly
if (require.main === module) {
  migrateKnowledgeCards()
    .then(() => {
      console.log('\n✅ Migration script completed');
      addMigrationScript();
    })
    .catch((error) => {
      console.error('\n💥 Migration script failed:', error);
      process.exit(1);
    });
}

export { migrateKnowledgeCards };