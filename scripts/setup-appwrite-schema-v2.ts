#!/usr/bin/env tsx

/**
 * AppWrite Schema Setup Script v2 for LearniScan
 * 
 * This script creates an improved database schema with split Knowledge Cards
 * to work around AppWrite plan limitations and improve performance.
 * 
 * New Architecture:
 * - knowledge_cards: Core card metadata
 * - knowledge_content: Large content and source data
 * - knowledge_reviews: Review data and statistics
 * - knowledge_tags: Tags and categorization
 * 
 * Prerequisites:
 * 1. AppWrite project created
 * 2. Server API key with full permissions
 * 3. Environment variables configured
 * 
 * Usage:
 * npm run setup:appwrite:v2
 * or
 * npx tsx scripts/setup-appwrite-schema-v2.ts
 */

import { Client, Databases, Storage, Permission, Role, IndexType } from 'node-appwrite';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Configuration
const config = {
  endpoint: process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
  projectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID!,
  apiKey: process.env.APPWRITE_API_KEY!, // Server-side API key
  databaseId: process.env.EXPO_PUBLIC_APPWRITE_DATABASE_ID || 'learni-scan-db',
};

// Validate configuration
if (!config.projectId) {
  console.error('❌ EXPO_PUBLIC_APPWRITE_PROJECT_ID is required');
  process.exit(1);
}

if (!config.apiKey) {
  console.error('❌ APPWRITE_API_KEY is required (server-side API key)');
  process.exit(1);
}

// Initialize AppWrite client
const client = new Client()
  .setEndpoint(config.endpoint)
  .setProject(config.projectId)
  .setKey(config.apiKey);

const databases = new Databases(client);
const storage = new Storage(client);

// Utility function to handle errors gracefully
async function safeExecute<T>(
  operation: () => Promise<T>,
  description: string,
  skipIfExists = true
): Promise<T | null> {
  try {
    const result = await operation();
    console.log(`✅ ${description}`);
    return result;
  } catch (error: any) {
    if (skipIfExists && (error.code === 409 || error.message?.includes('already exists'))) {
      console.log(`⚠️  ${description} (already exists, skipping)`);
      return null;
    }
    console.error(`❌ ${description}:`, error.message);
    return null;
  }
}

// Main setup function
async function setupAppwriteSchemaV2() {
  console.log('🚀 Starting AppWrite schema setup v2 for LearniScan...\n');
  console.log('📋 New Architecture: Split Knowledge Cards for better performance\n');

  try {
    // Step 1: Verify database exists (should already exist)
    console.log('🔍 Verifying existing database...');
    
    // Step 2: Create new knowledge collections
    await createKnowledgeCardsV2Collection();
    await createKnowledgeContentCollection();
    await createKnowledgeReviewsCollection();
    await createKnowledgeTagsCollection();

    // Step 3: Verify existing collections
    await verifyExistingCollections();

    // Step 4: Create storage buckets (if not exists)
    await createStorageBuckets();

    console.log('\n🎉 AppWrite schema v2 setup completed successfully!');
    console.log('\n📊 New Knowledge Cards Architecture:');
    console.log('   ├── knowledge_cards (Core metadata)');
    console.log('   ├── knowledge_content (Large content & source data)');
    console.log('   ├── knowledge_reviews (Review data & statistics)');
    console.log('   └── knowledge_tags (Tags & categorization)');
    console.log('\nNext steps:');
    console.log('1. Update TypeScript interfaces for new schema');
    console.log('2. Update database service methods');
    console.log('3. Test the new architecture');
    console.log('4. Migrate existing data if needed');

  } catch (error) {
    console.error('\n💥 Schema setup v2 failed:', error);
    process.exit(1);
  }
}

// Create Knowledge Cards v2 collection (Core metadata only)
async function createKnowledgeCardsV2Collection() {
  const collectionId = 'knowledge_cards_v2';
  console.log('\n📚 Setting up Knowledge Cards v2 collection (Core metadata)...');

  // Create collection
  await safeExecute(
    () => databases.createCollection(config.databaseId, collectionId, 'Knowledge Cards v2'),
    'Creating Knowledge Cards v2 collection'
  );

  // Add core attributes only
  const attributes = [
    { key: 'userId', size: 50, required: true },
    { key: 'title', size: 200, required: true },
    { key: 'difficulty', size: 20, required: true },
    { key: 'category', size: 100, required: true },
    { key: 'sourceType', size: 50, required: true },
    { key: 'status', size: 20, required: true }, // active, archived, deleted
  ];

  for (const attr of attributes) {
    await safeExecute(
      () => databases.createStringAttribute(config.databaseId, collectionId, attr.key, attr.size, attr.required),
      `Adding ${attr.key} attribute`
    );
  }

  // Boolean attributes
  await safeExecute(
    () => databases.createBooleanAttribute(config.databaseId, collectionId, 'aiEnhanced', true),
    'Adding aiEnhanced attribute'
  );

  await safeExecute(
    () => databases.createBooleanAttribute(config.databaseId, collectionId, 'isPublic', true),
    'Adding isPublic attribute'
  );

  // Create indexes
  await new Promise(resolve => setTimeout(resolve, 3000));

  const indexes = [
    { name: 'userId_index', type: IndexType.Key, attributes: ['userId'] },
    { name: 'category_index', type: IndexType.Key, attributes: ['category'] },
    { name: 'difficulty_index', type: IndexType.Key, attributes: ['difficulty'] },
    { name: 'status_index', type: IndexType.Key, attributes: ['status'] },
    { name: 'created_at_index', type: IndexType.Key, attributes: ['$createdAt'], orders: ['desc'] },
    { name: 'isPublic_index', type: IndexType.Key, attributes: ['isPublic'] },
    { name: 'title_fulltext', type: IndexType.Fulltext, attributes: ['title'] },
  ];

  for (const index of indexes) {
    await safeExecute(
      () => databases.createIndex(config.databaseId, collectionId, index.name, index.type, index.attributes, index.orders),
      `Creating ${index.name} index`
    );
  }
}

// Create Knowledge Content collection (Large content and source data)
async function createKnowledgeContentCollection() {
  const collectionId = 'knowledge_content';
  console.log('\n📄 Setting up Knowledge Content collection...');

  // Create collection
  await safeExecute(
    () => databases.createCollection(config.databaseId, collectionId, 'Knowledge Content'),
    'Creating Knowledge Content collection'
  );

  // Add content attributes
  const attributes = [
    { key: 'cardId', size: 50, required: true }, // Reference to knowledge_cards_v2
    { key: 'content', size: 50000, required: true }, // Large content field
    { key: 'sourceData', size: 10000, required: false }, // JSON source data
    { key: 'processedContent', size: 20000, required: false }, // AI-processed content
    { key: 'summary', size: 1000, required: false }, // Content summary
  ];

  for (const attr of attributes) {
    await safeExecute(
      () => databases.createStringAttribute(config.databaseId, collectionId, attr.key, attr.size, attr.required),
      `Adding ${attr.key} attribute`
    );
  }

  // Create indexes
  await new Promise(resolve => setTimeout(resolve, 2000));

  const indexes = [
    { name: 'cardId_unique', type: IndexType.Unique, attributes: ['cardId'] },
    { name: 'content_fulltext', type: IndexType.Fulltext, attributes: ['content'] },
    { name: 'summary_fulltext', type: IndexType.Fulltext, attributes: ['summary'] },
  ];

  for (const index of indexes) {
    await safeExecute(
      () => databases.createIndex(config.databaseId, collectionId, index.name, index.type, index.attributes, index.orders),
      `Creating ${index.name} index`
    );
  }
}

// Create Knowledge Reviews collection (Review data and statistics)
async function createKnowledgeReviewsCollection() {
  const collectionId = 'knowledge_reviews';
  console.log('\n🎯 Setting up Knowledge Reviews collection...');

  // Create collection
  await safeExecute(
    () => databases.createCollection(config.databaseId, collectionId, 'Knowledge Reviews'),
    'Creating Knowledge Reviews collection'
  );

  // Add review attributes
  const stringAttributes = [
    { key: 'cardId', size: 50, required: true }, // Reference to knowledge_cards_v2
    { key: 'reviewHistory', size: 5000, required: true }, // JSON review history
  ];

  for (const attr of stringAttributes) {
    await safeExecute(
      () => databases.createStringAttribute(config.databaseId, collectionId, attr.key, attr.size, attr.required),
      `Adding ${attr.key} attribute`
    );
  }

  // Numeric attributes
  await safeExecute(
    () => databases.createIntegerAttribute(config.databaseId, collectionId, 'interval', true),
    'Adding interval attribute'
  );

  await safeExecute(
    () => databases.createFloatAttribute(config.databaseId, collectionId, 'easeFactor', true),
    'Adding easeFactor attribute'
  );

  await safeExecute(
    () => databases.createIntegerAttribute(config.databaseId, collectionId, 'reviewCount', true),
    'Adding reviewCount attribute'
  );

  await safeExecute(
    () => databases.createIntegerAttribute(config.databaseId, collectionId, 'correctCount', true),
    'Adding correctCount attribute'
  );

  // Datetime attribute
  await safeExecute(
    () => databases.createDatetimeAttribute(config.databaseId, collectionId, 'nextReview', true),
    'Adding nextReview attribute'
  );

  // Create indexes
  await new Promise(resolve => setTimeout(resolve, 2000));

  const indexes = [
    { name: 'cardId_unique', type: IndexType.Unique, attributes: ['cardId'] },
    { name: 'nextReview_index', type: IndexType.Key, attributes: ['nextReview'] },
    { name: 'interval_index', type: IndexType.Key, attributes: ['interval'] },
  ];

  for (const index of indexes) {
    await safeExecute(
      () => databases.createIndex(config.databaseId, collectionId, index.name, index.type, index.attributes, index.orders),
      `Creating ${index.name} index`
    );
  }
}

// Create Knowledge Tags collection (Tags and categorization)
async function createKnowledgeTagsCollection() {
  const collectionId = 'knowledge_tags';
  console.log('\n🏷️  Setting up Knowledge Tags collection...');

  // Create collection
  await safeExecute(
    () => databases.createCollection(config.databaseId, collectionId, 'Knowledge Tags'),
    'Creating Knowledge Tags collection'
  );

  // Add tag attributes
  const attributes = [
    { key: 'cardId', size: 50, required: true }, // Reference to knowledge_cards_v2
    { key: 'tag', size: 100, required: true }, // Individual tag
    { key: 'tagType', size: 20, required: true }, // user, system, ai
    { key: 'confidence', size: 10, required: false }, // AI confidence for auto-tags
  ];

  for (const attr of attributes) {
    await safeExecute(
      () => databases.createStringAttribute(config.databaseId, collectionId, attr.key, attr.size, attr.required),
      `Adding ${attr.key} attribute`
    );
  }

  // Create indexes
  await new Promise(resolve => setTimeout(resolve, 2000));

  const indexes = [
    { name: 'cardId_index', type: IndexType.Key, attributes: ['cardId'] },
    { name: 'tag_index', type: IndexType.Key, attributes: ['tag'] },
    { name: 'tagType_index', type: IndexType.Key, attributes: ['tagType'] },
    { name: 'cardId_tag_unique', type: IndexType.Unique, attributes: ['cardId', 'tag'] },
  ];

  for (const index of indexes) {
    await safeExecute(
      () => databases.createIndex(config.databaseId, collectionId, index.name, index.type, index.attributes, index.orders),
      `Creating ${index.name} index`
    );
  }
}

// Verify existing collections are still working
async function verifyExistingCollections() {
  console.log('\n🔍 Verifying existing collections...');
  
  const existingCollections = ['users', 'learning_sessions', 'scan_history'];
  
  for (const collectionId of existingCollections) {
    await safeExecute(
      () => databases.getCollection(config.databaseId, collectionId),
      `Verifying ${collectionId} collection`
    );
  }
}

// Create storage buckets (same as before)
async function createStorageBuckets() {
  console.log('\n💾 Setting up Storage buckets...');

  const buckets = [
    {
      id: 'user-avatars',
      name: 'User Profile Pictures',
      permissions: [Permission.create(Role.users()), Permission.read(Role.users()), Permission.update(Role.users()), Permission.delete(Role.users())],
      fileSecurity: true,
      enabled: true,
      maximumFileSize: 5 * 1024 * 1024, // 5MB
      allowedFileExtensions: ['jpg', 'jpeg', 'png', 'webp'],
      compression: 'gzip',
      encryption: true,
      antivirus: true,
    },
    {
      id: 'scan-images',
      name: 'Scanned Documents and Images',
      permissions: [Permission.create(Role.users()), Permission.read(Role.users()), Permission.update(Role.users()), Permission.delete(Role.users())],
      fileSecurity: true,
      enabled: true,
      maximumFileSize: 20 * 1024 * 1024, // 20MB
      allowedFileExtensions: ['jpg', 'jpeg', 'png', 'pdf'],
      compression: 'none', // Preserve quality for OCR
      encryption: true,
      antivirus: true,
    },
    {
      id: 'card-assets',
      name: 'Knowledge Card Images and Assets',
      permissions: [Permission.create(Role.users()), Permission.read(Role.any()), Permission.update(Role.users()), Permission.delete(Role.users())],
      fileSecurity: true,
      enabled: true,
      maximumFileSize: 10 * 1024 * 1024, // 10MB
      allowedFileExtensions: ['jpg', 'jpeg', 'png', 'svg', 'gif'],
      compression: 'gzip',
      encryption: true,
      antivirus: true,
    },
    {
      id: 'user-exports',
      name: 'User Data Exports',
      permissions: [Permission.create(Role.users()), Permission.read(Role.users()), Permission.update(Role.users()), Permission.delete(Role.users())],
      fileSecurity: true,
      enabled: true,
      maximumFileSize: 50 * 1024 * 1024, // 50MB
      allowedFileExtensions: ['pdf', 'json', 'csv', 'zip'],
      compression: 'gzip',
      encryption: true,
      antivirus: true,
    },
  ];

  for (const bucket of buckets) {
    await safeExecute(
      () => storage.createBucket(
        bucket.id,
        bucket.name,
        bucket.permissions,
        bucket.fileSecurity,
        bucket.enabled,
        bucket.maximumFileSize,
        bucket.allowedFileExtensions,
        bucket.compression,
        bucket.encryption,
        bucket.antivirus
      ),
      `Creating ${bucket.name} bucket`
    );
  }
}

// Run the setup
if (require.main === module) {
  setupAppwriteSchemaV2().catch(console.error);
}

export { setupAppwriteSchemaV2 };