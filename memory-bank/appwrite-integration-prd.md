# LearniScan AppWrite Integration - Product Requirements Document

## Executive Summary

This document outlines the comprehensive integration of AppWrite Backend-as-a-Service (BaaS) into the LearniScan React Native application. AppWrite will serve as our primary backend infrastructure, providing authentication, database, file storage, real-time capabilities, and cloud functions.

## Backend Architecture Decisions

### Why AppWrite?

1. **React Native First**: Official React Native SDK with excellent TypeScript support
2. **Comprehensive BaaS**: Authentication, databases, storage, functions, and real-time in one platform
3. **Self-Hosted Option**: Can be deployed on-premise for enhanced privacy
4. **Developer Experience**: Intuitive APIs and extensive documentation
5. **Cost Effective**: Competitive pricing with generous free tier
6. **Open Source**: Full transparency and community support

### Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Native  │    │    AppWrite     │    │   External APIs │
│   LearniScan    │◄──►│     Cloud       │◄──►│   (AI Services) │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌────▼────┐             ┌────▼────┐             ┌────▼────┐
    │ Local   │             │Database │             │Functions│
    │ Storage │             │Storage  │             │& APIs   │
    │(AsyncStorage)│        │Files    │             │         │
    └─────────┘             └─────────┘             └─────────┘
```

## Data Models & Database Schema

### Core Collections

#### 1. Users Collection (`users`)
```typescript
interface User {
  $id: string;                    // AppWrite document ID
  email: string;                  // User email (unique)
  name: string;                   // Display name
  avatar?: string;                // Profile picture URL
  preferences: {
    language: string;             // UI language preference
    theme: 'light' | 'dark';      // Theme preference
    notifications: boolean;       // Notification settings
  };
  learningStats: {
    totalCards: number;           // Total knowledge cards created
    totalScans: number;           // Total document scans
    streakDays: number;           // Learning streak
    lastActive: string;           // ISO date string
  };
  subscription?: {
    plan: 'free' | 'premium';     // Subscription tier
    expiresAt?: string;           // ISO date string
  };
  createdAt: string;              // ISO date string
  updatedAt: string;              // ISO date string
}
```

#### 2. Knowledge Cards Collection (`knowledge_cards`)
```typescript
interface KnowledgeCard {
  $id: string;                    // AppWrite document ID
  userId: string;                 // Reference to user
  title: string;                  // Card title
  content: string;                // Main content/summary
  sourceType: 'scan' | 'manual' | 'ai_generated';
  sourceData?: {
    originalText?: string;        // OCR extracted text
    imageUrl?: string;            // Reference to stored image
    documentUrl?: string;         // Reference to stored document
  };
  tags: string[];                 // Categorization tags
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: string;               // Subject category
  reviewData: {
    nextReview: string;           // ISO date string
    interval: number;             // Days until next review
    easeFactor: number;           // Spaced repetition factor
    reviewCount: number;          // Total reviews
    correctCount: number;         // Correct answers
  };
  aiEnhanced: boolean;            // Whether AI has processed this card
  isPublic: boolean;              // Whether card is shared publicly
  createdAt: string;              // ISO date string
  updatedAt: string;              // ISO date string
}
```

#### 3. Learning Sessions Collection (`learning_sessions`)
```typescript
interface LearningSession {
  $id: string;                    // AppWrite document ID
  userId: string;                 // Reference to user
  sessionType: 'review' | 'study' | 'scan';
  cardsReviewed: string[];        // Array of knowledge card IDs
  performance: {
    totalCards: number;           // Cards in session
    correctAnswers: number;       // Correct responses
    averageTime: number;          // Average time per card (seconds)
    accuracy: number;             // Percentage accuracy
  };
  duration: number;               // Session duration in seconds
  startedAt: string;              // ISO date string
  completedAt: string;            // ISO date string
}
```

#### 4. Scan History Collection (`scan_history`)
```typescript
interface ScanHistory {
  $id: string;                    // AppWrite document ID
  userId: string;                 // Reference to user
  originalImageId: string;        // Reference to storage file
  extractedText: string;          // OCR result
  processedContent?: string;      // AI-enhanced content
  knowledgeCardId?: string;       // Generated knowledge card
  scanType: 'document' | 'handwriting' | 'book' | 'whiteboard';
  confidence: number;             // OCR confidence score
  language: string;               // Detected language
  metadata: {
    imageSize: number;            // File size in bytes
    dimensions: {
      width: number;
      height: number;
    };
    processingTime: number;       // Time taken to process
  };
  createdAt: string;              // ISO date string
}
```

### Database Permissions & Security

#### Permission Structure
```typescript
// Read permissions
const readPermissions = [
  Permission.read(Role.user(userId)),     // Owner can read
  Permission.read(Role.team('moderators')) // Moderators can read
];

// Write permissions  
const writePermissions = [
  Permission.write(Role.user(userId)),    // Owner can write
  Permission.update(Role.user(userId)),   // Owner can update
  Permission.delete(Role.user(userId))    // Owner can delete
];

// Public read for shared content
const publicReadPermissions = [
  Permission.read(Role.any()),            // Anyone can read
  Permission.write(Role.user(userId))     // Only owner can write
];
```

## Authentication Flow Design

### Supported Authentication Methods

1. **Email/Password Authentication**
   - Primary authentication method
   - Email verification required
   - Password reset functionality
   - Account recovery options

2. **OAuth Providers**
   - Google OAuth (primary)
   - GitHub OAuth (for developers)
   - Apple Sign-In (iOS requirement)

3. **Anonymous Sessions**
   - Guest mode for app exploration
   - Limited functionality
   - Easy upgrade to full account

### Authentication Flow

```mermaid
graph TD
    A[App Launch] --> B{User Logged In?}
    B -->|Yes| C[Load User Data]
    B -->|No| D[Show Welcome Screen]
    D --> E{Auth Method}
    E -->|Email/Password| F[Email/Password Form]
    E -->|OAuth| G[OAuth Provider]
    E -->|Guest| H[Anonymous Session]
    F --> I[Verify Email]
    G --> J[OAuth Callback]
    H --> K[Limited Access]
    I --> C
    J --> C
    K --> L[Prompt Account Creation]
    C --> M[Main App]
```

### Session Management

```typescript
interface SessionManager {
  // Initialize AppWrite client
  initializeClient(): Promise<void>;
  
  // Authentication methods
  signInWithEmail(email: string, password: string): Promise<User>;
  signUpWithEmail(email: string, password: string, name: string): Promise<User>;
  signInWithOAuth(provider: OAuthProvider): Promise<User>;
  signInAnonymously(): Promise<User>;
  
  // Session management
  getCurrentUser(): Promise<User | null>;
  refreshSession(): Promise<void>;
  signOut(): Promise<void>;
  
  // Account management
  updateProfile(data: Partial<User>): Promise<User>;
  changePassword(oldPassword: string, newPassword: string): Promise<void>;
  resetPassword(email: string): Promise<void>;
  verifyEmail(userId: string, secret: string): Promise<void>;
}
```

## File Storage Strategy

### Storage Buckets Organization

#### 1. User Avatars Bucket (`user-avatars`)
- **Purpose**: Profile pictures
- **File Types**: JPEG, PNG, WebP
- **Size Limit**: 5MB per file
- **Permissions**: User read/write own files
- **Compression**: Automatic image optimization

#### 2. Scan Images Bucket (`scan-images`)
- **Purpose**: Original scanned documents/images
- **File Types**: JPEG, PNG, PDF
- **Size Limit**: 20MB per file
- **Permissions**: User read/write own files
- **Retention**: Permanent storage

#### 3. Knowledge Card Assets Bucket (`card-assets`)
- **Purpose**: Images, diagrams for knowledge cards
- **File Types**: JPEG, PNG, SVG, GIF
- **Size Limit**: 10MB per file
- **Permissions**: User read/write own files, public read for shared cards
- **CDN**: Enabled for fast delivery

#### 4. Exports Bucket (`user-exports`)
- **Purpose**: Generated exports (PDF, JSON)
- **File Types**: PDF, JSON, CSV
- **Size Limit**: 50MB per file
- **Permissions**: User read/write own files
- **Retention**: 30 days auto-deletion

### File Upload Strategy

```typescript
interface FileUploadService {
  // Upload with progress tracking
  uploadFile(
    bucketId: string,
    file: File,
    permissions?: string[],
    onProgress?: (progress: number) => void
  ): Promise<FileUploadResult>;
  
  // Generate optimized thumbnails
  generateThumbnail(fileId: string, width: number, height: number): Promise<string>;
  
  // Get file download URL
  getFileUrl(bucketId: string, fileId: string): string;
  
  // Delete file
  deleteFile(bucketId: string, fileId: string): Promise<void>;
  
  // Batch operations
  uploadMultipleFiles(files: FileUpload[]): Promise<FileUploadResult[]>;
}
```

## Real-time Features Implementation

### Real-time Subscriptions

1. **Learning Progress Updates**
   - Live streak updates
   - Achievement notifications
   - Progress synchronization across devices

2. **Collaborative Features**
   - Shared knowledge card updates
   - Community contributions
   - Live study sessions

3. **System Notifications**
   - Review reminders
   - New feature announcements
   - Maintenance notifications

### WebSocket Integration

```typescript
interface RealtimeService {
  // Subscribe to user-specific updates
  subscribeToUserUpdates(userId: string, callback: (data: any) => void): () => void;
  
  // Subscribe to knowledge card changes
  subscribeToCardUpdates(cardId: string, callback: (data: any) => void): () => void;
  
  // Subscribe to learning session updates
  subscribeToSessionUpdates(sessionId: string, callback: (data: any) => void): () => void;
  
  // Manage connection state
  connect(): Promise<void>;
  disconnect(): void;
  isConnected(): boolean;
}
```

## Integration with Existing Features

### Knowledge Management System
- **Current**: Local storage with AsyncStorage
- **Enhanced**: Cloud synchronization with offline-first approach
- **Migration**: Gradual migration of existing data to AppWrite

### AI Integration
- **Current**: Direct API calls to AI services
- **Enhanced**: AppWrite Functions as middleware for AI processing
- **Benefits**: Rate limiting, caching, cost optimization

### Camera & OCR Features
- **Current**: Local image processing
- **Enhanced**: Cloud storage with processing pipeline
- **Workflow**: Capture → Upload → Process → Store Results

## Security & Privacy Considerations

### Data Protection
1. **Encryption**: All data encrypted in transit and at rest
2. **Access Control**: Role-based permissions for all resources
3. **Audit Logging**: Complete audit trail for data access
4. **GDPR Compliance**: Right to deletion and data portability

### Privacy Features
1. **Local-First**: Critical data cached locally
2. **Selective Sync**: Users control what syncs to cloud
3. **Anonymous Mode**: Full functionality without account
4. **Data Export**: Complete data export in standard formats

## Performance Optimization

### Caching Strategy
1. **Local Cache**: AsyncStorage for frequently accessed data
2. **Image Cache**: Cached thumbnails and optimized images
3. **Query Cache**: SWR for API response caching
4. **Offline Queue**: Queue operations when offline

### Bandwidth Optimization
1. **Incremental Sync**: Only sync changed data
2. **Compression**: Automatic file compression
3. **CDN**: Global content delivery network
4. **Lazy Loading**: Load data as needed

## Migration Plan

### Phase 1: Foundation (Week 1-2)
- Set up AppWrite project and basic configuration
- Install and configure React Native SDK
- Implement basic authentication

### Phase 2: Core Features (Week 3-4)
- Migrate user management to AppWrite
- Implement knowledge card storage
- Set up file upload system

### Phase 3: Advanced Features (Week 5-6)
- Add real-time subscriptions
- Implement collaborative features
- Optimize performance and caching

### Phase 4: Testing & Launch (Week 7-8)
- Comprehensive testing
- Data migration from existing users
- Gradual rollout with feature flags

## Success Metrics

### Technical Metrics
- **API Response Time**: < 200ms for 95% of requests
- **File Upload Speed**: < 5 seconds for 10MB files
- **Offline Sync**: < 30 seconds after reconnection
- **Crash Rate**: < 0.1% of sessions

### User Experience Metrics
- **Authentication Success Rate**: > 99%
- **Data Sync Reliability**: > 99.9%
- **Feature Adoption**: > 80% for core features
- **User Satisfaction**: > 4.5/5 rating

## Risk Mitigation

### Technical Risks
1. **Vendor Lock-in**: Implement abstraction layer for easy migration
2. **Service Downtime**: Implement robust offline capabilities
3. **Data Loss**: Multiple backup strategies and versioning
4. **Performance Issues**: Comprehensive monitoring and alerting

### Business Risks
1. **Cost Scaling**: Implement usage monitoring and optimization
2. **Compliance**: Regular security audits and compliance checks
3. **User Adoption**: Gradual rollout with user feedback integration
4. **Competition**: Focus on unique value propositions and user experience
