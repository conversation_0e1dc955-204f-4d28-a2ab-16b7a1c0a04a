# Project Brief: Pocket Manus

## Project Overview

Pocket Manus is a mobile application designed to provide users with an AI chat experience, featuring both remote and local AI model execution capabilities.

## Core Goals

1. Create a user-friendly AI chat interface for iOS and Android devices
2. Enable seamless integration with remote AI services via `ai-sdk`
3. Implement local AI model execution using `@pocketpalai/llama.rn`
4. Provide a responsive and engaging user experience

## Development Stages

### Stage 1: Remote AI Integration (Current)
- Integration with remote AI services using `ai-sdk`
- OpenAI-compatible API endpoints
- Real-time chat interface
- Message streaming capabilities

### Stage 2: Local Model Integration (Planned)
- Integration with `@pocketpalai/llama.rn`
- Local AI model execution on iOS and Android
- Offline chat capabilities
- Performance optimization for mobile devices

## Key Features

1. **AI Chat Interface**
   - Real-time message streaming
   - Chat history management
   - Support for both text input and output

2. **Authentication System**
   - User accounts
   - Secure session management
   - OAuth integration capabilities

3. **Cross-Platform Support**
   - iOS and Android compatibility
   - Responsive design
   - Platform-specific optimizations

4. **Performance**
   - Efficient message handling
   - Optimized local model execution
   - Smooth UI transitions

## Technical Foundation

- React Native with Expo for cross-platform development
- TypeScript for type safety
- NativeWind for styling
- Zustand for state management
- AI SDK for model integration
