# Technical Context

## Development Environment

### Core Requirements
- Node.js
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio (for Android development)
- Git

### Development Setup
```bash
# Install dependencies
npm install

# Start development server
npm run start

# Run on iOS
npm run ios

# Run on Android
npm run android

# Build for production
npm run build:android  # Android
npm run build:ios      # iOS
```

## Technology Stack

### Core Framework
- **React Native** (v0.76.3)
- **Expo** (v52.0.15)
  - Managed workflow
  - File-based routing with expo-router
  - EAS for builds

### State Management & Data
- **Zustand** (v5.0.2)
  - Global state management
  - Persistent storage integration
- **AsyncStorage**
  - Local data persistence
  - User preferences storage

### UI & Styling
- **NativeWind** (v4.1.23)
  - TailwindCSS integration
  - Cross-platform styling
- **Expo Components**
  - Vector Icons
  - Blur effects
  - Image handling
- **React Navigation**
  - Bottom tabs navigation
  - Stack navigation
  - Type-safe routing

### AI Integration
- **ai-sdk**
  - OpenAI compatibility
  - Streaming support
  - Local model integration
- **@ai-sdk/openai** (v1.2.3)
  - OpenAI API integration
  - Message streaming
- **@ai-sdk/openai-compatible** (v0.1.15)
  - Custom endpoints support
  - Model compatibility layer
- **@ai-sdk/react** (v1.1.22)
  - React hooks for AI integration
  - UI components

### Planned Integrations
- **@pocketpalai/llama.rn**
  - Local model execution
  - Mobile optimization
  - Offline capabilities

### Development Tools
- **TypeScript** (v5.3.3)
  - Type safety
  - Developer tooling
  - Code documentation
- **TypeDoc** (v0.27.3)
  - API documentation
  - Code documentation generation

### Testing & Quality
- **Jest** (v29.2.1)
  - Unit testing
  - Component testing
  - Integration testing
- **ESLint**
  - Code quality
  - Style enforcement
- **Commitlint**
  - Commit message standardization
  - Git workflow enforcement

## Core Dependencies

```json
{
  "dependencies": {
    "@expo/vector-icons": "^14.0.2",
    "@react-native-async-storage/async-storage": "1.23.1",
    "expo": "~52.0.15",
    "expo-router": "~4.0.11",
    "nativewind": "^4.1.23",
    "react": "18.3.1",
    "react-native": "0.76.3",
    "zustand": "^5.0.2"
  }
}
```

## Environment Configuration

### Configuration Files
- **.env.local**
  - Local environment variables
  - API keys and endpoints
- **app.config.js**
  - Expo configuration
  - Dynamic config values
- **tsconfig.json**
  - TypeScript settings
  - Path aliases
- **babel.config.js**
  - Babel configuration
  - Plugin management

### Environment Variables
```typescript
// Required environment variables
WEB_APP_URL: string
API_URL: string
GITHUB_CLIENT_ID: string
GITHUB_CLIENT_SECRET: string
GITHUB_CALLBACK_URL: string
```

## Platform-Specific Configuration

### iOS
```json
{
  "ios": {
    "supportsTablet": true,
    "bundleIdentifier": "com.barney666.pocket.manus",
    "infoPlist": {
      "NSPhotoLibraryUsageDescription": "..."
    }
  }
}
```

### Android
```json
{
  "android": {
    "adaptiveIcon": {
      "foregroundImage": "./assets/images/adaptive-icon.png",
      "backgroundColor": "#000000"
    },
    "package": "com.barney666.pocket.manus"
  }
}
```

## Technical Constraints

1. **Mobile Performance**
   - Memory usage for AI models
   - Battery optimization
   - Storage management

2. **Network Handling**
   - Offline support
   - Data synchronization
   - Connection state management

3. **Device Compatibility**
   - iOS minimum version
   - Android minimum version
   - Hardware requirements

4. **Security**
   - Data encryption
   - Token management
   - API security

## Development Guidelines

1. **Code Style**
   - TypeScript usage
   - ESLint rules
   - Component structure

2. **Documentation**
   - Code comments
   - TypeDoc annotations
   - README maintenance

3. **Testing**
   - Unit test coverage
   - Integration testing
   - E2E testing

4. **Version Control**
   - Commit conventions
   - Branch management
   - Release process
