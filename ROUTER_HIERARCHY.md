```
./app
├── _layout.tsx                 # Root layout
├── index.tsx                   # Entry point
├── welcome.tsx                 # ✅ matches welcome.html
│
├── (main)                      # Main app navigation
│   ├── _layout.tsx             # Tab navigator
│   ├── scan.tsx                # ✅ matches scan.html
│   ├── knowledge-cards.tsx     # 🆕 matches knowledge-cards.html
│   ├── knowledge-graph.tsx     # 🆕 matches knowledge-graph.html
│   ├── ai-chat.tsx             # 🆕 matches ai-chat.html
│   └── quiz.tsx                # 🆕 matches quiz.html
│
├── (workflows)                 # Document processing flows
│   ├── _layout.tsx             # Stack navigator
│   ├── text-review.tsx         # ✅ matches text-review.html (renamed from text-recognition)
│   └── social-share.tsx        # 🆕 matches social-share.html
│
├── components                  # Shared components
│   ├── ui/
│   └── learni-scan/
│
└── api
    └── chat+api.ts
```
