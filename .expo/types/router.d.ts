/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/modal`; params?: Router.UnknownInputParams; } | { pathname: `/welcome-new`; params?: Router.UnknownInputParams; } | { pathname: `/welcome`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(document-stack)'}/document-editor` | `/document-editor`; params?: Router.UnknownInputParams; } | { pathname: `${'/(document-stack)'}/document-viewer` | `/document-viewer`; params?: Router.UnknownInputParams; } | { pathname: `${'/(document-stack)'}/text-recognition` | `/text-recognition`; params?: Router.UnknownInputParams; } | { pathname: `${'/(drawer)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(features)'}/ai-chat` | `/ai-chat`; params?: Router.UnknownInputParams; } | { pathname: `${'/(features)'}/library` | `/library`; params?: Router.UnknownInputParams; } | { pathname: `${'/(features)'}/settings/knowledge-seeding` | `/settings/knowledge-seeding`; params?: Router.UnknownInputParams; } | { pathname: `${'/(features)'}/study` | `/study`; params?: Router.UnknownInputParams; } | { pathname: `${'/(features)'}/study/session` | `/study/session`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/ai-search-cards` | `/ai-search-cards`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/cards` | `/cards`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/graph` | `/graph`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/share` | `/share`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/bones/CardsList` | `/bones/CardsList`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/bones/KnowledgeCard` | `/bones/KnowledgeCard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/components/AIGraphAnalysisPanel` | `/components/AIGraphAnalysisPanel`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/components/AIInsightsDisplay` | `/components/AIInsightsDisplay`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/components/GraphControlsSheet` | `/components/GraphControlsSheet`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/components/InteractiveGraphSVG` | `/components/InteractiveGraphSVG`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/components/KnowledgeGraphSVG` | `/components/KnowledgeGraphSVG`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/components/SimpleKnowledgeGraph` | `/components/SimpleKnowledgeGraph`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/contexts/KnowledgeGraphContext` | `/contexts/KnowledgeGraphContext`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/types/graph` | `/types/graph`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/utils/graphDataConverter` | `/utils/graphDataConverter`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/utils/layoutAlgorithms` | `/utils/layoutAlgorithms`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/search` | `/search`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/knowledge` | `/knowledge`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/camera-working` | `/scan/camera-working`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/camera` | `/scan/camera`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/gallery` | `/scan/gallery`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/review` | `/scan/review`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/upload` | `/scan/upload`; params?: Router.UnknownInputParams; } | { pathname: `${'/(workflows)'}/social-share` | `/social-share`; params?: Router.UnknownInputParams; } | { pathname: `${'/(workflows)'}/text-review` | `/text-review`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/performance`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/signup`; params?: Router.UnknownInputParams; } | { pathname: `/settings/accessibility`; params?: Router.UnknownInputParams; } | { pathname: `/settings/voice`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/login`; params?: Router.UnknownOutputParams; } | { pathname: `/modal`; params?: Router.UnknownOutputParams; } | { pathname: `/welcome-new`; params?: Router.UnknownOutputParams; } | { pathname: `/welcome`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(document-stack)'}/document-editor` | `/document-editor`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(document-stack)'}/document-viewer` | `/document-viewer`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(document-stack)'}/text-recognition` | `/text-recognition`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(drawer)'}/settings` | `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(features)'}/ai-chat` | `/ai-chat`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(features)'}/library` | `/library`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(features)'}/settings/knowledge-seeding` | `/settings/knowledge-seeding`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(features)'}/study` | `/study`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(features)'}/study/session` | `/study/session`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/ai-search-cards` | `/ai-search-cards`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/cards` | `/cards`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/graph` | `/graph`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/share` | `/share`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/bones/CardsList` | `/bones/CardsList`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/bones/KnowledgeCard` | `/bones/KnowledgeCard`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/components/AIGraphAnalysisPanel` | `/components/AIGraphAnalysisPanel`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/components/AIInsightsDisplay` | `/components/AIInsightsDisplay`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/components/GraphControlsSheet` | `/components/GraphControlsSheet`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/components/InteractiveGraphSVG` | `/components/InteractiveGraphSVG`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/components/KnowledgeGraphSVG` | `/components/KnowledgeGraphSVG`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/components/SimpleKnowledgeGraph` | `/components/SimpleKnowledgeGraph`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/contexts/KnowledgeGraphContext` | `/contexts/KnowledgeGraphContext`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/types/graph` | `/types/graph`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/utils/graphDataConverter` | `/utils/graphDataConverter`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/utils/layoutAlgorithms` | `/utils/layoutAlgorithms`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/home` | `/home`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/search` | `/search`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/knowledge` | `/knowledge`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scan/camera-working` | `/scan/camera-working`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scan/camera` | `/scan/camera`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scan/gallery` | `/scan/gallery`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scan/review` | `/scan/review`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scan/upload` | `/scan/upload`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(workflows)'}/social-share` | `/social-share`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(workflows)'}/text-review` | `/text-review`; params?: Router.UnknownOutputParams; } | { pathname: `/analytics/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/analytics/performance`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/login`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/signup`; params?: Router.UnknownOutputParams; } | { pathname: `/settings/accessibility`; params?: Router.UnknownOutputParams; } | { pathname: `/settings/voice`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `/modal${`?${string}` | `#${string}` | ''}` | `/welcome-new${`?${string}` | `#${string}` | ''}` | `/welcome${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(document-stack)'}/document-editor${`?${string}` | `#${string}` | ''}` | `/document-editor${`?${string}` | `#${string}` | ''}` | `${'/(document-stack)'}/document-viewer${`?${string}` | `#${string}` | ''}` | `/document-viewer${`?${string}` | `#${string}` | ''}` | `${'/(document-stack)'}/text-recognition${`?${string}` | `#${string}` | ''}` | `/text-recognition${`?${string}` | `#${string}` | ''}` | `${'/(drawer)'}/settings${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | `${'/(features)'}/ai-chat${`?${string}` | `#${string}` | ''}` | `/ai-chat${`?${string}` | `#${string}` | ''}` | `${'/(features)'}/library${`?${string}` | `#${string}` | ''}` | `/library${`?${string}` | `#${string}` | ''}` | `${'/(features)'}/settings/knowledge-seeding${`?${string}` | `#${string}` | ''}` | `/settings/knowledge-seeding${`?${string}` | `#${string}` | ''}` | `${'/(features)'}/study${`?${string}` | `#${string}` | ''}` | `/study${`?${string}` | `#${string}` | ''}` | `${'/(features)'}/study/session${`?${string}` | `#${string}` | ''}` | `/study/session${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/ai-search-cards${`?${string}` | `#${string}` | ''}` | `/ai-search-cards${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/cards${`?${string}` | `#${string}` | ''}` | `/cards${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/graph${`?${string}` | `#${string}` | ''}` | `/graph${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/share${`?${string}` | `#${string}` | ''}` | `/share${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/bones/CardsList${`?${string}` | `#${string}` | ''}` | `/bones/CardsList${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/bones/KnowledgeCard${`?${string}` | `#${string}` | ''}` | `/bones/KnowledgeCard${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/components/AIGraphAnalysisPanel${`?${string}` | `#${string}` | ''}` | `/components/AIGraphAnalysisPanel${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/components/AIInsightsDisplay${`?${string}` | `#${string}` | ''}` | `/components/AIInsightsDisplay${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/components/GraphControlsSheet${`?${string}` | `#${string}` | ''}` | `/components/GraphControlsSheet${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/components/InteractiveGraphSVG${`?${string}` | `#${string}` | ''}` | `/components/InteractiveGraphSVG${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/components/KnowledgeGraphSVG${`?${string}` | `#${string}` | ''}` | `/components/KnowledgeGraphSVG${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/components/SimpleKnowledgeGraph${`?${string}` | `#${string}` | ''}` | `/components/SimpleKnowledgeGraph${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/contexts/KnowledgeGraphContext${`?${string}` | `#${string}` | ''}` | `/contexts/KnowledgeGraphContext${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/types/graph${`?${string}` | `#${string}` | ''}` | `/types/graph${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/utils/graphDataConverter${`?${string}` | `#${string}` | ''}` | `/utils/graphDataConverter${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/utils/layoutAlgorithms${`?${string}` | `#${string}` | ''}` | `/utils/layoutAlgorithms${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/home${`?${string}` | `#${string}` | ''}` | `/home${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/search${`?${string}` | `#${string}` | ''}` | `/search${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/knowledge${`?${string}` | `#${string}` | ''}` | `/knowledge${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scan/camera-working${`?${string}` | `#${string}` | ''}` | `/scan/camera-working${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scan/camera${`?${string}` | `#${string}` | ''}` | `/scan/camera${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scan/gallery${`?${string}` | `#${string}` | ''}` | `/scan/gallery${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scan/review${`?${string}` | `#${string}` | ''}` | `/scan/review${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scan/upload${`?${string}` | `#${string}` | ''}` | `/scan/upload${`?${string}` | `#${string}` | ''}` | `${'/(workflows)'}/social-share${`?${string}` | `#${string}` | ''}` | `/social-share${`?${string}` | `#${string}` | ''}` | `${'/(workflows)'}/text-review${`?${string}` | `#${string}` | ''}` | `/text-review${`?${string}` | `#${string}` | ''}` | `/analytics/dashboard${`?${string}` | `#${string}` | ''}` | `/analytics/performance${`?${string}` | `#${string}` | ''}` | `/auth/login${`?${string}` | `#${string}` | ''}` | `/auth/signup${`?${string}` | `#${string}` | ''}` | `/settings/accessibility${`?${string}` | `#${string}` | ''}` | `/settings/voice${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/modal`; params?: Router.UnknownInputParams; } | { pathname: `/welcome-new`; params?: Router.UnknownInputParams; } | { pathname: `/welcome`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(document-stack)'}/document-editor` | `/document-editor`; params?: Router.UnknownInputParams; } | { pathname: `${'/(document-stack)'}/document-viewer` | `/document-viewer`; params?: Router.UnknownInputParams; } | { pathname: `${'/(document-stack)'}/text-recognition` | `/text-recognition`; params?: Router.UnknownInputParams; } | { pathname: `${'/(drawer)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(features)'}/ai-chat` | `/ai-chat`; params?: Router.UnknownInputParams; } | { pathname: `${'/(features)'}/library` | `/library`; params?: Router.UnknownInputParams; } | { pathname: `${'/(features)'}/settings/knowledge-seeding` | `/settings/knowledge-seeding`; params?: Router.UnknownInputParams; } | { pathname: `${'/(features)'}/study` | `/study`; params?: Router.UnknownInputParams; } | { pathname: `${'/(features)'}/study/session` | `/study/session`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/ai-search-cards` | `/ai-search-cards`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/cards` | `/cards`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/graph` | `/graph`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/share` | `/share`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/bones/CardsList` | `/bones/CardsList`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/bones/KnowledgeCard` | `/bones/KnowledgeCard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/components/AIGraphAnalysisPanel` | `/components/AIGraphAnalysisPanel`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/components/AIInsightsDisplay` | `/components/AIInsightsDisplay`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/components/GraphControlsSheet` | `/components/GraphControlsSheet`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/components/InteractiveGraphSVG` | `/components/InteractiveGraphSVG`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/components/KnowledgeGraphSVG` | `/components/KnowledgeGraphSVG`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/components/SimpleKnowledgeGraph` | `/components/SimpleKnowledgeGraph`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/contexts/KnowledgeGraphContext` | `/contexts/KnowledgeGraphContext`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/types/graph` | `/types/graph`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/utils/graphDataConverter` | `/utils/graphDataConverter`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/utils/layoutAlgorithms` | `/utils/layoutAlgorithms`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/search` | `/search`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/knowledge` | `/knowledge`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/camera-working` | `/scan/camera-working`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/camera` | `/scan/camera`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/gallery` | `/scan/gallery`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/review` | `/scan/review`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/upload` | `/scan/upload`; params?: Router.UnknownInputParams; } | { pathname: `${'/(workflows)'}/social-share` | `/social-share`; params?: Router.UnknownInputParams; } | { pathname: `${'/(workflows)'}/text-review` | `/text-review`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/performance`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/signup`; params?: Router.UnknownInputParams; } | { pathname: `/settings/accessibility`; params?: Router.UnknownInputParams; } | { pathname: `/settings/voice`; params?: Router.UnknownInputParams; };
    }
  }
}
