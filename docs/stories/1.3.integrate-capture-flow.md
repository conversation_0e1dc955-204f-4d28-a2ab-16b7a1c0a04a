# Story 1.3: Integrate Content Capture Flow with Knowledge Features

## Status: Draft

## Story

**As a** user, **I want** to be guided to my new knowledge cards or graph after a successful scan, **so that** I can see the results of my action.

## Acceptance Criteria

1. After a successful scan, a prompt or button allows direct navigation to the new knowledge content.
2. Data from the scan is correctly passed and displayed on the destination screen.

## Integration Verification

1. IV1: The data flow from `TextRecognitionScreen` to the knowledge screens is functional.
2. IV2: The existing scanning functionality is not negatively affected.

## Tasks / Subtasks

### Task 1: Implement Post-Scan Navigation Flow
- [ ] Add navigation prompt/button to scan review screen after successful card generation
- [ ] Implement navigation to Knowledge Hub with newly created card highlighted
- [ ] Add navigation option to Knowledge Graph with new content integrated

### Task 2: Enhance Data Flow Integration
- [ ] Ensure scan data is properly passed to knowledge features
- [ ] Implement card highlighting/focus when navigating from scan
- [ ] Add scan metadata to knowledge card display

### Task 3: Update UI Components
- [ ] Add "View in Knowledge Hub" button to scan review screen
- [ ] Add "View in Knowledge Graph" button to scan review screen
- [ ] Implement success feedback with navigation options

## Dev Notes

### Previous Story Insights
- Story 1.1: CardHeader component is properly implemented and Knowledge Cards screen is functional
- Story 1.2: Knowledge Hub is implemented with modern UI and provides centralized navigation to all knowledge features

### Data Models
**Source: types/appwrite-v2.ts, lib/services/knowledge-cards.service.ts**
- `CompleteKnowledgeCard`: Main knowledge card structure with content, reviews, and metadata
- `CreateKnowledgeCardInput`: Input structure for creating new cards from scan data
- `ScanHistory`: Scan record structure linking to knowledge cards via `knowledgeCardId`

### API Specifications
**Source: lib/services/appwrite.service.ts, lib/services/knowledge-cards.service.ts**
- `createScanWithKnowledgeCard()`: Creates both scan record and knowledge card in single operation
- `createCard()`: Knowledge cards service method for creating cards with permissions and usage tracking
- Navigation flow: `app/(tabs)/scan/review.tsx` → Knowledge Hub or Knowledge Cards screen

### Component Architecture
**Source: app/(tabs)/scan/review.tsx, app/(tabs)/knowledge.tsx, app/(knowledge)/cards.tsx**
- Scan Review Screen: Contains AI card generation logic and success states
- Knowledge Hub: Centralized navigation with feature cards for Knowledge Cards and Graph
- Knowledge Cards Screen: Displays cards with filtering and view modes

### Navigation Patterns
**Source: app/(tabs)/_layout.tsx, app/(knowledge)/_layout.tsx**
- Tab Navigation: Main tabs include scan and knowledge features
- Stack Navigation: Knowledge features use stack navigation within knowledge route group
- Router Integration: Use `expo-router` for programmatic navigation between features

### Integration Points
**Source: app/(tabs)/scan/review.tsx lines 461-484**
- AI Card Generation: Existing logic creates knowledge cards from scan data
- Card Creation Flow: `aiCardGenerator.convertToCardInput()` → `createCard()` → success state
- Data Persistence: Cards are created with scan metadata and source references

### Technical Constraints
**Source: docs/architecture.md, .kiro/steering/tech.md**
- React Native/Expo framework with TypeScript
- Appwrite backend integration must remain unchanged
- Navigation must integrate with existing MorphingTabBar
- UI must follow candy color theme and glass morphism patterns

### File Paths and Naming
**Source: .kiro/steering/structure.md**
- Scan screens: `app/(tabs)/scan/`
- Knowledge screens: `app/(knowledge)/` and `app/(tabs)/knowledge.tsx`
- Navigation components: `components/navigation/`
- Services: `lib/services/`

### Testing Requirements
**Source: .kiro/steering/tech.md**
- Test navigation flow from scan to knowledge features
- Verify data integrity in knowledge card creation
- Test existing scan functionality remains unaffected
- Ensure proper error handling for navigation failures

### Security Considerations
**Source: lib/services/knowledge-cards.service.ts**
- Permission checks for card creation are already implemented
- Usage limits and tracking are handled by existing services
- User authentication is managed by existing auth flow

### Performance Considerations
**Source: .kiro/steering/tech.md**
- Navigation should maintain 60fps animations
- Card creation should not block UI during scan processing
- Proper cleanup of navigation state and subscriptions

## Testing

### Unit Tests
- [ ] Test navigation helper functions
- [ ] Test data passing between screens
- [ ] Test error handling for failed navigation

### Integration Tests
- [ ] Test complete scan-to-knowledge flow
- [ ] Test Knowledge Hub highlighting of new cards
- [ ] Test Knowledge Graph integration with new content

### User Acceptance Tests
- [ ] Verify user can navigate to new knowledge content after scan
- [ ] Verify scan data is correctly displayed in knowledge features
- [ ] Verify existing scan functionality is not affected

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-04 | 1.0 | Initial story creation | PM Agent |
