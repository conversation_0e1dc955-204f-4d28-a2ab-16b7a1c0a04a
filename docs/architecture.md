Here is the complete **Brownfield Enhancement Architecture** document we've constructed.

# LearniScan Brownfield Enhancement Architecture

### 1. Introduction

This document outlines the architectural approach for enhancing the **LearniScan** application with a **unified and refined knowledge presentation experience**. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development of the new UI flows while ensuring seamless integration with the existing system.

**Relationship to Existing Architecture:**
This document supplements the existing project architecture by defining how new components and libraries will integrate with the current systems. Where conflicts arise between new and existing patterns, this document provides guidance on maintaining consistency while implementing the enhancements.

#### **1.1 Existing Project Analysis**

* **Current Project State:**
    * **Primary Purpose:** A React Native (Expo) mobile application for knowledge capture and learning.
    * **Current Tech Stack:** The project uses React Native, Expo, TypeScript, and Appwrite for its backend. The PRD specifies that new development will incorporate `@gluestack-ui`, `nativewind`, `zustand`, `zod`, `axios`, and `@ai-sdk/react`.
    * **Architecture Style:** The application follows a component-based architecture, with a clear separation of screens (e.g., `CaptureScreen`, `DeckListScreen`) and services (e.g., `src/services/appwrite/config.ts`). Navigation is handled by `react-navigation`.
    * **Identified Constraints:** The system has critical technical debt, including a rendering error on the `Knowledge Cards Screen` that blocks the primary user flow. The data flow between the capture/OCR screen and the knowledge presentation screens is incomplete.

#### **1.2 Change Log**

| Change | Date | Version | Description | Author |
| :--- | :--- | :--- | :--- | :--- |
| Created | 2025-08-04 | 1.0 | Initial draft of the Brownfield Enhancement Architecture. | Winston (Architect) |

### 2. Enhancement Scope and Integration Strategy

#### **2.1 Enhancement Overview**

* **Enhancement Type:** UI/UX Overhaul & Major Feature Modification
* **Scope:** The project will analyze, refactor, and unify the knowledge presentation user flows, specifically on the `DeckListScreen`, `CardListScreen`, and `LessonGenerationScreen`.
* **Integration Impact:** Significant. This involves fixing critical bugs, introducing new libraries, and refactoring major user-facing features.

#### **2.2 Integration Approach**

* **Code Integration Strategy:** The enhancement will introduce `@gluestack-ui`, `nativewind`, and `tailwindcss` for building the new UI components. `Zustand` will be implemented for global state management related to the knowledge features, and `axios` will continue to be used for API requests. A new "Knowledge Hub" screen will be created to act as a central navigation point, simplifying the user journey.
* **Database Integration:** No direct database schema changes are required. The application will continue to interact with the Appwrite database using the existing service layer patterns.
* **API Integration:** All interactions with the Appwrite backend must remain compatible with the current API. No breaking backend changes are in scope.
* **UI Integration:** The redesigned screens must adopt the visual identity (colors, fonts, etc.) of the existing Home and Camera screens to ensure a cohesive look and feel.

#### **2.3 Compatibility Requirements**

* **Existing API Compatibility:** The enhancement must not require any breaking changes to the Appwrite backend.
* **Database Schema Compatibility:** All existing user data must remain compatible with the redesigned screens without requiring data migration.
* **UI/UX Consistency:** The redesigned screens must feel like a natural part of the existing application, maintaining consistency with unchanged screens.
* **Performance Impact:** The new implementation must not introduce performance degradation. It should meet or exceed the responsiveness of the current application.

### 3. Tech Stack Alignment

#### **3.1 Existing Technology Stack**

The enhancement must be built upon and remain fully compatible with the existing core technologies of the LearniScan application.

| Category | Current Technology | Version (from lockfile) | Usage in Enhancement | Notes |
| :--- | :--- | :--- | :--- | :--- |
| Framework | React Native / Expo | ~0.73.6 / ~50.0.17 | Core application runtime | Must maintain compatibility. |
| Language | TypeScript | ~5.3.3 | All new code must be strictly typed | Adherence to existing `tsconfig.json` is required. |
| Backend | Appwrite | (as configured) | All data fetching and mutations | No breaking changes to the Appwrite integration are permitted. |
| Navigation | React Navigation | ~6.1.17 | Handle all screen transitions | New screens must integrate with the existing navigation stack. |

#### **3.2 New Technology Additions**

The following libraries will be introduced to facilitate this enhancement. Their use should be standardized across all new components.

| Technology | Purpose | Rationale |
| :--- | :--- | :--- |
| `@gluestack-ui`, `nativewind`, `tailwindcss` | UI Framework | To build a consistent, modern, and maintainable design system for the refactored screens. |
| `axios` | HTTP Requests | A robust, standardized library for making promise-based requests to the Appwrite backend. |
| `zustand` | Global State Management | A minimal, fast solution for handling shared UI state across the knowledge screens without excessive boilerplate. |
| `usehooks-ts` | Utility Hooks | A collection of well-tested, reusable React hooks to accelerate development of common UI patterns. |
| `zod` | Runtime Validation | To ensure data from Appwrite and user inputs matches expected schemas, preventing runtime errors. |
| `@ai-sdk/react` | AI Integration | To integrate with Gemini for potential future AI-driven features related to knowledge digestion. |
| `react-native-reanimated` | Animation | To create performant, fluid animations for UI transitions and interactions. |
| `@shopify/react-native-skia`| 2D Graphics Rendering | To render high-performance 2D graphics for knowledge diagrams and cards, offering superior control. |

### 4. Data Models and Schema Changes

#### **4.1 New Data Models**

This enhancement is focused on the frontend and does not require any new data models or backend schema changes.

#### **4.2 Schema Integration Strategy**

* **Database Changes Required:** None. The project must adhere to the PRD's compatibility requirements.
* **Client-Side Validation:** To ensure data integrity, **Zod** schemas will be created on the client side to validate data received from the Appwrite API.

### 5. Component Architecture

#### **5.1 New Components**

We will implement a **Container/Presenter pattern** to separate logic from UI.

* **Component: `KnowledgeHub`**: A new container component serving as the central navigation point for knowledge features.
* **Component: `CardsListContainer`**: A "container" component to fetch card lists, manage loading/error states via **React Suspense**, and handle view logic.
* **Component: `CardsListPresenter`**: A "presenter" component that receives props and renders the UI for the card list using `@gluestack-ui`.
* **Component: `KnowledgeCardContainer`**: A "container" to manage state and actions for a single card, using `useOptimistic` for UI updates.
* **Component: `KnowledgeCardPresenter`**: A visual component for a single card, using `react-native-reanimated` for interactions.

#### **5.2 Component Interaction Diagram**

```mermaid
graph TD
    subgraph Navigation
        A[MorphingTabBar] --> B[KnowledgeHub];
    end

    subgraph Knowledge Cards Feature
        B --> C[CardsListContainer];
        C -- Manages State & Data --> D[zustand Store];
        C -- Fetches Data --> E[Appwrite Service];
        C -- Wraps in Suspense --> F[CardsListPresenter];
        F -- Renders multiple --> G[KnowledgeCardContainer];
        G -- Manages single card state --> D;
        G -- Wraps in Suspense --> H[KnowledgeCardPresenter];
    end
````

### 6\. API Design and Integration

#### **6.1 API Integration Strategy**

The strategy is to consume the existing Appwrite backend APIs without any modifications, as mandated by the PRD. Authentication will continue to be handled by the existing Appwrite integration.

#### **6.2 New API Endpoints**

No new internal API endpoints will be created.

### 7\. External API Integration

#### **7.1 Generative AI Service (Gemini / OpenAI)**

  * **Purpose:** To power new knowledge digestion features.
  * **Technology:** `@ai-sdk/react`.
  * **Authentication & Security:** The AI Service API Key **must** be stored securely in an **Appwrite Cloud Function**. The React Native client will call this secure function, which then calls the AI service. This prevents exposing the API key in the client app.

### 8\. Source Tree Integration

#### **8.1 New File Organization**

New files will be integrated into the existing structure to maintain organization:


project-root/
├── app/
│   ├── (knowledge)/
│   │   ├── _layout.tsx             # New: Layout for the knowledge hub
│   │   ├── index.tsx               # New: The KnowledgeHub screen
└── src/
    ├── components/
    │   └── knowledge/              # New: For reusable presentation components
    │       ├── CardsListPresenter.tsx
    │       └── KnowledgeCardPresenter.tsx
    ├── services/
    │   └── appwrite/
    │       └── functions/          # New: For Appwrite cloud functions
    │           └── call-ai-service.ts
    └── stores/                     # New: For Zustand stores
        └── knowledgeStore.ts


### 9\. Infrastructure and Deployment Integration

#### **9.1 Existing Infrastructure**

  * The project uses **Expo Application Services (EAS)** for building and deploying the client app and **Appwrite Cloud** for the backend.

#### **9.2 Enhancement Deployment Strategy**

1.  **Backend:** The new `call-ai-service` Appwrite Function must be deployed first.
2.  **Frontend:** A new version of the client app will be built and submitted via EAS.

#### **9.3 Rollback Strategy**

  * **Backend:** Rollback involves re-deploying a previous version of the Appwrite Function.
  * **Frontend (Recommended):** The UI refactor should be wrapped in a **feature flag** to allow for remote disabling in case of critical issues, avoiding a slow app store resubmission process.

### 10\. Coding Standards and Conventions

#### **10.1 Enhancement-Specific Standards**

  * **Control Flow:** Use early returns (guard clauses).
  * **Type Safety:** Use strict TypeScript and Zod for runtime validation.
  * **Design Patterns:** Use Strategy patterns over complex conditionals; use Decorator/Container patterns to separate concerns.
  * **State & Performance:** Utilize the specified React hooks (`Suspense`, `useOptimistic`, `useTransition`, `useActionState`, etc.) for state and performance management.
  * **Animation & Rendering:** Use `react-native-reanimated` for animations and consider `@shopify/react-native-skia` for complex graphics.
  * **State Management:** Prioritize Context Providers for state shared across more than two levels; prohibit misuse of global state.

### 11\. Testing Strategy

  * **Unit Tests:** "Presenter" components and complex hooks will be unit-tested with Jest and React Native Testing Library.
  * **Integration Tests:** "Container" components will be tested by mocking Appwrite services to verify data flows.
  * **Manual E2E Regression Testing:** After every completed story, a manual e2e test **must be performed** using the `#ios-simulator` to ensure UI consistency and prevent regressions.

### 12\. Security Integration

  * **API Key Management:** The Gemini/OpenAI API key **must** be stored as a secure environment variable in an Appwrite Cloud Function and never exposed in the client app.
  * **Data Privacy:** Ensure no unnecessary Personally Identifiable Information (PII) is sent to the external AI service.
  * **Security Testing:** Code reviews will verify the API key is not in the client bundle. The Appwrite Function's permissions will be tested to ensure it requires authentication.

### 13\. Checklist Results Report

  * **Status:** **APPROVED**. The architecture is validated against the architect-checklist and is deemed robust, comprehensive, and ready for the implementation phase. It successfully integrates new requirements while respecting the constraints of the existing system.

