# LearniScan Brownfield Enhancement PRD

### 1. Intro Project Analysis and Context

#### **1.1 Existing Project Overview**

**Analysis Source**
* IDE-based fresh analysis from `codebase_aug4.bak`.

**Current Project State (Revised)**
* The project is a React Native mobile app built with Expo, designed for knowledge capture and learning. It allows users to scan information and have it broken down into digestible formats like cards and lessons, all powered by an Appwrite backend. Key screens identified include `CaptureScreen.tsx`, `DeckListScreen.tsx`, `CardListScreen.tsx`, and `LessonGenerationScreen.tsx`.

#### **1.2 Available Documentation Analysis**

* **Available Documentation**
    * [ ] Tech Stack Documentation
    * [ ] Source Tree/Architecture
    * [ ] Coding Standards
    * [ ] API Documentation
    * [ ] External API Documentation
    * [ ] UX/UI Guidelines
    * [ ] Technical Debt Documentation
* **Note:** Dedicated documentation for these areas was not found in the codebase. It is recommended to run the `*document-project` task with the Architect agent to generate this for future work.

#### **1.3 Enhancement Scope Definition (Revised)**

**Enhancement Type**
* [x] UI/UX Overhaul
* [x] Major Feature Modification

**Enhancement Description**
* The primary goal is to analyze the application's existing user flows from the perspective of a real user, specifically focusing on the **knowledge breakdown and presentation screens** (e.g., `DeckListScreen`, `CardListScreen`, `LessonGenerationScreen`). This involves creating flowcharts for these features and then redesigning them to be more straightforward, intuitive, and efficient.
* **Out of Scope:** The `Home` and `CaptureScreen` (camera) flows will not be part of this enhancement.

**Impact Assessment**
* [x] **Significant Impact** (substantial existing code changes are expected to refactor user flows in the targeted screens)

#### **1.4 Goals and Background Context**

**Goals**
* Refine the user journey for knowledge digestion to be seamless and intuitive.
* Improve the efficiency of how users interact with and learn from their captured knowledge (cards, lessons, etc.).
* Increase user engagement by making the knowledge presentation more straightforward and visually appealing.

**Background Context**
* The application's core value is turning scanned information into digestible knowledge. The current user flow for accessing and interacting with this processed knowledge may not be fully optimized. This enhancement focuses on refining these critical post-capture screens to ensure the user experience is as frictionless and effective as possible, thereby strengthening the main purpose of the app.

#### **1.5 Change Log**

| Change | Date | Version | Description | Author |
| :--- | :--- | :--- | :--- | :--- |
| Created | 2025-08-04 | 1.0 | Initial draft of the Brownfield Enhancement PRD. | John (PM) |

### 2. Requirements

#### **Functional**

1.  FR1: The current user flows for the knowledge breakdown and presentation screens (`DeckListScreen`, `CardListScreen`, `LessonGenerationScreen`, etc.) must be analyzed and documented as flowcharts.
2.  FR2: A redesigned, more intuitive, and efficient user flow for accessing, managing, and learning from knowledge cards and lessons must be proposed and documented.
3.  FR3: The UI/UX for the knowledge presentation screens must be re-implemented according to the new, approved design.
4.  FR4: The new design must provide users with a clearer and more straightforward path to interact with their captured knowledge.

#### **Non-Functional**

1.  NFR1: The redesigned screens must maintain a consistent visual style and user experience with the existing, unchanged screens (e.g., Home, Camera).
2.  NFR2: The application's performance on target mobile devices must not be negatively impacted by the new UI/UX changes.
3.  NFR3: The new implementation must adhere to the existing technology stack, including React Native, Expo, TypeScript, and Appwrite services.

#### **Compatibility Requirements**

1.  CR1: The functionality and UI of the Home and Camera screens must remain completely unchanged.
2.  CR2: The enhancement must not require any breaking changes to the existing Appwrite backend schema or APIs.
3.  CR3: All existing user data (decks, cards, lessons) must be fully accessible and compatible with the redesigned screens without data loss or corruption.
4.  CR4: The existing navigation structure must be respected; new flows should integrate seamlessly without breaking existing navigation paths from other parts of the app.

### 3. User Interface Enhancement Goals

#### **3.1 Integration with Existing UI**

The new user flows for knowledge presentation will be designed to feel like a natural extension of the existing application. We will use the specified UI technology stack (`@gluestack-ui`, `nativewind`, `tailwind css`) to adopt the established visual patterns from the Home and Camera screens, including color palettes, typography, spacing, and component styles.

#### **3.2 Modified/New Screens and Views**

Based on the investigation, our focus for modification and redesign will be on the following screens and their components:

* **Knowledge Graph Screen (`app/(knowledge)/graph.tsx`)**
* **Knowledge Cards Screen (`app/(knowledge)/cards.tsx`)**
* **Text Recognition Screen (`app/(document-stack)/text-recognition.tsx`)**

#### **3.3 UI Consistency Requirements**

The redesigned flows must create a unified "Knowledge Center". This means establishing a single, clear entry point and ensuring consistent navigation patterns (like using the `MorphingTabBar`) across all knowledge-related screens.

### 4. Technical Constraints and Integration Requirements

#### **4.1 Existing and Required Technology Stack**

The enhancement must be implemented using the project's established technology stack:

* **UI Framework:** `@gluestack-ui` + `nativewind` + `tailwind css`
* **HTTP Requests:** `axios`
* **Global State Management:** `zustand`
* **Custom Hooks:** `usehooks-ts`
* **Type Validation:** `Zod`
* **AI Integration:** `@ai-sdk/react` for Gemini AI integration

#### **4.2 Integration Approach**

* A primary goal is to create a seamless data flow from content capture to knowledge presentation. The `TextRecognitionScreen` output must be clearly and functionally integrated with both the `Knowledge Graph` and `Knowledge Cards` screens.
* The scattered entry points must be unified. A single, logical navigation path should be established to access all knowledge features.

#### **4.3 Critical Technical Debt and Risks**

* **Blocking Render Errors:** The application is currently broken due to a missing `CardHeader` component, preventing access to the Knowledge Cards screen. This must be fixed before any new UX work can be implemented.
* **Incomplete Integration:** The scanning and knowledge presentation workflows are disconnected, which breaks the core user journey.

### 5. Epic and Story Structure

#### **5.1 Epic Approach**

* **Epic Structure Decision:** This enhancement will be managed as a single epic to ensure that the interconnected technical fixes, UI redesigns, and UX flow improvements are developed and tested as a cohesive unit.

### 6. Epic 1: Unify and Refine the Knowledge Presentation Experience

**Epic Goal:** To resolve critical technical blockers, consolidate the scattered knowledge features into a unified and intuitive user flow, and redesign the presentation screens to be more efficient and engaging for the user.

---

#### **Story 1.1: Fix Critical Rendering Error on Knowledge Cards Screen**

* **As a** user, **I want** to access the Knowledge Cards screen without the application crashing, **so that** I can view and interact with my knowledge cards.
* **Acceptance Criteria:**
    1.  The technical error related to the missing `CardHeader` component is resolved.
    2.  The `app/(knowledge)/cards.tsx` screen loads successfully.
    3.  The application remains stable, and no new console errors are introduced.
* **Integration Verification:**
    1.  IV1: Navigating to the Knowledge Cards screen works correctly.
    2.  IV2: The existing navigation header and tab bar render correctly on the screen.

---

#### **Story 1.2: Establish a Unified Navigation Hub for Knowledge Features**

* **As a** user, **I want** a single, clear entry point to access all my knowledge features, **so that** I don't get confused by scattered navigation paths.
* **Acceptance Criteria:**
    1.  A new "Knowledge Hub" screen or a unified tab is created.
    2.  This hub provides clear navigation to both "Knowledge Graph" and "Knowledge Cards".
    3.  Previous, scattered entry points are removed or redirected to this new hub.
* **Integration Verification:**
    1.  IV1: The new hub is accessible from the main application navigation.
    2.  IV2: Navigation from the hub to the Graph and Cards screens works correctly.

---

#### **Story 1.3: Integrate Content Capture Flow with Knowledge Features**

* **As a** user, **I want** to be guided to my new knowledge cards or graph after a successful scan, **so that** I can see the results of my action.
* **Acceptance Criteria:**
    1.  After a successful scan, a prompt or button allows direct navigation to the new knowledge content.
    2.  Data from the scan is correctly passed and displayed on the destination screen.
* **Integration Verification:**
    1.  IV1: The data flow from `TextRecognitionScreen` to the knowledge screens is functional.
    2.  IV2: The existing scanning functionality is not negatively affected.

---

#### **Story 1.4: Redesign Knowledge Graph Screen for Clarity and Onboarding**

* **As a** new user, **I want** to understand the Knowledge Graph and how to populate it, **so that** I can start using the feature effectively.
* **Acceptance Criteria:**
    1.  The empty graph screen displays a helpful onboarding message.
    2.  Each view mode (Flowchart, Mind Map, Network) includes a tooltip explaining its purpose.
    3.  Navigation and button labels are made more intuitive.
* **Integration Verification:**
    1.  IV1: The redesigned screen still correctly displays graph data when available.
    2.  IV2: The view mode toggles function as expected.

---

#### **Story 1.5: Redesign Knowledge Cards Screen for Intuitive Interaction**

* **As a** user, **I want** a clear and efficient interface for studying and managing my knowledge cards, **so that** I can focus on learning.
* **Acceptance Criteria:**
    1.  The user flow to access cards is simplified via the new Knowledge Hub.
    2.  The UI for viewing cards (Grid and Study views) is refined for clarity.
    3.  Quick actions on cards (flipping, changing status) are intuitive and responsive.
* **Integration Verification:**
    1.  IV1: The redesigned screen correctly fetches and displays card data from Appwrite.
    2.  IV2: All actions performed on a card are correctly persisted to the backend.