export function extractHFModelTitle(modelId: string): string {
  // Remove "GGUF", "-GGUF", or "_GGUF" at the end regardless of case
  const sanitizedModelId = modelId.replace(/[-_]?[Gg][Gg][Uu][Ff]$/, '');

  // If there is no "/" in the modelId, ie owner is not included, return sanitizedModelId
  if (!sanitizedModelId.includes('/')) {
    return sanitizedModelId;
  }

  // Remove owner from the modelId
  const match = sanitizedModelId.match(/^([^/]+)\/(.+)$/);
  return match ? match[2] : 'Unknown';
}
