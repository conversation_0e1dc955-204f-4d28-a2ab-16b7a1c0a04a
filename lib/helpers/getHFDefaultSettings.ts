import type { CompletionParams } from '@pocketpalai/llama.rn';
import type { IChatTemplateConfig } from '../../interfaces/IChatTemplateConfig';
import type { IHuggingFaceModel } from '@/interfaces/IHuggingFaceModel';
import { defaultCompletionParams } from '@/lib/configs/defaultCompletionParams';

export function getHFDefaultSettings(hfModel: IHuggingFaceModel): {
  chatTemplate: IChatTemplateConfig;
  completionParams: CompletionParams;
} {
  const _defaultChatTemplate = {
    addBosToken: false, // It is expected that chat templates will take care of this
    addEosToken: false, // It is expected that chat templates will take care of this
    bosToken: hfModel.specs?.gguf?.bos_token ?? '',
    eosToken: hfModel.specs?.gguf?.eos_token ?? '',
    //chatTemplate: hfModel.specs?.gguf?.chat_template ?? '',
    chatTemplate: '', // At the moment chatTemplate needs to be nunjucks, not jinja2. So by using empty string we force the use of gguf's chat template.
    addGenerationPrompt: true,
    systemPrompt: '',
    name: 'custom',
  };

  const _defaultCompletionParams = {
    ...defaultCompletionParams,
    stop: _defaultChatTemplate.eosToken ? [_defaultChatTemplate.eosToken] : [],
  };

  return {
    chatTemplate: _defaultChatTemplate,
    completionParams: _defaultCompletionParams,
  };
}
