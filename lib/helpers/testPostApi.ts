export const testPostApi = async () => {
  try {
    // Replace with your Expo dev server URL if different
    const response = await fetch('http://localhost:8081/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/octet-stream',
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: 'Hello, are you working?' }
        ]
      }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }
    
    // For streaming responses
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      throw new EvalError(`response body is null, ${JSON.stringify(response)}`);
    }
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      console.log(decoder.decode(value));
    }
  } catch (error) {
    console.error('Error testing API:', error);
  }
};