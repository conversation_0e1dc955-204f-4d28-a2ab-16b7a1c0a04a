export const testGetApi = async () => {
  try {
    // Replace with your Expo dev server URL if different
    const response = await fetch('http://localhost:8081/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/octet-stream',
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: 'Hello, are you working?' }
        ]
      }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }
    
    const data = await response;
    console.log('API Response:', data);
    return data;
  } catch (error) {
    console.error('Error testing API:', error);
  }
};