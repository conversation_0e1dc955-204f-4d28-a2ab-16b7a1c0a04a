import type { IModelFileDetails } from '@/interfaces/IModelFileDetails';
import { urls } from '@/lib/configs/url';
/**
 * Fetches the details of the model's files. Mainly the size is used.
 * @param modelId - The ID of the model.
 * @returns An array of ModelFileDetails.
 */
export const fetchModelFilesDetails = async (
  modelId: string,
): Promise<IModelFileDetails[]> => {
  const url = `${urls.modelTree(modelId)}?recursive=true`;

  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Error fetching model files: ${response.statusText}`);
    }

    const data: IModelFileDetails[] = await response.json();
    return data;
  } catch (error) {
    console.error('Failed to fetch model files:', error);
    throw error;
  }
};