import type { IHuggingFaceModel } from '@/interfaces/IHuggingFaceModel'
import type { IModelFile } from '@/interfaces/IModelFile';
import type { IModel } from '@/interfaces/IModel';
import { clone } from 'radash';
import { ModelOrigin } from '@/types/ModelOrigin';
import { getHFDefaultSettings } from '@/lib/helpers/getHFDefaultSettings';
import { extractHFModelType } from '@/lib/helpers/extractHFModelType';
import { extractHFModelTitle } from '@/lib/helpers/extractHFModelTitle';

export function hfAsModel(
  hfModel: IHuggingFaceModel,
  modelFile: IModelFile,
): IModel {
  const defaultSettings = getHFDefaultSettings(hfModel);

  const _model: IModel = {
    id: hfModel.id + '/' + modelFile.rfilename,
    type: extractHFModelType(hfModel.id),
    author: hfModel.author,
    name: extractHFModelTitle(modelFile.rfilename),
    description: '',
    size: modelFile.size ?? 0,
    params: hfModel.specs?.gguf?.total ?? 0,
    isDownloaded: false,
    downloadUrl: modelFile.url ?? '',
    hfUrl: hfModel.url ?? '',
    progress: 0,
    filename: modelFile.rfilename,
    //fullPath: '',
    isLocal: false,
    origin: ModelOrigin.HF,
    defaultChatTemplate: defaultSettings.chatTemplate,
    chatTemplate: clone(defaultSettings.chatTemplate),
    defaultCompletionSettings: defaultSettings.completionParams,
    completionSettings: {...defaultSettings.completionParams},
    defaultStopWords: defaultSettings.completionParams.stop,
    stopWords: defaultSettings.completionParams.stop,
    hfModelFile: modelFile,
    hfModel: hfModel,
  };

  return _model;
}