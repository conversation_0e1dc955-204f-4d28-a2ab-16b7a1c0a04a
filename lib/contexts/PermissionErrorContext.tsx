/**
 * Permission Error Context
 *
 * Global context for managing permission error popups throughout the app.
 * Shows registration encouragement modal when guest users encounter AppWrite permission errors.
 */

import type React from "react";
import {
	createContext,
	type ReactNode,
	useContext,
	useEffect,
	useState,
} from "react";
import { PermissionErrorBoundary } from "@/components/auth/PermissionErrorBoundary";
import { PermissionErrorModal } from "@/components/auth/PermissionErrorModal";
import {
	isGuestUserId,
	isPermissionError,
} from "@/lib/utils/permissionErrorUtils";
import { useAuth } from "./AuthContext";

interface PermissionErrorContextType {
	showPermissionError: (errorMessage?: string) => void;
	hidePermissionError: () => void;
	isPermissionErrorVisible: boolean;
}

const PermissionErrorContext = createContext<
	PermissionErrorContextType | undefined
>(undefined);

interface PermissionErrorProviderProps {
	children: ReactNode;
}

export const PermissionErrorProvider: React.FC<
	PermissionErrorProviderProps
> = ({ children }) => {
	const [isVisible, setIsVisible] = useState(false);
	const [errorMessage, setErrorMessage] = useState<string>("");
	const { user } = useAuth();

	const showPermissionError = (message: string = "Permission denied") => {
		// Only show for guest users (anonymous sessions)
		if (
			user &&
			(user.$id.includes("anonymous") || user.$id.startsWith("dev-guest-user"))
		) {
			setErrorMessage(message);
			setIsVisible(true);
		}
	};

	// Handle permission errors caught by ErrorBoundary
	const handleErrorBoundaryPermissionError = (error: Error, userId?: string) => {
		console.log('🔐 ErrorBoundary caught permission error:', error.message);

		// Check if current user is a guest user
		const currentUserId = userId || user?.$id;
		if (currentUserId && isGuestUserId(currentUserId)) {
			showPermissionError(error.message);
		}
	};

	const hidePermissionError = () => {
		setIsVisible(false);
		setErrorMessage("");
	};

	const contextValue: PermissionErrorContextType = {
		showPermissionError,
		hidePermissionError,
		isPermissionErrorVisible: isVisible,
	};

	return (
		<PermissionErrorContext.Provider value={contextValue}>
			<PermissionErrorBoundary onPermissionError={handleErrorBoundaryPermissionError}>
				{children}
			</PermissionErrorBoundary>
			<PermissionErrorModal
				isOpen={isVisible}
				onClose={hidePermissionError}
				errorMessage={errorMessage}
			/>
		</PermissionErrorContext.Provider>
	);
};

export const usePermissionError = (): PermissionErrorContextType => {
	const context = useContext(PermissionErrorContext);
	if (context === undefined) {
		throw new Error(
			"usePermissionError must be used within a PermissionErrorProvider",
		);
	}
	return context;
};

export default PermissionErrorProvider;
