/**
 * Knowledge Cards Context - Global State Management
 * 
 * This context provides global state management for knowledge cards:
 * - CRUD operations with optimistic updates
 * - Caching and synchronization
 * - Real-time updates and notifications
 * - Study session management
 * - Statistics tracking
 * 
 * Following Rule 11: State Management Architecture
 * Following Rule 12: Validation & Error Handling
 */

import React, { createContext, useContext, useEffect, useState, useCallback, useRef, type ReactNode } from 'react';
import { z } from 'zod';
import { knowledgeCardsService } from '@/lib/services/knowledge-cards.service';
import { useAuth } from './AuthContext';
import type { 
  CompleteKnowledgeCard, 
  CreateKnowledgeCardInput, 
  UpdateKnowledgeCardInput 
} from '@/types/appwrite-v2-fixed';

// Zod schemas for state validation (Rule 11)
const KnowledgeCardsStateSchema = z.object({
  cards: z.array(z.any()), // CompleteKnowledgeCard type
  isLoading: z.boolean(),
  error: z.string().nullable(),
  lastUpdated: z.string().nullable(),
  hasMore: z.boolean(),
  currentPage: z.number(),
  totalCards: z.number(),
  filters: z.object({
    category: z.string().optional(),
    difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
    searchTerm: z.string().optional(),
    tags: z.array(z.string()).optional(),
  }),
  studySession: z.object({
    isActive: z.boolean(),
    cards: z.array(z.any()),
    currentIndex: z.number(),
    startTime: z.string().nullable(),
  }),
  statistics: z.object({
    totalCards: z.number(),
    reviewsDue: z.number(),
    streakDays: z.number(),
    cardsByDifficulty: z.record(z.number()),
    cardsByCategory: z.record(z.number()),
  }),
});

type KnowledgeCardsState = z.infer<typeof KnowledgeCardsStateSchema>;

interface KnowledgeCardsContextType {
  // State
  state: KnowledgeCardsState;
  
  // Card operations
  createCard: (input: CreateKnowledgeCardInput) => Promise<CompleteKnowledgeCard>;
  updateCard: (cardId: string, updates: UpdateKnowledgeCardInput) => Promise<CompleteKnowledgeCard | null>;
  deleteCard: (cardId: string) => Promise<boolean>;
  getCard: (cardId: string) => Promise<CompleteKnowledgeCard | null>;
  
  // List operations
  loadCards: (refresh?: boolean) => Promise<void>;
  loadMore: () => Promise<void>;
  searchCards: (searchTerm: string) => Promise<void>;
  filterCards: (filters: KnowledgeCardsState['filters']) => Promise<void>;
  
  // Study operations
  startStudySession: (cardIds?: string[]) => Promise<void>;
  endStudySession: () => void;
  nextCard: () => void;
  previousCard: () => void;
  recordReview: (cardId: string, correct: boolean, difficulty: 'easy' | 'good' | 'hard' | 'again') => Promise<void>;
  
  // Statistics
  refreshStatistics: () => Promise<void>;
  
  // Utility
  clearError: () => void;
  refresh: () => Promise<void>;
}

const KnowledgeCardsContext = createContext<KnowledgeCardsContextType | undefined>(undefined);

interface KnowledgeCardsProviderProps {
  children: ReactNode;
}

export const KnowledgeCardsProvider: React.FC<KnowledgeCardsProviderProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  
  const [state, setState] = useState<KnowledgeCardsState>({
    cards: [],
    isLoading: false,
    error: null,
    lastUpdated: null,
    hasMore: true,
    currentPage: 1,
    totalCards: 0,
    filters: {},
    studySession: {
      isActive: false,
      cards: [],
      currentIndex: 0,
      startTime: null,
    },
    statistics: {
      totalCards: 0,
      reviewsDue: 0,
      streakDays: 0,
      cardsByDifficulty: {},
      cardsByCategory: {},
    },
  });

  // Use ref to store current state to avoid dependency issues
  const stateRef = useRef(state);
  stateRef.current = state;

  // Helper function to update state safely - using setState directly to avoid dependency issues
  const updateState = useCallback((updates: Partial<KnowledgeCardsState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []); // No dependencies to avoid re-creation

  // Helper function to handle errors (Rule 12) - using setState directly
  const handleError = useCallback((error: unknown, operation: string) => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error(`Knowledge Cards ${operation} error:`, error);
    setState(prev => ({
      ...prev,
      error: `${operation} failed: ${errorMessage}`,
      isLoading: false
    }));
  }, []); // No dependencies to avoid re-creation

  // Refresh statistics (moved up to avoid dependency issues) - using setState directly
  const refreshStatistics = useCallback(async () => {
    if (!user || !isAuthenticated) return;

    try {
      const stats = await knowledgeCardsService.getUserStatistics(user.$id);
      setState(prev => ({
        ...prev,
        statistics: {
          totalCards: stats.totalCards,
          reviewsDue: stats.reviewsDue,
          streakDays: stats.streakDays,
          cardsByDifficulty: stats.cardsByDifficulty,
          cardsByCategory: stats.cardsByCategory,
        },
      }));
    } catch (error) {
      console.warn('Failed to refresh statistics:', error);
    }
  }, [user, isAuthenticated]); // Only depend on stable values

  // Load cards with pagination - using setState directly
  const loadCards = useCallback(async (refresh: boolean = false) => {
    if (!user || !isAuthenticated) return;

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const currentState = stateRef.current;
      const page = refresh ? 1 : currentState.currentPage;
      const response = await knowledgeCardsService.getUserCards(
        user.$id,
        currentState.filters,
        { page, limit: 20, orderBy: 'created', orderDirection: 'desc' }
      );

      setState(prev => ({
        ...prev,
        cards: refresh ? response.cards : [...currentState.cards, ...response.cards],
        hasMore: response.hasMore,
        currentPage: refresh ? 2 : page + 1,
        totalCards: response.total,
        lastUpdated: new Date().toISOString(),
        isLoading: false,
      }));
    } catch (error) {
      handleError(error, 'Load cards');
    }
  }, [user, isAuthenticated, handleError]); // Removed updateState dependency

  // Load more cards (pagination)
  const loadMore = useCallback(async () => {
    const currentState = stateRef.current;
    if (!currentState.hasMore || currentState.isLoading) return;
    await loadCards(false);
  }, [loadCards]);

  // Create a new card - using setState directly
  const createCard = useCallback(async (input: CreateKnowledgeCardInput): Promise<CompleteKnowledgeCard> => {
    if (!user || !isAuthenticated) {
      throw new Error('User must be authenticated to create cards');
    }

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const newCard = await knowledgeCardsService.createCard(user.$id, input);

      // Optimistic update - add to beginning of list
      const currentState = stateRef.current;
      setState(prev => ({
        ...prev,
        cards: [newCard, ...currentState.cards],
        totalCards: currentState.totalCards + 1,
        isLoading: false,
      }));

      // Refresh statistics
      await refreshStatistics();

      return newCard;
    } catch (error) {
      handleError(error, 'Create card');
      throw error;
    }
  }, [user, isAuthenticated, handleError, refreshStatistics]); // Removed updateState dependency

  // Update a card - using setState directly
  const updateCard = useCallback(async (cardId: string, updates: UpdateKnowledgeCardInput): Promise<CompleteKnowledgeCard | null> => {
    if (!user || !isAuthenticated) {
      throw new Error('User must be authenticated to update cards');
    }

    try {
      setState(prev => ({ ...prev, error: null }));

      const updatedCard = await knowledgeCardsService.updateCard(cardId, updates);

      if (updatedCard) {
        // Optimistic update - replace in list
        const currentState = stateRef.current;
        setState(prev => ({
          ...prev,
          cards: currentState.cards.map(card => 
            card.card.$id === cardId ? updatedCard : card
          ),
        }));
      }

      return updatedCard;
    } catch (error) {
      handleError(error, 'Update card');
      throw error;
    }
  }, [user, isAuthenticated, handleError]); // Removed updateState dependency

  // Delete a card - using setState directly
  const deleteCard = useCallback(async (cardId: string): Promise<boolean> => {
    if (!user || !isAuthenticated) {
      throw new Error('User must be authenticated to delete cards');
    }

    try {
      setState(prev => ({ ...prev, error: null }));

      const success = await knowledgeCardsService.deleteCard(cardId);

      if (success) {
        // Optimistic update - remove from list
        const currentState = stateRef.current;
        setState(prev => ({
          ...prev,
          cards: currentState.cards.filter(card => card.card.$id !== cardId),
          totalCards: Math.max(0, currentState.totalCards - 1),
        }));

        // Refresh statistics
        await refreshStatistics();
      }

      return success;
    } catch (error) {
      handleError(error, 'Delete card');
      throw error;
    }
  }, [user, isAuthenticated, handleError, refreshStatistics]); // Removed updateState dependency

  // Get a single card
  const getCard = useCallback(async (cardId: string): Promise<CompleteKnowledgeCard | null> => {
    try {
      return await knowledgeCardsService.getCard(cardId);
    } catch (error) {
      handleError(error, 'Get card');
      return null;
    }
  }, [handleError]);

  // Search cards - using setState directly
  const searchCards = useCallback(async (searchTerm: string) => {
    if (!user || !isAuthenticated) return;

    try {
      const currentState = stateRef.current;
      const newFilters = { ...currentState.filters, searchTerm };
      
      setState(prev => ({ 
        ...prev,
        isLoading: true, 
        error: null,
        filters: newFilters,
        currentPage: 1,
      }));

      const response = await knowledgeCardsService.searchCards(
        user.$id,
        searchTerm,
        currentState.filters,
        { page: 1, limit: 20, orderBy: 'created', orderDirection: 'desc' }
      );

      setState(prev => ({
        ...prev,
        cards: response.cards,
        hasMore: response.hasMore,
        currentPage: 2,
        totalCards: response.total,
        lastUpdated: new Date().toISOString(),
        isLoading: false,
      }));
    } catch (error) {
      handleError(error, 'Search cards');
    }
  }, [user, isAuthenticated, handleError]); // Removed updateState dependency

  // Filter cards - using setState directly
  const filterCards = useCallback(async (filters: KnowledgeCardsState['filters']) => {
    if (!user || !isAuthenticated) return;

    try {
      setState(prev => ({ 
        ...prev,
        isLoading: true, 
        error: null,
        filters,
        currentPage: 1,
      }));

      const response = await knowledgeCardsService.getUserCards(
        user.$id,
        filters,
        { page: 1, limit: 20, orderBy: 'created', orderDirection: 'desc' }
      );

      setState(prev => ({
        ...prev,
        cards: response.cards,
        hasMore: response.hasMore,
        currentPage: 2,
        totalCards: response.total,
        lastUpdated: new Date().toISOString(),
        isLoading: false,
      }));
    } catch (error) {
      handleError(error, 'Filter cards');
    }
  }, [user, isAuthenticated, handleError]); // Removed updateState dependency

  // Start study session
  const startStudySession = useCallback(async (cardIds?: string[]) => {
    if (!user || !isAuthenticated) return;

    try {
      let studyCards: CompleteKnowledgeCard[];

      if (cardIds && cardIds.length > 0) {
        // Use specific cards
        studyCards = await knowledgeCardsService.createStudySession(user.$id, {
          cardIds,
          sessionType: 'study',
        });
      } else {
        // Get recommended cards
        studyCards = await knowledgeCardsService.getRecommendedCards(user.$id, 10);
      }

      setState(prev => ({
        ...prev,
        studySession: {
          isActive: true,
          cards: studyCards,
          currentIndex: 0,
          startTime: new Date().toISOString(),
        },
      }));
    } catch (error) {
      handleError(error, 'Start study session');
    }
  }, [user, isAuthenticated, handleError]); // Removed updateState dependency

  // End study session - using setState directly
  const endStudySession = useCallback(() => {
    setState(prev => ({
      ...prev,
      studySession: {
        isActive: false,
        cards: [],
        currentIndex: 0,
        startTime: null,
      },
    }));
  }, []); // No dependencies

  // Navigate to next card in study session - using setState directly
  const nextCard = useCallback(() => {
    const currentState = stateRef.current;
    if (currentState.studySession.currentIndex < currentState.studySession.cards.length - 1) {
      setState(prev => ({
        ...prev,
        studySession: {
          ...currentState.studySession,
          currentIndex: currentState.studySession.currentIndex + 1,
        },
      }));
    }
  }, []); // No dependencies

  // Navigate to previous card in study session - using setState directly
  const previousCard = useCallback(() => {
    const currentState = stateRef.current;
    if (currentState.studySession.currentIndex > 0) {
      setState(prev => ({
        ...prev,
        studySession: {
          ...currentState.studySession,
          currentIndex: currentState.studySession.currentIndex - 1,
        },
      }));
    }
  }, []); // No dependencies

  // Record review result
  const recordReview = useCallback(async (
    cardId: string, 
    correct: boolean, 
    difficulty: 'easy' | 'good' | 'hard' | 'again'
  ) => {
    try {
      await knowledgeCardsService.recordReview(cardId, {
        cardId,
        correct,
        difficulty,
        responseTime: 5000, // Default 5 seconds - could be tracked
      });

      // Refresh statistics after review
      await refreshStatistics();
    } catch (error) {
      handleError(error, 'Record review');
    }
  }, [handleError, refreshStatistics]);

  // refreshStatistics function moved up to avoid dependency issues

  // Clear error - using setState directly
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []); // No dependencies

  // Refresh all data
  const refresh = useCallback(async () => {
    await Promise.all([
      loadCards(true),
      refreshStatistics(),
    ]);
  }, [loadCards, refreshStatistics]);

  // Initialize data when user changes
  useEffect(() => {
    if (user && isAuthenticated) {
      // Initialize data directly to avoid dependency loop
      const initializeData = async () => {
        try {
          setState(prev => ({ ...prev, isLoading: true, error: null }));

          // Load initial cards
          const response = await knowledgeCardsService.getUserCards(
            user.$id,
            {},
            { page: 1, limit: 20, orderBy: 'created', orderDirection: 'desc' }
          );

          setState(prev => ({
            ...prev,
            cards: response.cards,
            hasMore: response.hasMore,
            currentPage: 2,
            totalCards: response.total,
            lastUpdated: new Date().toISOString(),
            isLoading: false,
          }));

          // Load statistics
          try {
            const stats = await knowledgeCardsService.getUserStatistics(user.$id);
            setState(prev => ({
              ...prev,
              statistics: {
                totalCards: stats.totalCards,
                reviewsDue: stats.reviewsDue,
                streakDays: stats.streakDays,
                cardsByDifficulty: stats.cardsByDifficulty,
                cardsByCategory: stats.cardsByCategory,
              },
            }));
          } catch (statsError) {
            console.warn('Failed to load statistics:', statsError);
          }
        } catch (error) {
          handleError(error, 'Initialize data');
        }
      };

      initializeData();
    } else {
      // Clear data when user logs out
      setState({
        cards: [],
        isLoading: false,
        error: null,
        lastUpdated: null,
        hasMore: true,
        currentPage: 1,
        totalCards: 0,
        filters: {},
        studySession: {
          isActive: false,
          cards: [],
          currentIndex: 0,
          startTime: null,
        },
        statistics: {
          totalCards: 0,
          reviewsDue: 0,
          streakDays: 0,
          cardsByDifficulty: {},
          cardsByCategory: {},
        },
      });
    }
  }, [user, isAuthenticated]); // Remove refresh dependency

  const value: KnowledgeCardsContextType = {
    state,
    createCard,
    updateCard,
    deleteCard,
    getCard,
    loadCards,
    loadMore,
    searchCards,
    filterCards,
    startStudySession,
    endStudySession,
    nextCard,
    previousCard,
    recordReview,
    refreshStatistics,
    clearError,
    refresh,
  };

  return (
    <KnowledgeCardsContext.Provider value={value}>
      {children}
    </KnowledgeCardsContext.Provider>
  );
};

export const useKnowledgeCards = (): KnowledgeCardsContextType => {
  const context = useContext(KnowledgeCardsContext);
  if (context === undefined) {
    throw new Error('useKnowledgeCards must be used within a KnowledgeCardsProvider');
  }
  return context;
};