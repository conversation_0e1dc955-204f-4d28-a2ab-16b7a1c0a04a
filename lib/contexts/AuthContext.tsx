import React, {
	createContext,
	type ReactNode,
	useContext,
	useEffect,
	useState,
} from "react";
import { appwriteService } from "@/lib/services/appwrite.service";
import { enhancedAuthService } from "@/lib/services/enhanced-auth.service";
import type { AppwriteUser, AuthUser } from "@/types/appwrite";

interface AuthContextType {
	// Auth state
	user: AuthUser | null;
	userProfile: AppwriteUser | null;
	isLoading: boolean;
	isAuthenticated: boolean;
	isAnonymous: boolean;

	// Auth actions
	signIn: (email: string, password: string) => Promise<AuthUser>;
	signUp: (email: string, password: string, name: string) => Promise<AuthUser>;
	signOut: () => Promise<void>;
	signOutFromAllDevices: () => Promise<void>;
	createAnonymousSession: () => Promise<AuthUser>;

	// Profile actions
	updateProfile: (data: { name?: string }) => Promise<AuthUser>;
	updateEmail: (email: string, password: string) => Promise<AuthUser>;
	changePassword: (newPassword: string, oldPassword: string) => Promise<void>;
	resetPassword: (email: string) => Promise<void>;

	// User profile actions
	updateUserProfile: (data: Partial<AppwriteUser>) => Promise<AppwriteUser>;
	refreshUserProfile: () => Promise<AppwriteUser | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
	children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
	const [user, setUser] = useState<AuthUser | null>(null);
	const [userProfile, setUserProfile] = useState<AppwriteUser | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	// Computed properties
	const isAuthenticated = user !== null;
	const isAnonymous = user?.email === "" || user?.email === undefined;

	useEffect(() => {
		initializeAuth();
	}, []);

	const initializeAuth = async () => {
		try {
			setIsLoading(true);
			const currentUser = await appwriteService.auth.getCurrentUser();

			if (currentUser) {
				setUser(currentUser);
				// Load user profile from database
				const profile = await enhancedAuthService.getUserProfile(
					currentUser.$id,
				);
				setUserProfile(profile);
			}
		} catch (error) {
			console.log("Auth initialization error:", error);
		} finally {
			setIsLoading(false);
		}
	};

	const signIn = async (email: string, password: string): Promise<AuthUser> => {
		try {
			const { user: authUser, profile } = await enhancedAuthService.signIn(
				email,
				password,
			);
			setUser(authUser);
			setUserProfile(profile);
			return authUser;
		} catch (error) {
			throw error;
		}
	};

	const signUp = async (
		email: string,
		password: string,
		name: string,
	): Promise<AuthUser> => {
		try {
			const { user: authUser, profile } = await enhancedAuthService.signUp(
				email,
				password,
				name,
			);
			setUser(authUser);
			setUserProfile(profile);
			return authUser;
		} catch (error) {
			throw error;
		}
	};

	const signOut = async (): Promise<void> => {
		try {
			await enhancedAuthService.signOut();
			setUser(null);
			setUserProfile(null);
		} catch (error) {
			throw error;
		}
	};

	const signOutFromAllDevices = async (): Promise<void> => {
		try {
			await appwriteService.auth.signOutFromAllDevices();
			setUser(null);
			setUserProfile(null);
		} catch (error) {
			throw error;
		}
	};

	const createAnonymousSession = async (): Promise<AuthUser> => {
		try {
			const { user: authUser, profile } =
				await enhancedAuthService.createAnonymousSession();
			setUser(authUser);
			setUserProfile(profile);
			return authUser;
		} catch (error) {
			throw error;
		}
	};

	const updateProfile = async (data: { name?: string }): Promise<AuthUser> => {
		try {
			const updatedUser = await appwriteService.auth.updateProfile(data);
			setUser(updatedUser);

			// Also update user profile in database
			if (userProfile) {
				const updatedProfile = await enhancedAuthService.updateUserProfile(
					updatedUser.$id,
					{
						name: data.name || userProfile.name,
					},
				);
				setUserProfile(updatedProfile);
			}

			return updatedUser;
		} catch (error) {
			throw error;
		}
	};

	const updateEmail = async (
		email: string,
		password: string,
	): Promise<AuthUser> => {
		try {
			const updatedUser = await appwriteService.auth.updateEmail(email, password);
			setUser(updatedUser);

			// Also update user profile in database
			if (userProfile) {
				const updatedProfile = await enhancedAuthService.updateUserProfile(
					updatedUser.$id,
					{
						email: email,
					},
				);
				setUserProfile(updatedProfile);
			}

			return updatedUser;
		} catch (error) {
			throw error;
		}
	};

	const changePassword = async (
		newPassword: string,
		oldPassword: string,
	): Promise<void> => {
		try {
			await appwriteService.auth.changePassword(newPassword, oldPassword);
		} catch (error) {
			throw error;
		}
	};

	const resetPassword = async (email: string): Promise<void> => {
		try {
			await appwriteService.auth.resetPassword(email);
		} catch (error) {
			throw error;
		}
	};

	const updateUserProfile = async (
		data: Partial<AppwriteUser>,
	): Promise<AppwriteUser> => {
		try {
			if (!user || !userProfile) {
				throw new Error("User not authenticated");
			}

			const updatedProfile = await enhancedAuthService.updateUserProfile(
				user.$id,
				data,
			);
			setUserProfile(updatedProfile);
			return updatedProfile;
		} catch (error) {
			throw error;
		}
	};

	const refreshUserProfile = async (): Promise<AppwriteUser | null> => {
		try {
			if (!user) {
				return null;
			}

			const profile = await enhancedAuthService.getUserProfile(user.$id);
			setUserProfile(profile);
			return profile;
		} catch (error) {
			console.log("Failed to refresh user profile:", error);
			return null;
		}
	};

	const value: AuthContextType = {
		// Auth state
		user,
		userProfile,
		isLoading,
		isAuthenticated,
		isAnonymous,

		// Auth actions
		signIn,
		signUp,
		signOut,
		signOutFromAllDevices,
		createAnonymousSession,

		// Profile actions
		updateProfile,
		updateEmail,
		changePassword,
		resetPassword,

		// User profile actions
		updateUserProfile,
		refreshUserProfile,
	};

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error("useAuth must be used within an AuthProvider");
	}
	return context;
};

export default AuthContext;
