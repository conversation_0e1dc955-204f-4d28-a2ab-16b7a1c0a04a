/**
 * Permission Error Handler Hook
 * 
 * Custom hook for handling AppWrite permission errors throughout the app.
 * Automatically detects permission-related errors and shows registration modal for guest users.
 */

import { useCallback } from 'react';
import { usePermissionError } from '@/lib/contexts/PermissionErrorContext';
import { useAuth } from '@/lib/contexts/AuthContext';

interface PermissionErrorHandlerOptions {
  showModalOnError?: boolean;
  customErrorMessage?: string;
}

export const usePermissionErrorHandler = (options: PermissionErrorHandlerOptions = {}) => {
  const { showPermissionError } = usePermissionError();
  const { user } = useAuth();
  const { showModalOnError = true, customErrorMessage } = options;

  /**
   * Check if an error is permission-related
   */
  const isPermissionError = useCallback((error: any): boolean => {
    if (!error) return false;
    
    const errorMessage = error.message || error.toString() || '';
    const errorCode = error.code || error.status || '';
    
    // Common AppWrite permission error patterns
    const permissionErrorPatterns = [
      'permission denied',
      'unauthorized',
      'forbidden',
      'invalid permissions',
      'role must have an id value',
      'insufficient permissions',
      'access denied',
      'not authorized',
      '401',
      '403'
    ];
    
    return permissionErrorPatterns.some(pattern => 
      errorMessage.toLowerCase().includes(pattern) || 
      errorCode.toString().includes(pattern)
    );
  }, []);

  /**
   * Check if current user is a guest user
   */
  const isGuestUser = useCallback((): boolean => {
    if (!user) return false;
    return user.$id.includes('anonymous') || user.$id.startsWith('dev-guest-user');
  }, [user]);

  /**
   * Handle any error and show permission modal if applicable
   */
  const handleError = useCallback((error: any, context?: string) => {
    console.error('Error occurred:', error);
    
    // Only show permission modal for guest users with permission errors
    if (showModalOnError && isGuestUser() && isPermissionError(error)) {
      const message = customErrorMessage || 
        `${context ? `${context}: ` : ''}${error.message || 'Permission denied'}`;
      showPermissionError(message);
      return true; // Indicates error was handled by permission modal
    }
    
    return false; // Indicates error was not handled by permission modal
  }, [showModalOnError, isGuestUser, isPermissionError, customErrorMessage, showPermissionError]);

  /**
   * Wrapper for async operations with automatic permission error handling
   */
  const withPermissionErrorHandling = useCallback(
    async <T>(
      operation: () => Promise<T>,
      context?: string
    ): Promise<T | null> => {
      try {
        return await operation();
      } catch (error) {
        const wasHandled = handleError(error, context);
        if (!wasHandled) {
          // Re-throw error if it wasn't a permission error for guest users
          throw error;
        }
        return null;
      }
    },
    [handleError]
  );

  return {
    handleError,
    withPermissionErrorHandling,
    isPermissionError,
    isGuestUser,
    showPermissionModal: showPermissionError,
  };
};

export default usePermissionErrorHandler;
