/**
 * Permission Error Utilities
 * 
 * Utility functions for handling AppWrite permission errors in services and components.
 * Provides error detection and handling for guest user permission issues.
 */

/**
 * Check if an error is permission-related
 */
export const isPermissionError = (error: any): boolean => {
  if (!error) return false;
  
  const errorMessage = error.message || error.toString() || '';
  const errorCode = error.code || error.status || '';
  
  // Common AppWrite permission error patterns
  const permissionErrorPatterns = [
    'permission denied',
    'unauthorized',
    'forbidden',
    'invalid permissions',
    'role must have an id value',
    'insufficient permissions',
    'access denied',
    'not authorized',
    '401',
    '403'
  ];
  
  return permissionErrorPatterns.some(pattern => 
    errorMessage.toLowerCase().includes(pattern) || 
    errorCode.toString().includes(pattern)
  );
};

/**
 * Check if a user ID represents a guest user
 */
export const isGuestUserId = (userId: string): boolean => {
  if (!userId) return false;
  return userId.includes('anonymous') || userId.startsWith('dev-guest-user');
};

/**
 * Create a user-friendly error message for permission errors
 */
export const createPermissionErrorMessage = (
  originalError: any, 
  context?: string
): string => {
  const baseMessage = context ? `${context}: ` : '';
  
  if (isPermissionError(originalError)) {
    return `${baseMessage}This action requires a registered account for enhanced security and features.`;
  }
  
  return `${baseMessage}${originalError.message || 'An error occurred'}`;
};

/**
 * Enhanced error object with permission context
 */
export interface PermissionAwareError extends Error {
  isPermissionError: boolean;
  isGuestUserError: boolean;
  originalError: any;
  context?: string;
}

/**
 * Create a permission-aware error object
 */
export const createPermissionAwareError = (
  error: any,
  userId?: string,
  context?: string
): PermissionAwareError => {
  const isPermError = isPermissionError(error);
  const isGuestError = userId ? isGuestUserId(userId) : false;
  
  const permissionError = new Error(
    createPermissionErrorMessage(error, context)
  ) as PermissionAwareError;
  
  permissionError.isPermissionError = isPermError;
  permissionError.isGuestUserError = isGuestError && isPermError;
  permissionError.originalError = error;
  permissionError.context = context;
  
  return permissionError;
};

/**
 * Wrapper for async operations with permission error handling
 */
export const withPermissionErrorHandling = async <T>(
  operation: () => Promise<T>,
  userId?: string,
  context?: string
): Promise<T> => {
  try {
    return await operation();
  } catch (error) {
    // Create permission-aware error and re-throw
    throw createPermissionAwareError(error, userId, context);
  }
};

/**
 * Log permission errors with context
 */
export const logPermissionError = (
  error: any,
  userId?: string,
  context?: string
): void => {
  const isPermError = isPermissionError(error);
  const isGuestError = userId ? isGuestUserId(userId) : false;
  
  if (isPermError && isGuestError) {
    console.warn(
      `🔐 Guest user permission error in ${context || 'unknown context'}:`,
      error.message || error
    );
  } else if (isPermError) {
    console.error(
      `🚨 Permission error in ${context || 'unknown context'}:`,
      error.message || error
    );
  } else {
    console.error(
      `❌ Error in ${context || 'unknown context'}:`,
      error.message || error
    );
  }
};

export default {
  isPermissionError,
  isGuestUserId,
  createPermissionErrorMessage,
  createPermissionAwareError,
  withPermissionErrorHandling,
  logPermissionError,
};
