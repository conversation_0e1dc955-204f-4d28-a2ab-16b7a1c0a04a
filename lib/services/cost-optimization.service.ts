import { z } from 'zod';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { analyticsService } from './analytics.service';

// Cost tracking schema
const CostTrackingSchema = z.object({
  period: z.enum(['daily', 'weekly', 'monthly']),
  startDate: z.string(),
  endDate: z.string(),
  totalCost: z.number(),
  costByService: z.record(z.number()),
  requestCounts: z.record(z.number()),
  averageCostPerRequest: z.number(),
  projectedMonthlyCost: z.number(),
});

export type CostTracking = z.infer<typeof CostTrackingSchema>;

// Cost limits schema
const CostLimitsSchema = z.object({
  dailyLimit: z.number().default(1.0), // $1.00 per day
  weeklyLimit: z.number().default(5.0), // $5.00 per week
  monthlyLimit: z.number().default(20.0), // $20.00 per month
  warningThreshold: z.number().min(0).max(1).default(0.8), // 80% of limit
  criticalThreshold: z.number().min(0).max(1).default(0.95), // 95% of limit
  autoOptimizeEnabled: z.boolean().default(true),
  emergencyStopEnabled: z.boolean().default(true),
});

export type CostLimits = z.infer<typeof CostLimitsSchema>;

// Cost optimization strategy schema
const OptimizationStrategySchema = z.object({
  enableCaching: z.boolean().default(true),
  cacheAggressiveness: z.enum(['conservative', 'moderate', 'aggressive']).default('moderate'),
  requestBatching: z.boolean().default(true),
  qualityReduction: z.boolean().default(false),
  featureThrottling: z.boolean().default(false),
  offlineMode: z.boolean().default(false),
});

export type OptimizationStrategy = z.infer<typeof OptimizationStrategySchema>;

// Cost alert schema
const CostAlertSchema = z.object({
  id: z.string(),
  type: z.enum(['warning', 'critical', 'limit_exceeded', 'optimization_applied']),
  message: z.string(),
  timestamp: z.number(),
  currentCost: z.number(),
  limit: z.number(),
  period: z.string(),
  resolved: z.boolean(),
});

export type CostAlert = z.infer<typeof CostAlertSchema>;

/**
 * Cost Optimization Service
 * Following Rule 12: Validation and Error Handling
 */
export class CostOptimizationService {
  private costLimits: CostLimits;
  private optimizationStrategy: OptimizationStrategy;
  private alerts: CostAlert[] = [];
  private isOptimizing = false;

  private readonly STORAGE_KEYS = {
    COST_LIMITS: 'cost_optimization_limits',
    STRATEGY: 'cost_optimization_strategy',
    ALERTS: 'cost_optimization_alerts',
    TRACKING: 'cost_tracking_data',
  };

  // Cost per request for different AI services (in USD)
  private readonly SERVICE_COSTS = {
    ocr: 0.002, // $0.002 per OCR request
    translation: 0.001, // $0.001 per translation
    card_generation: 0.005, // $0.005 per card generation
    search: 0.0005, // $0.0005 per search
    chat: 0.003, // $0.003 per chat message
    voice_processing: 0.004, // $0.004 per voice processing
  };

  constructor() {
    this.costLimits = CostLimitsSchema.parse({});
    this.optimizationStrategy = OptimizationStrategySchema.parse({});
    this.initialize();
  }

  /**
   * Initialize cost optimization service
   */
  private async initialize(): Promise<void> {
    try {
      await this.loadConfiguration();
      this.startCostMonitoring();
      console.log('Cost optimization service initialized');
    } catch (error) {
      console.warn('Failed to initialize cost optimization service:', error);
    }
  }

  /**
   * Load configuration from storage
   */
  private async loadConfiguration(): Promise<void> {
    try {
      const [limitsData, strategyData, alertsData] = await Promise.all([
        AsyncStorage.getItem(this.STORAGE_KEYS.COST_LIMITS),
        AsyncStorage.getItem(this.STORAGE_KEYS.STRATEGY),
        AsyncStorage.getItem(this.STORAGE_KEYS.ALERTS),
      ]);

      if (limitsData) {
        this.costLimits = CostLimitsSchema.parse(JSON.parse(limitsData));
      }

      if (strategyData) {
        this.optimizationStrategy = OptimizationStrategySchema.parse(JSON.parse(strategyData));
      }

      if (alertsData) {
        this.alerts = JSON.parse(alertsData).map((alert: any) => CostAlertSchema.parse(alert));
      }
    } catch (error) {
      console.warn('Failed to load cost optimization configuration:', error);
    }
  }

  /**
   * Save configuration to storage
   */
  private async saveConfiguration(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.setItem(this.STORAGE_KEYS.COST_LIMITS, JSON.stringify(this.costLimits)),
        AsyncStorage.setItem(this.STORAGE_KEYS.STRATEGY, JSON.stringify(this.optimizationStrategy)),
        AsyncStorage.setItem(this.STORAGE_KEYS.ALERTS, JSON.stringify(this.alerts)),
      ]);
    } catch (error) {
      console.warn('Failed to save cost optimization configuration:', error);
    }
  }

  /**
   * Start cost monitoring
   */
  private startCostMonitoring(): void {
    // Check costs every hour
    setInterval(() => {
      this.checkCostLimits();
    }, 60 * 60 * 1000);

    // Initial check
    this.checkCostLimits();
  }

  /**
   * Calculate cost for a specific AI service request
   */
  calculateRequestCost(
    serviceType: keyof typeof this.SERVICE_COSTS,
    requestData?: {
      inputSize?: number;
      outputSize?: number;
      complexity?: 'low' | 'medium' | 'high';
    }
  ): number {
    let baseCost = this.SERVICE_COSTS[serviceType] || 0.001;

    // Adjust cost based on request complexity
    if (requestData?.complexity) {
      const complexityMultiplier = {
        low: 0.8,
        medium: 1.0,
        high: 1.5,
      };
      baseCost *= complexityMultiplier[requestData.complexity];
    }

    // Adjust cost based on input/output size
    if (requestData?.inputSize) {
      const sizeMultiplier = Math.max(1, requestData.inputSize / 1000); // Base size 1KB
      baseCost *= Math.min(sizeMultiplier, 3); // Cap at 3x for very large inputs
    }

    return baseCost;
  }

  /**
   * Track cost for an AI service request
   */
  async trackCost(
    serviceType: keyof typeof this.SERVICE_COSTS,
    requestData?: {
      inputSize?: number;
      outputSize?: number;
      complexity?: 'low' | 'medium' | 'high';
      cacheHit?: boolean;
    }
  ): Promise<number> {
    try {
      // No cost for cache hits
      if (requestData?.cacheHit) {
        return 0;
      }

      const cost = this.calculateRequestCost(serviceType, requestData);

      // Track in analytics
      await analyticsService.trackEvent('cost_tracked', {
        serviceType,
        cost,
        requestData,
      });

      // Check if this pushes us over limits
      await this.checkCostLimits();

      return cost;
    } catch (error) {
      console.warn('Failed to track cost:', error);
      return 0;
    }
  }

  /**
   * Get current cost tracking data
   */
  async getCostTracking(period: 'daily' | 'weekly' | 'monthly'): Promise<CostTracking> {
    try {
      const now = new Date();
      let startDate: Date;
      let endDate = now;

      switch (period) {
        case 'daily':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'weekly':
          const dayOfWeek = now.getDay();
          startDate = new Date(now.getTime() - dayOfWeek * 24 * 60 * 60 * 1000);
          startDate.setHours(0, 0, 0, 0);
          break;
        case 'monthly':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
      }

      // Get analytics data for the period
      const aiAnalytics = await analyticsService.getAIUsageAnalytics();
      
      // Calculate costs by service
      const costByService: Record<string, number> = {};
      const requestCounts: Record<string, number> = {};
      let totalCost = 0;

      Object.entries(aiAnalytics.requestsByType).forEach(([serviceType, count]) => {
        const serviceCost = this.SERVICE_COSTS[serviceType as keyof typeof this.SERVICE_COSTS] || 0.001;
        const totalServiceCost = serviceCost * count;
        
        costByService[serviceType] = totalServiceCost;
        requestCounts[serviceType] = count;
        totalCost += totalServiceCost;
      });

      const totalRequests = Object.values(requestCounts).reduce((sum, count) => sum + count, 0);
      const averageCostPerRequest = totalRequests > 0 ? totalCost / totalRequests : 0;

      // Project monthly cost based on current usage
      const daysInPeriod = period === 'daily' ? 1 : period === 'weekly' ? 7 : 30;
      const dailyAverage = totalCost / daysInPeriod;
      const projectedMonthlyCost = dailyAverage * 30;

      return {
        period,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        totalCost,
        costByService,
        requestCounts,
        averageCostPerRequest,
        projectedMonthlyCost,
      };
    } catch (error) {
      console.warn('Failed to get cost tracking data:', error);
      return {
        period,
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        totalCost: 0,
        costByService: {},
        requestCounts: {},
        averageCostPerRequest: 0,
        projectedMonthlyCost: 0,
      };
    }
  }

  /**
   * Check cost limits and trigger optimizations
   */
  private async checkCostLimits(): Promise<void> {
    try {
      const [dailyCost, weeklyCost, monthlyCost] = await Promise.all([
        this.getCostTracking('daily'),
        this.getCostTracking('weekly'),
        this.getCostTracking('monthly'),
      ]);

      // Check daily limit
      await this.checkPeriodLimit('daily', dailyCost.totalCost, this.costLimits.dailyLimit);
      
      // Check weekly limit
      await this.checkPeriodLimit('weekly', weeklyCost.totalCost, this.costLimits.weeklyLimit);
      
      // Check monthly limit
      await this.checkPeriodLimit('monthly', monthlyCost.totalCost, this.costLimits.monthlyLimit);

    } catch (error) {
      console.warn('Failed to check cost limits:', error);
    }
  }

  /**
   * Check cost limit for a specific period
   */
  private async checkPeriodLimit(
    period: string,
    currentCost: number,
    limit: number
  ): Promise<void> {
    const percentage = currentCost / limit;

    if (percentage >= 1.0 && this.costLimits.emergencyStopEnabled) {
      // Limit exceeded - emergency stop
      await this.createAlert('limit_exceeded', 
        `${period} cost limit exceeded: $${currentCost.toFixed(3)} / $${limit.toFixed(3)}`,
        currentCost, limit, period);
      await this.applyEmergencyOptimization();
    } else if (percentage >= this.costLimits.criticalThreshold) {
      // Critical threshold
      await this.createAlert('critical',
        `${period} cost critical: $${currentCost.toFixed(3)} / $${limit.toFixed(3)} (${(percentage * 100).toFixed(1)}%)`,
        currentCost, limit, period);
      if (this.costLimits.autoOptimizeEnabled) {
        await this.applyAggressiveOptimization();
      }
    } else if (percentage >= this.costLimits.warningThreshold) {
      // Warning threshold
      await this.createAlert('warning',
        `${period} cost warning: $${currentCost.toFixed(3)} / $${limit.toFixed(3)} (${(percentage * 100).toFixed(1)}%)`,
        currentCost, limit, period);
      if (this.costLimits.autoOptimizeEnabled) {
        await this.applyModerateOptimization();
      }
    }
  }

  /**
   * Create cost alert
   */
  private async createAlert(
    type: CostAlert['type'],
    message: string,
    currentCost: number,
    limit: number,
    period: string
  ): Promise<void> {
    try {
      const alert: CostAlert = {
        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type,
        message,
        timestamp: Date.now(),
        currentCost,
        limit,
        period,
        resolved: false,
      };

      this.alerts.push(alert);
      await this.saveConfiguration();

      // Track alert in analytics
      await analyticsService.trackEvent('cost_alert', {
        alertType: type,
        message,
        currentCost,
        limit,
        period,
      });

      console.warn(`Cost Alert [${type.toUpperCase()}]: ${message}`);
    } catch (error) {
      console.warn('Failed to create cost alert:', error);
    }
  }

  /**
   * Apply moderate optimization
   */
  private async applyModerateOptimization(): Promise<void> {
    if (this.isOptimizing) return;
    this.isOptimizing = true;

    try {
      this.optimizationStrategy.enableCaching = true;
      this.optimizationStrategy.cacheAggressiveness = 'moderate';
      this.optimizationStrategy.requestBatching = true;

      await this.saveConfiguration();
      await this.createAlert('optimization_applied', 
        'Moderate cost optimization applied: Enhanced caching and request batching enabled',
        0, 0, 'optimization');

      console.log('Applied moderate cost optimization');
    } catch (error) {
      console.warn('Failed to apply moderate optimization:', error);
    } finally {
      this.isOptimizing = false;
    }
  }

  /**
   * Apply aggressive optimization
   */
  private async applyAggressiveOptimization(): Promise<void> {
    if (this.isOptimizing) return;
    this.isOptimizing = true;

    try {
      this.optimizationStrategy.enableCaching = true;
      this.optimizationStrategy.cacheAggressiveness = 'aggressive';
      this.optimizationStrategy.requestBatching = true;
      this.optimizationStrategy.qualityReduction = true;
      this.optimizationStrategy.featureThrottling = true;

      await this.saveConfiguration();
      await this.createAlert('optimization_applied',
        'Aggressive cost optimization applied: Maximum caching, quality reduction, and feature throttling enabled',
        0, 0, 'optimization');

      console.log('Applied aggressive cost optimization');
    } catch (error) {
      console.warn('Failed to apply aggressive optimization:', error);
    } finally {
      this.isOptimizing = false;
    }
  }

  /**
   * Apply emergency optimization (near-offline mode)
   */
  private async applyEmergencyOptimization(): Promise<void> {
    if (this.isOptimizing) return;
    this.isOptimizing = true;

    try {
      this.optimizationStrategy.enableCaching = true;
      this.optimizationStrategy.cacheAggressiveness = 'aggressive';
      this.optimizationStrategy.requestBatching = true;
      this.optimizationStrategy.qualityReduction = true;
      this.optimizationStrategy.featureThrottling = true;
      this.optimizationStrategy.offlineMode = true;

      await this.saveConfiguration();
      await this.createAlert('optimization_applied',
        'Emergency optimization applied: Near-offline mode enabled to prevent cost overrun',
        0, 0, 'emergency');

      console.warn('Applied emergency cost optimization - near-offline mode');
    } catch (error) {
      console.warn('Failed to apply emergency optimization:', error);
    } finally {
      this.isOptimizing = false;
    }
  }

  /**
   * Update cost limits
   */
  async updateCostLimits(newLimits: Partial<CostLimits>): Promise<void> {
    try {
      this.costLimits = CostLimitsSchema.parse({ ...this.costLimits, ...newLimits });
      await this.saveConfiguration();
    } catch (error) {
      console.warn('Failed to update cost limits:', error);
      throw error;
    }
  }

  /**
   * Update optimization strategy
   */
  async updateOptimizationStrategy(newStrategy: Partial<OptimizationStrategy>): Promise<void> {
    try {
      this.optimizationStrategy = OptimizationStrategySchema.parse({ 
        ...this.optimizationStrategy, 
        ...newStrategy 
      });
      await this.saveConfiguration();
    } catch (error) {
      console.warn('Failed to update optimization strategy:', error);
      throw error;
    }
  }

  /**
   * Get current cost limits
   */
  getCostLimits(): CostLimits {
    return { ...this.costLimits };
  }

  /**
   * Get current optimization strategy
   */
  getOptimizationStrategy(): OptimizationStrategy {
    return { ...this.optimizationStrategy };
  }

  /**
   * Get current alerts
   */
  getCurrentAlerts(): CostAlert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Resolve alert
   */
  async resolveAlert(alertId: string): Promise<void> {
    try {
      const alert = this.alerts.find(a => a.id === alertId);
      if (alert) {
        alert.resolved = true;
        await this.saveConfiguration();
      }
    } catch (error) {
      console.warn('Failed to resolve cost alert:', error);
    }
  }

  /**
   * Reset optimization to default
   */
  async resetOptimization(): Promise<void> {
    try {
      this.optimizationStrategy = OptimizationStrategySchema.parse({});
      await this.saveConfiguration();
      this.isOptimizing = false;
    } catch (error) {
      console.warn('Failed to reset optimization:', error);
    }
  }

  /**
   * Get cost optimization recommendations
   */
  async getCostOptimizationRecommendations(): Promise<string[]> {
    try {
      const recommendations: string[] = [];
      const monthlyCost = await this.getCostTracking('monthly');

      // Analyze cost patterns
      const topServices = Object.entries(monthlyCost.costByService)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 3);

      if (monthlyCost.projectedMonthlyCost > this.costLimits.monthlyLimit) {
        recommendations.push(`Projected monthly cost ($${monthlyCost.projectedMonthlyCost.toFixed(2)}) exceeds limit ($${this.costLimits.monthlyLimit.toFixed(2)})`);
      }

      if (topServices.length > 0) {
        const [topService, topCost] = topServices[0];
        recommendations.push(`${topService} is your highest cost service ($${topCost.toFixed(3)})`);
      }

      if (!this.optimizationStrategy.enableCaching) {
        recommendations.push('Enable caching to reduce API costs');
      }

      if (!this.optimizationStrategy.requestBatching) {
        recommendations.push('Enable request batching for better efficiency');
      }

      return recommendations;
    } catch (error) {
      console.warn('Failed to get cost optimization recommendations:', error);
      return [];
    }
  }
}

// Export singleton instance
export const costOptimizationService = new CostOptimizationService();
