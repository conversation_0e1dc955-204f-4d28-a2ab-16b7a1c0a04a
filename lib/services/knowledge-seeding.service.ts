/**
 * Knowledge Base Seeding Service
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 5: TypeScript safety with Zod validation
 * - Rule 12: Comprehensive error handling
 * - Rule 1: Interactive feedback protocol
 * 
 * This service provides functions to seed the knowledge base with programming content
 * directly from within the React Native app.
 */

import { z } from 'zod';
import { knowledgeCardsService } from './knowledge-cards.service';
import type { CreateKnowledgeCardInput } from '@/types/appwrite-v2-fixed';

// Zod schema for seeding validation (Rule 5)
const SeedingCardSchema = z.object({
  title: z.string().min(1).max(200),
  content: z.string().min(10),
  category: z.string().min(1),
  tags: z.array(z.string()),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
});

type SeedingCard = z.infer<typeof SeedingCardSchema>;

// Programming knowledge cards data
const PROGRAMMING_KNOWLEDGE_CARDS: SeedingCard[] = [
  // JavaScript Fundamentals
  {
    title: 'JavaScript Variables and Data Types',
    content: `# JavaScript Variables and Data Types

## Variable Declarations
- **var**: Function-scoped, can be redeclared
- **let**: Block-scoped, cannot be redeclared
- **const**: Block-scoped, cannot be reassigned

## Primitive Data Types
1. **Number**: 42, 3.14, NaN, Infinity
2. **String**: "Hello", 'World', \`Template\`
3. **Boolean**: true, false
4. **Undefined**: Variable declared but not assigned
5. **Null**: Intentional absence of value
6. **Symbol**: Unique identifier (ES6+)
7. **BigInt**: Large integers (ES2020+)

## Examples
\`\`\`javascript
let age = 25;           // Number
const name = "Alice";   // String
let isActive = true;    // Boolean
let data;               // Undefined
let result = null;      // Null
\`\`\`

## Key Concepts
- **Type coercion**: JavaScript automatically converts types
- **Hoisting**: var declarations are moved to top of scope
- **Temporal dead zone**: let/const cannot be used before declaration`,
    category: 'programming',
    tags: ['javascript', 'variables', 'data-types', 'fundamentals'],
    difficulty: 'beginner',
  },
  
  // Python Fundamentals
  {
    title: 'Python Functions and Scope',
    content: `# Python Functions and Scope

## Function Definition
\`\`\`python
def greet(name, greeting="Hello"):
    return f"{greeting}, {name}!"

# Function call
message = greet("Alice")
print(message)  # Output: Hello, Alice!
\`\`\`

## Scope Rules (LEGB)
1. **Local**: Inside function
2. **Enclosing**: In enclosing function
3. **Global**: At module level
4. **Built-in**: Built-in names

## Lambda Functions
\`\`\`python
# Lambda function
square = lambda x: x ** 2
print(square(5))  # Output: 25

# With map()
numbers = [1, 2, 3, 4, 5]
squared = list(map(lambda x: x ** 2, numbers))
\`\`\`

## Decorators
\`\`\`python
def timer(func):
    def wrapper(*args, **kwargs):
        import time
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} took {end - start:.2f} seconds")
        return result
    return wrapper

@timer
def slow_function():
    time.sleep(1)
    return "Done"
\`\`\``,
    category: 'programming',
    tags: ['python', 'functions', 'scope', 'decorators', 'lambda'],
    difficulty: 'intermediate',
  },
  
  // Data Structures
  {
    title: 'Arrays and Lists - Core Operations',
    content: `# Arrays and Lists - Core Operations

## JavaScript Arrays
\`\`\`javascript
// Creation
let fruits = ['apple', 'banana', 'orange'];
let numbers = new Array(1, 2, 3, 4, 5);

// Common Methods
fruits.push('grape');        // Add to end
fruits.unshift('mango');     // Add to beginning
let last = fruits.pop();     // Remove from end
let first = fruits.shift();  // Remove from beginning

// Iteration
fruits.forEach(fruit => console.log(fruit));
let upperFruits = fruits.map(fruit => fruit.toUpperCase());
let longFruits = fruits.filter(fruit => fruit.length > 5);
\`\`\`

## Python Lists
\`\`\`python
# Creation
fruits = ['apple', 'banana', 'orange']
numbers = list(range(1, 6))

# Common Methods
fruits.append('grape')       # Add to end
fruits.insert(0, 'mango')   # Insert at index
last = fruits.pop()         # Remove from end
fruits.remove('banana')     # Remove by value

# List Comprehensions
squares = [x**2 for x in range(10)]
even_squares = [x**2 for x in range(10) if x % 2 == 0]
\`\`\`

## Time Complexity
- **Access**: O(1)
- **Search**: O(n)
- **Insertion**: O(1) at end, O(n) at beginning
- **Deletion**: O(1) at end, O(n) at beginning`,
    category: 'programming',
    tags: ['data-structures', 'arrays', 'lists', 'javascript', 'python'],
    difficulty: 'beginner',
  },
  
  // React Concepts
  {
    title: 'React Hooks - useState and useEffect',
    content: `# React Hooks - useState and useEffect

## useState Hook
\`\`\`jsx
import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);
  const [name, setName] = useState('');

  const increment = () => setCount(count + 1);
  const decrement = () => setCount(prev => prev - 1);

  return (
    <div>
      <h2>Count: {count}</h2>
      <button onClick={increment}>+</button>
      <button onClick={decrement}>-</button>
      
      <input 
        value={name}
        onChange={(e) => setName(e.target.value)}
        placeholder="Enter name"
      />
      <p>Hello, {name}!</p>
    </div>
  );
}
\`\`\`

## useEffect Hook
\`\`\`jsx
import React, { useState, useEffect } from 'react';

function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Effect with dependency array
  useEffect(() => {
    const fetchUser = async () => {
      setLoading(true);
      try {
        const response = await fetch(\`/api/users/\${userId}\`);
        const userData = await response.json();
        setUser(userData);
      } catch (error) {
        console.error('Failed to fetch user:', error);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchUser();
    }
  }, [userId]); // Re-run when userId changes

  if (loading) return <div>Loading...</div>;
  if (!user) return <div>User not found</div>;

  return (
    <div>
      <h1>{user.name}</h1>
      <p>{user.email}</p>
    </div>
  );
}
\`\`\`

## Hook Rules
1. Only call hooks at the top level
2. Only call hooks from React functions
3. Use dependency arrays correctly
4. Clean up side effects`,
    category: 'programming',
    tags: ['react', 'hooks', 'useState', 'useEffect', 'javascript'],
    difficulty: 'intermediate',
  },
  
  // Algorithms
  {
    title: 'Sorting Algorithms Comparison',
    content: `# Sorting Algorithms Comparison

## Bubble Sort
\`\`\`python
def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n - i - 1):
            if arr[j] > arr[j + 1]:
                arr[j], arr[j + 1] = arr[j + 1], arr[j]
    return arr

# Time: O(n²), Space: O(1)
\`\`\`

## Quick Sort
\`\`\`python
def quick_sort(arr):
    if len(arr) <= 1:
        return arr
    
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    
    return quick_sort(left) + middle + quick_sort(right)

# Average: O(n log n), Worst: O(n²), Space: O(log n)
\`\`\`

## Merge Sort
\`\`\`python
def merge_sort(arr):
    if len(arr) <= 1:
        return arr
    
    mid = len(arr) // 2
    left = merge_sort(arr[:mid])
    right = merge_sort(arr[mid:])
    
    return merge(left, right)

def merge(left, right):
    result = []
    i = j = 0
    
    while i < len(left) and j < len(right):
        if left[i] <= right[j]:
            result.append(left[i])
            i += 1
        else:
            result.append(right[j])
            j += 1
    
    result.extend(left[i:])
    result.extend(right[j:])
    return result

# Time: O(n log n), Space: O(n)
\`\`\`

## Algorithm Comparison
| Algorithm | Best | Average | Worst | Space | Stable |
|-----------|------|---------|-------|-------|--------|
| Bubble    | O(n) | O(n²)   | O(n²) | O(1)  | Yes    |
| Quick     | O(n log n) | O(n log n) | O(n²) | O(log n) | No |
| Merge     | O(n log n) | O(n log n) | O(n log n) | O(n) | Yes |`,
    category: 'programming',
    tags: ['algorithms', 'sorting', 'time-complexity', 'python'],
    difficulty: 'intermediate',
  },
];

export interface SeedingResult {
  success: boolean;
  successCount: number;
  errorCount: number;
  errors: string[];
  createdCards: string[];
}

export interface SeedingProgress {
  current: number;
  total: number;
  currentCard: string;
  status: 'creating' | 'success' | 'error';
}

/**
 * Seed programming knowledge cards into the database
 */
export async function seedProgrammingKnowledge(
  onProgress?: (progress: SeedingProgress) => void
): Promise<SeedingResult> {
  console.log('🌱 Starting programming knowledge seeding...');
  
  const result: SeedingResult = {
    success: false,
    successCount: 0,
    errorCount: 0,
    errors: [],
    createdCards: [],
  };
  
  try {
    const totalCards = PROGRAMMING_KNOWLEDGE_CARDS.length;
    
    for (let i = 0; i < totalCards; i++) {
      const cardData = PROGRAMMING_KNOWLEDGE_CARDS[i];
      
      try {
        // Validate card data (Rule 5)
        const validatedCard = SeedingCardSchema.parse(cardData);
        
        // Report progress
        onProgress?.({
          current: i + 1,
          total: totalCards,
          currentCard: validatedCard.title,
          status: 'creating',
        });
        
        console.log(`Creating card ${i + 1}/${totalCards}: "${validatedCard.title}"`);
        
        // Create the knowledge card
        const createInput: CreateKnowledgeCardInput = {
          title: validatedCard.title,
          content: validatedCard.content,
          category: validatedCard.category,
          tags: validatedCard.tags,
          difficulty: validatedCard.difficulty,
          sourceType: 'manual',
          sourceData: {
            seedingScript: 'knowledge-seeding.service.ts',
            seedingDate: new Date().toISOString(),
          },
        };
        
        const createdCard = await knowledgeCardsService.createCard(createInput);
        
        result.successCount++;
        result.createdCards.push(createdCard.title);
        
        // Report success
        onProgress?.({
          current: i + 1,
          total: totalCards,
          currentCard: validatedCard.title,
          status: 'success',
        });
        
        console.log(`✅ Created: "${validatedCard.title}" (ID: ${createdCard.$id})`);
        
      } catch (error) {
        result.errorCount++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        result.errors.push(`"${cardData.title}": ${errorMessage}`);
        
        // Report error
        onProgress?.({
          current: i + 1,
          total: totalCards,
          currentCard: cardData.title,
          status: 'error',
        });
        
        console.error(`❌ Failed to create "${cardData.title}":`, errorMessage);
      }
    }
    
    result.success = result.successCount > 0;
    
    console.log('\n📈 Seeding Summary:');
    console.log(`   ✅ Successfully created: ${result.successCount} cards`);
    console.log(`   ❌ Failed to create: ${result.errorCount} cards`);
    
    if (result.success) {
      console.log('\n🎯 Programming Knowledge Categories Added:');
      console.log('   • JavaScript fundamentals (variables, data types)');
      console.log('   • Python functions and scope');
      console.log('   • Data structures (arrays, lists)');
      console.log('   • React hooks (useState, useEffect)');
      console.log('   • Sorting algorithms comparison');
      
      console.log('\n🔍 AI Search Testing Benefits:');
      console.log('   • Review Intent Test: Now has programming content to find');
      console.log('   • Semantic Search: Can match programming concepts');
      console.log('   • Intent Recognition: Better training data for AI');
      console.log('   • Knowledge Graph: Programming relationships');
    }
    
    return result;
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('💥 Seeding failed:', errorMessage);
    
    return {
      success: false,
      successCount: 0,
      errorCount: PROGRAMMING_KNOWLEDGE_CARDS.length,
      errors: [errorMessage],
      createdCards: [],
    };
  }
}

/**
 * Check if programming knowledge cards already exist
 */
export async function checkProgrammingKnowledgeExists(userId: string): Promise<{
  exists: boolean;
  existingCount: number;
  missingCards: string[];
}> {
  try {
    const existingCards = await knowledgeCardsService.getUserCards(
      userId,
      { category: 'programming' },
      { limit: 100 }
    );
    
    const existingTitles = existingCards.cards.map(card => card.title);
    const expectedTitles = PROGRAMMING_KNOWLEDGE_CARDS.map(card => card.title);
    const missingCards = expectedTitles.filter(title => !existingTitles.includes(title));
    
    return {
      exists: missingCards.length === 0,
      existingCount: existingCards.cards.length,
      missingCards,
    };
  } catch (error) {
    console.error('Failed to check existing programming knowledge:', error);
    return {
      exists: false,
      existingCount: 0,
      missingCards: PROGRAMMING_KNOWLEDGE_CARDS.map(card => card.title),
    };
  }
}

/**
 * Get programming knowledge cards data for preview
 */
export function getProgrammingKnowledgePreview(): SeedingCard[] {
  return PROGRAMMING_KNOWLEDGE_CARDS;
}

export { PROGRAMMING_KNOWLEDGE_CARDS };