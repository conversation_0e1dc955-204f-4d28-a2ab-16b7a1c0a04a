import Voice, { SpeechRecognizedEvent, SpeechResultsEvent, SpeechErrorEvent } from '@react-native-voice/voice';
import { z } from 'zod';
import { Platform, PermissionsAndroid, Alert } from 'react-native';

// Voice recognition result schema
const VoiceResultSchema = z.object({
  transcript: z.string(),
  confidence: z.number().min(0).max(1),
  isFinal: z.boolean(),
  language: z.string(),
  timestamp: z.number(),
});

export type VoiceResult = z.infer<typeof VoiceResultSchema>;

// Voice configuration schema
const VoiceConfigSchema = z.object({
  language: z.string().default('en-US'),
  maxAlternatives: z.number().min(1).max(5).default(3),
  partialResults: z.boolean().default(true),
  continuousListening: z.boolean().default(false),
  timeout: z.number().default(10000), // 10 seconds
  confidenceThreshold: z.number().min(0).max(1).default(0.7),
});

export type VoiceConfig = z.infer<typeof VoiceConfigSchema>;

// Voice state schema
const VoiceStateSchema = z.object({
  isListening: z.boolean(),
  isAvailable: z.boolean(),
  hasPermission: z.boolean(),
  currentLanguage: z.string(),
  error: z.string().nullable(),
});

export type VoiceState = z.infer<typeof VoiceStateSchema>;

/**
 * Voice Input Service for Enhanced AI Interactions
 * Following Rule 5: Code Quality and Type Safety
 * Following Rule 12: Comprehensive Error Handling
 */
export class VoiceInputService {
  private config: VoiceConfig;
  private state: VoiceState;
  private listeners: Map<string, (result: VoiceResult) => void> = new Map();
  private errorListeners: Map<string, (error: string) => void> = new Map();
  private stateListeners: Map<string, (state: VoiceState) => void> = new Map();

  constructor(config?: Partial<VoiceConfig>) {
    this.config = VoiceConfigSchema.parse(config || {});
    this.state = {
      isListening: false,
      isAvailable: false,
      hasPermission: false,
      currentLanguage: this.config.language,
      error: null,
    };

    this.initialize();
  }

  /**
   * Initialize voice service
   */
  private async initialize(): Promise<void> {
    try {
      // Check if voice recognition is available
      const isAvailable = await Voice.isAvailable();
      this.state.isAvailable = Boolean(isAvailable);

      if (!isAvailable) {
        this.state.error = 'Voice recognition is not available on this device';
        this.notifyStateListeners();
        return;
      }

      // Request permissions
      await this.requestPermissions();

      // Set up event listeners
      this.setupEventListeners();

      console.log('Voice input service initialized successfully');
      this.notifyStateListeners();
    } catch (error) {
      console.error('Failed to initialize voice service:', error);
      this.state.error = `Initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      this.notifyStateListeners();
    }
  }

  /**
   * Request necessary permissions
   */
  private async requestPermissions(): Promise<void> {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        ]);

        this.state.hasPermission = granted[PermissionsAndroid.PERMISSIONS.RECORD_AUDIO] === 'granted';
      } else {
        // iOS permissions are handled by the plugin configuration
        this.state.hasPermission = true;
      }

      if (!this.state.hasPermission) {
        throw new Error('Microphone permission not granted');
      }
    } catch (error) {
      console.error('Permission request failed:', error);
      this.state.hasPermission = false;
      throw error;
    }
  }

  /**
   * Setup Voice event listeners
   */
  private setupEventListeners(): void {
    Voice.onSpeechStart = this.onSpeechStart.bind(this);
    Voice.onSpeechEnd = this.onSpeechEnd.bind(this);
    Voice.onSpeechResults = this.onSpeechResults.bind(this);
    Voice.onSpeechPartialResults = this.onSpeechPartialResults.bind(this);
    Voice.onSpeechError = this.onSpeechError.bind(this);
    Voice.onSpeechRecognized = this.onSpeechRecognized.bind(this);
  }

  /**
   * Handle speech start event
   */
  private onSpeechStart(): void {
    this.state.isListening = true;
    this.state.error = null;
    this.notifyStateListeners();
  }

  /**
   * Handle speech end event
   */
  private onSpeechEnd(): void {
    this.state.isListening = false;
    this.notifyStateListeners();
  }

  /**
   * Handle speech results
   */
  private onSpeechResults(event: SpeechResultsEvent): void {
    if (event.value && event.value.length > 0) {
      const transcript = event.value[0];
      const confidence = 0.9; // Default confidence, actual confidence may not be available

      if (confidence >= this.config.confidenceThreshold) {
        const result: VoiceResult = {
          transcript,
          confidence,
          isFinal: true,
          language: this.state.currentLanguage,
          timestamp: Date.now(),
        };

        this.notifyListeners(result);
      }
    }
  }

  /**
   * Handle partial speech results
   */
  private onSpeechPartialResults(event: SpeechResultsEvent): void {
    if (this.config.partialResults && event.value && event.value.length > 0) {
      const transcript = event.value[0];
      const result: VoiceResult = {
        transcript,
        confidence: 0.5, // Lower confidence for partial results
        isFinal: false,
        language: this.state.currentLanguage,
        timestamp: Date.now(),
      };

      this.notifyListeners(result);
    }
  }

  /**
   * Handle speech recognition event
   */
  private onSpeechRecognized(event: SpeechRecognizedEvent): void {
    // Speech was recognized but results not yet available
    console.log('Speech recognized:', event);
  }

  /**
   * Handle speech errors
   */
  private onSpeechError(event: SpeechErrorEvent): void {
    const errorMessage = event.error?.message || 'Unknown speech recognition error';
    this.state.error = errorMessage;
    this.state.isListening = false;
    
    console.error('Speech recognition error:', errorMessage);
    this.notifyErrorListeners(errorMessage);
    this.notifyStateListeners();
  }

  /**
   * Start voice recognition
   */
  async startListening(language?: string): Promise<void> {
    try {
      if (!this.state.isAvailable) {
        throw new Error('Voice recognition is not available');
      }

      if (!this.state.hasPermission) {
        await this.requestPermissions();
      }

      if (this.state.isListening) {
        await this.stopListening();
      }

      const targetLanguage = language || this.config.language;
      this.state.currentLanguage = targetLanguage;

      await Voice.start(targetLanguage, {
        EXTRA_MAX_RESULTS: this.config.maxAlternatives,
        EXTRA_PARTIAL_RESULTS: this.config.partialResults,
        EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS: this.config.timeout,
      });

      console.log(`Started voice recognition for language: ${targetLanguage}`);
    } catch (error) {
      const errorMessage = `Failed to start voice recognition: ${error instanceof Error ? error.message : 'Unknown error'}`;
      this.state.error = errorMessage;
      this.notifyErrorListeners(errorMessage);
      throw new Error(errorMessage);
    }
  }

  /**
   * Stop voice recognition
   */
  async stopListening(): Promise<void> {
    try {
      await Voice.stop();
      this.state.isListening = false;
      this.notifyStateListeners();
    } catch (error) {
      console.error('Failed to stop voice recognition:', error);
      throw error;
    }
  }

  /**
   * Cancel voice recognition
   */
  async cancelListening(): Promise<void> {
    try {
      await Voice.cancel();
      this.state.isListening = false;
      this.state.error = null;
      this.notifyStateListeners();
    } catch (error) {
      console.error('Failed to cancel voice recognition:', error);
      throw error;
    }
  }

  /**
   * Check if voice recognition is currently listening
   */
  isListening(): boolean {
    return this.state.isListening;
  }

  /**
   * Get current voice state
   */
  getState(): VoiceState {
    return { ...this.state };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<VoiceConfig>): void {
    this.config = VoiceConfigSchema.parse({ ...this.config, ...newConfig });
  }

  /**
   * Get available languages
   */
  async getAvailableLanguages(): Promise<string[]> {
    try {
      // Note: getSupportedLocales may not be available in all versions
      const languages = await (Voice as any).getSupportedLocales?.() || [];
      return languages || [];
    } catch (error) {
      console.error('Failed to get available languages:', error);
      return ['en-US', 'en-GB', 'es-ES', 'fr-FR', 'de-DE', 'it-IT', 'pt-BR', 'ru-RU', 'ja-JP', 'ko-KR', 'zh-CN'];
    }
  }

  /**
   * Add result listener
   */
  addResultListener(id: string, callback: (result: VoiceResult) => void): void {
    this.listeners.set(id, callback);
  }

  /**
   * Remove result listener
   */
  removeResultListener(id: string): void {
    this.listeners.delete(id);
  }

  /**
   * Add error listener
   */
  addErrorListener(id: string, callback: (error: string) => void): void {
    this.errorListeners.set(id, callback);
  }

  /**
   * Remove error listener
   */
  removeErrorListener(id: string): void {
    this.errorListeners.delete(id);
  }

  /**
   * Add state listener
   */
  addStateListener(id: string, callback: (state: VoiceState) => void): void {
    this.stateListeners.set(id, callback);
  }

  /**
   * Remove state listener
   */
  removeStateListener(id: string): void {
    this.stateListeners.delete(id);
  }

  /**
   * Notify result listeners
   */
  private notifyListeners(result: VoiceResult): void {
    this.listeners.forEach(callback => {
      try {
        callback(result);
      } catch (error) {
        console.error('Error in voice result listener:', error);
      }
    });
  }

  /**
   * Notify error listeners
   */
  private notifyErrorListeners(error: string): void {
    this.errorListeners.forEach(callback => {
      try {
        callback(error);
      } catch (err) {
        console.error('Error in voice error listener:', err);
      }
    });
  }

  /**
   * Notify state listeners
   */
  private notifyStateListeners(): void {
    this.stateListeners.forEach(callback => {
      try {
        callback({ ...this.state });
      } catch (error) {
        console.error('Error in voice state listener:', error);
      }
    });
  }

  /**
   * Cleanup on service destruction
   */
  async destroy(): Promise<void> {
    try {
      if (this.state.isListening) {
        await this.cancelListening();
      }
      
      Voice.destroy();
      this.listeners.clear();
      this.errorListeners.clear();
      this.stateListeners.clear();
    } catch (error) {
      console.error('Error destroying voice service:', error);
    }
  }

  /**
   * Get current settings
   */
  async getSettings(): Promise<any> {
    return {
      voiceInputEnabled: this.settings.enabled,
      continuousListening: this.settings.continuousListening,
      voiceCommands: this.settings.voiceCommands,
      speechFeedback: this.settings.speechFeedback,
      language: this.settings.language,
      sensitivity: this.settings.sensitivity,
      noiseReduction: this.settings.noiseReduction,
    };
  }

  /**
   * Update settings
   */
  async updateSettings(newSettings: any): Promise<void> {
    this.settings = {
      ...this.settings,
      enabled: newSettings.voiceInputEnabled ?? this.settings.enabled,
      continuousListening: newSettings.continuousListening ?? this.settings.continuousListening,
      voiceCommands: newSettings.voiceCommands ?? this.settings.voiceCommands,
      speechFeedback: newSettings.speechFeedback ?? this.settings.speechFeedback,
      language: newSettings.language ?? this.settings.language,
      sensitivity: newSettings.sensitivity ?? this.settings.sensitivity,
      noiseReduction: newSettings.noiseReduction ?? this.settings.noiseReduction,
    };
    await this.saveSettings();
  }

  /**
   * Test voice input
   */
  async testVoiceInput(): Promise<{ success: boolean; text?: string; confidence?: number; error?: string }> {
    try {
      if (!this.state.isAvailable) {
        return { success: false, error: 'Voice recognition not available' };
      }

      // Start listening for test
      await Voice.start(this.settings.language);

      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          Voice.stop();
          resolve({ success: false, error: 'Test timeout' });
        }, 5000);

        const onSpeechResults = (e: any) => {
          clearTimeout(timeout);
          Voice.removeEventListener('onSpeechResults', onSpeechResults);
          Voice.stop();

          if (e.value && e.value.length > 0) {
            resolve({
              success: true,
              text: e.value[0],
              confidence: 0.8 // Mock confidence
            });
          } else {
            resolve({ success: false, error: 'No speech detected' });
          }
        };

        Voice.addEventListener('onSpeechResults', onSpeechResults);
      });
    } catch (error) {
      return { success: false, error: String(error) };
    }
  }

  /**
   * Calibrate microphone
   */
  async calibrateMicrophone(): Promise<{ success: boolean; error?: string }> {
    try {
      // Mock calibration process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Update sensitivity based on environment
      this.settings.sensitivity = 'medium';
      this.settings.noiseReduction = true;

      await this.saveSettings();

      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  }
}

// Export singleton instance
export const voiceInputService = new VoiceInputService();
