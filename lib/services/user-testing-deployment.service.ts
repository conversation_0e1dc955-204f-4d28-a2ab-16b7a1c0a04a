/**
 * User Testing Deployment Service
 * 
 * Handles deployment readiness checks, error monitoring, and user onboarding
 * Following LearniScan development workflow rules:
 * - Rule 11: Zod validation for all configurations
 * - Rule 12: Comprehensive error handling and monitoring
 * - Rule 10: Performance optimization and health checks
 */

import { z } from 'zod';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { userTestingAnalytics } from './user-testing-analytics.service';
import { aiSearchService } from './ai-search.service';

// Deployment configuration schema
const DeploymentConfigSchema = z.object({
  version: z.string(),
  environment: z.enum(['development', 'staging', 'production']),
  features: z.object({
    userTesting: z.boolean(),
    analytics: z.boolean(),
    errorReporting: z.boolean(),
    performanceMonitoring: z.boolean(),
  }),
  limits: z.object({
    maxTestingSessions: z.number(),
    maxFeedbacksPerSession: z.number(),
    sessionTimeoutMinutes: z.number(),
  }),
  healthChecks: z.object({
    aiSearchService: z.boolean(),
    analyticsService: z.boolean(),
    localStorage: z.boolean(),
  }),
});

const HealthCheckResultSchema = z.object({
  timestamp: z.string(),
  overall: z.enum(['healthy', 'degraded', 'unhealthy']),
  services: z.object({
    aiSearch: z.object({
      status: z.enum(['healthy', 'degraded', 'unhealthy']),
      responseTime: z.number().optional(),
      error: z.string().optional(),
    }),
    analytics: z.object({
      status: z.enum(['healthy', 'degraded', 'unhealthy']),
      error: z.string().optional(),
    }),
    localStorage: z.object({
      status: z.enum(['healthy', 'degraded', 'unhealthy']),
      error: z.string().optional(),
    }),
  }),
  recommendations: z.array(z.string()),
});

const ErrorReportSchema = z.object({
  id: z.string(),
  timestamp: z.string(),
  userId: z.string().optional(),
  error: z.object({
    name: z.string(),
    message: z.string(),
    stack: z.string().optional(),
  }),
  context: z.object({
    screen: z.string(),
    action: z.string(),
    userAgent: z.string().optional(),
    sessionId: z.string().optional(),
  }),
  severity: z.enum(['low', 'medium', 'high', 'critical']),
  resolved: z.boolean().default(false),
});

// Type exports
export type DeploymentConfig = z.infer<typeof DeploymentConfigSchema>;
export type HealthCheckResult = z.infer<typeof HealthCheckResultSchema>;
export type ErrorReport = z.infer<typeof ErrorReportSchema>;

/**
 * User Testing Deployment Service
 */
class UserTestingDeploymentService {
  private readonly STORAGE_KEYS = {
    DEPLOYMENT_CONFIG: 'deployment_config',
    ERROR_REPORTS: 'error_reports',
    HEALTH_CHECKS: 'health_checks',
    USER_ONBOARDING: 'user_onboarding_status',
  };

  private readonly DEFAULT_CONFIG: DeploymentConfig = {
    version: '1.0.0',
    environment: 'production',
    features: {
      userTesting: true,
      analytics: true,
      errorReporting: true,
      performanceMonitoring: true,
    },
    limits: {
      maxTestingSessions: 10,
      maxFeedbacksPerSession: 5,
      sessionTimeoutMinutes: 30,
    },
    healthChecks: {
      aiSearchService: true,
      analyticsService: true,
      localStorage: true,
    },
  };

  /**
   * Initialize deployment configuration
   */
  async initializeDeployment(): Promise<DeploymentConfig> {
    try {
      // Load existing config or use defaults
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.DEPLOYMENT_CONFIG);
      const config = stored ? JSON.parse(stored) : this.DEFAULT_CONFIG;
      
      // Validate configuration
      const validatedConfig = DeploymentConfigSchema.parse(config);
      
      // Store validated config
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.DEPLOYMENT_CONFIG,
        JSON.stringify(validatedConfig)
      );

      console.log('Deployment initialized:', validatedConfig);
      return validatedConfig;

    } catch (error) {
      console.error('Failed to initialize deployment:', error);
      
      // Fallback to default config
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.DEPLOYMENT_CONFIG,
        JSON.stringify(this.DEFAULT_CONFIG)
      );
      
      return this.DEFAULT_CONFIG;
    }
  }

  /**
   * Perform comprehensive health checks
   */
  async performHealthCheck(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    const recommendations: string[] = [];

    try {
      // Check AI Search Service
      const aiSearchHealth = await this.checkAISearchService();
      
      // Check Analytics Service
      const analyticsHealth = await this.checkAnalyticsService();
      
      // Check Local Storage
      const localStorageHealth = await this.checkLocalStorage();

      // Determine overall health
      const services = {
        aiSearch: aiSearchHealth,
        analytics: analyticsHealth,
        localStorage: localStorageHealth,
      };

      const healthStatuses = Object.values(services).map(s => s.status);
      const overall = healthStatuses.includes('unhealthy') ? 'unhealthy' :
                    healthStatuses.includes('degraded') ? 'degraded' : 'healthy';

      // Generate recommendations
      if (aiSearchHealth.status !== 'healthy') {
        recommendations.push('AI Search service needs attention - check network connectivity');
      }
      if (analyticsHealth.status !== 'healthy') {
        recommendations.push('Analytics service issues detected - check local storage');
      }
      if (localStorageHealth.status !== 'healthy') {
        recommendations.push('Local storage issues - may affect data persistence');
      }

      const result: HealthCheckResult = {
        timestamp: new Date().toISOString(),
        overall,
        services,
        recommendations,
      };

      // Validate and store health check result
      const validatedResult = HealthCheckResultSchema.parse(result);
      await this.storeHealthCheck(validatedResult);

      console.log(`Health check completed in ${Date.now() - startTime}ms:`, overall);
      return validatedResult;

    } catch (error) {
      console.error('Health check failed:', error);
      
      // Return unhealthy status with error info
      const errorResult: HealthCheckResult = {
        timestamp: new Date().toISOString(),
        overall: 'unhealthy',
        services: {
          aiSearch: { status: 'unhealthy', error: 'Health check failed' },
          analytics: { status: 'unhealthy', error: 'Health check failed' },
          localStorage: { status: 'unhealthy', error: 'Health check failed' },
        },
        recommendations: ['System health check failed - contact support'],
      };

      return errorResult;
    }
  }

  /**
   * Report and track errors
   */
  async reportError(
    error: Error,
    context: {
      screen: string;
      action: string;
      userId?: string;
      sessionId?: string;
    }
  ): Promise<void> {
    try {
      const errorReport: ErrorReport = {
        id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        userId: context.userId,
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
        context: {
          ...context,
          userAgent: 'React Native',
        },
        severity: this.determineSeverity(error, context),
        resolved: false,
      };

      // Validate error report
      const validatedReport = ErrorReportSchema.parse(errorReport);

      // Store error report
      const existingReports = await this.getErrorReports();
      const updatedReports = [...existingReports, validatedReport];
      
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.ERROR_REPORTS,
        JSON.stringify(updatedReports)
      );

      console.log('Error reported:', errorReport.id, errorReport.severity);

      // Auto-resolve low severity errors after 24 hours
      if (errorReport.severity === 'low') {
        setTimeout(() => {
          this.resolveError(errorReport.id);
        }, 24 * 60 * 60 * 1000);
      }

    } catch (reportError) {
      console.error('Failed to report error:', reportError);
      // Don't throw here to avoid error reporting loops
    }
  }

  /**
   * Check if user testing is ready for deployment
   */
  async isDeploymentReady(): Promise<{
    ready: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      // Perform health check
      const healthCheck = await this.performHealthCheck();
      
      if (healthCheck.overall === 'unhealthy') {
        issues.push('System health check failed');
        recommendations.push('Fix critical system issues before deployment');
      }

      // Check AI Search Service
      if (healthCheck.services.aiSearch.status === 'unhealthy') {
        issues.push('AI Search service is not responding');
        recommendations.push('Verify AI service configuration and connectivity');
      }

      // Check Analytics Service
      if (healthCheck.services.analytics.status === 'unhealthy') {
        issues.push('Analytics service is not working');
        recommendations.push('Check local storage permissions and availability');
      }

      // Check deployment configuration
      const config = await this.getDeploymentConfig();
      if (!config.features.userTesting) {
        issues.push('User testing feature is disabled');
        recommendations.push('Enable user testing in deployment configuration');
      }

      // Check for recent critical errors
      const recentErrors = await this.getRecentCriticalErrors();
      if (recentErrors.length > 0) {
        issues.push(`${recentErrors.length} unresolved critical errors`);
        recommendations.push('Resolve critical errors before deployment');
      }

      const ready = issues.length === 0;

      return {
        ready,
        issues,
        recommendations: ready ? ['System is ready for deployment'] : recommendations,
      };

    } catch (error) {
      console.error('Deployment readiness check failed:', error);
      return {
        ready: false,
        issues: ['Deployment readiness check failed'],
        recommendations: ['Fix system issues and retry deployment check'],
      };
    }
  }

  /**
   * Mark user as onboarded
   */
  async markUserOnboarded(userId: string): Promise<void> {
    try {
      const onboardingStatus = await this.getUserOnboardingStatus();
      onboardingStatus[userId] = {
        completed: true,
        timestamp: new Date().toISOString(),
      };

      await AsyncStorage.setItem(
        this.STORAGE_KEYS.USER_ONBOARDING,
        JSON.stringify(onboardingStatus)
      );

      console.log('User onboarded:', userId);

    } catch (error) {
      console.error('Failed to mark user as onboarded:', error);
    }
  }

  /**
   * Check if user needs onboarding
   */
  async needsOnboarding(userId: string): Promise<boolean> {
    try {
      const onboardingStatus = await this.getUserOnboardingStatus();
      return !onboardingStatus[userId]?.completed;
    } catch (error) {
      console.error('Failed to check onboarding status:', error);
      return true; // Default to showing onboarding
    }
  }

  // Private helper methods

  private async checkAISearchService(): Promise<HealthCheckResult['services']['aiSearch']> {
    try {
      const startTime = Date.now();
      
      // Perform a lightweight test search
      await aiSearchService.semanticSearch({
        query: 'health check',
        context: {
          userId: 'health-check',
          searchType: 'knowledge_cards',
          limit: 1,
        },
      });

      const responseTime = Date.now() - startTime;
      
      return {
        status: responseTime < 5000 ? 'healthy' : 'degraded',
        responseTime,
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkAnalyticsService(): Promise<HealthCheckResult['services']['analytics']> {
    try {
      // Test analytics service functionality
      await userTestingAnalytics.getTestingStatistics();
      
      return { status: 'healthy' };

    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkLocalStorage(): Promise<HealthCheckResult['services']['localStorage']> {
    try {
      // Test local storage read/write
      const testKey = 'health_check_test';
      const testValue = Date.now().toString();
      
      await AsyncStorage.setItem(testKey, testValue);
      const retrieved = await AsyncStorage.getItem(testKey);
      await AsyncStorage.removeItem(testKey);
      
      if (retrieved !== testValue) {
        throw new Error('Local storage read/write test failed');
      }
      
      return { status: 'healthy' };

    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private determineSeverity(error: Error, context: any): ErrorReport['severity'] {
    // Determine error severity based on error type and context
    if (error.name === 'TypeError' || error.name === 'ReferenceError') {
      return 'high';
    }
    if (context.screen === 'user-testing' || context.action === 'submit-feedback') {
      return 'medium';
    }
    return 'low';
  }

  private async getDeploymentConfig(): Promise<DeploymentConfig> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.DEPLOYMENT_CONFIG);
      return stored ? JSON.parse(stored) : this.DEFAULT_CONFIG;
    } catch (error) {
      return this.DEFAULT_CONFIG;
    }
  }

  private async getErrorReports(): Promise<ErrorReport[]> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.ERROR_REPORTS);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      return [];
    }
  }

  private async getRecentCriticalErrors(): Promise<ErrorReport[]> {
    const reports = await this.getErrorReports();
    const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
    
    return reports.filter(report => 
      report.severity === 'critical' && 
      !report.resolved &&
      new Date(report.timestamp).getTime() > oneDayAgo
    );
  }

  private async getUserOnboardingStatus(): Promise<Record<string, { completed: boolean; timestamp: string }>> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.USER_ONBOARDING);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      return {};
    }
  }

  private async storeHealthCheck(result: HealthCheckResult): Promise<void> {
    try {
      const existing = await AsyncStorage.getItem(this.STORAGE_KEYS.HEALTH_CHECKS);
      const healthChecks = existing ? JSON.parse(existing) : [];
      
      // Keep only last 10 health checks
      const updatedChecks = [result, ...healthChecks].slice(0, 10);
      
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.HEALTH_CHECKS,
        JSON.stringify(updatedChecks)
      );
    } catch (error) {
      console.error('Failed to store health check:', error);
    }
  }

  private async resolveError(errorId: string): Promise<void> {
    try {
      const reports = await this.getErrorReports();
      const updatedReports = reports.map(report =>
        report.id === errorId ? { ...report, resolved: true } : report
      );
      
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.ERROR_REPORTS,
        JSON.stringify(updatedReports)
      );
    } catch (error) {
      console.error('Failed to resolve error:', error);
    }
  }
}

// Export singleton instance
export const userTestingDeployment = new UserTestingDeploymentService();
