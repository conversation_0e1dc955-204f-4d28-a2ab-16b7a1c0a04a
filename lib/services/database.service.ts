import { ID, Query, Permission, Role } from 'react-native-appwrite';
import { databases } from '@/lib/config/appwrite';
import { APPWRITE_CONFIG } from '@/lib/config/appwrite';
import type { 
  KnowledgeCard, 
  LearningSession, 
  ScanHistory, 
  AppwriteUser,
  DatabaseResponse,
  AppwriteError 
} from '@/types/appwrite';

export class DatabaseService {
  private databaseId = APPWRITE_CONFIG.databaseId;
  private collections = APPWRITE_CONFIG.collections;

  // Knowledge Cards CRUD Operations
  async createKnowledgeCard(
    data: Omit<KnowledgeCard, '$id' | '$createdAt' | '$updatedAt' | '$permissions' | '$collectionId' | '$databaseId'>
  ): Promise<KnowledgeCard> {
    try {
      const now = new Date().toISOString();
      const permissions = [
        Permission.read(Role.user(data.userId)),
        Permission.write(Role.user(data.userId)),
        Permission.update(Role.user(data.userId)),
        Permission.delete(Role.user(data.userId)),
      ];

      // Add public read permission if card is public
      if (data.isPublic) {
        permissions.push(Permission.read(Role.any()));
      }

      const result = await databases.createDocument(
        this.databaseId,
        this.collections.knowledgeCards,
        ID.unique(),
        {
          ...data,
          tags: JSON.stringify(data.tags),
          sourceData: data.sourceData ? JSON.stringify(data.sourceData) : null,
          reviewData: JSON.stringify(data.reviewData),
          createdAt: now,
          updatedAt: now,
        },
        permissions
      );

      return this.parseKnowledgeCard(result);
    } catch (error) {
      throw this.handleError(error, 'Failed to create knowledge card');
    }
  }

  async getKnowledgeCard(cardId: string): Promise<KnowledgeCard> {
    try {
      const result = await databases.getDocument(
        this.databaseId,
        this.collections.knowledgeCards,
        cardId
      );

      return this.parseKnowledgeCard(result);
    } catch (error) {
      throw this.handleError(error, 'Failed to get knowledge card');
    }
  }

  async getKnowledgeCards(
    userId: string, 
    limit = 25, 
    offset = 0,
    category?: string,
    difficulty?: string,
    searchQuery?: string
  ): Promise<DatabaseResponse<KnowledgeCard>> {
    try {
      const queries = [
        Query.equal('userId', userId),
        Query.orderDesc('$createdAt'),
        Query.limit(limit),
        Query.offset(offset),
      ];

      if (category) {
        queries.push(Query.equal('category', category));
      }

      if (difficulty) {
        queries.push(Query.equal('difficulty', difficulty));
      }

      if (searchQuery) {
        queries.push(Query.search('title', searchQuery));
      }

      const result = await databases.listDocuments(
        this.databaseId,
        this.collections.knowledgeCards,
        queries
      );

      return {
        total: result.total,
        documents: result.documents.map(doc => this.parseKnowledgeCard(doc)),
      };
    } catch (error) {
      throw this.handleError(error, 'Failed to get knowledge cards');
    }
  }

  async updateKnowledgeCard(
    cardId: string, 
    data: Partial<Omit<KnowledgeCard, '$id' | '$createdAt' | '$updatedAt' | '$permissions' | '$collectionId' | '$databaseId'>>
  ): Promise<KnowledgeCard> {
    try {
      const updateData: any = {
        ...data,
        updatedAt: new Date().toISOString(),
      };

      // Serialize complex fields
      if (data.tags) {
        updateData.tags = JSON.stringify(data.tags);
      }
      if (data.sourceData) {
        updateData.sourceData = JSON.stringify(data.sourceData);
      }
      if (data.reviewData) {
        updateData.reviewData = JSON.stringify(data.reviewData);
      }

      const result = await databases.updateDocument(
        this.databaseId,
        this.collections.knowledgeCards,
        cardId,
        updateData
      );

      return this.parseKnowledgeCard(result);
    } catch (error) {
      throw this.handleError(error, 'Failed to update knowledge card');
    }
  }

  async deleteKnowledgeCard(cardId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        this.databaseId,
        this.collections.knowledgeCards,
        cardId
      );
    } catch (error) {
      throw this.handleError(error, 'Failed to delete knowledge card');
    }
  }

  // Learning Sessions CRUD Operations
  async createLearningSession(
    data: Omit<LearningSession, '$id' | '$createdAt' | '$updatedAt' | '$permissions' | '$collectionId' | '$databaseId'>
  ): Promise<LearningSession> {
    try {
      const permissions = [
        Permission.read(Role.user(data.userId)),
        Permission.write(Role.user(data.userId)),
        Permission.update(Role.user(data.userId)),
        Permission.delete(Role.user(data.userId)),
      ];

      const result = await databases.createDocument(
        this.databaseId,
        this.collections.learningSessions,
        ID.unique(),
        {
          ...data,
          cardsReviewed: JSON.stringify(data.cardsReviewed),
          performance: JSON.stringify(data.performance),
        },
        permissions
      );

      return this.parseLearningSession(result);
    } catch (error) {
      throw this.handleError(error, 'Failed to create learning session');
    }
  }

  async getLearningHistory(userId: string, limit = 50): Promise<DatabaseResponse<LearningSession>> {
    try {
      const result = await databases.listDocuments(
        this.databaseId,
        this.collections.learningSessions,
        [
          Query.equal('userId', userId),
          Query.orderDesc('startedAt'),
          Query.limit(limit),
        ]
      );

      return {
        total: result.total,
        documents: result.documents.map(doc => this.parseLearningSession(doc)),
      };
    } catch (error) {
      throw this.handleError(error, 'Failed to get learning history');
    }
  }

  // Scan History CRUD Operations
  async createScanRecord(
    data: Omit<ScanHistory, '$id' | '$createdAt' | '$updatedAt' | '$permissions' | '$collectionId' | '$databaseId'>
  ): Promise<ScanHistory> {
    try {
      const permissions = [
        Permission.read(Role.user(data.userId)),
        Permission.write(Role.user(data.userId)),
        Permission.update(Role.user(data.userId)),
        Permission.delete(Role.user(data.userId)),
      ];

      const result = await databases.createDocument(
        this.databaseId,
        this.collections.scanHistory,
        ID.unique(),
        {
          ...data,
          metadata: JSON.stringify(data.metadata),
        },
        permissions
      );

      return this.parseScanHistory(result);
    } catch (error) {
      throw this.handleError(error, 'Failed to create scan record');
    }
  }

  async getScanHistory(userId: string, limit = 50): Promise<DatabaseResponse<ScanHistory>> {
    try {
      const result = await databases.listDocuments(
        this.databaseId,
        this.collections.scanHistory,
        [
          Query.equal('userId', userId),
          Query.orderDesc('$createdAt'),
          Query.limit(limit),
        ]
      );

      return {
        total: result.total,
        documents: result.documents.map(doc => this.parseScanHistory(doc)),
      };
    } catch (error) {
      throw this.handleError(error, 'Failed to get scan history');
    }
  }

  // User Profile Operations
  async createUserProfile(
    data: Omit<AppwriteUser, '$id' | '$createdAt' | '$updatedAt' | '$permissions' | '$collectionId' | '$databaseId'>
  ): Promise<AppwriteUser> {
    try {
      const now = new Date().toISOString();
      const permissions = [
        Permission.read(Role.user(data.email)), // Use email as user identifier
        Permission.write(Role.user(data.email)),
        Permission.update(Role.user(data.email)),
        Permission.delete(Role.user(data.email)),
      ];

      const result = await databases.createDocument(
        this.databaseId,
        this.collections.users,
        ID.unique(),
        {
          ...data,
          preferences: JSON.stringify(data.preferences),
          learningStats: JSON.stringify(data.learningStats),
          subscription: data.subscription ? JSON.stringify(data.subscription) : null,
          createdAt: now,
          updatedAt: now,
        },
        permissions
      );

      return this.parseUser(result);
    } catch (error) {
      throw this.handleError(error, 'Failed to create user profile');
    }
  }

  async getUserProfile(userId: string): Promise<AppwriteUser> {
    try {
      const result = await databases.getDocument(
        this.databaseId,
        this.collections.users,
        userId
      );

      return this.parseUser(result);
    } catch (error) {
      throw this.handleError(error, 'Failed to get user profile');
    }
  }

  async updateUserProfile(
    userId: string, 
    data: Partial<Omit<AppwriteUser, '$id' | '$createdAt' | '$updatedAt' | '$permissions' | '$collectionId' | '$databaseId'>>
  ): Promise<AppwriteUser> {
    try {
      const updateData: any = {
        ...data,
        updatedAt: new Date().toISOString(),
      };

      // Serialize complex fields
      if (data.preferences) {
        updateData.preferences = JSON.stringify(data.preferences);
      }
      if (data.learningStats) {
        updateData.learningStats = JSON.stringify(data.learningStats);
      }
      if (data.subscription) {
        updateData.subscription = JSON.stringify(data.subscription);
      }

      const result = await databases.updateDocument(
        this.databaseId,
        this.collections.users,
        userId,
        updateData
      );

      return this.parseUser(result);
    } catch (error) {
      throw this.handleError(error, 'Failed to update user profile');
    }
  }

  // Parsing Methods
  private parseKnowledgeCard(doc: any): KnowledgeCard {
    return {
      ...doc,
      tags: doc.tags ? JSON.parse(doc.tags) : [],
      sourceData: doc.sourceData ? JSON.parse(doc.sourceData) : undefined,
      reviewData: JSON.parse(doc.reviewData),
    };
  }

  private parseLearningSession(doc: any): LearningSession {
    return {
      ...doc,
      cardsReviewed: JSON.parse(doc.cardsReviewed),
      performance: JSON.parse(doc.performance),
    };
  }

  private parseScanHistory(doc: any): ScanHistory {
    return {
      ...doc,
      metadata: JSON.parse(doc.metadata),
    };
  }

  private parseUser(doc: any): AppwriteUser {
    return {
      ...doc,
      preferences: JSON.parse(doc.preferences),
      learningStats: JSON.parse(doc.learningStats),
      subscription: doc.subscription ? JSON.parse(doc.subscription) : undefined,
    };
  }

  // Error Handling
  private handleError(error: any, defaultMessage: string): Error {
    if (error.code) {
      const appwriteError = error as AppwriteError;
      switch (appwriteError.code) {
        case 401:
          return new Error('Authentication required. Please sign in again.');
        case 403:
          return new Error('Access denied. You don\'t have permission for this action.');
        case 404:
          return new Error('Resource not found.');
        case 409:
          return new Error('Conflict. This resource already exists.');
        case 429:
          return new Error('Too many requests. Please try again later.');
        case 500:
          return new Error('Server error. Please try again later.');
        case 503:
          return new Error('Service unavailable. Please check your connection.');
        default:
          return new Error(appwriteError.message || defaultMessage);
      }
    }
    
    return new Error(error.message || defaultMessage);
  }

  // SCAN HISTORY METHODS

  /**
   * Create a new scan history record
   */
  async createScanHistory(scanHistoryData: Omit<ScanHistory, keyof AppwriteDocument>): Promise<ScanHistory> {
    try {
      const scanHistory = await databases.createDocument(
        this.databaseId,
        APPWRITE_CONFIG.collections.scanHistory,
        ID.unique(),
        scanHistoryData
      ) as ScanHistory;

      return scanHistory;
    } catch (error) {
      console.error('Error creating scan history:', error);
      throw error;
    }
  }

  /**
   * Get scan history for a user
   */
  async getUserScanHistory(
    userId: string,
    limit: number = 20,
    offset: number = 0
  ): Promise<{ total: number; documents: ScanHistory[] }> {
    try {
      const response = await databases.listDocuments(
        this.databaseId,
        APPWRITE_CONFIG.collections.scanHistory,
        [
          Query.equal('userId', userId),
          Query.orderDesc('$createdAt'),
          Query.limit(limit),
          Query.offset(offset),
        ]
      ) as DatabaseResponse<ScanHistory>;

      return response;
    } catch (error) {
      console.error('Error getting user scan history:', error);
      throw error;
    }
  }

  /**
   * Get a specific scan history record
   */
  async getScanHistory(scanHistoryId: string): Promise<ScanHistory | null> {
    try {
      const scanHistory = await databases.getDocument(
        this.databaseId,
        APPWRITE_CONFIG.collections.scanHistory,
        scanHistoryId
      ) as ScanHistory;

      return scanHistory;
    } catch (error) {
      console.error('Error getting scan history:', error);
      return null;
    }
  }

  /**
   * Update scan history with extracted text and processing results
   */
  async updateScanHistory(
    scanHistoryId: string,
    updates: {
      extractedText?: string;
      processedContent?: string;
      knowledgeCardId?: string;
      confidence?: number;
    }
  ): Promise<ScanHistory | null> {
    try {
      const scanHistory = await databases.updateDocument(
        this.databaseId,
        APPWRITE_CONFIG.collections.scanHistory,
        scanHistoryId,
        updates
      ) as ScanHistory;

      return scanHistory;
    } catch (error) {
      console.error('Error updating scan history:', error);
      return null;
    }
  }

  /**
   * Delete a scan history record
   */
  async deleteScanHistory(scanHistoryId: string): Promise<boolean> {
    try {
      await databases.deleteDocument(
        this.databaseId,
        APPWRITE_CONFIG.collections.scanHistory,
        scanHistoryId
      );
      return true;
    } catch (error) {
      console.error('Error deleting scan history:', error);
      return false;
    }
  }
}



export const databaseService = new DatabaseService();
