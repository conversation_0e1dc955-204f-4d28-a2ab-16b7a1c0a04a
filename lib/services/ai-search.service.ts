/**
 * AI-Powered Search & Discovery Service
 * 
 * Enhances search functionality with AI-powered features:
 * - Semantic search using embeddings
 * - Intent understanding for natural language queries
 * - Smart suggestions and autocomplete
 * - Cross-reference discovery and recommendations
 * 
 * Following LearniScan development workflow rules:
 * - Rule 11: Zod validation for all inputs/outputs
 * - Rule 12: Comprehensive error handling with early returns
 * - Rule 5: TypeScript quality and type safety
 * - Rule 10: Performance optimization with caching and streaming
 */

import { z } from 'zod';
import { generateText, generateObject, embed } from 'ai';
import { aiModels, AI_CONFIG } from '@/lib/config/ai.config';
import { AIServiceError } from './ai.service';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Knowledge Cards integration
import { knowledgeCardsService } from './knowledge-cards.service';
import type { CompleteKnowledgeCard } from '@/types/appwrite-v2-fixed';

// Permission system integration
import { permissionsService, PERMISSIONS } from './permissions.service';

// Input/Output schemas for type safety (Rule 11)
const SemanticSearchInputSchema = z.object({
  query: z.string().min(1, 'Search query is required'),
  context: z.object({
    userId: z.string(),
    searchType: z.enum(['knowledge_cards', 'documents', 'all']).default('all'),
    filters: z.object({
      category: z.string().optional(),
      difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
      tags: z.array(z.string()).optional(),
      dateRange: z.string().optional(),
    }).optional(),
    limit: z.number().min(1).max(50).default(20),
    includeRelated: z.boolean().default(true),
  }),
  options: z.object({
    enableSemanticSimilarity: z.boolean().default(true),
    enableIntentUnderstanding: z.boolean().default(true),
    enableSmartSuggestions: z.boolean().default(true),
    similarityThreshold: z.number().min(0).max(1).default(0.7),
  }).optional(),
});

const SearchIntentSchema = z.object({
  intent: z.enum(['search', 'learn', 'review', 'discover', 'compare', 'explain']),
  confidence: z.number().min(0).max(1),
  entities: z.array(z.object({
    text: z.string(),
    type: z.enum(['topic', 'concept', 'skill', 'category', 'difficulty']),
    confidence: z.number().min(0).max(1),
  })),
  suggestedFilters: z.object({
    category: z.string().optional(),
    difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
    tags: z.array(z.string()).optional(),
  }).optional(),
  reformulatedQuery: z.string().optional(),
});

const SemanticSearchResultSchema = z.object({
  id: z.string(),
  title: z.string(),
  content: z.string(),
  type: z.enum(['knowledge_card', 'document', 'concept']),
  relevanceScore: z.number().min(0).max(1),
  semanticSimilarity: z.number().min(0).max(1),
  metadata: z.object({
    category: z.string().optional(),
    difficulty: z.string().optional(),
    tags: z.array(z.string()).optional(),
    createdAt: z.string().optional(),
    lastModified: z.string().optional(),
  }),
  highlights: z.array(z.object({
    field: z.string(),
    text: z.string(),
    startIndex: z.number(),
    endIndex: z.number(),
  })).optional(),
  relatedConcepts: z.array(z.string()).optional(),
});

const SmartSuggestionSchema = z.object({
  id: z.string(),
  text: z.string(),
  type: z.enum(['query', 'concept', 'topic', 'related', 'trending']),
  confidence: z.number().min(0).max(1),
  reasoning: z.string().optional(),
  metadata: z.object({
    category: z.string().optional(),
    popularity: z.number().optional(),
    recentSearches: z.number().optional(),
  }).optional(),
});

const AISearchResponseSchema = z.object({
  query: z.string(),
  intent: SearchIntentSchema,
  results: z.array(SemanticSearchResultSchema),
  suggestions: z.array(SmartSuggestionSchema),
  relatedQueries: z.array(z.string()),
  totalResults: z.number(),
  processingTime: z.number(),
  searchMetadata: z.object({
    semanticSearchUsed: z.boolean(),
    embeddingModel: z.string(),
    cacheHit: z.boolean(),
  }),
});

// Type exports
export type SemanticSearchInput = z.infer<typeof SemanticSearchInputSchema>;
export type SearchIntent = z.infer<typeof SearchIntentSchema>;
export type SemanticSearchResult = z.infer<typeof SemanticSearchResultSchema>;
export type SmartSuggestion = z.infer<typeof SmartSuggestionSchema>;
export type AISearchResponse = z.infer<typeof AISearchResponseSchema>;

// Progress tracking for streaming search
export interface SearchProgress {
  stage: 'understanding' | 'embedding' | 'searching' | 'ranking' | 'suggesting';
  progress: number; // 0-100
  message: string;
  partialResults?: Partial<AISearchResponse>;
}

/**
 * AI-Powered Search & Discovery Service
 */
class AISearchService {
  private cache = new Map<string, any>();
  private readonly CACHE_TTL = 300000; // 5 minutes
  private readonly CACHE_PREFIX = 'ai_search_';

  /**
   * Perform semantic search with AI-powered understanding
   */
  async semanticSearch(
    input: SemanticSearchInput,
    onProgress?: (progress: SearchProgress) => void
  ): Promise<AISearchResponse> {
    // Input validation (Rule 11)
    const validatedInput = SemanticSearchInputSchema.parse(input);

    // Permission checking (Rule 12: Error handling)
    if (validatedInput.userId) {
      const permissionResult = await permissionsService.checkPermissionWithUsage(
        validatedInput.userId,
        PERMISSIONS.AI_SEARCH,
        'ai_searches',
        validatedInput.userRole
      );

      if (!permissionResult.hasPermission) {
        if (permissionResult.upgradeRequired) {
          throw new Error('AI search requires Premium subscription');
        }
        throw new Error(`Search limit reached: ${permissionResult.reason}`);
      }
    }
    
    // Check cache first (Rule 10: Performance optimization)
    const cacheKey = this.getCacheKey(validatedInput);
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      onProgress?.({
        stage: 'suggesting',
        progress: 100,
        message: 'Retrieved from cache',
        partialResults: cached,
      });
      return cached;
    }

    const startTime = Date.now();
    const { query, context, options } = validatedInput;

    try {
      // Stage 1: Intent Understanding
      onProgress?.({
        stage: 'understanding',
        progress: 10,
        message: 'Understanding search intent...',
      });

      const intent = await this.analyzeSearchIntent(query, context);

      // Stage 2: Query Embedding
      onProgress?.({
        stage: 'embedding',
        progress: 30,
        message: 'Creating semantic embeddings...',
      });

      const queryEmbedding = options?.enableSemanticSimilarity 
        ? await this.generateQueryEmbedding(intent.reformulatedQuery || query)
        : null;

      // Stage 3: Semantic Search
      onProgress?.({
        stage: 'searching',
        progress: 50,
        message: 'Searching knowledge base...',
      });

      const searchResults = await this.performSemanticSearch(
        query,
        intent,
        queryEmbedding,
        context,
        options
      );

      // Stage 4: Result Ranking and Enhancement
      onProgress?.({
        stage: 'ranking',
        progress: 70,
        message: 'Ranking and enhancing results...',
      });

      const rankedResults = await this.rankAndEnhanceResults(
        searchResults,
        intent,
        queryEmbedding,
        context
      );

      // Stage 5: Smart Suggestions
      onProgress?.({
        stage: 'suggesting',
        progress: 90,
        message: 'Generating smart suggestions...',
      });

      const suggestions = options?.enableSmartSuggestions
        ? await this.generateSmartSuggestions(query, intent, rankedResults, context)
        : [];

      const relatedQueries = await this.generateRelatedQueries(query, intent, context);

      const response: AISearchResponse = {
        query,
        intent,
        results: rankedResults,
        suggestions,
        relatedQueries,
        totalResults: rankedResults.length,
        processingTime: Date.now() - startTime,
        searchMetadata: {
          semanticSearchUsed: !!queryEmbedding,
          embeddingModel: AI_CONFIG.models.embedding,
          cacheHit: false,
        },
      };

      // Validate response (Rule 11)
      const validatedResponse = AISearchResponseSchema.parse(response);

      // Cache the results (Rule 10)
      this.setCache(cacheKey, validatedResponse);

      // Track usage after successful search (Rule 10: Performance optimization)
      if (validatedInput.userId) {
        try {
          await permissionsService.trackUsage(validatedInput.userId, 'ai_searches');
        } catch (error) {
          // Don't fail the search if usage tracking fails
          console.warn('Failed to track AI search usage:', error);
        }
      }

      onProgress?.({
        stage: 'suggesting',
        progress: 100,
        message: `Found ${rankedResults.length} results`,
        partialResults: validatedResponse,
      });

      return validatedResponse;

    } catch (error) {
      console.error('AI Search Error:', error);
      this.handleError(error, 'semanticSearch');
    }
  }

  /**
   * Analyze search intent using AI
   */
  private async analyzeSearchIntent(
    query: string,
    context: SemanticSearchInput['context']
  ): Promise<SearchIntent> {
    try {
      const prompt = `Analyze the search intent for this query: "${query}"
      
      Context:
      - Search type: ${context.searchType}
      - User context: Learning platform for knowledge cards and documents
      
      Determine:
      1. Primary intent (search, learn, review, discover, compare, explain)
      2. Extract entities (topics, concepts, skills, categories, difficulty levels)
      3. Suggest appropriate filters
      4. Reformulate query if needed for better search results
      
      Focus on educational and learning contexts.`;

      const { object } = await generateObject({
        model: aiModels.chat,
        schema: SearchIntentSchema,
        messages: [{
          role: 'system',
          content: 'You are an expert at understanding search intent in educational contexts. Analyze queries to help users find the most relevant learning content.'
        }, {
          role: 'user',
          content: prompt
        }],
        temperature: 0.3, // Low temperature for consistent intent analysis
      });

      return object;

    } catch (error) {
      // Fallback intent if AI analysis fails (Rule 12: Error handling)
      return {
        intent: 'search',
        confidence: 0.5,
        entities: [],
        reformulatedQuery: query,
      };
    }
  }

  /**
   * Generate query embedding for semantic similarity
   */
  private async generateQueryEmbedding(query: string): Promise<number[]> {
    try {
      const { embedding } = await embed({
        model: aiModels.embedding,
        value: query,
      });

      return embedding;
    } catch (error) {
      console.error('Embedding generation failed:', error);
      return []; // Return empty array as fallback
    }
  }

  /**
   * Perform semantic search with real Knowledge Cards data
   */
  private async performSemanticSearch(
    query: string,
    intent: SearchIntent,
    queryEmbedding: number[] | null,
    context: SemanticSearchInput['context'],
    options?: SemanticSearchInput['options']
  ): Promise<SemanticSearchResult[]> {
    try {
      // Get knowledge cards from the service
      const cardsResponse = await knowledgeCardsService.getUserCards(
        context.userId,
        {
          searchTerm: intent.reformulatedQuery || query,
          category: intent.suggestedFilters?.category,
          difficulty: intent.suggestedFilters?.difficulty,
          tags: intent.suggestedFilters?.tags,
        },
        {
          limit: context.limit || 20,
          orderBy: 'created',
          orderDirection: 'desc',
        }
      );

      // Convert knowledge cards to search results
      const searchResults: SemanticSearchResult[] = await Promise.all(
        cardsResponse.cards.map(async (knowledgeCard) => {
          const card = knowledgeCard.card;
          const content = knowledgeCard.content;

          // Calculate semantic similarity if embeddings are available
          let semanticSimilarity = 0.5; // Default similarity
          if (queryEmbedding && queryEmbedding.length > 0) {
            // In a real implementation, you would:
            // 1. Get or generate embeddings for the card content
            // 2. Calculate cosine similarity with query embedding
            // For now, use a heuristic based on text matching
            semanticSimilarity = this.calculateTextSimilarity(
              query,
              `${card.title} ${content?.content || ''} ${content?.summary || ''}`
            );
          }

          // Calculate relevance score based on multiple factors
          const relevanceScore = this.calculateRelevanceScore(
            query,
            intent,
            knowledgeCard,
            semanticSimilarity
          );

          // Generate highlights
          const highlights = this.generateHighlights(
            query,
            knowledgeCard
          );

          // Extract related concepts from tags and content
          const relatedConcepts = this.extractRelatedConcepts(knowledgeCard);

          return {
            id: card.$id,
            title: card.title,
            content: content?.content || content?.summary || 'No content available',
            type: 'knowledge_card' as const,
            relevanceScore,
            semanticSimilarity,
            metadata: {
              category: card.category,
              difficulty: card.difficulty,
              tags: knowledgeCard.tagNames || [],
              createdAt: card.$createdAt,
              lastModified: card.$updatedAt,
            },
            highlights,
            relatedConcepts,
          };
        })
      );

      // Sort by relevance score
      return searchResults.sort((a, b) => b.relevanceScore - a.relevanceScore);

    } catch (error) {
      console.error('Knowledge Cards search failed:', error);

      // Fallback to basic text search if AI search fails
      return this.performFallbackSearch(query, context);
    }
  }

  /**
   * Rank and enhance search results
   */
  private async rankAndEnhanceResults(
    results: SemanticSearchResult[],
    intent: SearchIntent,
    queryEmbedding: number[] | null,
    context: SemanticSearchInput['context']
  ): Promise<SemanticSearchResult[]> {
    // Apply intent-based ranking and enhancement
    return results.sort((a, b) => {
      // Combine relevance score and semantic similarity
      const scoreA = (a.relevanceScore * 0.6) + (a.semanticSimilarity * 0.4);
      const scoreB = (b.relevanceScore * 0.6) + (b.semanticSimilarity * 0.4);
      return scoreB - scoreA;
    });
  }

  /**
   * Generate smart suggestions based on search context and real data
   */
  private async generateSmartSuggestions(
    query: string,
    intent: SearchIntent,
    results: SemanticSearchResult[],
    context: SemanticSearchInput['context']
  ): Promise<SmartSuggestion[]> {
    const suggestions: SmartSuggestion[] = [];

    try {
      // Get related concepts from search results
      const relatedConcepts = new Set<string>();
      results.forEach(result => {
        result.relatedConcepts?.forEach(concept => relatedConcepts.add(concept));
        result.metadata.tags?.forEach(tag => relatedConcepts.add(tag));
      });

      // Convert related concepts to suggestions
      Array.from(relatedConcepts).slice(0, 5).forEach((concept, index) => {
        suggestions.push({
          id: `concept-${index}`,
          text: concept,
          type: 'concept',
          confidence: 0.7 + (Math.random() * 0.2), // 0.7-0.9 confidence
          reasoning: `Related concept found in search results`,
          metadata: {
            category: results[0]?.metadata.category,
            popularity: 0.6 + (Math.random() * 0.3),
          },
        });
      });

      // Get popular categories from user's cards
      const categoryResponse = await knowledgeCardsService.getUserCards(
        context.userId,
        {},
        { limit: 50, orderBy: 'created', orderDirection: 'desc' }
      );

      const categoryCount = new Map<string, number>();
      categoryResponse.cards.forEach(card => {
        const category = card.card.category;
        categoryCount.set(category, (categoryCount.get(category) || 0) + 1);
      });

      // Add category-based suggestions
      Array.from(categoryCount.entries())
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .forEach(([category, count], index) => {
          if (category.toLowerCase() !== query.toLowerCase()) {
            suggestions.push({
              id: `category-${index}`,
              text: `${category.toLowerCase()} concepts`,
              type: 'topic',
              confidence: Math.min(0.8, count / 10), // Higher confidence for more cards
              reasoning: `Popular category in your knowledge base`,
              metadata: {
                category,
                popularity: count / categoryResponse.cards.length,
              },
            });
          }
        });

      // Add intent-based suggestions
      if (intent.intent === 'learn') {
        suggestions.push({
          id: 'learn-suggestion',
          text: 'beginner friendly topics',
          type: 'query',
          confidence: 0.8,
          reasoning: 'Suggested for learning intent',
        });
      } else if (intent.intent === 'review') {
        suggestions.push({
          id: 'review-suggestion',
          text: 'cards due for review',
          type: 'query',
          confidence: 0.9,
          reasoning: 'Suggested for review intent',
        });
      }

      // Add trending suggestions based on recent activity
      const recentCards = categoryResponse.cards
        .filter(card => {
          const daysSinceCreated = (Date.now() - new Date(card.card.$createdAt).getTime()) / (1000 * 60 * 60 * 24);
          return daysSinceCreated <= 7; // Cards created in last 7 days
        })
        .slice(0, 3);

      recentCards.forEach((card, index) => {
        suggestions.push({
          id: `trending-${index}`,
          text: card.card.title.toLowerCase(),
          type: 'trending',
          confidence: 0.75,
          reasoning: 'Recently added to your knowledge base',
          metadata: {
            category: card.card.category,
            recentSearches: 1,
          },
        });
      });

      // Remove duplicates and sort by confidence
      const uniqueSuggestions = suggestions
        .filter((suggestion, index, self) =>
          index === self.findIndex(s => s.text === suggestion.text)
        )
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, 8); // Limit to 8 suggestions

      return uniqueSuggestions;

    } catch (error) {
      console.error('Failed to generate smart suggestions:', error);

      // Fallback suggestions
      return [
        {
          id: 'fallback-1',
          text: 'explore your knowledge cards',
          type: 'query',
          confidence: 0.5,
          reasoning: 'Fallback suggestion',
        },
      ];
    }
  }

  /**
   * Generate related queries based on user's knowledge cards
   */
  private async generateRelatedQueries(
    query: string,
    intent: SearchIntent,
    context: SemanticSearchInput['context']
  ): Promise<string[]> {
    try {
      const relatedQueries: string[] = [];

      // Get user's cards to find related topics
      const cardsResponse = await knowledgeCardsService.getUserCards(
        context.userId,
        {},
        { limit: 100, orderBy: 'created', orderDirection: 'desc' }
      );

      // Extract common terms and topics
      const topicFrequency = new Map<string, number>();

      cardsResponse.cards.forEach(card => {
        // Add category as potential query
        if (card.card.category) {
          const category = card.card.category.toLowerCase();
          topicFrequency.set(category, (topicFrequency.get(category) || 0) + 1);
        }

        // Add tags as potential queries
        card.tagNames?.forEach(tag => {
          const tagLower = tag.toLowerCase();
          if (tagLower !== query.toLowerCase()) {
            topicFrequency.set(tagLower, (topicFrequency.get(tagLower) || 0) + 1);
          }
        });

        // Extract key terms from titles
        const titleWords = card.card.title.toLowerCase()
          .split(/\s+/)
          .filter(word => word.length > 3 && !['the', 'and', 'for', 'with'].includes(word));

        titleWords.forEach(word => {
          if (word !== query.toLowerCase()) {
            topicFrequency.set(word, (topicFrequency.get(word) || 0) + 1);
          }
        });
      });

      // Convert top topics to related queries
      const sortedTopics = Array.from(topicFrequency.entries())
        .sort(([,a], [,b]) => b - a)
        .slice(0, 8);

      sortedTopics.forEach(([topic, frequency]) => {
        if (frequency >= 2) { // Only include topics that appear multiple times
          // Generate different query formats
          if (intent.intent === 'learn') {
            relatedQueries.push(`learn ${topic}`);
          } else if (intent.intent === 'review') {
            relatedQueries.push(`review ${topic}`);
          } else {
            relatedQueries.push(topic);
          }
        }
      });

      // Add intent-specific related queries
      if (intent.entities.length > 0) {
        intent.entities.forEach(entity => {
          if (entity.type === 'topic' && entity.text.toLowerCase() !== query.toLowerCase()) {
            relatedQueries.push(`${entity.text} fundamentals`);
            relatedQueries.push(`advanced ${entity.text}`);
          }
        });
      }

      // Add difficulty-based related queries
      const difficulties = ['beginner', 'intermediate', 'advanced'];
      const currentDifficulty = intent.suggestedFilters?.difficulty;

      if (currentDifficulty) {
        const otherDifficulties = difficulties.filter(d => d !== currentDifficulty);
        otherDifficulties.forEach(difficulty => {
          relatedQueries.push(`${query} ${difficulty}`);
        });
      }

      // Remove duplicates and limit results
      const uniqueQueries = [...new Set(relatedQueries)]
        .filter(q => q.trim().length > 0)
        .slice(0, 5);

      return uniqueQueries.length > 0 ? uniqueQueries : [
        `${query} basics`,
        `${query} advanced`,
        `${query} examples`,
      ];

    } catch (error) {
      console.error('Failed to generate related queries:', error);

      // Fallback related queries
      return [
        `${query} fundamentals`,
        `${query} examples`,
        `${query} tutorial`,
      ];
    }
  }

  /**
   * Cache management
   */
  private getCacheKey(input: SemanticSearchInput): string {
    return `${this.CACHE_PREFIX}${JSON.stringify(input)}`;
  }

  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  private setCache<T>(key: string, data: T): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  /**
   * Calculate text similarity using simple heuristics
   */
  private calculateTextSimilarity(query: string, text: string): number {
    const queryWords = query.toLowerCase().split(/\s+/);
    const textWords = text.toLowerCase().split(/\s+/);

    let matches = 0;
    for (const queryWord of queryWords) {
      if (textWords.some(textWord =>
        textWord.includes(queryWord) || queryWord.includes(textWord)
      )) {
        matches++;
      }
    }

    return Math.min(matches / queryWords.length, 1.0);
  }

  /**
   * Calculate relevance score based on multiple factors
   */
  private calculateRelevanceScore(
    query: string,
    intent: SearchIntent,
    knowledgeCard: CompleteKnowledgeCard,
    semanticSimilarity: number
  ): number {
    let score = 0;

    // Base semantic similarity (40% weight)
    score += semanticSimilarity * 0.4;

    // Title match (30% weight)
    const titleSimilarity = this.calculateTextSimilarity(query, knowledgeCard.card.title);
    score += titleSimilarity * 0.3;

    // Content match (20% weight)
    const contentSimilarity = this.calculateTextSimilarity(
      query,
      knowledgeCard.content?.content || ''
    );
    score += contentSimilarity * 0.2;

    // Tag match (10% weight)
    const tagMatch = knowledgeCard.tagNames?.some(tag =>
      query.toLowerCase().includes(tag.toLowerCase()) ||
      tag.toLowerCase().includes(query.toLowerCase())
    ) ? 1 : 0;
    score += tagMatch * 0.1;

    // Intent-based boosting
    if (intent.entities.length > 0) {
      const entityMatch = intent.entities.some(entity =>
        knowledgeCard.card.title.toLowerCase().includes(entity.text.toLowerCase()) ||
        knowledgeCard.tagNames?.some(tag =>
          tag.toLowerCase().includes(entity.text.toLowerCase())
        )
      );
      if (entityMatch) {
        score *= 1.2; // 20% boost for entity matches
      }
    }

    // Difficulty preference based on intent
    if (intent.intent === 'learn' && knowledgeCard.card.difficulty === 'beginner') {
      score *= 1.1; // Boost beginner content for learning intent
    }

    return Math.min(score, 1.0);
  }

  /**
   * Generate highlights for search results
   */
  private generateHighlights(
    query: string,
    knowledgeCard: CompleteKnowledgeCard
  ): SemanticSearchResult['highlights'] {
    const highlights: NonNullable<SemanticSearchResult['highlights']> = [];
    const queryWords = query.toLowerCase().split(/\s+/);

    // Check title for highlights
    const title = knowledgeCard.card.title.toLowerCase();
    for (const word of queryWords) {
      const index = title.indexOf(word);
      if (index !== -1) {
        highlights.push({
          field: 'title',
          text: knowledgeCard.card.title.substring(index, index + word.length),
          startIndex: index,
          endIndex: index + word.length,
        });
      }
    }

    // Check content for highlights
    const content = knowledgeCard.content?.content?.toLowerCase() || '';
    for (const word of queryWords) {
      const index = content.indexOf(word);
      if (index !== -1) {
        const start = Math.max(0, index - 20);
        const end = Math.min(content.length, index + word.length + 20);
        highlights.push({
          field: 'content',
          text: knowledgeCard.content?.content?.substring(start, end) || '',
          startIndex: index - start,
          endIndex: index - start + word.length,
        });
        break; // Only one content highlight per query
      }
    }

    return highlights.length > 0 ? highlights : undefined;
  }

  /**
   * Extract related concepts from knowledge card
   */
  private extractRelatedConcepts(knowledgeCard: CompleteKnowledgeCard): string[] {
    const concepts: string[] = [];

    // Add tags as related concepts
    if (knowledgeCard.tagNames) {
      concepts.push(...knowledgeCard.tagNames);
    }

    // Add category as related concept
    if (knowledgeCard.card.category) {
      concepts.push(knowledgeCard.card.category.toLowerCase());
    }

    // Extract concepts from AI-generated source data if available
    const sourceData = knowledgeCard.card.sourceData as any;
    if (sourceData?.relatedTopics && Array.isArray(sourceData.relatedTopics)) {
      concepts.push(...sourceData.relatedTopics);
    }

    // Remove duplicates and return
    return [...new Set(concepts)].slice(0, 5); // Limit to 5 concepts
  }

  /**
   * Fallback search when AI search fails
   */
  private async performFallbackSearch(
    query: string,
    context: SemanticSearchInput['context']
  ): Promise<SemanticSearchResult[]> {
    try {
      // Simple text-based search as fallback
      const cardsResponse = await knowledgeCardsService.searchCards(
        context.userId,
        query,
        {},
        { limit: context.limit || 10 }
      );

      return cardsResponse.cards.map((knowledgeCard) => ({
        id: knowledgeCard.card.$id,
        title: knowledgeCard.card.title,
        content: knowledgeCard.content?.content || knowledgeCard.content?.summary || 'No content available',
        type: 'knowledge_card' as const,
        relevanceScore: 0.5, // Default relevance for fallback
        semanticSimilarity: 0.5, // Default similarity for fallback
        metadata: {
          category: knowledgeCard.card.category,
          difficulty: knowledgeCard.card.difficulty,
          tags: knowledgeCard.tagNames || [],
          createdAt: knowledgeCard.card.$createdAt,
          lastModified: knowledgeCard.card.$updatedAt,
        },
        highlights: undefined,
        relatedConcepts: knowledgeCard.tagNames?.slice(0, 3) || [],
      }));
    } catch (error) {
      console.error('Fallback search failed:', error);
      return []; // Return empty results if everything fails
    }
  }

  /**
   * Error handling (Rule 12)
   */
  private handleError(error: unknown, operation: string): never {
    const message = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error(`AI Search Error (${operation}):`, error);
    throw new AIServiceError(`AI search failed: ${message}`, operation, 'SEARCH_ERROR', true);
  }
}

// Export singleton instance
export const aiSearchService = new AISearchService();
