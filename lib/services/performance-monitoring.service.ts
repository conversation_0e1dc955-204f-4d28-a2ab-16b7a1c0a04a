import { z } from 'zod';
import { AppState, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { analyticsService } from './analytics.service';
import { aiCacheService } from './ai-cache.service';
import { mobilePerformanceService } from './mobile-performance.service';

// Performance metric schema
const PerformanceMetricSchema = z.object({
  id: z.string(),
  name: z.string(),
  value: z.number(),
  unit: z.string(),
  timestamp: z.number(),
  category: z.enum(['memory', 'cpu', 'network', 'storage', 'ui', 'ai']),
  severity: z.enum(['info', 'warning', 'critical']),
  metadata: z.record(z.any()).optional(),
});

export type PerformanceMetric = z.infer<typeof PerformanceMetricSchema>;

// Performance alert schema
const PerformanceAlertSchema = z.object({
  id: z.string(),
  type: z.enum(['memory_high', 'response_slow', 'error_rate_high', 'cache_miss_high', 'battery_low']),
  severity: z.enum(['warning', 'critical']),
  message: z.string(),
  timestamp: z.number(),
  resolved: z.boolean(),
  resolvedAt: z.number().optional(),
  metadata: z.record(z.any()).optional(),
});

export type PerformanceAlert = z.infer<typeof PerformanceAlertSchema>;

// Performance summary schema
const PerformanceSummarySchema = z.object({
  period: z.enum(['hour', 'day', 'week', 'month']),
  startTime: z.number(),
  endTime: z.number(),
  metrics: z.object({
    averageResponseTime: z.number(),
    memoryUsage: z.object({
      average: z.number(),
      peak: z.number(),
      unit: z.string(),
    }),
    cachePerformance: z.object({
      hitRate: z.number(),
      missRate: z.number(),
      totalRequests: z.number(),
    }),
    aiServicePerformance: z.object({
      totalRequests: z.number(),
      averageResponseTime: z.number(),
      successRate: z.number(),
      errorRate: z.number(),
    }),
    userEngagement: z.object({
      sessionDuration: z.number(),
      featuresUsed: z.number(),
      actionsPerSession: z.number(),
    }),
  }),
  alerts: z.array(PerformanceAlertSchema),
  recommendations: z.array(z.string()),
});

export type PerformanceSummary = z.infer<typeof PerformanceSummarySchema>;

/**
 * Performance Monitoring Service
 * Following Rule 10: Performance Optimization
 */
export class PerformanceMonitoringService {
  private metrics: PerformanceMetric[] = [];
  private alerts: PerformanceAlert[] = [];
  private monitoringInterval?: NodeJS.Timeout;
  private isMonitoring = false;

  private readonly STORAGE_KEYS = {
    METRICS: 'performance_metrics',
    ALERTS: 'performance_alerts',
    CONFIG: 'performance_monitoring_config',
  };

  private readonly MAX_STORED_METRICS = 1000;
  private readonly MONITORING_INTERVAL = 30000; // 30 seconds
  private readonly ALERT_THRESHOLDS = {
    MEMORY_WARNING: 0.8, // 80% memory usage
    MEMORY_CRITICAL: 0.9, // 90% memory usage
    RESPONSE_TIME_WARNING: 2000, // 2 seconds
    RESPONSE_TIME_CRITICAL: 5000, // 5 seconds
    ERROR_RATE_WARNING: 0.05, // 5% error rate
    ERROR_RATE_CRITICAL: 0.1, // 10% error rate
    CACHE_MISS_WARNING: 0.7, // 70% cache miss rate
  };

  constructor() {
    this.initialize();
  }

  /**
   * Initialize performance monitoring
   */
  private async initialize(): Promise<void> {
    try {
      await this.loadStoredData();
      this.startMonitoring();
      console.log('Performance monitoring service initialized');
    } catch (error) {
      console.warn('Failed to initialize performance monitoring:', error);
    }
  }

  /**
   * Load stored metrics and alerts
   */
  private async loadStoredData(): Promise<void> {
    try {
      const [metricsData, alertsData] = await Promise.all([
        AsyncStorage.getItem(this.STORAGE_KEYS.METRICS),
        AsyncStorage.getItem(this.STORAGE_KEYS.ALERTS),
      ]);

      if (metricsData) {
        const metrics = JSON.parse(metricsData);
        this.metrics = metrics.map((metric: any) => PerformanceMetricSchema.parse(metric));
      }

      if (alertsData) {
        const alerts = JSON.parse(alertsData);
        this.alerts = alerts.map((alert: any) => PerformanceAlertSchema.parse(alert));
      }
    } catch (error) {
      console.warn('Failed to load performance data:', error);
    }
  }

  /**
   * Save metrics and alerts to storage
   */
  private async saveData(): Promise<void> {
    try {
      // Keep only recent metrics to prevent storage bloat
      const recentMetrics = this.metrics.slice(-this.MAX_STORED_METRICS);
      const recentAlerts = this.alerts.filter(alert => 
        Date.now() - alert.timestamp < 7 * 24 * 60 * 60 * 1000 // Keep alerts for 7 days
      );

      await Promise.all([
        AsyncStorage.setItem(this.STORAGE_KEYS.METRICS, JSON.stringify(recentMetrics)),
        AsyncStorage.setItem(this.STORAGE_KEYS.ALERTS, JSON.stringify(recentAlerts)),
      ]);

      this.metrics = recentMetrics;
      this.alerts = recentAlerts;
    } catch (error) {
      console.warn('Failed to save performance data:', error);
    }
  }

  /**
   * Start performance monitoring
   */
  startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, this.MONITORING_INTERVAL);

    console.log('Performance monitoring started');
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
    this.isMonitoring = false;
    console.log('Performance monitoring stopped');
  }

  /**
   * Collect performance metrics
   */
  private async collectMetrics(): Promise<void> {
    try {
      const timestamp = Date.now();

      // Collect memory metrics
      await this.collectMemoryMetrics(timestamp);

      // Collect cache metrics
      await this.collectCacheMetrics(timestamp);

      // Collect AI service metrics
      await this.collectAIServiceMetrics(timestamp);

      // Collect mobile performance metrics
      await this.collectMobileMetrics(timestamp);

      // Check for alerts
      await this.checkAlerts();

      // Save data periodically
      if (this.metrics.length % 10 === 0) {
        await this.saveData();
      }
    } catch (error) {
      console.warn('Failed to collect performance metrics:', error);
    }
  }

  /**
   * Collect memory metrics
   */
  private async collectMemoryMetrics(timestamp: number): Promise<void> {
    try {
      const mobileMetrics = mobilePerformanceService.getCurrentMetrics();
      if (mobileMetrics) {
        await this.recordMetric({
          name: 'memory_usage',
          value: mobileMetrics.memoryUsage,
          unit: 'MB',
          timestamp,
          category: 'memory',
          severity: mobileMetrics.memoryUsage > 100 ? 'warning' : 'info',
        });
      }
    } catch (error) {
      console.warn('Failed to collect memory metrics:', error);
    }
  }

  /**
   * Collect cache metrics
   */
  private async collectCacheMetrics(timestamp: number): Promise<void> {
    try {
      const cacheStats = aiCacheService.getCacheStats();
      
      await this.recordMetric({
        name: 'cache_memory_utilization',
        value: cacheStats.memoryUtilization,
        unit: '%',
        timestamp,
        category: 'memory',
        severity: cacheStats.memoryUtilization > 80 ? 'warning' : 'info',
      });

      await this.recordMetric({
        name: 'cache_persistent_utilization',
        value: cacheStats.persistentUtilization,
        unit: '%',
        timestamp,
        category: 'storage',
        severity: cacheStats.persistentUtilization > 80 ? 'warning' : 'info',
      });
    } catch (error) {
      console.warn('Failed to collect cache metrics:', error);
    }
  }

  /**
   * Collect AI service metrics
   */
  private async collectAIServiceMetrics(timestamp: number): Promise<void> {
    try {
      const aiAnalytics = await analyticsService.getAIUsageAnalytics();
      
      await this.recordMetric({
        name: 'ai_average_response_time',
        value: aiAnalytics.averageResponseTime,
        unit: 'ms',
        timestamp,
        category: 'ai',
        severity: aiAnalytics.averageResponseTime > this.ALERT_THRESHOLDS.RESPONSE_TIME_WARNING ? 'warning' : 'info',
      });

      await this.recordMetric({
        name: 'ai_success_rate',
        value: aiAnalytics.successRate,
        unit: '%',
        timestamp,
        category: 'ai',
        severity: aiAnalytics.successRate < 95 ? 'warning' : 'info',
      });

      await this.recordMetric({
        name: 'ai_cache_hit_rate',
        value: aiAnalytics.cacheHitRate,
        unit: '%',
        timestamp,
        category: 'ai',
        severity: aiAnalytics.cacheHitRate < 30 ? 'warning' : 'info',
      });
    } catch (error) {
      console.warn('Failed to collect AI service metrics:', error);
    }
  }

  /**
   * Collect mobile-specific metrics
   */
  private async collectMobileMetrics(timestamp: number): Promise<void> {
    try {
      const appState = AppState.currentState;
      
      await this.recordMetric({
        name: 'app_state',
        value: appState === 'active' ? 1 : 0,
        unit: 'boolean',
        timestamp,
        category: 'ui',
        severity: 'info',
        metadata: { appState },
      });
    } catch (error) {
      console.warn('Failed to collect mobile metrics:', error);
    }
  }

  /**
   * Record a performance metric
   */
  async recordMetric(metricData: Omit<PerformanceMetric, 'id'>): Promise<void> {
    try {
      const metric: PerformanceMetric = {
        id: `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...metricData,
      };

      this.metrics.push(metric);

      // Track in analytics
      await analyticsService.trackPerformance(
        metric.name,
        metric.value,
        metric.unit,
        { category: metric.category, severity: metric.severity }
      );
    } catch (error) {
      console.warn('Failed to record performance metric:', error);
    }
  }

  /**
   * Check for performance alerts
   */
  private async checkAlerts(): Promise<void> {
    try {
      const recentMetrics = this.metrics.filter(m => 
        Date.now() - m.timestamp < 5 * 60 * 1000 // Last 5 minutes
      );

      // Check memory usage
      const memoryMetrics = recentMetrics.filter(m => m.name === 'memory_usage');
      if (memoryMetrics.length > 0) {
        const avgMemory = memoryMetrics.reduce((sum, m) => sum + m.value, 0) / memoryMetrics.length;
        const memoryUtilization = avgMemory / 100; // Assuming 100MB is our baseline

        if (memoryUtilization > this.ALERT_THRESHOLDS.MEMORY_CRITICAL) {
          await this.createAlert('memory_high', 'critical', 
            `Critical memory usage: ${(memoryUtilization * 100).toFixed(1)}%`);
        } else if (memoryUtilization > this.ALERT_THRESHOLDS.MEMORY_WARNING) {
          await this.createAlert('memory_high', 'warning', 
            `High memory usage: ${(memoryUtilization * 100).toFixed(1)}%`);
        }
      }

      // Check AI response times
      const responseTimeMetrics = recentMetrics.filter(m => m.name === 'ai_average_response_time');
      if (responseTimeMetrics.length > 0) {
        const avgResponseTime = responseTimeMetrics.reduce((sum, m) => sum + m.value, 0) / responseTimeMetrics.length;

        if (avgResponseTime > this.ALERT_THRESHOLDS.RESPONSE_TIME_CRITICAL) {
          await this.createAlert('response_slow', 'critical', 
            `Critical AI response time: ${avgResponseTime.toFixed(0)}ms`);
        } else if (avgResponseTime > this.ALERT_THRESHOLDS.RESPONSE_TIME_WARNING) {
          await this.createAlert('response_slow', 'warning', 
            `Slow AI response time: ${avgResponseTime.toFixed(0)}ms`);
        }
      }

      // Check cache miss rate
      const cacheHitMetrics = recentMetrics.filter(m => m.name === 'ai_cache_hit_rate');
      if (cacheHitMetrics.length > 0) {
        const avgCacheHitRate = cacheHitMetrics.reduce((sum, m) => sum + m.value, 0) / cacheHitMetrics.length;
        const cacheMissRate = 100 - avgCacheHitRate;

        if (cacheMissRate > this.ALERT_THRESHOLDS.CACHE_MISS_WARNING * 100) {
          await this.createAlert('cache_miss_high', 'warning', 
            `High cache miss rate: ${cacheMissRate.toFixed(1)}%`);
        }
      }
    } catch (error) {
      console.warn('Failed to check performance alerts:', error);
    }
  }

  /**
   * Create a performance alert
   */
  private async createAlert(
    type: PerformanceAlert['type'],
    severity: PerformanceAlert['severity'],
    message: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      // Check if similar alert already exists and is not resolved
      const existingAlert = this.alerts.find(alert => 
        alert.type === type && 
        !alert.resolved && 
        Date.now() - alert.timestamp < 10 * 60 * 1000 // Within last 10 minutes
      );

      if (existingAlert) {
        return; // Don't create duplicate alerts
      }

      const alert: PerformanceAlert = {
        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type,
        severity,
        message,
        timestamp: Date.now(),
        resolved: false,
        metadata,
      };

      this.alerts.push(alert);

      // Track alert in analytics
      await analyticsService.trackEvent('performance_alert', {
        alertType: type,
        severity,
        message,
      });

      console.warn(`Performance Alert [${severity.toUpperCase()}]: ${message}`);
    } catch (error) {
      console.warn('Failed to create performance alert:', error);
    }
  }

  /**
   * Resolve an alert
   */
  async resolveAlert(alertId: string): Promise<void> {
    try {
      const alert = this.alerts.find(a => a.id === alertId);
      if (alert && !alert.resolved) {
        alert.resolved = true;
        alert.resolvedAt = Date.now();
        await this.saveData();
      }
    } catch (error) {
      console.warn('Failed to resolve alert:', error);
    }
  }

  /**
   * Get performance summary for a time period
   */
  async getPerformanceSummary(period: 'hour' | 'day' | 'week' | 'month'): Promise<PerformanceSummary> {
    try {
      const now = Date.now();
      const periodMs = {
        hour: 60 * 60 * 1000,
        day: 24 * 60 * 60 * 1000,
        week: 7 * 24 * 60 * 60 * 1000,
        month: 30 * 24 * 60 * 60 * 1000,
      };

      const startTime = now - periodMs[period];
      const periodMetrics = this.metrics.filter(m => m.timestamp >= startTime);
      const periodAlerts = this.alerts.filter(a => a.timestamp >= startTime);

      // Calculate averages and summaries
      const responseTimeMetrics = periodMetrics.filter(m => m.name === 'ai_average_response_time');
      const memoryMetrics = periodMetrics.filter(m => m.name === 'memory_usage');
      const cacheHitMetrics = periodMetrics.filter(m => m.name === 'ai_cache_hit_rate');

      const summary: PerformanceSummary = {
        period,
        startTime,
        endTime: now,
        metrics: {
          averageResponseTime: responseTimeMetrics.length > 0 
            ? responseTimeMetrics.reduce((sum, m) => sum + m.value, 0) / responseTimeMetrics.length 
            : 0,
          memoryUsage: {
            average: memoryMetrics.length > 0 
              ? memoryMetrics.reduce((sum, m) => sum + m.value, 0) / memoryMetrics.length 
              : 0,
            peak: memoryMetrics.length > 0 
              ? Math.max(...memoryMetrics.map(m => m.value)) 
              : 0,
            unit: 'MB',
          },
          cachePerformance: {
            hitRate: cacheHitMetrics.length > 0 
              ? cacheHitMetrics.reduce((sum, m) => sum + m.value, 0) / cacheHitMetrics.length 
              : 0,
            missRate: cacheHitMetrics.length > 0 
              ? 100 - (cacheHitMetrics.reduce((sum, m) => sum + m.value, 0) / cacheHitMetrics.length)
              : 0,
            totalRequests: cacheHitMetrics.length,
          },
          aiServicePerformance: await this.getAIServiceSummary(startTime),
          userEngagement: await this.getUserEngagementSummary(startTime),
        },
        alerts: periodAlerts,
        recommendations: this.generateRecommendations(periodMetrics, periodAlerts),
      };

      return summary;
    } catch (error) {
      console.warn('Failed to get performance summary:', error);
      throw error;
    }
  }

  /**
   * Get AI service performance summary
   */
  private async getAIServiceSummary(startTime: number) {
    try {
      const aiAnalytics = await analyticsService.getAIUsageAnalytics();
      return {
        totalRequests: aiAnalytics.totalRequests,
        averageResponseTime: aiAnalytics.averageResponseTime,
        successRate: aiAnalytics.successRate,
        errorRate: aiAnalytics.errorRate,
      };
    } catch {
      return {
        totalRequests: 0,
        averageResponseTime: 0,
        successRate: 0,
        errorRate: 0,
      };
    }
  }

  /**
   * Get user engagement summary
   */
  private async getUserEngagementSummary(startTime: number) {
    try {
      const sessionAnalytics = analyticsService.getSessionAnalytics();
      return {
        sessionDuration: sessionAnalytics.duration,
        featuresUsed: sessionAnalytics.featuresUsed.length,
        actionsPerSession: sessionAnalytics.eventCount,
      };
    } catch {
      return {
        sessionDuration: 0,
        featuresUsed: 0,
        actionsPerSession: 0,
      };
    }
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(
    metrics: PerformanceMetric[],
    alerts: PerformanceAlert[]
  ): string[] {
    const recommendations: string[] = [];

    // Memory recommendations
    const memoryMetrics = metrics.filter(m => m.name === 'memory_usage');
    if (memoryMetrics.length > 0) {
      const avgMemory = memoryMetrics.reduce((sum, m) => sum + m.value, 0) / memoryMetrics.length;
      if (avgMemory > 80) {
        recommendations.push('Consider clearing cache or reducing memory usage');
      }
    }

    // Cache recommendations
    const cacheHitMetrics = metrics.filter(m => m.name === 'ai_cache_hit_rate');
    if (cacheHitMetrics.length > 0) {
      const avgCacheHitRate = cacheHitMetrics.reduce((sum, m) => sum + m.value, 0) / cacheHitMetrics.length;
      if (avgCacheHitRate < 30) {
        recommendations.push('Cache hit rate is low - consider adjusting cache strategy');
      }
    }

    // Response time recommendations
    const responseTimeMetrics = metrics.filter(m => m.name === 'ai_average_response_time');
    if (responseTimeMetrics.length > 0) {
      const avgResponseTime = responseTimeMetrics.reduce((sum, m) => sum + m.value, 0) / responseTimeMetrics.length;
      if (avgResponseTime > 2000) {
        recommendations.push('AI response times are slow - check network connection or reduce request complexity');
      }
    }

    // Alert-based recommendations
    const criticalAlerts = alerts.filter(a => a.severity === 'critical' && !a.resolved);
    if (criticalAlerts.length > 0) {
      recommendations.push('Address critical performance alerts immediately');
    }

    return recommendations;
  }

  /**
   * Get current alerts
   */
  getCurrentAlerts(): PerformanceAlert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Get recent metrics
   */
  getRecentMetrics(minutes: number = 60): PerformanceMetric[] {
    const cutoff = Date.now() - (minutes * 60 * 1000);
    return this.metrics.filter(m => m.timestamp >= cutoff);
  }

  /**
   * Cleanup on service destruction
   */
  async destroy(): Promise<void> {
    this.stopMonitoring();
    await this.saveData();
  }

  /**
   * Optimize performance
   */
  async optimizePerformance(): Promise<void> {
    try {
      // Clear caches
      await this.clearCaches();

      // Optimize memory usage
      await this.optimizeMemory();

      // Update performance metrics
      await this.collectMetrics();

      console.log('Performance optimization completed');
    } catch (error) {
      console.warn('Failed to optimize performance:', error);
      throw error;
    }
  }

  /**
   * Clear caches
   */
  private async clearCaches(): Promise<void> {
    // Clear various caches
    // This would integrate with actual cache systems
  }

  /**
   * Optimize memory usage
   */
  private async optimizeMemory(): Promise<void> {
    // Trigger garbage collection if possible
    // Optimize memory usage patterns
  }
}

// Export singleton instance
export const performanceMonitoringService = new PerformanceMonitoringService();
