/**
 * Camera Service for LearniScan
 * 
 * This service handles camera operations and integrates with AppWrite storage
 * for image uploads and scan history management.
 * 
 * Features:
 * - Photo capture with quality optimization
 * - Image upload to AppWrite storage
 * - Scan history creation in database
 * - Error handling and retry logic
 * - Progress tracking for uploads
 */

import { CameraView } from 'expo-camera';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import { ID } from 'react-native-appwrite';
import { storage, APPWRITE_CONFIG } from '../config/appwrite';
import { databaseService } from './database.service';
import type { ScanHistory, AppwriteDocument } from '../../types/appwrite';

export interface CapturePhotoOptions {
  quality?: number;
  skipProcessing?: boolean;
  base64?: boolean;
}

export interface ProcessedPhoto {
  uri: string;
  width: number;
  height: number;
  fileSize: number;
  base64?: string;
}

export interface UploadProgress {
  progress: number;
  total: number;
  loaded: number;
}

export interface ScanResult {
  photoId: string;
  photoUrl: string;
  scanHistoryId: string;
  processedPhoto: ProcessedPhoto;
  uploadProgress?: UploadProgress;
}

export interface CreateScanHistoryInput {
  userId: string;
  originalImageId: string;
  extractedText?: string;
  scanType: 'document' | 'handwriting' | 'book' | 'whiteboard';
  language?: string;
  confidence?: number;
  metadata: {
    imageSize: number;
    dimensions: {
      width: number;
      height: number;
    };
    processingTime: number;
  };
}

export class CameraService {
  private bucketId: string;

  constructor() {
    this.bucketId = APPWRITE_CONFIG.buckets.scanImages;
  }

  /**
   * Capture photo with the camera
   */
  async capturePhoto(
    cameraRef: React.RefObject<CameraView>,
    options: CapturePhotoOptions = {}
  ): Promise<ProcessedPhoto> {
    if (!cameraRef.current) {
      throw new Error('Camera reference is not available');
    }

    const startTime = Date.now();

    try {
      // Default capture options optimized for document scanning
      const captureOptions = {
        quality: options.quality || 0.8,
        skipProcessing: options.skipProcessing || false,
        base64: options.base64 || false,
      };

      // Capture the photo
      const photo = await cameraRef.current.takePictureAsync(captureOptions);
      
      if (!photo || !photo.uri) {
        throw new Error('Failed to capture photo');
      }

      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(photo.uri);
      if (!fileInfo.exists) {
        throw new Error('Captured photo file does not exist');
      }

      // Process and optimize the image
      const processedPhoto = await this.processImage(photo.uri, {
        compress: 0.8,
        format: ImageManipulator.SaveFormat.JPEG,
      });

      const processingTime = Date.now() - startTime;

      return {
        uri: processedPhoto.uri,
        width: processedPhoto.width,
        height: processedPhoto.height,
        fileSize: fileInfo.size || 0,
        base64: photo.base64,
      };

    } catch (error) {
      console.error('Error capturing photo:', error);
      throw new Error(`Failed to capture photo: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process and optimize image for document scanning
   */
  async processImage(
    imageUri: string,
    options: {
      compress?: number;
      format?: ImageManipulator.SaveFormat;
      resize?: { width?: number; height?: number };
    } = {}
  ): Promise<ImageManipulator.ImageResult> {
    try {
      const actions: ImageManipulator.Action[] = [];

      // Add resize action if specified
      if (options.resize) {
        actions.push({
          resize: options.resize,
        });
      }

      // Process the image
      const result = await ImageManipulator.manipulateAsync(
        imageUri,
        actions,
        {
          compress: options.compress || 0.8,
          format: options.format || ImageManipulator.SaveFormat.JPEG,
          base64: false,
        }
      );

      return result;
    } catch (error) {
      console.error('Error processing image:', error);
      throw new Error(`Failed to process image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload photo to AppWrite storage
   */
  async uploadPhoto(
    photoUri: string,
    fileName?: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<{ fileId: string; fileUrl: string }> {
    try {
      // Generate unique filename if not provided
      const finalFileName = fileName || `scan_${Date.now()}_${ID.unique()}.jpg`;

      // Read file as blob for upload
      const fileInfo = await FileSystem.getInfoAsync(photoUri);
      if (!fileInfo.exists) {
        throw new Error('Photo file does not exist');
      }

      // Create file object for upload
      const file = {
        name: finalFileName,
        type: 'image/jpeg',
        size: fileInfo.size || 0,
        uri: photoUri,
      };

      // Upload to AppWrite storage
      const uploadResult = await storage.createFile(
        this.bucketId,
        ID.unique(),
        file as any,
        undefined, // permissions (use bucket defaults)
        onProgress ? (progress) => {
          onProgress({
            progress: (progress.loaded / progress.total) * 100,
            total: progress.total,
            loaded: progress.loaded,
          });
        } : undefined
      );

      // Get file URL for viewing
      const fileUrl = storage.getFileView(this.bucketId, uploadResult.$id);

      return {
        fileId: uploadResult.$id,
        fileUrl: fileUrl.toString(),
      };

    } catch (error) {
      console.error('Error uploading photo:', error);
      throw new Error(`Failed to upload photo: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create scan history record in database
   */
  async createScanHistory(input: CreateScanHistoryInput): Promise<ScanHistory> {
    try {
      const scanHistoryData = {
        userId: input.userId,
        originalImageId: input.originalImageId,
        extractedText: input.extractedText || '',
        processedContent: '', // Will be filled by OCR service
        knowledgeCardId: '', // Will be filled when card is created
        scanType: input.scanType,
        confidence: input.confidence || 0,
        language: input.language || 'en',
        metadata: JSON.stringify(input.metadata),
      };

      // Use the existing database service to create scan history
      // Note: We'll need to add this method to the existing database service
      const scanHistory = await this.createScanHistoryRecord(scanHistoryData);

      return scanHistory;

    } catch (error) {
      console.error('Error creating scan history:', error);
      throw new Error(`Failed to create scan history: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Complete scan workflow: capture, upload, and save to database
   */
  async completeScanWorkflow(
    cameraRef: React.RefObject<CameraView>,
    userId: string,
    scanType: 'document' | 'handwriting' | 'book' | 'whiteboard' = 'document',
    options: {
      captureOptions?: CapturePhotoOptions;
      onProgress?: (stage: string, progress?: UploadProgress) => void;
    } = {}
  ): Promise<ScanResult> {
    const { captureOptions, onProgress } = options;

    try {
      // Stage 1: Capture photo
      onProgress?.('capturing');
      const processedPhoto = await this.capturePhoto(cameraRef, captureOptions);

      // Stage 2: Upload photo
      onProgress?.('uploading');
      const { fileId, fileUrl } = await this.uploadPhoto(
        processedPhoto.uri,
        undefined,
        (progress) => onProgress?.('uploading', progress)
      );

      // Stage 3: Create scan history
      onProgress?.('saving');
      const scanHistory = await this.createScanHistory({
        userId,
        originalImageId: fileId,
        scanType,
        language: 'en',
        confidence: 0.95, // Default confidence, will be updated by OCR
        metadata: {
          imageSize: processedPhoto.fileSize,
          dimensions: {
            width: processedPhoto.width,
            height: processedPhoto.height,
          },
          processingTime: Date.now(),
        },
      });

      onProgress?.('complete');

      return {
        photoId: fileId,
        photoUrl: fileUrl,
        scanHistoryId: scanHistory.$id,
        processedPhoto,
      };

    } catch (error) {
      console.error('Error in complete scan workflow:', error);
      throw error;
    }
  }

  /**
   * Delete photo from storage
   */
  async deletePhoto(fileId: string): Promise<boolean> {
    try {
      await storage.deleteFile(this.bucketId, fileId);
      return true;
    } catch (error) {
      console.error('Error deleting photo:', error);
      return false;
    }
  }

  /**
   * Get photo URL from storage
   */
  getPhotoUrl(fileId: string): string {
    return storage.getFileView(this.bucketId, fileId).toString();
  }

  /**
   * Get photo download URL
   */
  getPhotoDownloadUrl(fileId: string): string {
    return storage.getFileDownload(this.bucketId, fileId).toString();
  }

  // Private helper method to create scan history record
  private async createScanHistoryRecord(data: any): Promise<ScanHistory> {
    try {
      const scanHistory = await databaseService.createScanHistory(data);
      return scanHistory;
    } catch (error) {
      console.error('Error creating scan history record:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const cameraService = new CameraService();