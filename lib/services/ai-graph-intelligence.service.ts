/**
 * AI Graph Intelligence Service
 * 
 * Enhances knowledge graphs with AI-powered semantic analysis:
 * - Intelligent relationship detection between knowledge cards
 * - Semantic clustering and categorization
 * - Concept hierarchy generation
 * - Graph optimization recommendations
 * 
 * Following LearniScan development workflow rules:
 * - Rule 11: Zod validation for all inputs/outputs
 * - Rule 12: Comprehensive error handling with early returns
 * - Rule 5: TypeScript quality and type safety
 * - Rule 10: Performance optimization with caching and streaming
 */

import { z } from 'zod';
import { streamObject } from 'ai';
import { aiModels } from '@/lib/config/ai.config';
import { AIServiceError } from '@/lib/services/ai.service';
import { CompleteKnowledgeCard } from '@/types/appwrite-v2';
import { KnowledgeNode, KnowledgeEdge, KnowledgeGraph } from '@/app/(knowledge)/types/graph';

// Input validation schemas (Rule 11)
const GraphAnalysisInputSchema = z.object({
  cards: z.array(z.object({
    card: z.object({
      $id: z.string(),
      title: z.string(),
      category: z.string(),
      difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
    }),
    content: z.object({
      content: z.string(),
      summary: z.string().optional(),
    }).optional(),
    tagNames: z.array(z.string()).optional(),
  })),
  options: z.object({
    enableSemanticAnalysis: z.boolean().default(true),
    enableClustering: z.boolean().default(true),
    enableHierarchy: z.boolean().default(true),
    maxRelationships: z.number().min(1).max(50).default(20),
    minSimilarityThreshold: z.number().min(0).max(1).default(0.3),
  }).optional(),
});

// AI analysis output schemas (Rule 11)
const SemanticRelationshipSchema = z.object({
  sourceId: z.string(),
  targetId: z.string(),
  type: z.enum(['prerequisite', 'related', 'contains', 'similar', 'builds_on', 'applies_to']),
  strength: z.number().min(0).max(1),
  reasoning: z.string(),
  keywords: z.array(z.string()).max(5),
});

const ConceptClusterSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  cardIds: z.array(z.string()),
  centerConcept: z.string(),
  importance: z.number().min(0).max(1),
  color: z.string(),
});

const GraphIntelligenceSchema = z.object({
  relationships: z.array(SemanticRelationshipSchema),
  clusters: z.array(ConceptClusterSchema),
  hierarchy: z.object({
    levels: z.record(z.string(), z.number()),
    prerequisites: z.record(z.string(), z.array(z.string())),
    learningPath: z.array(z.string()),
  }),
  insights: z.object({
    totalConcepts: z.number(),
    connectionDensity: z.number(),
    learningComplexity: z.enum(['beginner', 'intermediate', 'advanced']),
    recommendedStudyOrder: z.array(z.string()),
    keyTopics: z.array(z.string()),
  }),
});

export type GraphAnalysisInput = z.infer<typeof GraphAnalysisInputSchema>;
export type SemanticRelationship = z.infer<typeof SemanticRelationshipSchema>;
export type ConceptCluster = z.infer<typeof ConceptClusterSchema>;
export type GraphIntelligence = z.infer<typeof GraphIntelligenceSchema>;

// Progress callback type
export type GraphAnalysisProgress = {
  stage: 'analyzing' | 'clustering' | 'relationships' | 'optimizing';
  progress: number;
  message: string;
  partialResults?: Partial<GraphIntelligence>;
};

export class AIGraphIntelligenceService {
  private cache = new Map<string, { data: GraphIntelligence; timestamp: number }>();
  private readonly CACHE_TTL = 600000; // 10 minutes

  /**
   * Analyze knowledge cards and generate AI-enhanced graph intelligence
   */
  async analyzeKnowledgeGraph(
    input: GraphAnalysisInput,
    onProgress?: (progress: GraphAnalysisProgress) => void
  ): Promise<GraphIntelligence> {
    // Input validation (Rule 11)
    const validatedInput = GraphAnalysisInputSchema.parse(input);
    
    // Check cache first (Rule 10: Performance optimization)
    const cacheKey = this.getCacheKey(validatedInput);
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      onProgress?.({
        stage: 'optimizing',
        progress: 100,
        message: 'Retrieved from cache',
      });
      return cached;
    }

    const { cards, options } = validatedInput;

    try {
      // Stage 1: Analyzing content
      onProgress?.({
        stage: 'analyzing',
        progress: 10,
        message: 'Analyzing knowledge card content...',
      });

      const prompt = this.buildAnalysisPrompt(cards, options);

      // Stage 2: AI processing with streaming
      onProgress?.({
        stage: 'clustering',
        progress: 30,
        message: 'Identifying concept clusters...',
      });

      // Use generateText with JSON format for compatibility
      const { generateText } = await import('ai');

      const enhancedPrompt = `${prompt}

Please respond with a valid JSON object that matches this exact structure:
{
  "relationships": [
    {
      "sourceId": "string",
      "targetId": "string",
      "type": "prerequisite|related|contains|similar|builds_on|applies_to",
      "strength": 0.8,
      "reasoning": "explanation",
      "keywords": ["keyword1", "keyword2"]
    }
  ],
  "clusters": [
    {
      "id": "cluster-1",
      "name": "Cluster Name",
      "description": "Description",
      "cardIds": ["card-1", "card-2"],
      "centerConcept": "Main concept",
      "importance": 0.9,
      "color": "#A855F7"
    }
  ],
  "hierarchy": {
    "levels": {"card-1": 1, "card-2": 2},
    "prerequisites": {"card-2": ["card-1"]},
    "learningPath": ["card-1", "card-2"]
  },
  "insights": {
    "totalConcepts": 5,
    "connectionDensity": 0.6,
    "learningComplexity": "intermediate",
    "recommendedStudyOrder": ["card-1", "card-2"],
    "keyTopics": ["topic1", "topic2"]
  }
}

Respond ONLY with valid JSON, no additional text.`;

      const { text: response } = await generateText({
        model: aiModels.chat,
        messages: [{
          role: 'system',
          content: 'You are an expert knowledge graph analyst. Respond only with valid JSON matching the requested structure.'
        }, {
          role: 'user',
          content: enhancedPrompt
        }],
        temperature: 0.3,
      });

      // Update progress during processing
      onProgress?.({
        stage: 'relationships',
        progress: 60,
        message: 'Detecting semantic relationships...',
      });

      // Parse and validate the JSON response
      let parsedResult;
      try {
        // Clean the response by removing markdown code blocks if present
        let cleanedResponse = response.trim();
        if (cleanedResponse.startsWith('```json')) {
          cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanedResponse.startsWith('```')) {
          cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        parsedResult = JSON.parse(cleanedResponse);
      } catch (parseError) {
        console.error('Failed to parse AI response:', response);
        throw new Error('AI returned invalid JSON response');
      }

      onProgress?.({
        stage: 'relationships',
        progress: 80,
        message: 'Processing learning hierarchy...',
      });

      // Validate the parsed result against our schema
      const finalResult = GraphIntelligenceSchema.parse(parsedResult);

      // Stage 4: Post-processing and optimization
      onProgress?.({
        stage: 'optimizing',
        progress: 95,
        message: 'Optimizing graph structure...',
      });

      const optimizedResult = this.postProcessIntelligence(finalResult, cards);
      
      // Cache the results (Rule 10)
      this.setCache(cacheKey, optimizedResult);

      onProgress?.({
        stage: 'optimizing',
        progress: 100,
        message: `Analysis complete: ${optimizedResult.relationships.length} relationships, ${optimizedResult.clusters.length} clusters`,
      });

      return optimizedResult;

    } catch (error) {
      console.error('AI Graph Intelligence Error:', error);
      this.handleError(error, 'analyzeKnowledgeGraph');
    }
  }

  /**
   * Apply AI intelligence to enhance existing knowledge graph
   */
  enhanceKnowledgeGraph(
    graph: KnowledgeGraph,
    intelligence: GraphIntelligence
  ): KnowledgeGraph {
    const enhancedNodes = [...graph.nodes];
    const enhancedEdges = [...graph.edges];

    // Apply clustering information to nodes
    intelligence.clusters.forEach(cluster => {
      cluster.cardIds.forEach(cardId => {
        const node = enhancedNodes.find(n => n.id === cardId);
        if (node) {
          node.metadata = {
            ...node.metadata,
            cluster: cluster.name,
            importance: Math.max(node.metadata.importance, cluster.importance),
            color: cluster.color,
          };
        }
      });
    });

    // Apply hierarchy levels to nodes
    Object.entries(intelligence.hierarchy.levels).forEach(([cardId, level]) => {
      const node = enhancedNodes.find(n => n.id === cardId);
      if (node) {
        node.metadata.level = level;
      }
    });

    // Add AI-detected relationships as edges
    intelligence.relationships.forEach(relationship => {
      const existingEdge = enhancedEdges.find(
        e => (e.source === relationship.sourceId && e.target === relationship.targetId) ||
             (e.source === relationship.targetId && e.target === relationship.sourceId)
      );

      if (!existingEdge) {
        const edgeId = `ai-${relationship.sourceId}-${relationship.targetId}`;
        enhancedEdges.push({
          id: edgeId,
          source: relationship.sourceId,
          target: relationship.targetId,
          type: relationship.type,
          strength: relationship.strength,
          metadata: {
            color: this.getRelationshipColor(relationship.type),
            width: Math.max(1, relationship.strength * 4),
            style: relationship.type === 'prerequisite' ? 'dashed' : 'solid',
            animated: relationship.strength > 0.8,
          },
        });

        // Update node connections
        const sourceNode = enhancedNodes.find(n => n.id === relationship.sourceId);
        const targetNode = enhancedNodes.find(n => n.id === relationship.targetId);
        
        if (sourceNode && !sourceNode.connections.includes(relationship.targetId)) {
          sourceNode.connections.push(relationship.targetId);
        }
        if (targetNode && !targetNode.connections.includes(relationship.sourceId)) {
          targetNode.connections.push(relationship.sourceId);
        }
      }
    });

    return {
      ...graph,
      nodes: enhancedNodes,
      edges: enhancedEdges,
      metadata: {
        ...graph.metadata,
        description: `AI-enhanced graph with ${intelligence.relationships.length} semantic relationships`,
        updated: new Date(),
      },
    };
  }

  /**
   * Build AI analysis prompt
   */
  private buildAnalysisPrompt(
    cards: GraphAnalysisInput['cards'],
    options?: GraphAnalysisInput['options']
  ): string {
    let prompt = `Analyze the following ${cards.length} knowledge cards and generate intelligent graph insights:\n\n`;

    cards.forEach((card, index) => {
      prompt += `Card ${index + 1}:\n`;
      prompt += `- ID: ${card.card.$id}\n`;
      prompt += `- Title: ${card.card.title}\n`;
      prompt += `- Category: ${card.card.category}\n`;
      prompt += `- Difficulty: ${card.card.difficulty}\n`;
      if (card.content?.summary) {
        prompt += `- Summary: ${card.content.summary}\n`;
      }
      if (card.tagNames?.length) {
        prompt += `- Tags: ${card.tagNames.join(', ')}\n`;
      }
      prompt += '\n';
    });

    prompt += 'Analysis Requirements:\n';
    prompt += '- Identify semantic relationships between concepts\n';
    prompt += '- Create meaningful concept clusters\n';
    prompt += '- Establish learning hierarchy and prerequisites\n';
    prompt += '- Recommend optimal study order\n';
    prompt += '- Focus on educational value and learning pathways\n';

    if (options) {
      prompt += '\nOptions:\n';
      if (options.maxRelationships) {
        prompt += `- Maximum relationships: ${options.maxRelationships}\n`;
      }
      if (options.minSimilarityThreshold) {
        prompt += `- Minimum similarity threshold: ${options.minSimilarityThreshold}\n`;
      }
    }

    return prompt;
  }

  /**
   * Post-process AI intelligence results
   */
  private postProcessIntelligence(
    intelligence: GraphIntelligence,
    cards: GraphAnalysisInput['cards']
  ): GraphIntelligence {
    // Validate and filter relationships
    const validRelationships = intelligence.relationships.filter(rel => {
      const sourceExists = cards.some(c => c.card.$id === rel.sourceId);
      const targetExists = cards.some(c => c.card.$id === rel.targetId);
      return sourceExists && targetExists && rel.strength >= 0.2;
    });

    // Ensure clusters contain valid card IDs
    const validClusters = intelligence.clusters.map(cluster => ({
      ...cluster,
      cardIds: cluster.cardIds.filter(id => cards.some(c => c.card.$id === id)),
    })).filter(cluster => cluster.cardIds.length > 0);

    return {
      ...intelligence,
      relationships: validRelationships.slice(0, 30), // Limit for performance
      clusters: validClusters,
    };
  }

  /**
   * Get relationship color based on type
   */
  private getRelationshipColor(type: SemanticRelationship['type']): string {
    const colors = {
      prerequisite: '#F59E0B', // Amber
      related: '#A855F7',       // Purple
      contains: '#10B981',      // Emerald
      similar: '#3B82F6',       // Blue
      builds_on: '#EC4899',     // Rose
      applies_to: '#06B6D4',    // Cyan
    };
    return colors[type] || '#A855F7';
  }

  /**
   * Cache management
   */
  private getCacheKey(input: GraphAnalysisInput): string {
    const cardIds = input.cards.map(c => c.card.$id).sort();
    return `graph_intelligence_${JSON.stringify({ cardIds, options: input.options })}`;
  }

  private getFromCache(key: string): GraphIntelligence | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    const now = Date.now();
    if (now - cached.timestamp > this.CACHE_TTL) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  private setCache(key: string, data: GraphIntelligence): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  /**
   * Error handling (Rule 12)
   */
  private handleError(error: any, operation: string): never {
    console.error(`AI Graph Intelligence Error (${operation}):`, error);

    // Early return pattern for different error types (Rule 12)
    if (error instanceof z.ZodError) {
      throw new AIServiceError(
        'Invalid input for graph analysis',
        operation,
        'VALIDATION_ERROR',
        false
      );
    }

    if (error.message?.includes('rate limit')) {
      throw new AIServiceError(
        'AI service is temporarily busy. Please try again in a moment.',
        operation,
        'RATE_LIMIT',
        true
      );
    }

    // Generic error fallback
    throw new AIServiceError(
      `Graph intelligence analysis failed: ${error.message || 'Unknown error'}`,
      operation,
      'UNKNOWN_ERROR',
      false
    );
  }

  /**
   * Clear cache (for memory management)
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Export singleton instance
export const aiGraphIntelligence = new AIGraphIntelligenceService();
