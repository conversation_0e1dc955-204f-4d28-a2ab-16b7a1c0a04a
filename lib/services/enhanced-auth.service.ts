/**
 * Enhanced Authentication Service with AppWrite Integration
 * 
 * This service extends the basic AppWrite authentication with:
 * - User profile persistence and management
 * - Session management with proper error handling
 * - Learning statistics tracking
 * - Subscription management
 * 
 * Following Rule 12: Validation & Error Handling
 * Following Rule 11: State Management Architecture
 */

import { ID, Permission, Role } from "react-native-appwrite";
import { z } from "zod";
import { appwriteService } from "./appwrite.service";
// Using appwriteService.database methods instead of direct database service
import type { AuthUser, AppwriteUser } from "@/types/appwrite";

// Zod schemas for validation (Rule 11)
const UserPreferencesSchema = z.object({
  language: z.string().default('en'),
  theme: z.enum(['light', 'dark']).default('light'),
  notifications: z.boolean().default(true),
});

const LearningStatsSchema = z.object({
  totalCards: z.number().default(0),
  totalScans: z.number().default(0),
  streakDays: z.number().default(0),
  lastActive: z.string().default(() => new Date().toISOString()),
});

const SubscriptionSchema = z.object({
  plan: z.enum(['free', 'premium']).default('free'),
  expiresAt: z.string().optional(),
});

const CreateUserProfileSchema = z.object({
  email: z.string().email(),
  name: z.string().min(1),
  avatar: z.string().optional(),
  preferences: UserPreferencesSchema.default({}),
  learningStats: LearningStatsSchema.default({}),
  subscription: SubscriptionSchema.default({}),
});

const UpdateUserProfileSchema = CreateUserProfileSchema.partial();

type CreateUserProfileInput = z.infer<typeof CreateUserProfileSchema>;
type UpdateUserProfileInput = z.infer<typeof UpdateUserProfileSchema>;

export class EnhancedAuthService {
  /**
   * Sign up with automatic user profile creation
   */
  async signUp(email: string, password: string, name: string): Promise<{ user: AuthUser; profile: AppwriteUser }> {
    try {
      // Validate input
      const validatedInput = CreateUserProfileSchema.parse({
        email,
        name,
        preferences: {},
        learningStats: {},
        subscription: { plan: 'free' },
      });

      // Create authentication account
      const authUser = await appwriteService.auth.signUp(email, password, name);

      // Create user profile in database
      const userProfile = await this.createUserProfile(authUser.$id, validatedInput);

      return { user: authUser, profile: userProfile };
    } catch (error) {
      // Early return pattern (Rule 12)
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map(e => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  /**
   * Sign in with profile loading
   */
  async signIn(email: string, password: string): Promise<{ user: AuthUser; profile: AppwriteUser | null }> {
    try {
      const authUser = await appwriteService.auth.signIn(email, password);
      const userProfile = await this.getUserProfile(authUser.$id);
      
      // Update last active timestamp
      if (userProfile) {
        await this.updateUserProfile(authUser.$id, {
          learningStats: {
            ...userProfile.learningStats,
            lastActive: new Date().toISOString(),
          },
        });
      }

      return { user: authUser, profile: userProfile };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create anonymous session with temporary profile
   */
  async createAnonymousSession(): Promise<{ user: AuthUser; profile: AppwriteUser }> {
    try {
      const authUser = await appwriteService.auth.createAnonymousSession();
      
      // Create temporary profile for anonymous user
      const tempProfile = await this.createUserProfile(authUser.$id, {
        email: '',
        name: 'Guest User',
        preferences: {},
        learningStats: {},
        subscription: { plan: 'free' },
      });

      return { user: authUser, profile: tempProfile };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create user profile in database
   */
  private async createUserProfile(userId: string, profileData: CreateUserProfileInput): Promise<AppwriteUser> {
    try {
      const validatedData = CreateUserProfileSchema.parse(profileData);
      
      const userProfile: Omit<AppwriteUser, '$id' | '$createdAt' | '$updatedAt' | '$permissions' | '$collectionId' | '$databaseId'> = {
        email: validatedData.email,
        name: validatedData.name,
        avatar: validatedData.avatar || `https://api.dicebear.com/7.x/initials/svg?seed=${validatedData.name}`,
        preferences: validatedData.preferences,
        learningStats: validatedData.learningStats,
        subscription: validatedData.subscription,
      };

      const createdProfile = await appwriteService.database.createUserProfile(userProfile);

      return createdProfile as AppwriteUser;
    } catch (error) {
      throw new Error(`Failed to create user profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user profile from database
   */
  async getUserProfile(userId: string): Promise<AppwriteUser | null> {
    try {
      const profile = await appwriteService.database.getUserProfile(userId);
      return profile as AppwriteUser;
    } catch (error) {
      // Return null if profile doesn't exist (not an error for anonymous users)
      return null;
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(userId: string, updates: UpdateUserProfileInput): Promise<AppwriteUser> {
    try {
      const validatedUpdates = UpdateUserProfileSchema.parse(updates);
      
      const updatedProfile = await appwriteService.database.updateUserProfile(userId, validatedUpdates);

      return updatedProfile as AppwriteUser;
    } catch (error) {
      throw new Error(`Failed to update user profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update learning statistics
   */
  async updateLearningStats(userId: string, stats: Partial<z.infer<typeof LearningStatsSchema>>): Promise<AppwriteUser> {
    try {
      const currentProfile = await this.getUserProfile(userId);
      if (!currentProfile) {
        throw new Error('User profile not found');
      }

      const updatedStats = {
        ...currentProfile.learningStats,
        ...stats,
        lastActive: new Date().toISOString(),
      };

      return await this.updateUserProfile(userId, {
        learningStats: updatedStats,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Increment scan count
   */
  async incrementScanCount(userId: string): Promise<AppwriteUser> {
    try {
      const currentProfile = await this.getUserProfile(userId);
      if (!currentProfile) {
        throw new Error('User profile not found');
      }

      return await this.updateLearningStats(userId, {
        totalScans: currentProfile.learningStats.totalScans + 1,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Increment knowledge card count
   */
  async incrementCardCount(userId: string): Promise<AppwriteUser> {
    try {
      const currentProfile = await this.getUserProfile(userId);
      if (!currentProfile) {
        throw new Error('User profile not found');
      }

      return await this.updateLearningStats(userId, {
        totalCards: currentProfile.learningStats.totalCards + 1,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update streak days
   */
  async updateStreakDays(userId: string): Promise<AppwriteUser> {
    try {
      const currentProfile = await this.getUserProfile(userId);
      if (!currentProfile) {
        throw new Error('User profile not found');
      }

      const lastActive = new Date(currentProfile.learningStats.lastActive);
      const today = new Date();
      const daysDiff = Math.floor((today.getTime() - lastActive.getTime()) / (1000 * 60 * 60 * 24));

      let newStreakDays = currentProfile.learningStats.streakDays;

      if (daysDiff === 1) {
        // Consecutive day - increment streak
        newStreakDays += 1;
      } else if (daysDiff > 1) {
        // Streak broken - reset to 1
        newStreakDays = 1;
      }
      // If daysDiff === 0, same day - no change to streak

      return await this.updateLearningStats(userId, {
        streakDays: newStreakDays,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Sign out with cleanup
   */
  async signOut(): Promise<void> {
    try {
      await appwriteService.auth.signOut();
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete user account and profile
   */
  /**
   * Delete user account and profile
   * TODO: Implement when deleteAccount method is available in auth service
   */
  async deleteAccount(userId: string): Promise<void> {
    throw new Error('Account deletion not yet implemented');
  }
}

export const enhancedAuthService = new EnhancedAuthService();