import { authService } from './auth.service';
import { databaseService } from './database.service';
import { storageService } from './storage.service';
import { client } from '@/lib/config/appwrite';
import {
  COLLECTIONS,
  UserPermissionSchema,
  UsageTrackingSchema,
  type UserPermission,
  type UsageTracking
} from '@/lib/config/appwrite-permissions.config';
import type {
  AuthUser,
  KnowledgeCard,
  LearningSession,
  ScanHistory,
  AppwriteUser,
  FileUploadResult,
  FileUploadProgress
} from '@/types/appwrite';

/**
 * Comprehensive AppWrite service that provides a unified interface
 * for all AppWrite operations in the LearniScan app
 */
export class AppwriteService {
  // Authentication methods
  get auth() {
    return {
      signUp: authService.signUp.bind(authService),
      signIn: authService.signIn.bind(authService),
      signInWithOAuth: authService.signInWithOAuth.bind(authService),
      createAnonymousSession: authService.createAnonymousSession.bind(authService),
      getCurrentUser: authService.getCurrentUser.bind(authService),
      getCurrentSession: authService.getCurrentSession.bind(authService),
      signOut: authService.signOut.bind(authService),
      signOutFromAllDevices: authService.signOutFromAllDevices.bind(authService),
      updateProfile: authService.updateProfile.bind(authService),
      updateEmail: authService.updateEmail.bind(authService),
      changePassword: authService.changePassword.bind(authService),
      resetPassword: authService.resetPassword.bind(authService),
      verifyEmail: authService.verifyEmail.bind(authService),
      updatePreferences: authService.updatePreferences.bind(authService),
      isAuthenticated: authService.isAuthenticated.bind(authService),
    };
  }

  // Database methods
  get database() {
    return {
      // Knowledge Cards
      createKnowledgeCard: databaseService.createKnowledgeCard.bind(databaseService),
      getKnowledgeCard: databaseService.getKnowledgeCard.bind(databaseService),
      getKnowledgeCards: databaseService.getKnowledgeCards.bind(databaseService),
      updateKnowledgeCard: databaseService.updateKnowledgeCard.bind(databaseService),
      deleteKnowledgeCard: databaseService.deleteKnowledgeCard.bind(databaseService),
      
      // Learning Sessions
      createLearningSession: databaseService.createLearningSession.bind(databaseService),
      getLearningHistory: databaseService.getLearningHistory.bind(databaseService),
      
      // Scan History
      createScanRecord: databaseService.createScanRecord.bind(databaseService),
      getScanHistory: databaseService.getScanHistory.bind(databaseService),
      
      // User Profile
      createUserProfile: databaseService.createUserProfile.bind(databaseService),
      getUserProfile: databaseService.getUserProfile.bind(databaseService),
      updateUserProfile: databaseService.updateUserProfile.bind(databaseService),
    };
  }

  // Storage methods
  get storage() {
    return {
      uploadFile: storageService.uploadFile.bind(storageService),
      uploadAvatar: storageService.uploadAvatar.bind(storageService),
      uploadScanImage: storageService.uploadScanImage.bind(storageService),
      uploadCardAsset: storageService.uploadCardAsset.bind(storageService),
      uploadExport: storageService.uploadExport.bind(storageService),
      uploadMultipleFiles: storageService.uploadMultipleFiles.bind(storageService),
      
      getFileUrl: storageService.getFileUrl.bind(storageService),
      getFilePreview: storageService.getFilePreview.bind(storageService),
      getFileView: storageService.getFileView.bind(storageService),
      getImageThumbnail: storageService.getImageThumbnail.bind(storageService),
      getOptimizedImage: storageService.getOptimizedImage.bind(storageService),
      
      deleteFile: storageService.deleteFile.bind(storageService),
      getFile: storageService.getFile.bind(storageService),
      listFiles: storageService.listFiles.bind(storageService),
      updateFile: storageService.updateFile.bind(storageService),
      
      // Helper methods
      uploadProfilePicture: storageService.uploadProfilePicture.bind(storageService),
      uploadDocumentScan: storageService.uploadDocumentScan.bind(storageService),
      formatFileSize: storageService.formatFileSize.bind(storageService),
      isImageFile: storageService.isImageFile.bind(storageService),
      isPDFFile: storageService.isPDFFile.bind(storageService),
    };
  }

  // High-level workflow methods
  async initializeUser(authUser: AuthUser): Promise<AppwriteUser> {
    try {
      // Check if user profile already exists
      try {
        const existingProfile = await this.database.getUserProfile(authUser.$id);
        return existingProfile;
      } catch (error) {
        // Profile doesn't exist, create it
      }

      // Create new user profile
      const userProfile: Omit<AppwriteUser, '$id' | '$createdAt' | '$updatedAt' | '$permissions' | '$collectionId' | '$databaseId'> = {
        email: authUser.email,
        name: authUser.name,
        preferences: {
          language: 'en',
          theme: 'light',
          notifications: true,
        },
        learningStats: {
          totalCards: 0,
          totalScans: 0,
          streakDays: 0,
          lastActive: new Date().toISOString(),
        },
      };

      return await this.database.createUserProfile(userProfile);
    } catch (error) {
      throw new Error(`Failed to initialize user: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async createKnowledgeCardFromScan(
    userId: string,
    scanData: {
      imageFile: any;
      extractedText: string;
      title: string;
      category: string;
      difficulty: 'beginner' | 'intermediate' | 'advanced';
    },
    onProgress?: (progress: FileUploadProgress) => void
  ): Promise<{ card: KnowledgeCard; scanRecord: ScanHistory }> {
    try {
      // Upload the scanned image
      const uploadResult = await this.storage.uploadDocumentScan(
        userId,
        scanData.imageFile,
        onProgress
      );

      // Create scan history record
      const scanRecord = await this.database.createScanRecord({
        userId,
        originalImageId: uploadResult.fileId,
        extractedText: scanData.extractedText,
        scanType: 'document',
        confidence: 0.95, // This would come from OCR service
        language: 'en', // This would be detected
        metadata: {
          imageSize: scanData.imageFile.size || 0,
          dimensions: {
            width: scanData.imageFile.width || 0,
            height: scanData.imageFile.height || 0,
          },
          processingTime: 0, // This would be measured
        },
      });

      // Create knowledge card
      const knowledgeCard = await this.database.createKnowledgeCard({
        userId,
        title: scanData.title,
        content: scanData.extractedText,
        sourceType: 'scan',
        sourceData: {
          originalText: scanData.extractedText,
          imageUrl: uploadResult.previewUrl,
          documentUrl: uploadResult.downloadUrl,
        },
        tags: [],
        difficulty: scanData.difficulty,
        category: scanData.category,
        reviewData: {
          nextReview: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
          interval: 1,
          easeFactor: 2.5,
          reviewCount: 0,
          correctCount: 0,
        },
        aiEnhanced: false,
        isPublic: false,
      });

      // Update scan record with knowledge card reference
      await this.database.createScanRecord({
        ...scanRecord,
        knowledgeCardId: knowledgeCard.$id,
      });

      return { card: knowledgeCard, scanRecord };
    } catch (error) {
      throw new Error(`Failed to create knowledge card from scan: ${error.message}`);
    }
  }

  async completeReviewSession(
    userId: string,
    sessionData: {
      cardsReviewed: string[];
      performance: {
        totalCards: number;
        correctAnswers: number;
        averageTime: number;
      };
      startedAt: string;
    }
  ): Promise<LearningSession> {
    try {
      const completedAt = new Date().toISOString();
      const duration = new Date(completedAt).getTime() - new Date(sessionData.startedAt).getTime();

      const session = await this.database.createLearningSession({
        userId,
        sessionType: 'review',
        cardsReviewed: sessionData.cardsReviewed,
        performance: {
          ...sessionData.performance,
          accuracy: (sessionData.performance.correctAnswers / sessionData.performance.totalCards) * 100,
        },
        duration: Math.floor(duration / 1000), // Convert to seconds
        startedAt: sessionData.startedAt,
        completedAt,
      });

      // Update user learning stats
      const userProfile = await this.database.getUserProfile(userId);
      const updatedStats = {
        ...userProfile.learningStats,
        lastActive: completedAt,
        // Add streak logic here
      };

      await this.database.updateUserProfile(userId, {
        learningStats: updatedStats,
      });

      return session;
    } catch (error) {
      throw new Error(`Failed to complete review session: ${error.message}`);
    }
  }

  // Utility methods
  async checkConnection(): Promise<boolean> {
    try {
      await this.auth.getCurrentUser();
      return true;
    } catch (error) {
      return false;
    }
  }

  async getAppInfo(): Promise<{ version: string; endpoint: string; projectId: string }> {
    const { APPWRITE_CONFIG } = await import('@/lib/config/appwrite');
    return {
      version: '1.0.0', // This could come from package.json
      endpoint: APPWRITE_CONFIG.endpoint,
      projectId: APPWRITE_CONFIG.projectId,
    };
  }

  // Permission and usage tracking methods
  get permissions() {
    return {
      // User permissions management
      getUserPermissions: async (userId: string): Promise<UserPermission | null> => {
        try {
          const result = await databaseService.listDocuments(
            COLLECTIONS.USER_PERMISSIONS,
            [`userId=${userId}`]
          );

          if (result.documents.length === 0) {
            return null;
          }

          const doc = result.documents[0];
          return UserPermissionSchema.parse({
            userId: doc.userId,
            role: doc.role,
            subscription: doc.subscription ? JSON.parse(doc.subscription) : undefined,
            permissions: doc.permissions ? JSON.parse(doc.permissions) : [],
            metadata: doc.metadata ? JSON.parse(doc.metadata) : undefined,
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
          });
        } catch (error) {
          console.error('Failed to get user permissions:', error);
          return null;
        }
      },

      createUserPermissions: async (userPermission: Omit<UserPermission, 'createdAt' | 'updatedAt'>): Promise<UserPermission> => {
        try {
          const now = new Date().toISOString();
          const doc = await databaseService.createDocument(
            COLLECTIONS.USER_PERMISSIONS,
            {
              userId: userPermission.userId,
              role: userPermission.role,
              subscription: userPermission.subscription ? JSON.stringify(userPermission.subscription) : undefined,
              permissions: JSON.stringify(userPermission.permissions),
              metadata: userPermission.metadata ? JSON.stringify(userPermission.metadata) : undefined,
              createdAt: now,
              updatedAt: now,
            }
          );

          return UserPermissionSchema.parse({
            ...userPermission,
            createdAt: now,
            updatedAt: now,
          });
        } catch (error) {
          console.error('Failed to create user permissions:', error);
          throw error;
        }
      },

      getUsageTracking: async (userId: string, feature: string, date?: string): Promise<UsageTracking | null> => {
        try {
          const targetDate = date || new Date().toISOString().split('T')[0];
          const result = await databaseService.listDocuments(
            COLLECTIONS.USAGE_TRACKING,
            [`userId=${userId}`, `feature=${feature}`, `date=${targetDate}`]
          );

          if (result.documents.length === 0) {
            return null;
          }

          const doc = result.documents[0];
          return UsageTrackingSchema.parse({
            userId: doc.userId,
            feature: doc.feature,
            date: doc.date,
            usage: doc.usage,
            limit: doc.limit,
            resetPeriod: doc.resetPeriod,
            metadata: doc.metadata ? JSON.parse(doc.metadata) : undefined,
            timestamp: doc.timestamp,
          });
        } catch (error) {
          console.error('Failed to get usage tracking:', error);
          return null;
        }
      },

      createOrUpdateUsageTracking: async (usageData: Omit<UsageTracking, 'timestamp'>): Promise<UsageTracking> => {
        try {
          const existing = await this.getUsageTracking(usageData.userId, usageData.feature, usageData.date);
          const timestamp = new Date().toISOString();

          if (existing) {
            // Update existing record
            const result = await databaseService.listDocuments(
              COLLECTIONS.USAGE_TRACKING,
              [`userId=${usageData.userId}`, `feature=${usageData.feature}`, `date=${usageData.date}`]
            );

            await databaseService.updateDocument(
              COLLECTIONS.USAGE_TRACKING,
              result.documents[0].$id,
              {
                usage: usageData.usage,
                limit: usageData.limit,
                resetPeriod: usageData.resetPeriod,
                metadata: usageData.metadata ? JSON.stringify(usageData.metadata) : undefined,
                timestamp,
              }
            );

            return { ...usageData, timestamp };
          } else {
            // Create new record
            await databaseService.createDocument(
              COLLECTIONS.USAGE_TRACKING,
              {
                userId: usageData.userId,
                feature: usageData.feature,
                date: usageData.date,
                usage: usageData.usage,
                limit: usageData.limit,
                resetPeriod: usageData.resetPeriod,
                metadata: usageData.metadata ? JSON.stringify(usageData.metadata) : undefined,
                timestamp,
              }
            );

            return { ...usageData, timestamp };
          }
        } catch (error) {
          console.error('Failed to create/update usage tracking:', error);
          throw error;
        }
      },
    };
  }

  // Cleanup methods
  async cleanup(): Promise<void> {
    try {
      // Perform any necessary cleanup
      console.log('AppWrite service cleanup completed');
    } catch (error) {
      console.error('AppWrite service cleanup failed:', error);
    }
  }
}

// Export singleton instance
export const appwriteService = new AppwriteService();

// Export individual services for direct access if needed
export { authService, databaseService, storageService };

// Export the client for direct AppWrite SDK access
export { client as appwriteClient };
