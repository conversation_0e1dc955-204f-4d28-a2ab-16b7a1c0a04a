/**
 * User Testing Analytics Service
 * 
 * Collects and analyzes user feedback for AI Search functionality
 * Following LearniScan development workflow rules:
 * - Rule 11: Zod validation for all data structures
 * - Rule 12: Comprehensive error handling
 * - Rule 10: Performance optimization with local storage
 */

import { z } from 'zod';
import AsyncStorage from '@react-native-async-storage/async-storage';
import type { AISearchResponse } from './ai-search.service';

// Zod schemas for validation (Rule 11)
const UserFeedbackSchema = z.object({
  scenarioId: z.string(),
  userId: z.string(),
  query: z.string(),
  searchResults: z.object({
    totalResults: z.number(),
    processingTime: z.number(),
    intent: z.object({
      intent: z.enum(['search', 'learn', 'review', 'discover', 'compare', 'explain']),
      confidence: z.number().min(0).max(1),
    }),
    suggestions: z.array(z.any()),
    relatedQueries: z.array(z.string()),
  }),
  ratings: z.object({
    relevance: z.number().min(1).max(5),
    intentAccuracy: z.number().min(1).max(5),
    suggestionQuality: z.number().min(1).max(5),
    overallExperience: z.number().min(1).max(5),
  }),
  feedback: z.object({
    helpful: z.boolean(),
    comments: z.string(),
    improvements: z.string(),
  }),
  timestamp: z.string(),
  deviceInfo: z.object({
    platform: z.string(),
    version: z.string(),
    screenSize: z.string(),
  }).optional(),
});

const TestingSessionSchema = z.object({
  sessionId: z.string(),
  userId: z.string(),
  startTime: z.string(),
  endTime: z.string().optional(),
  completedScenarios: z.array(z.string()),
  totalFeedbacks: z.number(),
  averageRatings: z.object({
    relevance: z.number(),
    intentAccuracy: z.number(),
    suggestionQuality: z.number(),
    overallExperience: z.number(),
  }),
  sessionNotes: z.string().optional(),
});

const AnalyticsReportSchema = z.object({
  reportId: z.string(),
  generatedAt: z.string(),
  totalSessions: z.number(),
  totalFeedbacks: z.number(),
  averageRatings: z.object({
    relevance: z.number(),
    intentAccuracy: z.number(),
    suggestionQuality: z.number(),
    overallExperience: z.number(),
  }),
  intentAccuracy: z.object({
    totalTests: z.number(),
    correctPredictions: z.number(),
    accuracy: z.number(),
  }),
  commonIssues: z.array(z.object({
    issue: z.string(),
    frequency: z.number(),
    severity: z.enum(['low', 'medium', 'high']),
  })),
  improvements: z.array(z.object({
    suggestion: z.string(),
    frequency: z.number(),
    priority: z.enum(['low', 'medium', 'high']),
  })),
  performanceMetrics: z.object({
    averageSearchTime: z.number(),
    averageResultCount: z.number(),
    successRate: z.number(),
  }),
});

// Type exports
export type UserFeedback = z.infer<typeof UserFeedbackSchema>;
export type TestingSession = z.infer<typeof TestingSessionSchema>;
export type AnalyticsReport = z.infer<typeof AnalyticsReportSchema>;

/**
 * User Testing Analytics Service
 */
class UserTestingAnalyticsService {
  private readonly STORAGE_KEYS = {
    FEEDBACKS: 'user_testing_feedbacks',
    SESSIONS: 'user_testing_sessions',
    CURRENT_SESSION: 'current_testing_session',
  };

  /**
   * Start a new testing session
   */
  async startTestingSession(userId: string): Promise<string> {
    try {
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const session: TestingSession = {
        sessionId,
        userId,
        startTime: new Date().toISOString(),
        completedScenarios: [],
        totalFeedbacks: 0,
        averageRatings: {
          relevance: 0,
          intentAccuracy: 0,
          suggestionQuality: 0,
          overallExperience: 0,
        },
      };

      // Validate session data
      const validatedSession = TestingSessionSchema.parse(session);

      // Store current session
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.CURRENT_SESSION,
        JSON.stringify(validatedSession)
      );

      console.log(`Started testing session: ${sessionId}`);
      return sessionId;

    } catch (error) {
      console.error('Failed to start testing session:', error);
      throw new Error('Failed to start testing session');
    }
  }

  /**
   * Submit user feedback
   */
  async submitFeedback(feedback: Omit<UserFeedback, 'deviceInfo'>): Promise<void> {
    try {
      // Add device info
      const deviceInfo = {
        platform: 'react-native',
        version: '1.0.0',
        screenSize: 'mobile', // Could be determined dynamically
      };

      const completeFeedback: UserFeedback = {
        ...feedback,
        deviceInfo,
      };

      // Validate feedback data
      const validatedFeedback = UserFeedbackSchema.parse(completeFeedback);

      // Get existing feedbacks
      const existingFeedbacks = await this.getFeedbacks();
      const updatedFeedbacks = [...existingFeedbacks, validatedFeedback];

      // Store updated feedbacks
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.FEEDBACKS,
        JSON.stringify(updatedFeedbacks)
      );

      // Update current session
      await this.updateCurrentSession(validatedFeedback);

      console.log('Feedback submitted successfully');

    } catch (error) {
      console.error('Failed to submit feedback:', error);
      throw new Error('Failed to submit feedback');
    }
  }

  /**
   * End current testing session
   */
  async endTestingSession(): Promise<void> {
    try {
      const currentSession = await this.getCurrentSession();
      if (!currentSession) {
        throw new Error('No active testing session');
      }

      // Update session with end time
      const updatedSession: TestingSession = {
        ...currentSession,
        endTime: new Date().toISOString(),
      };

      // Store completed session
      const existingSessions = await this.getSessions();
      const updatedSessions = [...existingSessions, updatedSession];
      
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.SESSIONS,
        JSON.stringify(updatedSessions)
      );

      // Clear current session
      await AsyncStorage.removeItem(this.STORAGE_KEYS.CURRENT_SESSION);

      console.log('Testing session ended successfully');

    } catch (error) {
      console.error('Failed to end testing session:', error);
      throw new Error('Failed to end testing session');
    }
  }

  /**
   * Generate analytics report
   */
  async generateAnalyticsReport(): Promise<AnalyticsReport> {
    try {
      const feedbacks = await this.getFeedbacks();
      const sessions = await this.getSessions();

      if (feedbacks.length === 0) {
        throw new Error('No feedback data available for report generation');
      }

      // Calculate average ratings
      const totalRatings = feedbacks.reduce(
        (acc, feedback) => ({
          relevance: acc.relevance + feedback.ratings.relevance,
          intentAccuracy: acc.intentAccuracy + feedback.ratings.intentAccuracy,
          suggestionQuality: acc.suggestionQuality + feedback.ratings.suggestionQuality,
          overallExperience: acc.overallExperience + feedback.ratings.overallExperience,
        }),
        { relevance: 0, intentAccuracy: 0, suggestionQuality: 0, overallExperience: 0 }
      );

      const averageRatings = {
        relevance: totalRatings.relevance / feedbacks.length,
        intentAccuracy: totalRatings.intentAccuracy / feedbacks.length,
        suggestionQuality: totalRatings.suggestionQuality / feedbacks.length,
        overallExperience: totalRatings.overallExperience / feedbacks.length,
      };

      // Analyze intent accuracy
      const intentTests = feedbacks.filter(f => f.scenarioId.includes('intent'));
      const correctIntentPredictions = intentTests.filter(f => f.ratings.intentAccuracy >= 4).length;
      const intentAccuracy = {
        totalTests: intentTests.length,
        correctPredictions: correctIntentPredictions,
        accuracy: intentTests.length > 0 ? correctIntentPredictions / intentTests.length : 0,
      };

      // Extract common issues
      const issueFrequency = new Map<string, number>();
      feedbacks.forEach(feedback => {
        if (feedback.feedback.comments) {
          // Simple keyword extraction for common issues
          const keywords = ['slow', 'irrelevant', 'confusing', 'inaccurate', 'missing'];
          keywords.forEach(keyword => {
            if (feedback.feedback.comments.toLowerCase().includes(keyword)) {
              issueFrequency.set(keyword, (issueFrequency.get(keyword) || 0) + 1);
            }
          });
        }
      });

      const commonIssues = Array.from(issueFrequency.entries())
        .map(([issue, frequency]) => ({
          issue,
          frequency,
          severity: frequency > feedbacks.length * 0.3 ? 'high' as const : 
                   frequency > feedbacks.length * 0.1 ? 'medium' as const : 'low' as const,
        }))
        .sort((a, b) => b.frequency - a.frequency)
        .slice(0, 5);

      // Extract improvement suggestions
      const improvementFrequency = new Map<string, number>();
      feedbacks.forEach(feedback => {
        if (feedback.feedback.improvements) {
          // Simple keyword extraction for improvements
          const keywords = ['faster', 'better', 'more accurate', 'clearer', 'easier'];
          keywords.forEach(keyword => {
            if (feedback.feedback.improvements.toLowerCase().includes(keyword)) {
              improvementFrequency.set(keyword, (improvementFrequency.get(keyword) || 0) + 1);
            }
          });
        }
      });

      const improvements = Array.from(improvementFrequency.entries())
        .map(([suggestion, frequency]) => ({
          suggestion,
          frequency,
          priority: frequency > feedbacks.length * 0.2 ? 'high' as const : 
                   frequency > feedbacks.length * 0.1 ? 'medium' as const : 'low' as const,
        }))
        .sort((a, b) => b.frequency - a.frequency)
        .slice(0, 5);

      // Calculate performance metrics
      const totalSearchTime = feedbacks.reduce((acc, f) => acc + f.searchResults.processingTime, 0);
      const totalResultCount = feedbacks.reduce((acc, f) => acc + f.searchResults.totalResults, 0);
      const successfulSearches = feedbacks.filter(f => f.ratings.overallExperience >= 3).length;

      const performanceMetrics = {
        averageSearchTime: totalSearchTime / feedbacks.length,
        averageResultCount: totalResultCount / feedbacks.length,
        successRate: successfulSearches / feedbacks.length,
      };

      const report: AnalyticsReport = {
        reportId: `report_${Date.now()}`,
        generatedAt: new Date().toISOString(),
        totalSessions: sessions.length,
        totalFeedbacks: feedbacks.length,
        averageRatings,
        intentAccuracy,
        commonIssues,
        improvements,
        performanceMetrics,
      };

      // Validate report
      const validatedReport = AnalyticsReportSchema.parse(report);

      console.log('Analytics report generated successfully');
      return validatedReport;

    } catch (error) {
      console.error('Failed to generate analytics report:', error);
      throw new Error('Failed to generate analytics report');
    }
  }

  /**
   * Get all feedbacks
   */
  private async getFeedbacks(): Promise<UserFeedback[]> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.FEEDBACKS);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get feedbacks:', error);
      return [];
    }
  }

  /**
   * Get all sessions
   */
  private async getSessions(): Promise<TestingSession[]> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.SESSIONS);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get sessions:', error);
      return [];
    }
  }

  /**
   * Get current session
   */
  private async getCurrentSession(): Promise<TestingSession | null> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.CURRENT_SESSION);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Failed to get current session:', error);
      return null;
    }
  }

  /**
   * Update current session with new feedback
   */
  private async updateCurrentSession(feedback: UserFeedback): Promise<void> {
    try {
      const currentSession = await this.getCurrentSession();
      if (!currentSession) return;

      // Update session statistics
      const updatedSession: TestingSession = {
        ...currentSession,
        completedScenarios: [...new Set([...currentSession.completedScenarios, feedback.scenarioId])],
        totalFeedbacks: currentSession.totalFeedbacks + 1,
        averageRatings: {
          relevance: (currentSession.averageRatings.relevance * currentSession.totalFeedbacks + feedback.ratings.relevance) / (currentSession.totalFeedbacks + 1),
          intentAccuracy: (currentSession.averageRatings.intentAccuracy * currentSession.totalFeedbacks + feedback.ratings.intentAccuracy) / (currentSession.totalFeedbacks + 1),
          suggestionQuality: (currentSession.averageRatings.suggestionQuality * currentSession.totalFeedbacks + feedback.ratings.suggestionQuality) / (currentSession.totalFeedbacks + 1),
          overallExperience: (currentSession.averageRatings.overallExperience * currentSession.totalFeedbacks + feedback.ratings.overallExperience) / (currentSession.totalFeedbacks + 1),
        },
      };

      await AsyncStorage.setItem(
        this.STORAGE_KEYS.CURRENT_SESSION,
        JSON.stringify(updatedSession)
      );

    } catch (error) {
      console.error('Failed to update current session:', error);
    }
  }

  /**
   * Get testing statistics for dashboard
   */
  async getTestingStatistics(): Promise<{
    totalFeedbacks: number;
    averageRatings: TestingSession['averageRatings'];
    recentFeedbacks: UserFeedback[];
    topIssues: string[];
    completionRate: number;
  }> {
    try {
      const feedbacks = await this.getFeedbacks();
      const sessions = await this.getSessions();

      if (feedbacks.length === 0) {
        return {
          totalFeedbacks: 0,
          averageRatings: { relevance: 0, intentAccuracy: 0, suggestionQuality: 0, overallExperience: 0 },
          recentFeedbacks: [],
          topIssues: [],
          completionRate: 0,
        };
      }

      // Calculate average ratings
      const totalRatings = feedbacks.reduce(
        (acc, feedback) => ({
          relevance: acc.relevance + feedback.ratings.relevance,
          intentAccuracy: acc.intentAccuracy + feedback.ratings.intentAccuracy,
          suggestionQuality: acc.suggestionQuality + feedback.ratings.suggestionQuality,
          overallExperience: acc.overallExperience + feedback.ratings.overallExperience,
        }),
        { relevance: 0, intentAccuracy: 0, suggestionQuality: 0, overallExperience: 0 }
      );

      const averageRatings = {
        relevance: totalRatings.relevance / feedbacks.length,
        intentAccuracy: totalRatings.intentAccuracy / feedbacks.length,
        suggestionQuality: totalRatings.suggestionQuality / feedbacks.length,
        overallExperience: totalRatings.overallExperience / feedbacks.length,
      };

      // Get recent feedbacks (last 10)
      const recentFeedbacks = feedbacks
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 10);

      // Extract top issues
      const issueKeywords = ['slow', 'irrelevant', 'confusing', 'inaccurate', 'missing'];
      const topIssues = issueKeywords.filter(keyword =>
        feedbacks.some(f => f.feedback.comments.toLowerCase().includes(keyword))
      );

      // Calculate completion rate
      const completedSessions = sessions.filter(s => s.endTime).length;
      const completionRate = sessions.length > 0 ? completedSessions / sessions.length : 0;

      return {
        totalFeedbacks: feedbacks.length,
        averageRatings,
        recentFeedbacks,
        topIssues,
        completionRate,
      };

    } catch (error) {
      console.error('Failed to get testing statistics:', error);
      throw new Error('Failed to get testing statistics');
    }
  }

  /**
   * Clear all testing data (for development/testing purposes)
   */
  async clearAllData(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem(this.STORAGE_KEYS.FEEDBACKS),
        AsyncStorage.removeItem(this.STORAGE_KEYS.SESSIONS),
        AsyncStorage.removeItem(this.STORAGE_KEYS.CURRENT_SESSION),
      ]);
      console.log('All testing data cleared');
    } catch (error) {
      console.error('Failed to clear testing data:', error);
      throw new Error('Failed to clear testing data');
    }
  }
}

// Export singleton instance
export const userTestingAnalytics = new UserTestingAnalyticsService();
