/**
 * AI Service for LearniScan
 * 
 * Centralized AI service handling all AI operations including:
 * - O<PERSON> (Optical Character Recognition)
 * - Translation
 * - Knowledge Card Generation
 * - Graph Generation
 * - Chat/Conversation
 * 
 * Following LearniScan development workflow rules:
 * - Rule 11: Zod validation for all inputs/outputs
 * - Rule 12: Comprehensive error handling with early returns
 * - Rule 5: TypeScript quality and type safety
 * - Rule 10: Performance optimization with caching
 */

import { z } from 'zod';
import { streamText, streamObject, generateText, generateObject } from 'ai';
import { aiModels, AIOperation, AIOperationSchema, AI_CONFIG } from '@/lib/config/ai.config';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { aiCacheService } from './ai-cache.service';

// Input/Output schemas for type safety (Rule 11)
const OCRInputSchema = z.object({
  imageUri: z.string().url(),
  language: z.string().optional(),
  enhanceAccuracy: z.boolean().default(true),
});

const OCROutputSchema = z.object({
  extractedText: z.string(),
  confidence: z.number().min(0).max(100),
  language: z.string(),
  textBlocks: z.array(z.object({
    text: z.string(),
    bounds: z.object({
      x: z.number(),
      y: z.number(),
      width: z.number(),
      height: z.number(),
    }),
    confidence: z.number(),
  })),
  processingTime: z.number(),
});

const TranslationInputSchema = z.object({
  text: z.string().min(1),
  targetLanguage: z.string().min(2).max(5),
  sourceLanguage: z.string().optional(),
  context: z.string().optional(),
});

const TranslationOutputSchema = z.object({
  translatedText: z.string(),
  sourceLanguage: z.string(),
  targetLanguage: z.string(),
  confidence: z.number().min(0).max(100),
  processingTime: z.number(),
});

export type OCRInput = z.infer<typeof OCRInputSchema>;
export type OCROutput = z.infer<typeof OCROutputSchema>;
export type TranslationInput = z.infer<typeof TranslationInputSchema>;
export type TranslationOutput = z.infer<typeof TranslationOutputSchema>;

// AI Service Error Types (Rule 12)
export class AIServiceError extends Error {
  constructor(
    message: string,
    public readonly operation: string,
    public readonly code: string,
    public readonly retryable: boolean = false
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

// Rate limiting helper
class RateLimiter {
  private requests: number[] = [];
  private tokens: number = 0;
  private lastReset: number = Date.now();

  canMakeRequest(): boolean {
    const now = Date.now();
    const oneMinute = 60 * 1000;

    // Reset counters every minute
    if (now - this.lastReset > oneMinute) {
      this.requests = [];
      this.tokens = 0;
      this.lastReset = now;
    }

    // Filter requests from last minute
    this.requests = this.requests.filter(time => now - time < oneMinute);

    return this.requests.length < AI_CONFIG.rateLimits.requestsPerMinute;
  }

  recordRequest(tokenCount: number = 0): void {
    this.requests.push(Date.now());
    this.tokens += tokenCount;
  }
}

export class AIService {
  private rateLimiter = new RateLimiter();

  // Enhanced caching with persistent storage (Rule 10: Performance optimization)
  private async getFromCache<T>(operation: string, input: any): Promise<T | null> {
    return await aiCacheService.get<T>(operation, input);
  }

  private async setCache<T>(operation: string, input: any, data: T, ttl: number = 300000): Promise<void> {
    await aiCacheService.set(operation, input, data, ttl);
  }

  // Error handling helper (Rule 12)
  private handleError(error: any, operation: string): never {
    console.error(`AI Service Error (${operation}):`, error);

    // Early return pattern for different error types (Rule 12)
    if (error.message?.includes('rate limit')) {
      throw new AIServiceError(
        'AI service is temporarily busy. Please try again in a moment.',
        operation,
        'RATE_LIMIT',
        true
      );
    }

    if (error.message?.includes('timeout')) {
      throw new AIServiceError(
        'AI processing timed out. Please try again.',
        operation,
        'TIMEOUT',
        true
      );
    }

    if (error.message?.includes('network') || error.message?.includes('fetch')) {
      throw new AIServiceError(
        'Network error. Please check your connection and try again.',
        operation,
        'NETWORK_ERROR',
        true
      );
    }

    if (error.message?.includes('token')) {
      throw new AIServiceError(
        'Content is too large for AI processing. Please try with smaller content.',
        operation,
        'TOKEN_LIMIT',
        false
      );
    }

    // Generic error fallback
    throw new AIServiceError(
      `AI processing failed: ${error.message || 'Unknown error'}`,
      operation,
      'UNKNOWN_ERROR',
      false
    );
  }

  // Rate limiting check (Rule 12: Early return pattern)
  private checkRateLimit(operation: string): void {
    if (!this.rateLimiter.canMakeRequest()) {
      throw new AIServiceError(
        'Rate limit exceeded. Please wait before making another request.',
        operation,
        'RATE_LIMIT',
        true
      );
    }
  }

  /**
   * OCR (Optical Character Recognition) Service
   */
  async extractTextFromImage(
    input: OCRInput,
    onProgress?: (progress: number) => void
  ): Promise<OCROutput> {
    // Input validation (Rule 11)
    const validatedInput = OCRInputSchema.parse(input);
    
    // Rate limiting check (Rule 12: Early return)
    this.checkRateLimit('ocr');

    // Check cache first (Rule 10: Performance optimization)
    const cached = await this.getFromCache<OCROutput>('ocr', validatedInput);
    if (cached) {
      onProgress?.(100);
      return cached;
    }

    const startTime = Date.now();
    onProgress?.(10);

    try {
      this.rateLimiter.recordRequest();

      const prompt = `Extract all text from this image with high accuracy. 
      ${validatedInput.language ? `Expected language: ${validatedInput.language}` : 'Detect language automatically'}
      ${validatedInput.enhanceAccuracy ? 'Use maximum accuracy settings.' : ''}
      
      Provide the extracted text, confidence scores, and text block positions.`;

      onProgress?.(30);

      const { object } = await generateObject({
        model: aiModels.vision,
        schema: OCROutputSchema.omit({ processingTime: true }),
        messages: [{
          role: 'user',
          content: [
            { type: 'text', text: prompt },
            { type: 'image_url', image_url: { url: validatedInput.imageUri, detail: 'high' } }
          ]
        }],
        temperature: 0.1, // Low temperature for consistent OCR results
        maxTokens: 2048, // Add max tokens for better response
      });

      onProgress?.(90);

      const result: OCROutput = {
        ...object,
        processingTime: Date.now() - startTime,
      };

      // Cache the result (Rule 10)
      await this.setCache('ocr', validatedInput, result, 600000); // 10 minutes cache

      onProgress?.(100);
      return result;

    } catch (error) {
      this.handleError(error, 'ocr');
    }
  }

  /**
   * Translation Service
   */
  async translateText(
    input: TranslationInput,
    onProgress?: (chunk: string) => void
  ): Promise<TranslationOutput> {
    // Input validation (Rule 11)
    const validatedInput = TranslationInputSchema.parse(input);
    
    // Rate limiting check (Rule 12: Early return)
    this.checkRateLimit('translation');

    // Check cache first (Rule 10)
    const cached = await this.getFromCache<TranslationOutput>('translation', validatedInput);
    if (cached) {
      onProgress?.(cached.translatedText);
      return cached;
    }

    const startTime = Date.now();

    try {
      this.rateLimiter.recordRequest();

      const prompt = `Translate the following text to ${validatedInput.targetLanguage}.
      ${validatedInput.sourceLanguage ? `Source language: ${validatedInput.sourceLanguage}` : 'Detect source language automatically'}
      ${validatedInput.context ? `Context: ${validatedInput.context}` : ''}
      
      Maintain formatting and provide accurate translation:
      
      ${validatedInput.text}`;

      const { textStream } = await streamText({
        model: aiModels.chat,
        prompt,
        temperature: 0.3, // Lower temperature for consistent translations
      });

      let translatedText = '';
      for await (const chunk of textStream) {
        translatedText += chunk;
        onProgress?.(translatedText);
      }

      const result: TranslationOutput = {
        translatedText: translatedText.trim(),
        sourceLanguage: validatedInput.sourceLanguage || 'auto-detected',
        targetLanguage: validatedInput.targetLanguage,
        confidence: 85, // Default confidence, could be enhanced with actual confidence detection
        processingTime: Date.now() - startTime,
      };

      // Cache the result (Rule 10)
      await this.setCache('translation', validatedInput, result, 1800000); // 30 minutes cache

      return result;

    } catch (error) {
      this.handleError(error, 'translation');
    }
  }

  /**
   * Health check for AI service
   */
  async checkHealth(): Promise<{ isHealthy: boolean; latency?: number; error?: string }> {
    try {
      const startTime = Date.now();
      
      // Simple test with minimal token usage
      const { text } = await generateText({
        model: aiModels.chat,
        prompt: 'Respond with "OK"',
        maxTokens: 5,
      });

      const latency = Date.now() - startTime;
      
      return {
        isHealthy: text.trim().toLowerCase().includes('ok'),
        latency,
      };
    } catch (error) {
      return {
        isHealthy: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Clear cache (for memory management)
   */
  async clearCache(): Promise<void> {
    await aiCacheService.clearAll();
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats() {
    return aiCacheService.getCacheStats();
  }
}

// Export singleton instance
export const aiService = new AIService();
