import { z } from 'zod';
import { AppState, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { aiCacheService } from './ai-cache.service';

// Performance metrics schema
const PerformanceMetricsSchema = z.object({
  memoryUsage: z.number(),
  batteryLevel: z.number().optional(),
  networkType: z.string().optional(),
  appState: z.enum(['active', 'background', 'inactive']),
  timestamp: z.number(),
});

export type PerformanceMetrics = z.infer<typeof PerformanceMetricsSchema>;

// Performance configuration schema
const PerformanceConfigSchema = z.object({
  enableBatteryOptimization: z.boolean().default(true),
  enableMemoryOptimization: z.boolean().default(true),
  enableNetworkOptimization: z.boolean().default(true),
  lowBatteryThreshold: z.number().min(0).max(100).default(20),
  lowMemoryThreshold: z.number().default(0.8), // 80% memory usage
  backgroundProcessingDelay: z.number().default(5000), // 5 seconds
  aggressiveCleanupThreshold: z.number().default(0.9), // 90% memory usage
});

export type PerformanceConfig = z.infer<typeof PerformanceConfigSchema>;

/**
 * Mobile Performance Optimization Service
 * Following Rule 10: Performance Optimization for Mobile Devices
 */
export class MobilePerformanceService {
  private config: PerformanceConfig;
  private appStateSubscription?: any;
  private performanceTimer?: NodeJS.Timeout;
  private currentMetrics: PerformanceMetrics | null = null;
  private isOptimizing = false;

  private readonly STORAGE_KEY = 'mobile_performance_config';
  private readonly METRICS_KEY = 'mobile_performance_metrics';

  constructor(config?: Partial<PerformanceConfig>) {
    this.config = PerformanceConfigSchema.parse(config || {});
    this.initialize();
  }

  /**
   * Initialize performance monitoring
   */
  private async initialize(): Promise<void> {
    try {
      // Load saved configuration
      await this.loadConfiguration();

      // Start app state monitoring
      this.startAppStateMonitoring();

      // Start performance monitoring
      this.startPerformanceMonitoring();

      console.log('Mobile performance optimization initialized');
    } catch (error) {
      console.warn('Failed to initialize mobile performance service:', error);
    }
  }

  /**
   * Load configuration from storage
   */
  private async loadConfiguration(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const config = JSON.parse(stored);
        this.config = PerformanceConfigSchema.parse({ ...this.config, ...config });
      }
    } catch (error) {
      console.warn('Failed to load performance configuration:', error);
    }
  }

  /**
   * Save configuration to storage
   */
  private async saveConfiguration(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save performance configuration:', error);
    }
  }

  /**
   * Start monitoring app state changes
   */
  private startAppStateMonitoring(): void {
    this.appStateSubscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      this.handleAppStateChange(nextAppState);
    });
  }

  /**
   * Handle app state changes for optimization
   */
  private async handleAppStateChange(nextAppState: AppStateStatus): Promise<void> {
    try {
      if (nextAppState === 'background') {
        // App went to background - optimize for battery
        await this.optimizeForBackground();
      } else if (nextAppState === 'active') {
        // App became active - restore performance
        await this.optimizeForForeground();
      }

      // Update current metrics
      if (this.currentMetrics) {
        this.currentMetrics.appState = nextAppState;
      }
    } catch (error) {
      console.warn('Failed to handle app state change:', error);
    }
  }

  /**
   * Start performance monitoring timer
   */
  private startPerformanceMonitoring(): void {
    this.performanceTimer = setInterval(async () => {
      await this.collectMetrics();
      await this.optimizePerformance();
    }, 30000); // Check every 30 seconds
  }

  /**
   * Collect current performance metrics
   */
  private async collectMetrics(): Promise<void> {
    try {
      const metrics: PerformanceMetrics = {
        memoryUsage: this.getMemoryUsage(),
        appState: AppState.currentState,
        timestamp: Date.now(),
      };

      // Add battery level if available (iOS/Android specific)
      try {
        // This would require a native module for accurate battery info
        // For now, we'll use a placeholder
        metrics.batteryLevel = 100; // Placeholder
      } catch {
        // Battery info not available
      }

      this.currentMetrics = metrics;

      // Store metrics for analysis
      await this.storeMetrics(metrics);
    } catch (error) {
      console.warn('Failed to collect performance metrics:', error);
    }
  }

  /**
   * Get estimated memory usage
   */
  private getMemoryUsage(): number {
    // This is an estimation - in a real app, you'd use native modules
    // for accurate memory information
    const cacheStats = aiCacheService.getCacheStats();
    const estimatedUsage = (cacheStats.memoryUsage + cacheStats.persistentUsage) / (1024 * 1024); // MB
    return estimatedUsage;
  }

  /**
   * Store performance metrics
   */
  private async storeMetrics(metrics: PerformanceMetrics): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.METRICS_KEY);
      const history = stored ? JSON.parse(stored) : [];
      
      // Keep only last 100 entries
      history.push(metrics);
      if (history.length > 100) {
        history.splice(0, history.length - 100);
      }

      await AsyncStorage.setItem(this.METRICS_KEY, JSON.stringify(history));
    } catch (error) {
      console.warn('Failed to store performance metrics:', error);
    }
  }

  /**
   * Optimize performance based on current conditions
   */
  private async optimizePerformance(): Promise<void> {
    if (this.isOptimizing || !this.currentMetrics) return;

    this.isOptimizing = true;

    try {
      const { memoryUsage, batteryLevel, appState } = this.currentMetrics;

      // Memory optimization
      if (this.config.enableMemoryOptimization) {
        if (memoryUsage > this.config.aggressiveCleanupThreshold * 100) {
          await this.aggressiveMemoryCleanup();
        } else if (memoryUsage > this.config.lowMemoryThreshold * 100) {
          await this.moderateMemoryCleanup();
        }
      }

      // Battery optimization
      if (this.config.enableBatteryOptimization && batteryLevel) {
        if (batteryLevel < this.config.lowBatteryThreshold) {
          await this.optimizeForLowBattery();
        }
      }

      // Background optimization
      if (appState === 'background') {
        await this.optimizeForBackground();
      }
    } catch (error) {
      console.warn('Performance optimization failed:', error);
    } finally {
      this.isOptimizing = false;
    }
  }

  /**
   * Optimize for background state
   */
  private async optimizeForBackground(): Promise<void> {
    try {
      // Reduce cache cleanup frequency
      // Clear non-essential memory caches
      // Pause non-critical background tasks
      console.log('Optimizing for background state');
    } catch (error) {
      console.warn('Background optimization failed:', error);
    }
  }

  /**
   * Optimize for foreground state
   */
  private async optimizeForForeground(): Promise<void> {
    try {
      // Restore normal cache cleanup frequency
      // Preload frequently accessed data
      console.log('Optimizing for foreground state');
    } catch (error) {
      console.warn('Foreground optimization failed:', error);
    }
  }

  /**
   * Optimize for low battery
   */
  private async optimizeForLowBattery(): Promise<void> {
    try {
      // Reduce AI processing frequency
      // Increase cache TTL to reduce API calls
      // Disable non-essential features
      console.log('Optimizing for low battery');
    } catch (error) {
      console.warn('Low battery optimization failed:', error);
    }
  }

  /**
   * Moderate memory cleanup
   */
  private async moderateMemoryCleanup(): Promise<void> {
    try {
      // Clean up old cache entries
      const cacheStats = aiCacheService.getCacheStats();
      if (cacheStats.memoryUtilization > 70) {
        // Trigger cache cleanup
        console.log('Performing moderate memory cleanup');
      }
    } catch (error) {
      console.warn('Moderate memory cleanup failed:', error);
    }
  }

  /**
   * Aggressive memory cleanup
   */
  private async aggressiveMemoryCleanup(): Promise<void> {
    try {
      // Clear all non-essential caches
      await aiCacheService.clearAll();
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      console.log('Performed aggressive memory cleanup');
    } catch (error) {
      console.warn('Aggressive memory cleanup failed:', error);
    }
  }

  /**
   * Update configuration
   */
  async updateConfiguration(newConfig: Partial<PerformanceConfig>): Promise<void> {
    try {
      this.config = PerformanceConfigSchema.parse({ ...this.config, ...newConfig });
      await this.saveConfiguration();
    } catch (error) {
      console.warn('Failed to update performance configuration:', error);
      throw error;
    }
  }

  /**
   * Get current performance metrics
   */
  getCurrentMetrics(): PerformanceMetrics | null {
    return this.currentMetrics;
  }

  /**
   * Get performance history
   */
  async getPerformanceHistory(): Promise<PerformanceMetrics[]> {
    try {
      const stored = await AsyncStorage.getItem(this.METRICS_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('Failed to get performance history:', error);
      return [];
    }
  }

  /**
   * Get current configuration
   */
  getConfiguration(): PerformanceConfig {
    return { ...this.config };
  }

  /**
   * Cleanup on service destruction
   */
  destroy(): void {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
    
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
    }
  }
}

// Export singleton instance
export const mobilePerformanceService = new MobilePerformanceService();
