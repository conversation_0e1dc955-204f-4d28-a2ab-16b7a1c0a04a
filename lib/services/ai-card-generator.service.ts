/**
 * AI Knowledge Card Generator Service
 * 
 * Generates comprehensive knowledge cards from extracted text using AI
 * Following LearniScan development workflow rules:
 * - Rule 11: Zod validation for all inputs/outputs
 * - Rule 12: Comprehensive error handling with early returns
 * - Rule 5: TypeScript quality and type safety
 * - Rule 10: Performance optimization with caching and streaming
 */

import { z } from 'zod';
import { streamObject } from 'ai';
import { aiModels } from '@/lib/config/ai.config';
import { AIServiceError } from '@/lib/services/ai.service';
import { CreateKnowledgeCardInput } from '@/types/appwrite-v2';
import { permissionsService, PERMISSIONS } from './permissions.service';

// Input validation schema (Rule 11)
const CardGenerationInputSchema = z.object({
  extractedText: z.string().min(10, 'Text must be at least 10 characters'),
  userId: z.string().optional(), // For permission checking
  userRole: z.string().optional(), // For permission checking
  context: z.object({
    subject: z.string().optional(),
    level: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
    focus: z.string().optional(),
    language: z.string().optional(),
  }).optional(),
  options: z.object({
    generateMultiple: z.boolean().default(false),
    maxCards: z.number().min(1).max(5).default(1),
    includeQuestions: z.boolean().default(true),
    enhanceWithAI: z.boolean().default(true),
  }).optional(),
});

// AI-generated card schema (Rule 11)
const AIGeneratedCardSchema = z.object({
  title: z.string().min(3).max(100),
  summary: z.string().min(10).max(500),
  content: z.string().min(20),
  keyPoints: z.array(z.string()).min(1).max(8),
  tags: z.array(z.string()).min(1).max(10),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  category: z.string().min(2).max(50),
  studyQuestions: z.array(z.object({
    question: z.string(),
    answer: z.string(),
    type: z.enum(['multiple-choice', 'short-answer', 'true-false']),
    options: z.array(z.string()).optional(),
  })).max(10),
  estimatedStudyTime: z.number().min(1).max(120), // minutes
  prerequisites: z.array(z.string()).max(5),
  relatedTopics: z.array(z.string()).max(8),
  confidence: z.number().min(0).max(100),
});

export type CardGenerationInput = z.infer<typeof CardGenerationInputSchema>;
export type AIGeneratedCard = z.infer<typeof AIGeneratedCardSchema>;

// Progress callback type
export type CardGenerationProgress = {
  stage: 'analyzing' | 'structuring' | 'enhancing' | 'finalizing';
  progress: number;
  message: string;
  partialCard?: Partial<AIGeneratedCard>;
};

export class AICardGeneratorService {
  private cache = new Map<string, { data: AIGeneratedCard[]; timestamp: number }>();
  private readonly CACHE_TTL = 300000; // 5 minutes

  /**
   * Generate knowledge cards from extracted text using AI
   */
  async generateKnowledgeCards(
    input: CardGenerationInput,
    onProgress?: (progress: CardGenerationProgress) => void
  ): Promise<AIGeneratedCard[]> {
    // Input validation (Rule 11)
    const validatedInput = CardGenerationInputSchema.parse(input);

    // Permission checking for AI generation (Rule 12: Error handling)
    if (validatedInput.userId) {
      const permissionResult = await permissionsService.checkPermissionWithUsage(
        validatedInput.userId,
        PERMISSIONS.AI_GENERATE,
        'ai_generations',
        validatedInput.userRole
      );

      if (!permissionResult.hasPermission) {
        if (permissionResult.upgradeRequired) {
          throw new Error('AI card generation requires Premium subscription');
        }
        throw new Error(`AI generation limit reached: ${permissionResult.reason}`);
      }
    }
    
    // Check cache first (Rule 10: Performance optimization)
    const cacheKey = this.getCacheKey(validatedInput);
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      onProgress?.({
        stage: 'finalizing',
        progress: 100,
        message: 'Retrieved from cache',
      });
      return cached;
    }

    const { extractedText, context, options } = validatedInput;
    const maxCards = options?.maxCards || 1;

    try {
      // Stage 1: Analyzing content
      onProgress?.({
        stage: 'analyzing',
        progress: 10,
        message: 'Analyzing extracted text...',
      });

      const prompt = this.buildGenerationPrompt(extractedText, context, options);

      // Stage 2: Structuring content
      onProgress?.({
        stage: 'structuring',
        progress: 30,
        message: 'Structuring knowledge cards...',
      });

      // Generate single or multiple cards based on options
      const schema = maxCards > 1 
        ? z.object({ cards: z.array(AIGeneratedCardSchema) })
        : AIGeneratedCardSchema;

      const { partialObjectStream } = await streamObject({
        model: aiModels.chat,
        schema,
        messages: [{
          role: 'system',
          content: 'You are an expert educational content creator specializing in creating effective knowledge cards for active learning. Focus on clarity, accuracy, and pedagogical value.'
        }, {
          role: 'user',
          content: prompt
        }],
        temperature: 0.7, // Balanced creativity and consistency
      });

      let finalResult: AIGeneratedCard[] = [];
      let progressValue = 30;

      // Stage 3: Enhancing with streaming updates
      for await (const partialObject of partialObjectStream) {
        progressValue = Math.min(progressValue + 10, 90);
        
        if (partialObject) {
          const cards = maxCards > 1 
            ? (partialObject as { cards: AIGeneratedCard[] }).cards || []
            : [partialObject as AIGeneratedCard];

          // Update progress with partial results
          onProgress?.({
            stage: 'enhancing',
            progress: progressValue,
            message: `Processing card ${cards.length}/${maxCards}...`,
            partialCard: cards[cards.length - 1],
          });

          finalResult = cards;
        }
      }

      // Stage 4: Finalizing
      onProgress?.({
        stage: 'finalizing',
        progress: 95,
        message: 'Finalizing knowledge cards...',
      });

      // Post-process and validate results
      const processedCards = finalResult.map(card => this.postProcessCard(card, extractedText));
      
      // Cache the results (Rule 10)
      this.setCache(cacheKey, processedCards);

      // Track usage after successful generation (Rule 10: Performance optimization)
      if (validatedInput.userId) {
        try {
          await permissionsService.trackUsage(validatedInput.userId, 'ai_generations');
        } catch (error) {
          console.warn('Failed to track AI generation usage:', error);
          // Don't fail the generation if usage tracking fails
        }
      }

      onProgress?.({
        stage: 'finalizing',
        progress: 100,
        message: `Generated ${processedCards.length} knowledge card(s)`,
      });

      return processedCards;

    } catch (error) {
      console.error('AI Card Generation Error:', error);
      this.handleError(error, 'generateKnowledgeCards');
    }
  }

  /**
   * Convert AI-generated card to CreateKnowledgeCardInput format
   */
  convertToCardInput(
    aiCard: AIGeneratedCard,
    userId: string,
    sourceData?: any
  ): CreateKnowledgeCardInput {
    return {
      title: aiCard.title,
      content: this.formatCardContent(aiCard),
      difficulty: aiCard.difficulty,
      category: aiCard.category,
      sourceType: 'ai_generated',
      sourceData: {
        originalText: sourceData?.originalText,
        confidence: aiCard.confidence,
        aiGenerated: true,
        studyQuestions: aiCard.studyQuestions,
        keyPoints: aiCard.keyPoints,
        prerequisites: aiCard.prerequisites,
        relatedTopics: aiCard.relatedTopics,
        estimatedStudyTime: aiCard.estimatedStudyTime,
        ...sourceData,
      },
      tags: aiCard.tags,
      summary: aiCard.summary,
      aiEnhanced: true,
      isPublic: false,
    };
  }

  /**
   * Build AI generation prompt
   */
  private buildGenerationPrompt(
    text: string,
    context?: CardGenerationInput['context'],
    options?: CardGenerationInput['options']
  ): string {
    let prompt = `Create ${options?.maxCards || 1} comprehensive knowledge card(s) from the following text:\n\n${text}\n\n`;

    if (context) {
      prompt += 'Additional context:\n';
      if (context.subject) prompt += `- Subject: ${context.subject}\n`;
      if (context.level) prompt += `- Level: ${context.level}\n`;
      if (context.focus) prompt += `- Focus: ${context.focus}\n`;
      if (context.language) prompt += `- Language: ${context.language}\n`;
      prompt += '\n';
    }

    prompt += 'Requirements:\n';
    prompt += '- Create educational, well-structured knowledge cards\n';
    prompt += '- Include practical study questions for active recall\n';
    prompt += '- Provide accurate difficulty assessment\n';
    prompt += '- Generate relevant tags for categorization\n';
    prompt += '- Estimate realistic study time\n';
    prompt += '- Ensure content is clear and pedagogically sound\n';

    if (options?.generateMultiple && options.maxCards > 1) {
      prompt += `- Create ${options.maxCards} distinct cards covering different aspects\n`;
      prompt += '- Avoid significant overlap between cards\n';
    }

    return prompt;
  }

  /**
   * Format card content with structured sections
   */
  private formatCardContent(card: AIGeneratedCard): string {
    let content = `${card.summary}\n\n`;
    
    content += '## Key Points\n';
    card.keyPoints.forEach((point, index) => {
      content += `${index + 1}. ${point}\n`;
    });
    
    if (card.prerequisites.length > 0) {
      content += '\n## Prerequisites\n';
      card.prerequisites.forEach(prereq => {
        content += `• ${prereq}\n`;
      });
    }
    
    if (card.relatedTopics.length > 0) {
      content += '\n## Related Topics\n';
      card.relatedTopics.forEach(topic => {
        content += `• ${topic}\n`;
      });
    }
    
    content += `\n## Estimated Study Time\n${card.estimatedStudyTime} minutes`;
    
    return content;
  }

  /**
   * Post-process generated card
   */
  private postProcessCard(card: AIGeneratedCard, originalText: string): AIGeneratedCard {
    return {
      ...card,
      // Ensure minimum quality standards
      keyPoints: card.keyPoints.slice(0, 6), // Limit to 6 key points
      studyQuestions: card.studyQuestions.slice(0, 8), // Limit to 8 questions
      tags: [...new Set(card.tags)].slice(0, 8), // Remove duplicates and limit
      // Add metadata
      confidence: Math.min(card.confidence || 85, 95), // Cap confidence at 95%
    };
  }

  /**
   * Cache management
   */
  private getCacheKey(input: CardGenerationInput): string {
    return `card_gen_${JSON.stringify(input)}`;
  }

  private getFromCache(key: string): AIGeneratedCard[] | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    const now = Date.now();
    if (now - cached.timestamp > this.CACHE_TTL) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  private setCache(key: string, data: AIGeneratedCard[]): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  /**
   * Error handling (Rule 12)
   */
  private handleError(error: any, operation: string): never {
    console.error(`AI Card Generator Error (${operation}):`, error);

    // Early return pattern for different error types (Rule 12)
    if (error instanceof z.ZodError) {
      throw new AIServiceError(
        'Invalid input for card generation',
        operation,
        'VALIDATION_ERROR',
        false
      );
    }

    if (error.message?.includes('rate limit')) {
      throw new AIServiceError(
        'AI service is temporarily busy. Please try again in a moment.',
        operation,
        'RATE_LIMIT',
        true
      );
    }

    if (error.message?.includes('token')) {
      throw new AIServiceError(
        'Content is too large for AI processing. Please try with shorter text.',
        operation,
        'TOKEN_LIMIT',
        false
      );
    }

    // Generic error fallback
    throw new AIServiceError(
      `Card generation failed: ${error.message || 'Unknown error'}`,
      operation,
      'UNKNOWN_ERROR',
      false
    );
  }

  /**
   * Clear cache (for memory management)
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Export singleton instance
export const aiCardGenerator = new AICardGeneratorService();
