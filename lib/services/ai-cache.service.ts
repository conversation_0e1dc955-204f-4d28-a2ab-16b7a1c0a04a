import AsyncStorage from '@react-native-async-storage/async-storage';
import { z } from 'zod';

// Cache entry schema for validation (Rule 5: Code Quality and Type Safety)
const CacheEntrySchema = z.object({
  data: z.any(),
  timestamp: z.number(),
  ttl: z.number(),
  size: z.number().optional(),
  accessCount: z.number().default(0),
  lastAccessed: z.number(),
});

export type CacheEntry = z.infer<typeof CacheEntrySchema>;

// Cache configuration schema
const CacheConfigSchema = z.object({
  maxMemorySize: z.number().default(50 * 1024 * 1024), // 50MB
  maxPersistentSize: z.number().default(100 * 1024 * 1024), // 100MB
  defaultTTL: z.number().default(300000), // 5 minutes
  cleanupInterval: z.number().default(600000), // 10 minutes
  compressionThreshold: z.number().default(1024), // 1KB
});

export type CacheConfig = z.infer<typeof CacheConfigSchema>;

/**
 * Enhanced AI Cache Service for Mobile Performance Optimization
 * Following Rule 10: Performance Optimization
 */
export class AICacheService {
  private memoryCache = new Map<string, CacheEntry>();
  private config: CacheConfig;
  private cleanupTimer?: NodeJS.Timeout;
  private memoryUsage = 0;
  private persistentUsage = 0;

  private readonly STORAGE_PREFIX = 'ai_cache_';
  private readonly METADATA_KEY = 'ai_cache_metadata';

  constructor(config?: Partial<CacheConfig>) {
    this.config = CacheConfigSchema.parse(config || {});
    this.startCleanupTimer();
    this.loadCacheMetadata();
  }

  /**
   * Get cache key with operation prefix
   */
  private getCacheKey(operation: string, input: any): string {
    const inputHash = this.hashInput(input);
    return `${operation}_${inputHash}`;
  }

  /**
   * Create hash from input for consistent cache keys
   */
  private hashInput(input: any): string {
    const str = JSON.stringify(input);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Calculate data size in bytes
   */
  private calculateSize(data: any): number {
    return new Blob([JSON.stringify(data)]).size;
  }

  /**
   * Check if entry is expired
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * Get from memory cache first, then persistent cache
   */
  async get<T>(operation: string, input: any): Promise<T | null> {
    const key = this.getCacheKey(operation, input);
    
    try {
      // Check memory cache first
      const memoryEntry = this.memoryCache.get(key);
      if (memoryEntry && !this.isExpired(memoryEntry)) {
        memoryEntry.accessCount++;
        memoryEntry.lastAccessed = Date.now();
        return memoryEntry.data as T;
      }

      // Check persistent cache
      const persistentData = await AsyncStorage.getItem(`${this.STORAGE_PREFIX}${key}`);
      if (persistentData) {
        const entry = CacheEntrySchema.parse(JSON.parse(persistentData));
        
        if (!this.isExpired(entry)) {
          entry.accessCount++;
          entry.lastAccessed = Date.now();
          
          // Promote to memory cache if frequently accessed
          if (entry.accessCount > 3) {
            this.setMemoryCache(key, entry);
          }
          
          // Update persistent cache with new access info
          await AsyncStorage.setItem(
            `${this.STORAGE_PREFIX}${key}`,
            JSON.stringify(entry)
          );
          
          return entry.data as T;
        } else {
          // Remove expired entry
          await AsyncStorage.removeItem(`${this.STORAGE_PREFIX}${key}`);
        }
      }

      return null;
    } catch (error) {
      console.warn('Cache get error:', error);
      return null;
    }
  }

  /**
   * Set cache entry with intelligent storage selection
   */
  async set<T>(
    operation: string, 
    input: any, 
    data: T, 
    ttl: number = this.config.defaultTTL
  ): Promise<void> {
    const key = this.getCacheKey(operation, input);
    const size = this.calculateSize(data);
    const now = Date.now();

    const entry: CacheEntry = {
      data,
      timestamp: now,
      ttl,
      size,
      accessCount: 1,
      lastAccessed: now,
    };

    try {
      // Always try to store in memory cache for fast access
      if (this.memoryUsage + size <= this.config.maxMemorySize) {
        this.setMemoryCache(key, entry);
      }

      // Store in persistent cache for durability
      if (this.persistentUsage + size <= this.config.maxPersistentSize) {
        await AsyncStorage.setItem(
          `${this.STORAGE_PREFIX}${key}`,
          JSON.stringify(entry)
        );
        this.persistentUsage += size;
        await this.updateCacheMetadata();
      } else {
        // Clean up old entries to make space
        await this.cleanupPersistentCache();
        
        // Try again after cleanup
        if (this.persistentUsage + size <= this.config.maxPersistentSize) {
          await AsyncStorage.setItem(
            `${this.STORAGE_PREFIX}${key}`,
            JSON.stringify(entry)
          );
          this.persistentUsage += size;
          await this.updateCacheMetadata();
        }
      }
    } catch (error) {
      console.warn('Cache set error:', error);
    }
  }

  /**
   * Set memory cache with size management
   */
  private setMemoryCache(key: string, entry: CacheEntry): void {
    // Remove old entry if exists
    const oldEntry = this.memoryCache.get(key);
    if (oldEntry?.size) {
      this.memoryUsage -= oldEntry.size;
    }

    // Add new entry
    this.memoryCache.set(key, entry);
    if (entry.size) {
      this.memoryUsage += entry.size;
    }

    // Cleanup if over limit
    if (this.memoryUsage > this.config.maxMemorySize) {
      this.cleanupMemoryCache();
    }
  }

  /**
   * Clean up memory cache using LRU strategy
   */
  private cleanupMemoryCache(): void {
    const entries = Array.from(this.memoryCache.entries())
      .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

    // Remove oldest entries until under limit
    for (const [key, entry] of entries) {
      if (this.memoryUsage <= this.config.maxMemorySize * 0.8) break;
      
      this.memoryCache.delete(key);
      if (entry.size) {
        this.memoryUsage -= entry.size;
      }
    }
  }

  /**
   * Clean up persistent cache
   */
  private async cleanupPersistentCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(this.STORAGE_PREFIX));
      
      const entries: Array<[string, CacheEntry]> = [];
      
      // Load all cache entries
      for (const key of cacheKeys) {
        const data = await AsyncStorage.getItem(key);
        if (data) {
          try {
            const entry = CacheEntrySchema.parse(JSON.parse(data));
            entries.push([key, entry]);
          } catch {
            // Remove invalid entries
            await AsyncStorage.removeItem(key);
          }
        }
      }

      // Sort by last accessed (LRU)
      entries.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

      // Remove oldest entries until under 80% of limit
      const targetSize = this.config.maxPersistentSize * 0.8;
      let currentSize = this.persistentUsage;

      for (const [key, entry] of entries) {
        if (currentSize <= targetSize) break;
        
        await AsyncStorage.removeItem(key);
        if (entry.size) {
          currentSize -= entry.size;
        }
      }

      this.persistentUsage = currentSize;
      await this.updateCacheMetadata();
    } catch (error) {
      console.warn('Persistent cache cleanup error:', error);
    }
  }

  /**
   * Load cache metadata
   */
  private async loadCacheMetadata(): Promise<void> {
    try {
      const metadata = await AsyncStorage.getItem(this.METADATA_KEY);
      if (metadata) {
        const parsed = JSON.parse(metadata);
        this.persistentUsage = parsed.persistentUsage || 0;
      }
    } catch (error) {
      console.warn('Failed to load cache metadata:', error);
      this.persistentUsage = 0;
    }
  }

  /**
   * Update cache metadata
   */
  private async updateCacheMetadata(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.METADATA_KEY, JSON.stringify({
        persistentUsage: this.persistentUsage,
        lastUpdated: Date.now(),
      }));
    } catch (error) {
      console.warn('Failed to update cache metadata:', error);
    }
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupMemoryCache();
      this.cleanupPersistentCache();
    }, this.config.cleanupInterval);
  }

  /**
   * Clear all cache
   */
  async clearAll(): Promise<void> {
    try {
      this.memoryCache.clear();
      this.memoryUsage = 0;
      
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(this.STORAGE_PREFIX));
      await AsyncStorage.multiRemove([...cacheKeys, this.METADATA_KEY]);
      
      this.persistentUsage = 0;
    } catch (error) {
      console.warn('Cache clear error:', error);
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      memoryEntries: this.memoryCache.size,
      memoryUsage: this.memoryUsage,
      memoryLimit: this.config.maxMemorySize,
      persistentUsage: this.persistentUsage,
      persistentLimit: this.config.maxPersistentSize,
      memoryUtilization: (this.memoryUsage / this.config.maxMemorySize) * 100,
      persistentUtilization: (this.persistentUsage / this.config.maxPersistentSize) * 100,
    };
  }

  /**
   * Cleanup on service destruction
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
  }
}

// Export singleton instance
export const aiCacheService = new AICacheService();
