import { ID, Permission, Role } from 'react-native-appwrite';
import { storage } from '@/lib/config/appwrite';
import { APPWRITE_CONFIG } from '@/lib/config/appwrite';
import type { FileUploadProgress, FileUploadResult, AppwriteError } from '@/types/appwrite';

export class StorageService {
  private buckets = APPWRITE_CONFIG.buckets;

  // Upload file with progress tracking
  async uploadFile(
    bucketId: string,
    file: any, // File object from react-native-image-picker or similar
    permissions?: string[],
    onProgress?: (progress: FileUploadProgress) => void
  ): Promise<FileUploadResult> {
    try {
      const fileId = ID.unique();
      
      // Default permissions: user can read/write their own files
      const defaultPermissions = permissions || [
        Permission.read(Role.user(file.userId || 'current')),
        Permission.write(Role.user(file.userId || 'current')),
        Permission.update(Role.user(file.userId || 'current')),
        Permission.delete(Role.user(file.userId || 'current')),
      ];

      const result = await storage.createFile(
        bucketId,
        fileId,
        file,
        defaultPermissions
      );

      return result as FileUploadResult;
    } catch (error) {
      throw this.handleError(error, 'File upload failed');
    }
  }

  // Upload user avatar
  async uploadAvatar(userId: string, file: any): Promise<FileUploadResult> {
    const permissions = [
      Permission.read(Role.user(userId)),
      Permission.write(Role.user(userId)),
      Permission.update(Role.user(userId)),
      Permission.delete(Role.user(userId)),
    ];

    return this.uploadFile(this.buckets.userAvatars, file, permissions);
  }

  // Upload scan image
  async uploadScanImage(
    userId: string, 
    file: any, 
    onProgress?: (progress: FileUploadProgress) => void
  ): Promise<FileUploadResult> {
    const permissions = [
      Permission.read(Role.user(userId)),
      Permission.write(Role.user(userId)),
      Permission.delete(Role.user(userId)),
    ];

    return this.uploadFile(this.buckets.scanImages, file, permissions, onProgress);
  }

  // Upload knowledge card asset
  async uploadCardAsset(userId: string, file: any, isPublic = false): Promise<FileUploadResult> {
    const permissions = isPublic
      ? [
          Permission.read(Role.any()), // Public read
          Permission.write(Role.user(userId)),
          Permission.update(Role.user(userId)),
          Permission.delete(Role.user(userId)),
        ]
      : [
          Permission.read(Role.user(userId)),
          Permission.write(Role.user(userId)),
          Permission.update(Role.user(userId)),
          Permission.delete(Role.user(userId)),
        ];

    return this.uploadFile(this.buckets.cardAssets, file, permissions);
  }

  // Upload user export file
  async uploadExport(userId: string, file: any): Promise<FileUploadResult> {
    const permissions = [
      Permission.read(Role.user(userId)),
      Permission.write(Role.user(userId)),
      Permission.delete(Role.user(userId)),
    ];

    return this.uploadFile(this.buckets.userExports, file, permissions);
  }

  // Get file download URL
  getFileUrl(bucketId: string, fileId: string): string {
    try {
      return storage.getFileDownload(bucketId, fileId);
    } catch (error) {
      throw this.handleError(error, 'Failed to get file URL');
    }
  }

  // Get file preview URL (for images)
  getFilePreview(
    bucketId: string,
    fileId: string,
    width?: number,
    height?: number,
    gravity?: string,
    quality?: number
  ): string {
    try {
      return storage.getFilePreview(
        bucketId,
        fileId,
        width,
        height,
        gravity,
        quality
      );
    } catch (error) {
      throw this.handleError(error, 'Failed to get file preview');
    }
  }

  // Get file view URL
  getFileView(bucketId: string, fileId: string): string {
    try {
      return storage.getFileView(bucketId, fileId);
    } catch (error) {
      throw this.handleError(error, 'Failed to get file view');
    }
  }

  // Delete file
  async deleteFile(bucketId: string, fileId: string): Promise<void> {
    try {
      await storage.deleteFile(bucketId, fileId);
    } catch (error) {
      throw this.handleError(error, 'File deletion failed');
    }
  }

  // Get file metadata
  async getFile(bucketId: string, fileId: string): Promise<any> {
    try {
      return await storage.getFile(bucketId, fileId);
    } catch (error) {
      throw this.handleError(error, 'Failed to get file metadata');
    }
  }

  // List files in bucket
  async listFiles(bucketId: string, queries?: string[]): Promise<any> {
    try {
      return await storage.listFiles(bucketId, queries);
    } catch (error) {
      throw this.handleError(error, 'Failed to list files');
    }
  }

  // Update file metadata
  async updateFile(
    bucketId: string,
    fileId: string,
    name?: string,
    permissions?: string[]
  ): Promise<FileUploadResult> {
    try {
      const result = await storage.updateFile(bucketId, fileId, name, permissions);
      return result as FileUploadResult;
    } catch (error) {
      throw this.handleError(error, 'Failed to update file');
    }
  }

  // Batch upload multiple files
  async uploadMultipleFiles(
    bucketId: string,
    files: any[],
    permissions?: string[],
    onProgress?: (fileIndex: number, progress: FileUploadProgress) => void
  ): Promise<FileUploadResult[]> {
    const results: FileUploadResult[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      try {
        const result = await this.uploadFile(
          bucketId,
          file,
          permissions,
          onProgress ? (progress) => onProgress(i, progress) : undefined
        );
        results.push(result);
      } catch (error) {
        console.error(`Failed to upload file ${i}:`, error);
        // Continue with other files even if one fails
      }
    }
    
    return results;
  }

  // Helper methods for specific file types
  async uploadProfilePicture(userId: string, imageFile: any): Promise<string> {
    try {
      const result = await this.uploadAvatar(userId, imageFile);
      return this.getFilePreview(this.buckets.userAvatars, result.$id, 200, 200);
    } catch (error) {
      throw this.handleError(error, 'Failed to upload profile picture');
    }
  }

  async uploadDocumentScan(
    userId: string, 
    imageFile: any,
    onProgress?: (progress: FileUploadProgress) => void
  ): Promise<{ fileId: string; previewUrl: string; downloadUrl: string }> {
    try {
      const result = await this.uploadScanImage(userId, imageFile, onProgress);
      
      return {
        fileId: result.$id,
        previewUrl: this.getFilePreview(this.buckets.scanImages, result.$id, 800, 600),
        downloadUrl: this.getFileUrl(this.buckets.scanImages, result.$id),
      };
    } catch (error) {
      throw this.handleError(error, 'Failed to upload document scan');
    }
  }

  // Cleanup methods
  async deleteUserFiles(userId: string): Promise<void> {
    try {
      // This would require listing files by user and deleting them
      // Implementation depends on how you track user files
      console.warn('deleteUserFiles not fully implemented - requires file tracking');
    } catch (error) {
      throw this.handleError(error, 'Failed to delete user files');
    }
  }

  async cleanupExpiredExports(): Promise<void> {
    try {
      // Implementation for cleaning up expired export files
      // This could be done via a scheduled function
      console.warn('cleanupExpiredExports not fully implemented - requires scheduled cleanup');
    } catch (error) {
      throw this.handleError(error, 'Failed to cleanup expired exports');
    }
  }

  // Error Handling
  private handleError(error: any, defaultMessage: string): Error {
    if (error.code) {
      const appwriteError = error as AppwriteError;
      switch (appwriteError.code) {
        case 401:
          return new Error('Authentication required. Please sign in again.');
        case 403:
          return new Error('Access denied. You don\'t have permission for this action.');
        case 404:
          return new Error('File not found.');
        case 413:
          return new Error('File too large. Please choose a smaller file.');
        case 415:
          return new Error('Unsupported file type.');
        case 429:
          return new Error('Too many requests. Please try again later.');
        case 500:
          return new Error('Server error. Please try again later.');
        case 503:
          return new Error('Service unavailable. Please check your connection.');
        default:
          return new Error(appwriteError.message || defaultMessage);
      }
    }
    
    return new Error(error.message || defaultMessage);
  }

  // Utility methods
  getImageThumbnail(bucketId: string, fileId: string, size = 150): string {
    return this.getFilePreview(bucketId, fileId, size, size, 'center', 80);
  }

  getOptimizedImage(bucketId: string, fileId: string, width: number, height: number): string {
    return this.getFilePreview(bucketId, fileId, width, height, 'center', 85);
  }

  isImageFile(mimeType: string): boolean {
    return mimeType.startsWith('image/');
  }

  isPDFFile(mimeType: string): boolean {
    return mimeType === 'application/pdf';
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export const storageService = new StorageService();
