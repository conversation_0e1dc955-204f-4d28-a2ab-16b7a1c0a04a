/**
 * Post-Scan Navigation Service
 * 
 * Handles navigation flow after successful scan and knowledge card generation.
 * Provides seamless integration between capture flow and knowledge features.
 */

import { router } from 'expo-router';
import type { CompleteKnowledgeCard } from '@/types/appwrite-v2';

export interface PostScanNavigationOptions {
  /** The newly created knowledge cards */
  createdCards: CompleteKnowledgeCard[];
  /** Original scan data for context */
  scanData: {
    extractedText: string;
    confidence: number;
    language?: string;
  };
  /** Navigation preferences */
  preferences?: {
    /** Default destination after scan */
    defaultDestination?: 'hub' | 'cards' | 'graph';
    /** Whether to highlight new content */
    highlightNew?: boolean;
  };
}

export interface NavigationDestination {
  route: string;
  params?: Record<string, any>;
  title: string;
  description: string;
  icon: string;
}

/**
 * Post-Scan Navigation Service
 * 
 * Manages navigation flow after successful scan and card generation
 */
export class PostScanNavigationService {
  
  /**
   * Get available navigation destinations after scan
   */
  static getNavigationDestinations(options: PostScanNavigationOptions): NavigationDestination[] {
    const { createdCards } = options;
    const cardCount = createdCards.length;
    
    return [
      {
        route: '/(tabs)/knowledge',
        params: { 
          highlightNew: true,
          newCardIds: createdCards.map(card => card.card.$id)
        },
        title: 'Knowledge Hub',
        description: `View your ${cardCount} new card${cardCount > 1 ? 's' : ''} in the central hub`,
        icon: 'brain'
      },
      {
        route: '/(knowledge)/cards',
        params: { 
          highlightNew: true,
          newCardIds: createdCards.map(card => card.card.$id),
          viewMode: 'grid'
        },
        title: 'Knowledge Cards',
        description: `Study your ${cardCount} new card${cardCount > 1 ? 's' : ''} immediately`,
        icon: 'book-open'
      },
      {
        route: '/(knowledge)/graph',
        params: { 
          highlightNew: true,
          newCardIds: createdCards.map(card => card.card.$id)
        },
        title: 'Knowledge Graph',
        description: 'See how your new knowledge connects to existing content',
        icon: 'git-branch'
      }
    ];
  }

  /**
   * Navigate to Knowledge Hub with new card highlighting
   */
  static navigateToKnowledgeHub(options: PostScanNavigationOptions): void {
    const { createdCards } = options;
    
    router.push({
      pathname: '/(tabs)/knowledge',
      params: {
        highlightNew: 'true',
        newCardIds: JSON.stringify(createdCards.map(card => card.card.$id)),
        source: 'scan'
      }
    });
  }

  /**
   * Navigate to Knowledge Cards with new cards highlighted
   */
  static navigateToKnowledgeCards(options: PostScanNavigationOptions): void {
    const { createdCards } = options;
    
    router.push({
      pathname: '/(knowledge)/cards',
      params: {
        highlightNew: 'true',
        newCardIds: JSON.stringify(createdCards.map(card => card.card.$id)),
        viewMode: 'grid',
        source: 'scan'
      }
    });
  }

  /**
   * Navigate to Knowledge Graph with new content integration
   */
  static navigateToKnowledgeGraph(options: PostScanNavigationOptions): void {
    const { createdCards } = options;
    
    router.push({
      pathname: '/(knowledge)/graph',
      params: {
        highlightNew: 'true',
        newCardIds: JSON.stringify(createdCards.map(card => card.card.$id)),
        source: 'scan'
      }
    });
  }

  /**
   * Navigate to specific card for immediate study
   */
  static navigateToSpecificCard(cardId: string): void {
    router.push({
      pathname: '/(knowledge)/cards',
      params: {
        focusCardId: cardId,
        viewMode: 'study',
        source: 'scan'
      }
    });
  }

  /**
   * Get default navigation based on user preferences and context
   */
  static getDefaultNavigation(options: PostScanNavigationOptions): NavigationDestination {
    const destinations = this.getNavigationDestinations(options);
    const { preferences } = options;
    
    // Use user preference if available
    if (preferences?.defaultDestination) {
      const preferred = destinations.find(dest => 
        dest.route.includes(preferences.defaultDestination!)
      );
      if (preferred) return preferred;
    }
    
    // Default to Knowledge Hub for centralized experience
    return destinations[0];
  }

  /**
   * Create navigation parameters for highlighting new content
   */
  static createHighlightParams(cardIds: string[]): Record<string, string> {
    return {
      highlightNew: 'true',
      newCardIds: JSON.stringify(cardIds),
      source: 'scan',
      timestamp: Date.now().toString()
    };
  }
}

export default PostScanNavigationService;
