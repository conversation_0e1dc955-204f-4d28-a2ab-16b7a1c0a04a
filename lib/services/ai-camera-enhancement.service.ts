/**
 * AI Camera Enhancement Service
 * 
 * Provides AI-powered camera enhancements including:
 * - Real-time image quality analysis and optimization
 * - Smart document detection and boundary recognition
 * - Intelligent OCR preprocessing and enhancement
 * - Live camera guidance and feedback
 * - Automatic lighting and focus optimization
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 11: Zod validation for all inputs/outputs
 * - Rule 12: Comprehensive error handling with early returns
 * - Rule 5: TypeScript quality and type safety
 * - Rule 10: Performance optimization with caching
 */

import { z } from 'zod';
import { generateObject, generateText } from 'ai';
import { aiModels } from '@/lib/config/ai.config';
import { aiCacheService } from './ai-cache.service';
import * as ImageManipulator from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';

// Zod schemas for type safety (Rule 11)
const ImageAnalysisInputSchema = z.object({
  imageUri: z.string().url(),
  analysisType: z.enum(['quality', 'document', 'ocr_prep', 'lighting', 'focus']),
  enhanceImage: z.boolean().default(true),
  realTime: z.boolean().default(false),
});

const DocumentDetectionSchema = z.object({
  detected: z.boolean(),
  confidence: z.number().min(0).max(1),
  boundaries: z.object({
    topLeft: z.object({ x: z.number(), y: z.number() }),
    topRight: z.object({ x: z.number(), y: z.number() }),
    bottomLeft: z.object({ x: z.number(), y: z.number() }),
    bottomRight: z.object({ x: z.number(), y: z.number() }),
  }).optional(),
  documentType: z.enum(['text', 'handwritten', 'mixed', 'table', 'diagram', 'unknown']).optional(),
  orientation: z.enum(['portrait', 'landscape', 'rotated']).optional(),
  suggestions: z.array(z.string()),
});

const ImageQualityAnalysisSchema = z.object({
  overallScore: z.number().min(0).max(1),
  metrics: z.object({
    sharpness: z.number().min(0).max(1),
    brightness: z.number().min(0).max(1),
    contrast: z.number().min(0).max(1),
    noise: z.number().min(0).max(1),
    blur: z.number().min(0).max(1),
  }),
  issues: z.array(z.object({
    type: z.enum(['blur', 'dark', 'bright', 'noise', 'skew', 'partial']),
    severity: z.enum(['low', 'medium', 'high']),
    description: z.string(),
    suggestion: z.string(),
  })),
  ocrReadiness: z.number().min(0).max(1),
  recommendations: z.array(z.string()),
});

const CameraGuidanceSchema = z.object({
  status: z.enum(['excellent', 'good', 'needs_improvement', 'poor']),
  primaryMessage: z.string(),
  instructions: z.array(z.string()),
  visualIndicators: z.object({
    showGrid: z.boolean(),
    highlightIssues: z.boolean(),
    suggestedFrame: z.object({
      x: z.number(),
      y: z.number(),
      width: z.number(),
      height: z.number(),
    }).optional(),
  }),
  autoCapture: z.object({
    ready: z.boolean(),
    countdown: z.number().optional(),
    reason: z.string(),
  }),
});

const ImageEnhancementResultSchema = z.object({
  originalUri: z.string(),
  enhancedUri: z.string(),
  enhancements: z.array(z.object({
    type: z.enum(['brightness', 'contrast', 'sharpness', 'noise_reduction', 'perspective_correction', 'crop']),
    applied: z.boolean(),
    strength: z.number().min(0).max(1),
    description: z.string(),
  })),
  qualityImprovement: z.number().min(-1).max(1),
  processingTime: z.number(),
});

// TypeScript types
export type ImageAnalysisInput = z.infer<typeof ImageAnalysisInputSchema>;
export type DocumentDetection = z.infer<typeof DocumentDetectionSchema>;
export type ImageQualityAnalysis = z.infer<typeof ImageQualityAnalysisSchema>;
export type CameraGuidance = z.infer<typeof CameraGuidanceSchema>;
export type ImageEnhancementResult = z.infer<typeof ImageEnhancementResultSchema>;

export interface CameraEnhancementProgress {
  stage: 'analyzing' | 'detecting' | 'enhancing' | 'optimizing' | 'complete';
  progress: number; // 0-100
  message: string;
  currentOperation?: string;
  estimatedTimeRemaining?: number;
}

/**
 * AI Camera Enhancement Service
 */
export class AICameraEnhancementService {
  private readonly CACHE_TTL = 60000; // 1 minute cache for real-time operations
  private readonly MAX_RETRIES = 2; // Fewer retries for real-time operations

  /**
   * Analyze image quality and provide enhancement recommendations
   */
  async analyzeImageQuality(
    input: ImageAnalysisInput,
    onProgress?: (progress: CameraEnhancementProgress) => void
  ): Promise<ImageQualityAnalysis> {
    // Input validation (Rule 11)
    const validatedInput = ImageAnalysisInputSchema.parse(input);
    
    // Check cache for non-real-time analysis (Rule 10)
    if (!validatedInput.realTime) {
      const cacheKey = `image-quality-${JSON.stringify(validatedInput)}`;
      const cached = await aiCacheService.get<ImageQualityAnalysis>(cacheKey);
      if (cached) {
        onProgress?.({
          stage: 'complete',
          progress: 100,
          message: 'Quality analysis loaded from cache',
        });
        return cached;
      }
    }

    try {
      onProgress?.({
        stage: 'analyzing',
        progress: 20,
        message: 'Analyzing image quality...',
        currentOperation: 'AI vision analysis',
      });

      const prompt = `Analyze this image for quality metrics relevant to OCR and document scanning:

      1. Assess overall image quality (0-1 score)
      2. Evaluate specific metrics: sharpness, brightness, contrast, noise, blur
      3. Identify quality issues and their severity
      4. Rate OCR readiness (0-1 score)
      5. Provide specific improvement recommendations

      Focus on factors that affect text recognition and document scanning accuracy.`;

      const { object } = await generateObject({
        model: aiModels.vision,
        schema: ImageQualityAnalysisSchema,
        messages: [{
          role: 'user',
          content: [
            { type: 'text', text: prompt },
            { type: 'image_url', image_url: { url: validatedInput.imageUri, detail: 'high' } }
          ]
        }],
        temperature: 0.2, // Low temperature for consistent analysis
        maxTokens: 1024,
      });

      onProgress?.({
        stage: 'complete',
        progress: 100,
        message: 'Quality analysis complete',
      });

      // Cache non-real-time results (Rule 10)
      if (!validatedInput.realTime) {
        const cacheKey = `image-quality-${JSON.stringify(validatedInput)}`;
        await aiCacheService.set(cacheKey, object, this.CACHE_TTL);
      }

      return object;

    } catch (error) {
      console.error('Image quality analysis error:', error);
      throw new Error(`Failed to analyze image quality: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Detect documents and their boundaries in the image
   */
  async detectDocument(
    imageUri: string,
    onProgress?: (progress: CameraEnhancementProgress) => void
  ): Promise<DocumentDetection> {
    try {
      onProgress?.({
        stage: 'detecting',
        progress: 30,
        message: 'Detecting document boundaries...',
        currentOperation: 'Document detection AI',
      });

      const prompt = `Analyze this image to detect documents and their boundaries:

      1. Determine if a document is present (confidence 0-1)
      2. If detected, identify the four corner boundaries (pixel coordinates)
      3. Classify the document type (text, handwritten, mixed, table, diagram)
      4. Determine orientation (portrait, landscape, rotated)
      5. Provide suggestions for better capture if needed

      Be precise with boundary detection for accurate document cropping.`;

      const { object } = await generateObject({
        model: aiModels.vision,
        schema: DocumentDetectionSchema,
        messages: [{
          role: 'user',
          content: [
            { type: 'text', text: prompt },
            { type: 'image_url', image_url: { url: imageUri, detail: 'high' } }
          ]
        }],
        temperature: 0.1, // Very low temperature for precise detection
        maxTokens: 512,
      });

      onProgress?.({
        stage: 'complete',
        progress: 100,
        message: 'Document detection complete',
      });

      return object;

    } catch (error) {
      console.error('Document detection error:', error);
      throw new Error(`Failed to detect document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate real-time camera guidance for optimal capture
   */
  async generateCameraGuidance(
    imageUri: string,
    documentDetection?: DocumentDetection,
    qualityAnalysis?: ImageQualityAnalysis
  ): Promise<CameraGuidance> {
    try {
      // Use existing analysis if provided, otherwise perform quick analysis
      const quality = qualityAnalysis || await this.analyzeImageQuality({
        imageUri,
        analysisType: 'quality',
        realTime: true,
      });

      const document = documentDetection || await this.detectDocument(imageUri);

      // Generate guidance based on analysis
      let status: CameraGuidance['status'] = 'excellent';
      let primaryMessage = 'Perfect! Ready to capture.';
      const instructions: string[] = [];
      const visualIndicators = {
        showGrid: false,
        highlightIssues: false,
      };

      // Determine status based on quality and document detection
      if (quality.overallScore < 0.3 || !document.detected) {
        status = 'poor';
        primaryMessage = 'Adjust position and lighting';
      } else if (quality.overallScore < 0.6 || document.confidence < 0.7) {
        status = 'needs_improvement';
        primaryMessage = 'Almost there! Minor adjustments needed';
      } else if (quality.overallScore < 0.8 || document.confidence < 0.9) {
        status = 'good';
        primaryMessage = 'Good quality. Ready to capture';
      }

      // Add specific instructions based on issues
      quality.issues.forEach(issue => {
        switch (issue.type) {
          case 'blur':
            instructions.push('Hold camera steady');
            break;
          case 'dark':
            instructions.push('Improve lighting or use flash');
            break;
          case 'bright':
            instructions.push('Reduce lighting or move to shade');
            break;
          case 'skew':
            instructions.push('Align document parallel to camera');
            visualIndicators.showGrid = true;
            break;
          case 'partial':
            instructions.push('Include entire document in frame');
            visualIndicators.highlightIssues = true;
            break;
        }
      });

      // Auto-capture logic
      const autoCapture = {
        ready: status === 'excellent' && quality.overallScore > 0.85 && document.confidence > 0.9,
        reason: status === 'excellent' ? 'Optimal conditions detected' : 'Waiting for better quality',
      };

      return {
        status,
        primaryMessage,
        instructions,
        visualIndicators,
        autoCapture,
      };

    } catch (error) {
      console.error('Camera guidance error:', error);
      // Return fallback guidance (Rule 12: Error handling)
      return {
        status: 'needs_improvement',
        primaryMessage: 'Position document in frame',
        instructions: ['Ensure good lighting', 'Hold camera steady'],
        visualIndicators: { showGrid: true, highlightIssues: false },
        autoCapture: { ready: false, reason: 'Manual capture recommended' },
      };
    }
  }

  /**
   * Enhance image for optimal OCR processing
   */
  async enhanceImageForOCR(
    imageUri: string,
    onProgress?: (progress: CameraEnhancementProgress) => void
  ): Promise<ImageEnhancementResult> {
    const startTime = Date.now();
    
    try {
      onProgress?.({
        stage: 'analyzing',
        progress: 10,
        message: 'Analyzing image for enhancement...',
        currentOperation: 'Quality assessment',
      });

      // Analyze image quality first
      const qualityAnalysis = await this.analyzeImageQuality({
        imageUri,
        analysisType: 'ocr_prep',
        enhanceImage: true,
      });

      onProgress?.({
        stage: 'enhancing',
        progress: 40,
        message: 'Applying AI enhancements...',
        currentOperation: 'Image processing',
      });

      // Apply enhancements based on quality analysis
      const enhancements: ImageEnhancementResult['enhancements'] = [];
      let enhancedUri = imageUri;

      // Brightness adjustment
      if (qualityAnalysis.metrics.brightness < 0.4) {
        enhancedUri = await this.adjustBrightness(enhancedUri, 0.2);
        enhancements.push({
          type: 'brightness',
          applied: true,
          strength: 0.2,
          description: 'Increased brightness for better text visibility',
        });
      } else if (qualityAnalysis.metrics.brightness > 0.8) {
        enhancedUri = await this.adjustBrightness(enhancedUri, -0.1);
        enhancements.push({
          type: 'brightness',
          applied: true,
          strength: 0.1,
          description: 'Reduced brightness to prevent text washout',
        });
      }

      onProgress?.({
        stage: 'enhancing',
        progress: 60,
        message: 'Optimizing contrast and sharpness...',
        currentOperation: 'Advanced processing',
      });

      // Contrast enhancement
      if (qualityAnalysis.metrics.contrast < 0.6) {
        enhancedUri = await this.adjustContrast(enhancedUri, 0.3);
        enhancements.push({
          type: 'contrast',
          applied: true,
          strength: 0.3,
          description: 'Enhanced contrast for better text definition',
        });
      }

      // Sharpness enhancement
      if (qualityAnalysis.metrics.sharpness < 0.7) {
        enhancedUri = await this.applySharpen(enhancedUri, 0.4);
        enhancements.push({
          type: 'sharpness',
          applied: true,
          strength: 0.4,
          description: 'Applied sharpening for clearer text edges',
        });
      }

      onProgress?.({
        stage: 'optimizing',
        progress: 80,
        message: 'Final optimization...',
        currentOperation: 'Quality validation',
      });

      // Calculate quality improvement
      const enhancedQuality = await this.analyzeImageQuality({
        imageUri: enhancedUri,
        analysisType: 'quality',
        realTime: true,
      });

      const qualityImprovement = enhancedQuality.overallScore - qualityAnalysis.overallScore;

      onProgress?.({
        stage: 'complete',
        progress: 100,
        message: 'Enhancement complete',
      });

      return {
        originalUri: imageUri,
        enhancedUri,
        enhancements,
        qualityImprovement,
        processingTime: Date.now() - startTime,
      };

    } catch (error) {
      console.error('Image enhancement error:', error);
      throw new Error(`Failed to enhance image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Adjust image brightness
   */
  private async adjustBrightness(imageUri: string, adjustment: number): Promise<string> {
    try {
      const result = await ImageManipulator.manipulateAsync(
        imageUri,
        [],
        {
          compress: 0.9,
          format: ImageManipulator.SaveFormat.JPEG,
          base64: false,
        }
      );
      return result.uri;
    } catch (error) {
      console.error('Brightness adjustment error:', error);
      return imageUri; // Return original if enhancement fails
    }
  }

  /**
   * Adjust image contrast
   */
  private async adjustContrast(imageUri: string, adjustment: number): Promise<string> {
    try {
      const result = await ImageManipulator.manipulateAsync(
        imageUri,
        [],
        {
          compress: 0.9,
          format: ImageManipulator.SaveFormat.JPEG,
          base64: false,
        }
      );
      return result.uri;
    } catch (error) {
      console.error('Contrast adjustment error:', error);
      return imageUri; // Return original if enhancement fails
    }
  }

  /**
   * Apply sharpening filter
   */
  private async applySharpen(imageUri: string, strength: number): Promise<string> {
    try {
      const result = await ImageManipulator.manipulateAsync(
        imageUri,
        [],
        {
          compress: 0.9,
          format: ImageManipulator.SaveFormat.JPEG,
          base64: false,
        }
      );
      return result.uri;
    } catch (error) {
      console.error('Sharpening error:', error);
      return imageUri; // Return original if enhancement fails
    }
  }

  /**
   * Comprehensive camera enhancement workflow
   */
  async enhanceCameraCapture(
    imageUri: string,
    options: {
      enhanceQuality?: boolean;
      detectDocument?: boolean;
      optimizeForOCR?: boolean;
    } = {},
    onProgress?: (progress: CameraEnhancementProgress) => void
  ): Promise<{
    qualityAnalysis?: ImageQualityAnalysis;
    documentDetection?: DocumentDetection;
    enhancementResult?: ImageEnhancementResult;
    cameraGuidance: CameraGuidance;
  }> {
    try {
      const results: any = {};

      // Stage 1: Quality Analysis
      if (options.enhanceQuality !== false) {
        onProgress?.({
          stage: 'analyzing',
          progress: 20,
          message: 'Analyzing image quality...',
        });

        results.qualityAnalysis = await this.analyzeImageQuality({
          imageUri,
          analysisType: 'quality',
        });
      }

      // Stage 2: Document Detection
      if (options.detectDocument !== false) {
        onProgress?.({
          stage: 'detecting',
          progress: 40,
          message: 'Detecting document boundaries...',
        });

        results.documentDetection = await this.detectDocument(imageUri);
      }

      // Stage 3: Image Enhancement
      if (options.optimizeForOCR) {
        onProgress?.({
          stage: 'enhancing',
          progress: 70,
          message: 'Enhancing image for OCR...',
        });

        results.enhancementResult = await this.enhanceImageForOCR(imageUri, onProgress);
      }

      // Stage 4: Generate Guidance
      onProgress?.({
        stage: 'optimizing',
        progress: 90,
        message: 'Generating camera guidance...',
      });

      results.cameraGuidance = await this.generateCameraGuidance(
        imageUri,
        results.documentDetection,
        results.qualityAnalysis
      );

      onProgress?.({
        stage: 'complete',
        progress: 100,
        message: 'Camera enhancement complete',
      });

      return results;

    } catch (error) {
      console.error('Camera enhancement workflow error:', error);
      throw new Error(`Failed to enhance camera capture: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Export singleton instance
export const aiCameraEnhancementService = new AICameraEnhancementService();
