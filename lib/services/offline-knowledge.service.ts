import AsyncStorage from '@react-native-async-storage/async-storage';
import { z } from 'zod';
import { CompleteKnowledgeCard } from '@/types/appwrite-v2';

// Offline sync status schema
const OfflineSyncStatusSchema = z.object({
  lastSync: z.number(),
  pendingUploads: z.number(),
  pendingDownloads: z.number(),
  totalOfflineCards: z.number(),
  syncInProgress: z.boolean(),
});

export type OfflineSyncStatus = z.infer<typeof OfflineSyncStatusSchema>;

// Offline card schema with sync metadata
const OfflineCardSchema = z.object({
  card: z.any(), // CompleteKnowledgeCard
  syncStatus: z.enum(['synced', 'pending_upload', 'pending_download', 'conflict']),
  lastModified: z.number(),
  offlineCreated: z.boolean().default(false),
  conflictData: z.any().optional(),
});

export type OfflineCard = z.infer<typeof OfflineCardSchema>;

// Offline configuration schema
const OfflineConfigSchema = z.object({
  maxOfflineCards: z.number().default(1000),
  autoSyncEnabled: z.boolean().default(true),
  syncOnWifiOnly: z.boolean().default(false),
  maxStorageSize: z.number().default(100 * 1024 * 1024), // 100MB
  retentionDays: z.number().default(30),
});

export type OfflineConfig = z.infer<typeof OfflineConfigSchema>;

/**
 * Offline Knowledge Cards Service
 * Provides offline access to knowledge cards with intelligent sync
 * Following Rule 10: Performance Optimization and Rule 11: Zod Validation
 */
export class OfflineKnowledgeService {
  private config: OfflineConfig;
  private syncStatus: OfflineSyncStatus;
  private isSyncing = false;

  private readonly STORAGE_KEYS = {
    CARDS: 'offline_knowledge_cards',
    CONFIG: 'offline_knowledge_config',
    SYNC_STATUS: 'offline_knowledge_sync_status',
    METADATA: 'offline_knowledge_metadata',
  };

  constructor(config?: Partial<OfflineConfig>) {
    this.config = OfflineConfigSchema.parse(config || {});
    this.syncStatus = {
      lastSync: 0,
      pendingUploads: 0,
      pendingDownloads: 0,
      totalOfflineCards: 0,
      syncInProgress: false,
    };
    this.initialize();
  }

  /**
   * Initialize offline service
   */
  private async initialize(): Promise<void> {
    try {
      await this.loadConfiguration();
      await this.loadSyncStatus();
      await this.cleanupOldCards();
      console.log('Offline knowledge service initialized');
    } catch (error) {
      console.warn('Failed to initialize offline knowledge service:', error);
    }
  }

  /**
   * Load configuration from storage
   */
  private async loadConfiguration(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.CONFIG);
      if (stored) {
        const config = JSON.parse(stored);
        this.config = OfflineConfigSchema.parse({ ...this.config, ...config });
      }
    } catch (error) {
      console.warn('Failed to load offline configuration:', error);
    }
  }

  /**
   * Load sync status from storage
   */
  private async loadSyncStatus(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.SYNC_STATUS);
      if (stored) {
        this.syncStatus = OfflineSyncStatusSchema.parse(JSON.parse(stored));
      }
    } catch (error) {
      console.warn('Failed to load sync status:', error);
    }
  }

  /**
   * Save sync status to storage
   */
  private async saveSyncStatus(): Promise<void> {
    try {
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.SYNC_STATUS,
        JSON.stringify(this.syncStatus)
      );
    } catch (error) {
      console.warn('Failed to save sync status:', error);
    }
  }

  /**
   * Store knowledge card for offline access
   */
  async storeCardOffline(card: CompleteKnowledgeCard): Promise<void> {
    try {
      const offlineCard: OfflineCard = {
        card,
        syncStatus: 'synced',
        lastModified: Date.now(),
        offlineCreated: false,
      };

      const stored = await this.getStoredCards();
      const existingIndex = stored.findIndex(c => c.card.$id === card.$id);

      if (existingIndex >= 0) {
        stored[existingIndex] = offlineCard;
      } else {
        stored.push(offlineCard);
      }

      // Enforce storage limits
      await this.enforceStorageLimits(stored);

      await AsyncStorage.setItem(this.STORAGE_KEYS.CARDS, JSON.stringify(stored));
      
      // Update sync status
      this.syncStatus.totalOfflineCards = stored.length;
      await this.saveSyncStatus();
    } catch (error) {
      console.warn('Failed to store card offline:', error);
      throw error;
    }
  }

  /**
   * Get all stored offline cards
   */
  private async getStoredCards(): Promise<OfflineCard[]> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.CARDS);
      if (!stored) return [];

      const cards = JSON.parse(stored);
      return cards.map((card: any) => OfflineCardSchema.parse(card));
    } catch (error) {
      console.warn('Failed to get stored cards:', error);
      return [];
    }
  }

  /**
   * Get offline cards with filtering
   */
  async getOfflineCards(filters?: {
    category?: string;
    status?: string;
    searchTerm?: string;
  }): Promise<CompleteKnowledgeCard[]> {
    try {
      const stored = await this.getStoredCards();
      let filtered = stored.map(oc => oc.card);

      if (filters?.category) {
        filtered = filtered.filter(card => card.category === filters.category);
      }

      if (filters?.status) {
        filtered = filtered.filter(card => card.status === filters.status);
      }

      if (filters?.searchTerm) {
        const term = filters.searchTerm.toLowerCase();
        filtered = filtered.filter(card => 
          card.title.toLowerCase().includes(term) ||
          card.content.toLowerCase().includes(term)
        );
      }

      return filtered;
    } catch (error) {
      console.warn('Failed to get offline cards:', error);
      return [];
    }
  }

  /**
   * Create new card offline
   */
  async createCardOffline(cardData: Partial<CompleteKnowledgeCard>): Promise<string> {
    try {
      const tempId = `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const card: CompleteKnowledgeCard = {
        $id: tempId,
        title: cardData.title || 'Untitled',
        content: cardData.content || '',
        category: cardData.category || 'general',
        status: cardData.status || 'active',
        tags: cardData.tags || [],
        difficulty: cardData.difficulty || 'beginner',
        studyCount: 0,
        lastStudied: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: cardData.userId || 'offline_user',
        ...cardData,
      } as CompleteKnowledgeCard;

      const offlineCard: OfflineCard = {
        card,
        syncStatus: 'pending_upload',
        lastModified: Date.now(),
        offlineCreated: true,
      };

      const stored = await this.getStoredCards();
      stored.push(offlineCard);

      await AsyncStorage.setItem(this.STORAGE_KEYS.CARDS, JSON.stringify(stored));

      // Update sync status
      this.syncStatus.totalOfflineCards = stored.length;
      this.syncStatus.pendingUploads++;
      await this.saveSyncStatus();

      return tempId;
    } catch (error) {
      console.warn('Failed to create card offline:', error);
      throw error;
    }
  }

  /**
   * Update card offline
   */
  async updateCardOffline(cardId: string, updates: Partial<CompleteKnowledgeCard>): Promise<void> {
    try {
      const stored = await this.getStoredCards();
      const cardIndex = stored.findIndex(c => c.card.$id === cardId);

      if (cardIndex === -1) {
        throw new Error('Card not found offline');
      }

      const offlineCard = stored[cardIndex];
      offlineCard.card = { ...offlineCard.card, ...updates, updatedAt: new Date().toISOString() };
      offlineCard.lastModified = Date.now();
      
      // Mark as pending upload if not already offline-created
      if (!offlineCard.offlineCreated && offlineCard.syncStatus === 'synced') {
        offlineCard.syncStatus = 'pending_upload';
        this.syncStatus.pendingUploads++;
      }

      stored[cardIndex] = offlineCard;
      await AsyncStorage.setItem(this.STORAGE_KEYS.CARDS, JSON.stringify(stored));
      await this.saveSyncStatus();
    } catch (error) {
      console.warn('Failed to update card offline:', error);
      throw error;
    }
  }

  /**
   * Delete card offline
   */
  async deleteCardOffline(cardId: string): Promise<void> {
    try {
      const stored = await this.getStoredCards();
      const filteredCards = stored.filter(c => c.card.$id !== cardId);

      await AsyncStorage.setItem(this.STORAGE_KEYS.CARDS, JSON.stringify(filteredCards));

      // Update sync status
      this.syncStatus.totalOfflineCards = filteredCards.length;
      await this.saveSyncStatus();
    } catch (error) {
      console.warn('Failed to delete card offline:', error);
      throw error;
    }
  }

  /**
   * Enforce storage limits
   */
  private async enforceStorageLimits(cards: OfflineCard[]): Promise<void> {
    // Sort by last modified (oldest first)
    cards.sort((a, b) => a.lastModified - b.lastModified);

    // Remove oldest cards if over limit
    while (cards.length > this.config.maxOfflineCards) {
      cards.shift();
    }

    // Check storage size (approximate)
    const storageSize = JSON.stringify(cards).length;
    if (storageSize > this.config.maxStorageSize) {
      // Remove oldest cards until under size limit
      while (cards.length > 0 && JSON.stringify(cards).length > this.config.maxStorageSize * 0.8) {
        cards.shift();
      }
    }
  }

  /**
   * Clean up old cards based on retention policy
   */
  private async cleanupOldCards(): Promise<void> {
    try {
      const stored = await this.getStoredCards();
      const cutoffTime = Date.now() - (this.config.retentionDays * 24 * 60 * 60 * 1000);
      
      const filteredCards = stored.filter(card => 
        card.lastModified > cutoffTime || card.syncStatus !== 'synced'
      );

      if (filteredCards.length !== stored.length) {
        await AsyncStorage.setItem(this.STORAGE_KEYS.CARDS, JSON.stringify(filteredCards));
        this.syncStatus.totalOfflineCards = filteredCards.length;
        await this.saveSyncStatus();
      }
    } catch (error) {
      console.warn('Failed to cleanup old cards:', error);
    }
  }

  /**
   * Get sync status
   */
  getSyncStatus(): OfflineSyncStatus {
    return { ...this.syncStatus };
  }

  /**
   * Check if card exists offline
   */
  async hasCardOffline(cardId: string): Promise<boolean> {
    try {
      const stored = await this.getStoredCards();
      return stored.some(c => c.card.$id === cardId);
    } catch {
      return false;
    }
  }

  /**
   * Get offline storage statistics
   */
  async getStorageStats() {
    try {
      const stored = await this.getStoredCards();
      const storageSize = JSON.stringify(stored).length;
      
      return {
        totalCards: stored.length,
        storageSize,
        maxStorageSize: this.config.maxStorageSize,
        utilization: (storageSize / this.config.maxStorageSize) * 100,
        pendingUploads: this.syncStatus.pendingUploads,
        pendingDownloads: this.syncStatus.pendingDownloads,
        lastSync: this.syncStatus.lastSync,
      };
    } catch (error) {
      console.warn('Failed to get storage stats:', error);
      return {
        totalCards: 0,
        storageSize: 0,
        maxStorageSize: this.config.maxStorageSize,
        utilization: 0,
        pendingUploads: 0,
        pendingDownloads: 0,
        lastSync: 0,
      };
    }
  }

  /**
   * Clear all offline data
   */
  async clearOfflineData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        this.STORAGE_KEYS.CARDS,
        this.STORAGE_KEYS.SYNC_STATUS,
        this.STORAGE_KEYS.METADATA,
      ]);

      this.syncStatus = {
        lastSync: 0,
        pendingUploads: 0,
        pendingDownloads: 0,
        totalOfflineCards: 0,
        syncInProgress: false,
      };
    } catch (error) {
      console.warn('Failed to clear offline data:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const offlineKnowledgeService = new OfflineKnowledgeService();
