/**
 * AI Study Session Optimization Service
 * 
 * Provides AI-powered study session optimization including:
 * - Spaced repetition algorithms (SM-2, Anki-style)
 * - Personalized study recommendations
 * - Learning progress tracking and analytics
 * - Adaptive difficulty adjustment
 * - Study session scheduling optimization
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 11: Zod validation for all inputs/outputs
 * - Rule 12: Comprehensive error handling with early returns
 * - Rule 5: TypeScript quality and type safety
 * - Rule 10: Performance optimization with caching
 */

import { z } from 'zod';
import { generateObject, generateText } from 'ai';
import { aiModels } from '@/lib/config/ai.config';
import { aiCacheService } from './ai-cache.service';
import { knowledgeCardsService } from './knowledge-cards.service';
import type { KnowledgeCardV2 } from '@/types/appwrite-v2-fixed';

// Zod schemas for type safety (Rule 11)
const StudySessionInputSchema = z.object({
  userId: z.string(),
  sessionType: z.enum(['review', 'learn', 'practice', 'adaptive']),
  targetDuration: z.number().min(5).max(180), // 5 minutes to 3 hours
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  categories: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  maxCards: z.number().min(1).max(50).default(20),
});

const StudyMetricsSchema = z.object({
  cardId: z.string(),
  userId: z.string(),
  difficulty: z.number().min(0).max(1), // 0 = easy, 1 = hard
  responseTime: z.number().min(0), // milliseconds
  accuracy: z.number().min(0).max(1), // 0-1 scale
  confidence: z.number().min(1).max(5), // 1-5 scale
  timestamp: z.string(),
  sessionId: z.string(),
});

const SpacedRepetitionDataSchema = z.object({
  cardId: z.string(),
  userId: z.string(),
  easeFactor: z.number().min(1.3).max(2.5).default(2.5), // SM-2 algorithm
  interval: z.number().min(1).default(1), // days
  repetitions: z.number().min(0).default(0),
  nextReviewDate: z.string(),
  lastReviewDate: z.string().optional(),
  averageGrade: z.number().min(0).max(5).optional(),
});

const StudyRecommendationSchema = z.object({
  recommendationType: z.enum(['review', 'learn_new', 'practice_weak', 'reinforce_strong']),
  cards: z.array(z.object({
    cardId: z.string(),
    priority: z.number().min(0).max(1),
    reason: z.string(),
    estimatedDifficulty: z.number().min(0).max(1),
    estimatedTime: z.number().min(1).max(30), // minutes
  })),
  totalEstimatedTime: z.number(),
  confidenceScore: z.number().min(0).max(1),
  reasoning: z.string(),
});

const StudySessionResultSchema = z.object({
  sessionId: z.string(),
  userId: z.string(),
  sessionType: z.string(),
  startTime: z.string(),
  endTime: z.string(),
  duration: z.number(), // milliseconds
  cardsStudied: z.number(),
  averageAccuracy: z.number().min(0).max(1),
  averageResponseTime: z.number(),
  difficultyProgression: z.array(z.number()),
  recommendations: z.object({
    nextSessionType: z.string(),
    suggestedBreakTime: z.number(), // minutes
    focusAreas: z.array(z.string()),
    difficultyAdjustment: z.enum(['increase', 'maintain', 'decrease']),
  }),
});

// TypeScript types
export type StudySessionInput = z.infer<typeof StudySessionInputSchema>;
export type StudyMetrics = z.infer<typeof StudyMetricsSchema>;
export type SpacedRepetitionData = z.infer<typeof SpacedRepetitionDataSchema>;
export type StudyRecommendation = z.infer<typeof StudyRecommendationSchema>;
export type StudySessionResult = z.infer<typeof StudySessionResultSchema>;

export interface StudyProgress {
  stage: 'analyzing' | 'selecting' | 'optimizing' | 'complete';
  progress: number; // 0-100
  message: string;
  currentCard?: string;
  estimatedTimeRemaining?: number;
}

/**
 * AI Study Session Optimization Service
 */
export class AIStudySessionService {
  private readonly CACHE_TTL = 300000; // 5 minutes cache
  private readonly MAX_RETRIES = 3;

  /**
   * Generate personalized study session recommendations
   */
  async generateStudySession(
    input: StudySessionInput,
    onProgress?: (progress: StudyProgress) => void
  ): Promise<StudyRecommendation> {
    // Input validation (Rule 11)
    const validatedInput = StudySessionInputSchema.parse(input);
    
    // Check cache first (Rule 10: Performance optimization)
    const cacheKey = `study-session-${JSON.stringify(validatedInput)}`;
    const cached = await aiCacheService.get<StudyRecommendation>(cacheKey);
    if (cached) {
      onProgress?.({
        stage: 'complete',
        progress: 100,
        message: 'Study session loaded from cache',
      });
      return cached;
    }

    try {
      // Stage 1: Analyze user's learning history
      onProgress?.({
        stage: 'analyzing',
        progress: 20,
        message: 'Analyzing your learning history...',
      });

      const userHistory = await this.getUserStudyHistory(validatedInput.userId);
      const spacedRepetitionData = await this.getSpacedRepetitionData(validatedInput.userId);

      // Stage 2: Select optimal cards
      onProgress?.({
        stage: 'selecting',
        progress: 50,
        message: 'Selecting optimal study cards...',
      });

      const availableCards = await this.getAvailableCards(validatedInput);
      const cardPriorities = await this.calculateCardPriorities(
        availableCards,
        userHistory,
        spacedRepetitionData,
        validatedInput
      );

      // Stage 3: AI-powered optimization
      onProgress?.({
        stage: 'optimizing',
        progress: 80,
        message: 'Optimizing study session with AI...',
      });

      const recommendation = await this.generateAIRecommendation(
        cardPriorities,
        userHistory,
        validatedInput
      );

      // Cache the result (Rule 10)
      await aiCacheService.set(cacheKey, recommendation, this.CACHE_TTL);

      onProgress?.({
        stage: 'complete',
        progress: 100,
        message: 'Study session ready!',
      });

      return recommendation;

    } catch (error) {
      console.error('Study session generation error:', error);
      throw new Error(`Failed to generate study session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update spaced repetition data based on study performance
   */
  async updateSpacedRepetition(
    metrics: StudyMetrics
  ): Promise<SpacedRepetitionData> {
    // Input validation (Rule 11)
    const validatedMetrics = StudyMetricsSchema.parse(metrics);

    try {
      // Get current spaced repetition data
      const currentData = await this.getCardSpacedRepetitionData(
        validatedMetrics.cardId,
        validatedMetrics.userId
      );

      // Calculate new values using SM-2 algorithm
      const grade = this.convertAccuracyToGrade(validatedMetrics.accuracy, validatedMetrics.confidence);
      const updatedData = this.calculateSM2Algorithm(currentData, grade);

      // Save updated data
      await this.saveSpacedRepetitionData(updatedData);

      return updatedData;

    } catch (error) {
      console.error('Spaced repetition update error:', error);
      throw new Error(`Failed to update spaced repetition: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Analyze study session performance and provide insights
   */
  async analyzeStudySession(
    sessionMetrics: StudyMetrics[],
    sessionInfo: Omit<StudySessionResult, 'recommendations'>
  ): Promise<StudySessionResult> {
    try {
      // Calculate session statistics
      const averageAccuracy = sessionMetrics.reduce((sum, m) => sum + m.accuracy, 0) / sessionMetrics.length;
      const averageResponseTime = sessionMetrics.reduce((sum, m) => sum + m.responseTime, 0) / sessionMetrics.length;
      const difficultyProgression = sessionMetrics.map(m => m.difficulty);

      // Generate AI-powered recommendations
      const recommendations = await this.generateSessionRecommendations(
        sessionMetrics,
        { ...sessionInfo, averageAccuracy, averageResponseTime, difficultyProgression }
      );

      const result: StudySessionResult = {
        ...sessionInfo,
        averageAccuracy,
        averageResponseTime,
        difficultyProgression,
        recommendations,
      };

      return StudySessionResultSchema.parse(result);

    } catch (error) {
      console.error('Study session analysis error:', error);
      throw new Error(`Failed to analyze study session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user's study history for analysis
   */
  private async getUserStudyHistory(userId: string): Promise<StudyMetrics[]> {
    // Implementation would fetch from AppWrite database
    // For now, return empty array as placeholder
    return [];
  }

  /**
   * Get spaced repetition data for user
   */
  private async getSpacedRepetitionData(userId: string): Promise<SpacedRepetitionData[]> {
    // Implementation would fetch from AppWrite database
    // For now, return empty array as placeholder
    return [];
  }

  /**
   * Get available cards based on input criteria
   */
  private async getAvailableCards(input: StudySessionInput): Promise<KnowledgeCardV2[]> {
    try {
      const result = await knowledgeCardsService.getUserCards(
        input.userId,
        {
          difficulty: input.difficulty,
          category: input.categories?.[0],
          tags: input.tags,
        },
        { limit: input.maxCards * 2 } // Get more cards for better selection
      );

      return result.cards;
    } catch (error) {
      console.error('Error fetching available cards:', error);
      return [];
    }
  }

  /**
   * Calculate priority scores for cards
   */
  private async calculateCardPriorities(
    cards: KnowledgeCardV2[],
    history: StudyMetrics[],
    spacedRepetition: SpacedRepetitionData[],
    input: StudySessionInput
  ): Promise<Array<{ card: KnowledgeCardV2; priority: number; reason: string }>> {
    return cards.map(card => {
      const cardHistory = history.filter(h => h.cardId === card.$id);
      const cardSR = spacedRepetition.find(sr => sr.cardId === card.$id);
      
      let priority = 0.5; // Base priority
      let reason = 'New card';

      // Spaced repetition priority
      if (cardSR && new Date(cardSR.nextReviewDate) <= new Date()) {
        priority += 0.3;
        reason = 'Due for review';
      }

      // Performance-based priority
      if (cardHistory.length > 0) {
        const avgAccuracy = cardHistory.reduce((sum, h) => sum + h.accuracy, 0) / cardHistory.length;
        if (avgAccuracy < 0.7) {
          priority += 0.2;
          reason = 'Needs practice';
        }
      }

      return { card, priority: Math.min(priority, 1), reason };
    });
  }

  /**
   * Generate AI-powered study recommendations
   */
  private async generateAIRecommendation(
    cardPriorities: Array<{ card: KnowledgeCardV2; priority: number; reason: string }>,
    history: StudyMetrics[],
    input: StudySessionInput
  ): Promise<StudyRecommendation> {
    try {
      const prompt = `Generate optimal study session recommendations based on:
      
      Session Type: ${input.sessionType}
      Target Duration: ${input.targetDuration} minutes
      Max Cards: ${input.maxCards}
      
      Available Cards: ${cardPriorities.length}
      User History: ${history.length} previous sessions
      
      Prioritize cards that:
      1. Are due for spaced repetition review
      2. Have low accuracy scores (need practice)
      3. Match the session type and difficulty
      4. Fit within the target duration
      
      Provide specific reasoning for card selection and session structure.`;

      const { object } = await generateObject({
        model: aiModels.chat,
        schema: StudyRecommendationSchema,
        messages: [{
          role: 'system',
          content: 'You are an expert learning scientist specializing in spaced repetition and personalized study optimization. Create study sessions that maximize learning efficiency and retention.'
        }, {
          role: 'user',
          content: prompt
        }],
        temperature: 0.3, // Low temperature for consistent recommendations
      });

      return object;

    } catch (error) {
      // Fallback recommendation if AI fails (Rule 12: Error handling)
      const topCards = cardPriorities
        .sort((a, b) => b.priority - a.priority)
        .slice(0, input.maxCards)
        .map(cp => ({
          cardId: cp.card.$id,
          priority: cp.priority,
          reason: cp.reason,
          estimatedDifficulty: 0.5,
          estimatedTime: Math.ceil(input.targetDuration / input.maxCards),
        }));

      return {
        recommendationType: 'review',
        cards: topCards,
        totalEstimatedTime: input.targetDuration,
        confidenceScore: 0.7,
        reasoning: 'Fallback recommendation based on card priorities',
      };
    }
  }

  /**
   * SM-2 Spaced Repetition Algorithm Implementation
   */
  private calculateSM2Algorithm(
    currentData: SpacedRepetitionData,
    grade: number // 0-5 scale
  ): SpacedRepetitionData {
    let { easeFactor, interval, repetitions } = currentData;

    if (grade >= 3) {
      // Correct response
      if (repetitions === 0) {
        interval = 1;
      } else if (repetitions === 1) {
        interval = 6;
      } else {
        interval = Math.round(interval * easeFactor);
      }
      repetitions += 1;
    } else {
      // Incorrect response
      repetitions = 0;
      interval = 1;
    }

    // Update ease factor
    easeFactor = easeFactor + (0.1 - (5 - grade) * (0.08 + (5 - grade) * 0.02));
    easeFactor = Math.max(1.3, easeFactor); // Minimum ease factor

    const nextReviewDate = new Date();
    nextReviewDate.setDate(nextReviewDate.getDate() + interval);

    return {
      ...currentData,
      easeFactor,
      interval,
      repetitions,
      nextReviewDate: nextReviewDate.toISOString(),
      lastReviewDate: new Date().toISOString(),
      averageGrade: currentData.averageGrade 
        ? (currentData.averageGrade + grade) / 2 
        : grade,
    };
  }

  /**
   * Convert accuracy and confidence to SM-2 grade (0-5)
   */
  private convertAccuracyToGrade(accuracy: number, confidence: number): number {
    // Combine accuracy and confidence into SM-2 grade
    const baseGrade = accuracy * 4; // 0-4 from accuracy
    const confidenceBonus = (confidence - 3) * 0.5; // -1 to +1 from confidence
    return Math.max(0, Math.min(5, Math.round(baseGrade + confidenceBonus)));
  }

  /**
   * Get spaced repetition data for a specific card
   */
  private async getCardSpacedRepetitionData(
    cardId: string,
    userId: string
  ): Promise<SpacedRepetitionData> {
    // Implementation would fetch from AppWrite database
    // For now, return default data
    return {
      cardId,
      userId,
      easeFactor: 2.5,
      interval: 1,
      repetitions: 0,
      nextReviewDate: new Date().toISOString(),
    };
  }

  /**
   * Save spaced repetition data
   */
  private async saveSpacedRepetitionData(data: SpacedRepetitionData): Promise<void> {
    // Implementation would save to AppWrite database
    console.log('Saving spaced repetition data:', data);
  }

  /**
   * Generate session-end recommendations
   */
  private async generateSessionRecommendations(
    metrics: StudyMetrics[],
    sessionInfo: any
  ): Promise<StudySessionResult['recommendations']> {
    // Simple heuristic-based recommendations
    // In a full implementation, this would use AI analysis
    
    const avgAccuracy = sessionInfo.averageAccuracy;
    let difficultyAdjustment: 'increase' | 'maintain' | 'decrease' = 'maintain';
    
    if (avgAccuracy > 0.85) {
      difficultyAdjustment = 'increase';
    } else if (avgAccuracy < 0.65) {
      difficultyAdjustment = 'decrease';
    }

    return {
      nextSessionType: avgAccuracy > 0.8 ? 'learn' : 'review',
      suggestedBreakTime: sessionInfo.duration > 1800000 ? 15 : 5, // 15 min for long sessions
      focusAreas: ['review', 'practice'],
      difficultyAdjustment,
    };
  }
}

// Export singleton instance
export const aiStudySessionService = new AIStudySessionService();
