/**
 * Permissions Service - Role-Based Access Control
 * 
 * Implements comprehensive permission management for LearniScan's freemium model:
 * - Role-based feature access control
 * - Usage limit enforcement
 * - Upgrade prompt triggers
 * - Cross-device usage tracking
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 11: Zod validation for all inputs/outputs
 * - Rule 12: Comprehensive error handling with early returns
 * - Rule 5: TypeScript quality and type safety
 * - Rule 10: Performance optimization with caching
 */

import { z } from 'zod';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { appwriteService } from './appwrite.service';
import {
  GUEST_LIMITS,
  AUTHENTICATED_LIMITS,
  PREMIUM_LIMITS,
  getLimitsForRole,
  isUnlimited,
  TESTING_CONFIG,
  logCurrentLimits
} from '../config/permissions-limits.config';

// User roles and permissions configuration
export const USER_ROLES = {
  GUEST: 'guest',
  AUTHENTICATED: 'authenticated', 
  PREMIUM: 'premium',
  ADMIN: 'admin'
} as const;

export const PERMISSIONS = {
  // Document Scanning
  BASIC_SCAN: 'scan:basic',
  AI_CAMERA: 'scan:ai_camera',
  BATCH_PROCESS: 'scan:batch',
  
  // Knowledge Cards
  CREATE_CARDS: 'cards:create',
  AI_GENERATE: 'cards:ai_generate',
  UNLIMITED_CARDS: 'cards:unlimited',
  
  // AI Features
  AI_SEARCH: 'ai:search',
  AI_STUDY: 'ai:study',
  AI_GRAPH: 'ai:graph',
  AI_CHAT: 'ai:chat',
  
  // Storage & Sync
  CLOUD_SYNC: 'storage:cloud',
  UNLIMITED_STORAGE: 'storage:unlimited',
  
  // Premium Features
  PRIORITY_SUPPORT: 'premium:support',
  ADVANCED_ANALYTICS: 'premium:analytics',
  API_ACCESS: 'premium:api'
} as const;

// Zod schemas for validation (Rule 11)
const UserRoleSchema = z.enum(['guest', 'authenticated', 'premium', 'admin']);
const PermissionSchema = z.string();
const UsageTrackingSchema = z.object({
  userId: z.string(),
  feature: z.string(),
  date: z.string(),
  usage: z.number().min(0),
  limit: z.number().min(-1), // -1 means unlimited
});

type UserRole = z.infer<typeof UserRoleSchema>;
type Permission = z.infer<typeof PermissionSchema>;
type UsageTracking = z.infer<typeof UsageTrackingSchema>;

export interface PermissionCheckResult {
  hasPermission: boolean;
  reason?: string;
  upgradeRequired?: boolean;
  usageInfo?: {
    current: number;
    limit: number;
    remaining: number;
  };
}

export class PermissionsService {
  private static instance: PermissionsService;
  private cache = new Map<string, any>();
  private readonly CACHE_TTL = 300000; // 5 minutes

  static getInstance(): PermissionsService {
    if (!PermissionsService.instance) {
      PermissionsService.instance = new PermissionsService();
    }
    return PermissionsService.instance;
  }

  /**
   * Check if user has specific permission
   */
  hasPermission(permission: Permission, userRole?: UserRole): boolean {
    try {
      const validatedRole = UserRoleSchema.parse(userRole || 'guest');
      const validatedPermission = PermissionSchema.parse(permission);
      
      switch (validatedRole) {
        case USER_ROLES.GUEST:
          return this.getGuestPermissions().includes(validatedPermission);
        case USER_ROLES.AUTHENTICATED:
          return this.getAuthenticatedPermissions().includes(validatedPermission);
        case USER_ROLES.PREMIUM:
          return this.getPremiumPermissions().includes(validatedPermission);
        case USER_ROLES.ADMIN:
          return true; // Admin has all permissions
        default:
          return false;
      }
    } catch (error) {
      console.error('Permission check error:', error);
      return false; // Fail closed for security
    }
  }

  /**
   * Comprehensive permission check with usage limits
   */
  async checkPermissionWithUsage(
    userId: string,
    permission: Permission,
    feature: string,
    userRole?: UserRole
  ): Promise<PermissionCheckResult> {
    try {
      const role = UserRoleSchema.parse(userRole || 'guest');
      
      // Check basic permission first
      const hasBasicPermission = this.hasPermission(permission, role);
      if (!hasBasicPermission) {
        return {
          hasPermission: false,
          reason: 'Permission denied for user role',
          upgradeRequired: role !== USER_ROLES.PREMIUM
        };
      }

      // Check usage limits
      const limit = this.getUsageLimit(feature, role);
      if (limit === -1) {
        // Unlimited access
        return { hasPermission: true };
      }

      const currentUsage = await this.getCurrentUsage(userId, feature);
      const remaining = Math.max(0, limit - currentUsage);

      if (currentUsage >= limit) {
        return {
          hasPermission: false,
          reason: 'Usage limit exceeded',
          upgradeRequired: role !== USER_ROLES.PREMIUM,
          usageInfo: {
            current: currentUsage,
            limit,
            remaining: 0
          }
        };
      }

      return {
        hasPermission: true,
        usageInfo: {
          current: currentUsage,
          limit,
          remaining
        }
      };

    } catch (error) {
      console.error('Permission check with usage error:', error);
      return {
        hasPermission: false,
        reason: 'Permission check failed'
      };
    }
  }

  /**
   * Get usage limits for specific features
   */
  getUsageLimit(feature: string, userRole?: UserRole): number {
    const role = userRole || 'guest';

    // Log current limits in development/testing mode
    if (__DEV__ && TESTING_CONFIG.enableTestingMode) {
      logCurrentLimits();
    }

    // Get limits from configuration
    let limits;
    switch (role) {
      case USER_ROLES.GUEST:
        limits = GUEST_LIMITS;
        break;
      case USER_ROLES.AUTHENTICATED:
        limits = AUTHENTICATED_LIMITS;
        break;
      case USER_ROLES.PREMIUM:
      case USER_ROLES.ADMIN:
        limits = PREMIUM_LIMITS;
        break;
      default:
        limits = GUEST_LIMITS; // Fail safe to most restrictive
    }

    // Map feature names to limit properties
    const featureMap: Record<string, keyof typeof limits> = {
      daily_scans: 'dailyScans',
      total_cards: 'totalCards',
      ai_searches: 'aiSearches',
      ai_generations: 'aiGenerations',
      storage_mb: 'storageMb',
    };

    const limitKey = featureMap[feature];
    return limitKey ? limits[limitKey] : 0;
  }

  /**
   * Track feature usage
   */
  async trackUsage(userId: string, feature: string): Promise<boolean> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const key = `usage_${userId}_${feature}_${today}`;
      
      // Update local storage
      const currentUsage = await AsyncStorage.getItem(key);
      const usage = currentUsage ? parseInt(currentUsage) : 0;
      const newUsage = usage + 1;
      
      await AsyncStorage.setItem(key, newUsage.toString());
      
      // Sync to cloud for cross-device tracking
      await this.syncUsageToCloud(userId, feature, newUsage);
      
      // Clear cache to force refresh
      this.cache.delete(`usage_${userId}_${feature}`);
      
      return true;
    } catch (error) {
      console.error('Usage tracking error:', error);
      return false;
    }
  }

  /**
   * Get current usage for a feature
   */
  private async getCurrentUsage(userId: string, feature: string): Promise<number> {
    try {
      const cacheKey = `usage_${userId}_${feature}`;
      
      // Check cache first (Rule 10: Performance optimization)
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.CACHE_TTL) {
          return cached.value;
        }
      }

      const today = new Date().toISOString().split('T')[0];
      const key = `usage_${userId}_${feature}_${today}`;
      
      const currentUsage = await AsyncStorage.getItem(key);
      const usage = currentUsage ? parseInt(currentUsage) : 0;
      
      // Cache the result
      this.cache.set(cacheKey, {
        value: usage,
        timestamp: Date.now()
      });
      
      return usage;
    } catch (error) {
      console.error('Get current usage error:', error);
      return 0;
    }
  }

  /**
   * Sync usage data to cloud
   */
  private async syncUsageToCloud(userId: string, feature: string, usage: number): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const limit = this.getUsageLimit(feature, 'authenticated'); // Default limit for sync

      await appwriteService.permissions.createOrUpdateUsageTracking({
        userId,
        feature,
        date: today,
        usage,
        limit,
        resetPeriod: 'daily',
      });
    } catch (error) {
      // Fail silently for usage tracking to not block user actions
      console.log('Cloud usage sync failed:', error);
    }
  }

  /**
   * Get permissions for guest users
   */
  private getGuestPermissions(): Permission[] {
    return [
      PERMISSIONS.BASIC_SCAN, // Limited to 5 per day
    ];
  }

  /**
   * Get permissions for authenticated users
   */
  private getAuthenticatedPermissions(): Permission[] {
    return [
      ...this.getGuestPermissions(),
      PERMISSIONS.CREATE_CARDS,
      PERMISSIONS.AI_GENERATE, // Limited to 20 per day
      PERMISSIONS.AI_SEARCH, // Limited to 10 per day
      PERMISSIONS.CLOUD_SYNC, // Limited to 1GB
    ];
  }

  /**
   * Get permissions for premium users
   */
  private getPremiumPermissions(): Permission[] {
    return [
      ...this.getAuthenticatedPermissions(),
      PERMISSIONS.AI_CAMERA,
      PERMISSIONS.BATCH_PROCESS,
      PERMISSIONS.UNLIMITED_CARDS,
      PERMISSIONS.AI_STUDY,
      PERMISSIONS.AI_GRAPH,
      PERMISSIONS.AI_CHAT,
      PERMISSIONS.UNLIMITED_STORAGE,
      PERMISSIONS.PRIORITY_SUPPORT,
      PERMISSIONS.ADVANCED_ANALYTICS,
      PERMISSIONS.API_ACCESS,
    ];
  }

  /**
   * Clear usage cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get user's current plan status
   */
  getUserPlanInfo(userRole: UserRole): {
    plan: string;
    features: string[];
    limits: Record<string, number>;
  } {
    const features = {
      [USER_ROLES.GUEST]: this.getGuestPermissions(),
      [USER_ROLES.AUTHENTICATED]: this.getAuthenticatedPermissions(),
      [USER_ROLES.PREMIUM]: this.getPremiumPermissions(),
      [USER_ROLES.ADMIN]: this.getPremiumPermissions()
    };

    const limits = {
      [USER_ROLES.GUEST]: {
        daily_scans: 5,
        total_cards: 10,
        storage_mb: 0
      },
      [USER_ROLES.AUTHENTICATED]: {
        daily_scans: 50,
        total_cards: 100,
        ai_searches: 10,
        storage_mb: 1024
      },
      [USER_ROLES.PREMIUM]: {
        daily_scans: -1,
        total_cards: -1,
        ai_searches: -1,
        storage_mb: -1
      }
    };

    return {
      plan: userRole,
      features: features[userRole] || [],
      limits: limits[userRole] || {}
    };
  }
}

// Export singleton instance
export const permissionsService = PermissionsService.getInstance();
