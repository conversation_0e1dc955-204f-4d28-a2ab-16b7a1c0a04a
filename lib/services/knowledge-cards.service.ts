/**
 * Knowledge Cards Service - Comprehensive CRUD Operations
 * 
 * This service provides a high-level interface for knowledge cards management:
 * - Full CRUD operations with proper validation
 * - User-specific data access and permissions
 * - Learning progress tracking and spaced repetition
 * - Search and filtering capabilities
 * - Batch operations for efficiency
 * 
 * Following Rule 11: State Management Architecture
 * Following Rule 12: Validation & Error Handling
 */

import { z } from "zod";
import { databaseServiceV2 } from "./database-v2.service";
import { enhancedAuthService } from "./enhanced-auth.service";
import { permissionsService, PERMISSIONS } from './permissions.service';
import type {
  CompleteKnowledgeCard,
  CreateKnowledgeCardInput,
  UpdateKnowledgeCardInput,
  KnowledgeCardQuery,
  ReviewSessionInput
} from "@/types/appwrite-v2-fixed";

// Zod schemas for validation (Rule 11)
const KnowledgeCardFiltersSchema = z.object({
  category: z.string().optional(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  status: z.enum(['active', 'archived', 'deleted']).optional(),
  tags: z.array(z.string()).optional(),
  searchTerm: z.string().optional(),
  isPublic: z.boolean().optional(),
  dateRange: z.object({
    from: z.string().optional(),
    to: z.string().optional(),
  }).optional(),
});

const PaginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  orderBy: z.enum(['created', 'updated', 'title', 'nextReview']).default('created'),
  orderDirection: z.enum(['asc', 'desc']).default('desc'),
});

const BatchOperationSchema = z.object({
  cardIds: z.array(z.string()).min(1),
  operation: z.enum(['delete', 'archive', 'activate', 'makePublic', 'makePrivate']),
});

const StudySessionSchema = z.object({
  cardIds: z.array(z.string()).min(1),
  sessionType: z.enum(['review', 'study', 'practice']).default('review'),
  timeLimit: z.number().min(60).max(3600).optional(), // 1 minute to 1 hour
});

// TypeScript types
type KnowledgeCardFilters = z.infer<typeof KnowledgeCardFiltersSchema>;
type PaginationOptions = z.infer<typeof PaginationSchema>;
type BatchOperation = z.infer<typeof BatchOperationSchema>;
type StudySession = z.infer<typeof StudySessionSchema>;

interface KnowledgeCardsResponse {
  cards: CompleteKnowledgeCard[];
  total: number;
  page: number;
  totalPages: number;
  hasMore: boolean;
}

interface UserStatistics {
  totalCards: number;
  cardsByDifficulty: Record<string, number>;
  cardsByCategory: Record<string, number>;
  cardsByStatus: Record<string, number>;
  reviewsDue: number;
  streakDays: number;
  lastStudySession: string | null;
}

export class KnowledgeCardsService {
  /**
   * Create a new knowledge card
   */
  async createCard(userId: string, input: CreateKnowledgeCardInput, userRole?: string): Promise<CompleteKnowledgeCard> {
    try {
      // Validate input
      if (!userId) {
        throw new Error('User ID is required');
      }

      // Check permission and usage limits (Rule 12: Error handling)
      const permissionResult = await permissionsService.checkPermissionWithUsage(
        userId,
        PERMISSIONS.CREATE_CARDS,
        'total_cards',
        userRole
      );

      if (!permissionResult.hasPermission) {
        if (permissionResult.upgradeRequired) {
          throw new Error('Card creation limit reached. Upgrade to Premium for unlimited cards!');
        }
        throw new Error(`Card creation failed: ${permissionResult.reason}`);
      }

      // Create the knowledge card using v2 service
      const knowledgeCard = await databaseServiceV2.createKnowledgeCard(userId, input);

      // Track usage after successful creation (Rule 10: Performance optimization)
      try {
        await permissionsService.trackUsage(userId, 'total_cards');
      } catch (error) {
        console.warn('Failed to track card creation usage:', error);
        // Don't fail the operation if usage tracking fails
      }

      // Update user statistics
      try {
        await enhancedAuthService.incrementCardCount(userId);
        await enhancedAuthService.updateStreakDays(userId);
      } catch (statsError) {
        console.warn('Failed to update user statistics:', statsError);
        // Don't fail the whole operation if stats update fails
      }

      return knowledgeCard;
    } catch (error) {
      throw new Error(`Failed to create knowledge card: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get a knowledge card by ID
   */
  async getCard(cardId: string): Promise<CompleteKnowledgeCard | null> {
    try {
      return await databaseServiceV2.getKnowledgeCard(cardId);
    } catch (error) {
      throw new Error(`Failed to get knowledge card: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user's knowledge cards with filtering and pagination
   */
  async getUserCards(
    userId: string, 
    filters: KnowledgeCardFilters = {}, 
    pagination: Partial<PaginationOptions> = {}
  ): Promise<KnowledgeCardsResponse> {
    try {
      // Validate inputs
      const validatedFilters = KnowledgeCardFiltersSchema.parse(filters);
      const validatedPagination = PaginationSchema.parse(pagination);

      // Build query
      const query: KnowledgeCardQuery = {
        userId,
        ...validatedFilters,
        orderBy: validatedPagination.orderBy,
        orderDirection: validatedPagination.orderDirection,
        limit: validatedPagination.limit,
        offset: (validatedPagination.page - 1) * validatedPagination.limit,
      };

      // Execute query
      const response = await databaseServiceV2.queryKnowledgeCards(query);

      // Calculate pagination info
      const totalPages = Math.ceil(response.total / validatedPagination.limit);
      const hasMore = validatedPagination.page < totalPages;

      return {
        cards: response.cards,
        total: response.total,
        page: validatedPagination.page,
        totalPages,
        hasMore,
      };
    } catch (error) {
      throw new Error(`Failed to get user cards: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update a knowledge card
   */
  async updateCard(cardId: string, updates: UpdateKnowledgeCardInput): Promise<CompleteKnowledgeCard | null> {
    try {
      return await databaseServiceV2.updateKnowledgeCard(cardId, updates);
    } catch (error) {
      throw new Error(`Failed to update knowledge card: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete a knowledge card
   */
  async deleteCard(cardId: string): Promise<boolean> {
    try {
      return await databaseServiceV2.deleteKnowledgeCard(cardId);
    } catch (error) {
      throw new Error(`Failed to delete knowledge card: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get cards due for review
   */
  /**
   * Get cards due for review
   */
  async getCardsDueForReview(userId: string, limit: number = 20): Promise<CompleteKnowledgeCard[]> {
    try {
      const response = await databaseServiceV2.getCardsDueForReview({
        userId,
        beforeDate: new Date().toISOString(),
        limit: limit * 2, // Get more to account for filtering
      });

      // Filter by user ID and limit results
      const userCards = response.cards
        .filter(card => card.card.userId === userId)
        .slice(0, limit);

      return userCards;
    } catch (error) {
      throw new Error(`Failed to get cards due for review: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Record a review session for a card
   */
  async recordReview(cardId: string, review: ReviewSessionInput): Promise<void> {
    try {
      await databaseServiceV2.recordReviewSession(review);
    } catch (error) {
      throw new Error(`Failed to record review: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Search knowledge cards
   */
  async searchCards(
    userId: string, 
    searchTerm: string, 
    filters: Omit<KnowledgeCardFilters, 'searchTerm'> = {},
    pagination: Partial<PaginationOptions> = {}
  ): Promise<KnowledgeCardsResponse> {
    try {
      return await this.getUserCards(
        userId, 
        { ...filters, searchTerm }, 
        pagination
      );
    } catch (error) {
      throw new Error(`Failed to search cards: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get cards by category
   */
  async getCardsByCategory(
    userId: string, 
    category: string, 
    pagination: Partial<PaginationOptions> = {}
  ): Promise<KnowledgeCardsResponse> {
    try {
      return await this.getUserCards(userId, { category }, pagination);
    } catch (error) {
      throw new Error(`Failed to get cards by category: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get cards by difficulty
   */
  async getCardsByDifficulty(
    userId: string, 
    difficulty: 'beginner' | 'intermediate' | 'advanced', 
    pagination: Partial<PaginationOptions> = {}
  ): Promise<KnowledgeCardsResponse> {
    try {
      return await this.getUserCards(userId, { difficulty }, pagination);
    } catch (error) {
      throw new Error(`Failed to get cards by difficulty: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get cards by tags
   */
  async getCardsByTags(
    userId: string, 
    tags: string[], 
    pagination: Partial<PaginationOptions> = {}
  ): Promise<KnowledgeCardsResponse> {
    try {
      return await this.getUserCards(userId, { tags }, pagination);
    } catch (error) {
      throw new Error(`Failed to get cards by tags: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Batch operations on multiple cards
   */
  async batchOperation(userId: string, operation: BatchOperation): Promise<{ success: string[]; failed: string[] }> {
    try {
      const validatedOperation = BatchOperationSchema.parse(operation);
      const results = { success: [], failed: [] };

      for (const cardId of validatedOperation.cardIds) {
        try {
          // Verify card belongs to user
          const card = await this.getCard(cardId);
          if (!card || card.card.userId !== userId) {
            results.failed.push(cardId);
            continue;
          }

          switch (validatedOperation.operation) {
            case 'delete':
              await this.deleteCard(cardId);
              break;
            case 'archive':
              await this.updateCard(cardId, { status: 'archived' });
              break;
            case 'activate':
              await this.updateCard(cardId, { status: 'active' });
              break;
            case 'makePublic':
              await this.updateCard(cardId, { isPublic: true });
              break;
            case 'makePrivate':
              await this.updateCard(cardId, { isPublic: false });
              break;
          }

          results.success.push(cardId);
        } catch (error) {
          console.error(`Batch operation failed for card ${cardId}:`, error);
          results.failed.push(cardId);
        }
      }

      return results;
    } catch (error) {
      throw new Error(`Failed to perform batch operation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user statistics
   */
  async getUserStatistics(userId: string): Promise<UserStatistics> {
    try {
      // Get all user cards (using max allowed limit)
      const allCardsResponse = await this.getUserCards(userId, {}, {
        limit: 100,
        page: 1,
        orderBy: 'created',
        orderDirection: 'desc'
      }); // Get cards with max limit
      const cards = allCardsResponse.cards;

      // Calculate statistics
      const stats: UserStatistics = {
        totalCards: cards.length,
        cardsByDifficulty: {
          beginner: 0,
          intermediate: 0,
          advanced: 0,
        },
        cardsByCategory: {},
        cardsByStatus: {
          active: 0,
          archived: 0,
          deleted: 0,
        },
        reviewsDue: 0,
        streakDays: 0,
        lastStudySession: null,
      };

      // Process each card
      for (const card of cards) {
        // Count by difficulty
        stats.cardsByDifficulty[card.card.difficulty] = 
          (stats.cardsByDifficulty[card.card.difficulty] || 0) + 1;

        // Count by category
        stats.cardsByCategory[card.card.category] = 
          (stats.cardsByCategory[card.card.category] || 0) + 1;

        // Count by status
        stats.cardsByStatus[card.card.status] = 
          (stats.cardsByStatus[card.card.status] || 0) + 1;

        // Count reviews due
        if (card.isReviewDue) {
          stats.reviewsDue++;
        }
      }

      // Get user profile for streak info
      try {
        const userProfile = await enhancedAuthService.getUserProfile(userId);
        if (userProfile) {
          stats.streakDays = userProfile.learningStats.streakDays;
          stats.lastStudySession = userProfile.learningStats.lastActive;
        }
      } catch (error) {
        console.warn('Failed to get user profile for statistics:', error);
      }

      return stats;
    } catch (error) {
      throw new Error(`Failed to get user statistics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create a study session
   */
  async createStudySession(userId: string, session: StudySession): Promise<CompleteKnowledgeCard[]> {
    try {
      const validatedSession = StudySessionSchema.parse(session);
      
      // Get the cards for the study session
      const cards: CompleteKnowledgeCard[] = [];
      for (const cardId of validatedSession.cardIds) {
        const card = await this.getCard(cardId);
        if (card && card.card.userId === userId) {
          cards.push(card);
        }
      }

      // Update user's last active timestamp
      try {
        await enhancedAuthService.updateStreakDays(userId);
      } catch (error) {
        console.warn('Failed to update user streak:', error);
      }

      return cards;
    } catch (error) {
      throw new Error(`Failed to create study session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get recommended cards for study
   */
  async getRecommendedCards(userId: string, limit: number = 10): Promise<CompleteKnowledgeCard[]> {
    try {
      // Get cards due for review first
      const reviewCards = await this.getCardsDueForReview(userId, limit);
      
      if (reviewCards.length >= limit) {
        return reviewCards.slice(0, limit);
      }

      // If not enough review cards, get recent cards
      const recentCards = await this.getUserCards(userId, {}, { 
        limit: limit - reviewCards.length,
        page: 1,
        orderBy: 'created',
        orderDirection: 'desc'
      });

      // Combine and deduplicate
      const allCards = [...reviewCards];
      const reviewCardIds = new Set(reviewCards.map(card => card.card.$id));
      
      for (const card of recentCards.cards) {
        if (!reviewCardIds.has(card.card.$id)) {
          allCards.push(card);
        }
      }

      return allCards.slice(0, limit);
    } catch (error) {
      throw new Error(`Failed to get recommended cards: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export user's knowledge cards
   */
  async exportCards(userId: string, format: 'json' | 'csv' = 'json'): Promise<string> {
    try {
      const allCards = await this.getUserCards(userId, {}, {
        limit: 100,
        page: 1, 
        orderBy: 'created', 
        orderDirection: 'desc' 
      });
      
      if (format === 'json') {
        return JSON.stringify(allCards.cards, null, 2);
      } else if (format === 'csv') {
        // Simple CSV export
        const headers = ['ID', 'Title', 'Category', 'Difficulty', 'Status', 'Created', 'Tags'];
        const rows = allCards.cards.map(card => [
          card.card.$id,
          card.card.title,
          card.card.category,
          card.card.difficulty,
          card.card.status,
          card.card.$createdAt,
          card.tagNames?.join(';') || ''
        ]);
        
        return [headers, ...rows].map(row => row.join(',')).join('\n');
      }

      throw new Error('Unsupported export format');
    } catch (error) {
      throw new Error(`Failed to export cards: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

export const knowledgeCardsService = new KnowledgeCardsService();