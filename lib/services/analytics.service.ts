import { z } from 'zod';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { CompleteKnowledgeCard } from '@/types/appwrite-v2';

// Analytics event schema
const AnalyticsEventSchema = z.object({
  id: z.string(),
  type: z.enum([
    'ai_ocr_request',
    'ai_translation_request', 
    'ai_card_generation',
    'ai_search_query',
    'ai_chat_message',
    'voice_input_used',
    'knowledge_card_created',
    'knowledge_card_studied',
    'study_session_completed',
    'app_opened',
    'app_backgrounded',
    'feature_used',
    'error_occurred',
    'performance_metric'
  ]),
  timestamp: z.number(),
  userId: z.string(),
  sessionId: z.string(),
  data: z.record(z.any()),
  metadata: z.object({
    platform: z.string(),
    appVersion: z.string(),
    deviceInfo: z.record(z.string()).optional(),
  }),
});

export type AnalyticsEvent = z.infer<typeof AnalyticsEventSchema>;

// Learning progress schema
const LearningProgressSchema = z.object({
  userId: z.string(),
  totalStudyTime: z.number(), // in minutes
  totalCards: z.number(),
  totalSessions: z.number(),
  averageSessionDuration: z.number(),
  accuracyRate: z.number(),
  streakDays: z.number(),
  lastStudyDate: z.string(),
  categoryProgress: z.record(z.object({
    cardsStudied: z.number(),
    averageAccuracy: z.number(),
    timeSpent: z.number(),
  })),
  difficultyProgress: z.record(z.object({
    cardsStudied: z.number(),
    averageAccuracy: z.number(),
    timeSpent: z.number(),
  })),
  weeklyGoals: z.object({
    studyMinutes: z.number(),
    cardsToStudy: z.number(),
    sessionsToComplete: z.number(),
  }),
  achievements: z.array(z.object({
    id: z.string(),
    name: z.string(),
    description: z.string(),
    unlockedAt: z.string(),
    category: z.string(),
  })),
});

export type LearningProgress = z.infer<typeof LearningProgressSchema>;

// AI usage analytics schema
const AIUsageAnalyticsSchema = z.object({
  totalRequests: z.number(),
  requestsByType: z.record(z.number()),
  averageResponseTime: z.number(),
  successRate: z.number(),
  errorRate: z.number(),
  costEstimate: z.number(),
  cacheHitRate: z.number(),
  popularFeatures: z.array(z.object({
    feature: z.string(),
    usageCount: z.number(),
    lastUsed: z.string(),
  })),
  performanceMetrics: z.object({
    averageOCRTime: z.number(),
    averageTranslationTime: z.number(),
    averageCardGenerationTime: z.number(),
    averageSearchTime: z.number(),
  }),
});

export type AIUsageAnalytics = z.infer<typeof AIUsageAnalyticsSchema>;

/**
 * Comprehensive Analytics Service
 * Following Rule 11: State Management Architecture
 */
export class AnalyticsService {
  private events: AnalyticsEvent[] = [];
  private currentSessionId: string;
  private userId: string = 'anonymous';
  private isInitialized = false;

  private readonly STORAGE_KEYS = {
    EVENTS: 'analytics_events',
    LEARNING_PROGRESS: 'learning_progress',
    AI_USAGE: 'ai_usage_analytics',
    SESSION_DATA: 'session_data',
  };

  private readonly MAX_STORED_EVENTS = 1000;
  private readonly BATCH_SIZE = 50;

  constructor() {
    this.currentSessionId = this.generateSessionId();
    this.initialize();
  }

  /**
   * Initialize analytics service
   */
  private async initialize(): Promise<void> {
    try {
      await this.loadStoredEvents();
      await this.startSession();
      this.isInitialized = true;
      console.log('Analytics service initialized');
    } catch (error) {
      console.warn('Failed to initialize analytics service:', error);
    }
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Load stored events from AsyncStorage
   */
  private async loadStoredEvents(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.EVENTS);
      if (stored) {
        const events = JSON.parse(stored);
        this.events = events.map((event: any) => AnalyticsEventSchema.parse(event));
      }
    } catch (error) {
      console.warn('Failed to load stored analytics events:', error);
      this.events = [];
    }
  }

  /**
   * Save events to AsyncStorage
   */
  private async saveEvents(): Promise<void> {
    try {
      // Keep only the most recent events to prevent storage bloat
      const eventsToStore = this.events.slice(-this.MAX_STORED_EVENTS);
      await AsyncStorage.setItem(this.STORAGE_KEYS.EVENTS, JSON.stringify(eventsToStore));
    } catch (error) {
      console.warn('Failed to save analytics events:', error);
    }
  }

  /**
   * Start new session
   */
  private async startSession(): Promise<void> {
    await this.trackEvent('app_opened', {
      sessionStartTime: Date.now(),
      previousSessionId: this.currentSessionId,
    });
  }

  /**
   * Set user ID for analytics
   */
  setUserId(userId: string): void {
    this.userId = userId;
  }

  /**
   * Track analytics event
   */
  async trackEvent(
    type: AnalyticsEvent['type'],
    data: Record<string, any> = {},
    metadata?: Partial<AnalyticsEvent['metadata']>
  ): Promise<void> {
    try {
      const event: AnalyticsEvent = {
        id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type,
        timestamp: Date.now(),
        userId: this.userId,
        sessionId: this.currentSessionId,
        data,
        metadata: {
          platform: 'react-native',
          appVersion: '1.0.0', // This should come from app config
          ...metadata,
        },
      };

      this.events.push(event);

      // Save events periodically
      if (this.events.length % this.BATCH_SIZE === 0) {
        await this.saveEvents();
      }
    } catch (error) {
      console.warn('Failed to track analytics event:', error);
    }
  }

  /**
   * Track AI service usage
   */
  async trackAIUsage(
    serviceType: 'ocr' | 'translation' | 'card_generation' | 'search' | 'chat',
    requestData: {
      inputSize?: number;
      outputSize?: number;
      processingTime: number;
      success: boolean;
      errorMessage?: string;
      cacheHit?: boolean;
    }
  ): Promise<void> {
    await this.trackEvent(`ai_${serviceType}_request` as AnalyticsEvent['type'], {
      serviceType,
      ...requestData,
    });
  }

  /**
   * Track learning activity
   */
  async trackLearningActivity(
    activityType: 'card_created' | 'card_studied' | 'session_completed',
    data: {
      cardId?: string;
      category?: string;
      difficulty?: string;
      studyTime?: number;
      accuracy?: number;
      sessionData?: any;
    }
  ): Promise<void> {
    await this.trackEvent(`knowledge_${activityType}` as AnalyticsEvent['type'], data);
    
    // Update learning progress
    await this.updateLearningProgress(activityType, data);
  }

  /**
   * Track feature usage
   */
  async trackFeatureUsage(
    featureName: string,
    context: Record<string, any> = {}
  ): Promise<void> {
    await this.trackEvent('feature_used', {
      featureName,
      context,
    });
  }

  /**
   * Track performance metrics
   */
  async trackPerformance(
    metricName: string,
    value: number,
    unit: string = 'ms',
    context: Record<string, any> = {}
  ): Promise<void> {
    await this.trackEvent('performance_metric', {
      metricName,
      value,
      unit,
      context,
    });
  }

  /**
   * Track errors
   */
  async trackError(
    error: Error | string,
    context: Record<string, any> = {}
  ): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : undefined;

    await this.trackEvent('error_occurred', {
      errorMessage,
      errorStack,
      context,
    });
  }

  /**
   * Update learning progress
   */
  private async updateLearningProgress(
    activityType: string,
    data: any
  ): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.LEARNING_PROGRESS);
      let progress: LearningProgress;

      if (stored) {
        progress = LearningProgressSchema.parse(JSON.parse(stored));
      } else {
        progress = {
          userId: this.userId,
          totalStudyTime: 0,
          totalCards: 0,
          totalSessions: 0,
          averageSessionDuration: 0,
          accuracyRate: 0,
          streakDays: 0,
          lastStudyDate: new Date().toISOString(),
          categoryProgress: {},
          difficultyProgress: {},
          weeklyGoals: {
            studyMinutes: 60,
            cardsToStudy: 20,
            sessionsToComplete: 5,
          },
          achievements: [],
        };
      }

      // Update progress based on activity
      switch (activityType) {
        case 'card_created':
          progress.totalCards++;
          break;
        case 'card_studied':
          if (data.studyTime) {
            progress.totalStudyTime += data.studyTime;
          }
          if (data.category) {
            if (!progress.categoryProgress[data.category]) {
              progress.categoryProgress[data.category] = {
                cardsStudied: 0,
                averageAccuracy: 0,
                timeSpent: 0,
              };
            }
            progress.categoryProgress[data.category].cardsStudied++;
            if (data.studyTime) {
              progress.categoryProgress[data.category].timeSpent += data.studyTime;
            }
          }
          break;
        case 'session_completed':
          progress.totalSessions++;
          if (data.sessionData) {
            progress.averageSessionDuration = 
              (progress.averageSessionDuration * (progress.totalSessions - 1) + data.sessionData.duration) / 
              progress.totalSessions;
          }
          break;
      }

      progress.lastStudyDate = new Date().toISOString();
      
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.LEARNING_PROGRESS,
        JSON.stringify(progress)
      );
    } catch (error) {
      console.warn('Failed to update learning progress:', error);
    }
  }

  /**
   * Get learning progress
   */
  async getLearningProgress(): Promise<LearningProgress | null> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.LEARNING_PROGRESS);
      if (stored) {
        return LearningProgressSchema.parse(JSON.parse(stored));
      }
      return null;
    } catch (error) {
      console.warn('Failed to get learning progress:', error);
      return null;
    }
  }

  /**
   * Get AI usage analytics
   */
  async getAIUsageAnalytics(): Promise<AIUsageAnalytics> {
    try {
      const aiEvents = this.events.filter(event => 
        event.type.startsWith('ai_') && event.type.endsWith('_request')
      );

      const totalRequests = aiEvents.length;
      const requestsByType: Record<string, number> = {};
      let totalResponseTime = 0;
      let successCount = 0;
      let cacheHits = 0;

      aiEvents.forEach(event => {
        const serviceType = event.data.serviceType || event.type;
        requestsByType[serviceType] = (requestsByType[serviceType] || 0) + 1;
        
        if (event.data.processingTime) {
          totalResponseTime += event.data.processingTime;
        }
        
        if (event.data.success) {
          successCount++;
        }
        
        if (event.data.cacheHit) {
          cacheHits++;
        }
      });

      const analytics: AIUsageAnalytics = {
        totalRequests,
        requestsByType,
        averageResponseTime: totalRequests > 0 ? totalResponseTime / totalRequests : 0,
        successRate: totalRequests > 0 ? (successCount / totalRequests) * 100 : 0,
        errorRate: totalRequests > 0 ? ((totalRequests - successCount) / totalRequests) * 100 : 0,
        costEstimate: this.calculateCostEstimate(aiEvents),
        cacheHitRate: totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0,
        popularFeatures: this.getPopularFeatures(),
        performanceMetrics: {
          averageOCRTime: this.getAverageTimeForService('ocr'),
          averageTranslationTime: this.getAverageTimeForService('translation'),
          averageCardGenerationTime: this.getAverageTimeForService('card_generation'),
          averageSearchTime: this.getAverageTimeForService('search'),
        },
      };

      return analytics;
    } catch (error) {
      console.warn('Failed to get AI usage analytics:', error);
      return {
        totalRequests: 0,
        requestsByType: {},
        averageResponseTime: 0,
        successRate: 0,
        errorRate: 0,
        costEstimate: 0,
        cacheHitRate: 0,
        popularFeatures: [],
        performanceMetrics: {
          averageOCRTime: 0,
          averageTranslationTime: 0,
          averageCardGenerationTime: 0,
          averageSearchTime: 0,
        },
      };
    }
  }

  /**
   * Calculate estimated cost for AI usage
   */
  private calculateCostEstimate(aiEvents: AnalyticsEvent[]): number {
    // Simplified cost calculation based on service usage
    const costPerRequest = {
      ocr: 0.002, // $0.002 per OCR request
      translation: 0.001, // $0.001 per translation
      card_generation: 0.005, // $0.005 per card generation
      search: 0.0005, // $0.0005 per search
      chat: 0.003, // $0.003 per chat message
    };

    return aiEvents.reduce((total, event) => {
      const serviceType = event.data.serviceType as keyof typeof costPerRequest;
      return total + (costPerRequest[serviceType] || 0.001);
    }, 0);
  }

  /**
   * Get popular features based on usage
   */
  private getPopularFeatures(): Array<{ feature: string; usageCount: number; lastUsed: string }> {
    const featureUsage: Record<string, { count: number; lastUsed: number }> = {};

    this.events
      .filter(event => event.type === 'feature_used')
      .forEach(event => {
        const feature = event.data.featureName;
        if (!featureUsage[feature]) {
          featureUsage[feature] = { count: 0, lastUsed: 0 };
        }
        featureUsage[feature].count++;
        featureUsage[feature].lastUsed = Math.max(featureUsage[feature].lastUsed, event.timestamp);
      });

    return Object.entries(featureUsage)
      .map(([feature, data]) => ({
        feature,
        usageCount: data.count,
        lastUsed: new Date(data.lastUsed).toISOString(),
      }))
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, 10);
  }

  /**
   * Get average processing time for a specific service
   */
  private getAverageTimeForService(serviceType: string): number {
    const serviceEvents = this.events.filter(event => 
      event.data.serviceType === serviceType && event.data.processingTime
    );

    if (serviceEvents.length === 0) return 0;

    const totalTime = serviceEvents.reduce((sum, event) => sum + event.data.processingTime, 0);
    return totalTime / serviceEvents.length;
  }

  /**
   * Get session analytics
   */
  getSessionAnalytics() {
    const sessionEvents = this.events.filter(event => event.sessionId === this.currentSessionId);
    const sessionStart = Math.min(...sessionEvents.map(e => e.timestamp));
    const sessionDuration = Date.now() - sessionStart;

    return {
      sessionId: this.currentSessionId,
      duration: sessionDuration,
      eventCount: sessionEvents.length,
      featuresUsed: [...new Set(sessionEvents
        .filter(e => e.type === 'feature_used')
        .map(e => e.data.featureName)
      )],
      aiRequestsCount: sessionEvents.filter(e => e.type.startsWith('ai_')).length,
      errorsCount: sessionEvents.filter(e => e.type === 'error_occurred').length,
    };
  }

  /**
   * Export analytics data
   */
  async exportAnalyticsData(): Promise<{
    events: AnalyticsEvent[];
    learningProgress: LearningProgress | null;
    aiUsageAnalytics: AIUsageAnalytics;
    sessionAnalytics: any;
  }> {
    const learningProgress = await this.getLearningProgress();
    const aiUsageAnalytics = await this.getAIUsageAnalytics();
    const sessionAnalytics = this.getSessionAnalytics();

    return {
      events: this.events,
      learningProgress,
      aiUsageAnalytics,
      sessionAnalytics,
    };
  }

  /**
   * Clear analytics data
   */
  async clearAnalyticsData(): Promise<void> {
    try {
      this.events = [];
      await AsyncStorage.multiRemove([
        this.STORAGE_KEYS.EVENTS,
        this.STORAGE_KEYS.LEARNING_PROGRESS,
        this.STORAGE_KEYS.AI_USAGE,
        this.STORAGE_KEYS.SESSION_DATA,
      ]);
    } catch (error) {
      console.warn('Failed to clear analytics data:', error);
    }
  }

  /**
   * End current session
   */
  async endSession(): Promise<void> {
    await this.trackEvent('app_backgrounded', {
      sessionEndTime: Date.now(),
      sessionDuration: this.getSessionAnalytics().duration,
    });
    
    await this.saveEvents();
    this.currentSessionId = this.generateSessionId();
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService();
