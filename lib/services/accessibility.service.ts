import { z } from 'zod';
import { AccessibilityInfo, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Speech from 'expo-speech';
import * as Haptics from 'expo-haptics';

// Accessibility preferences schema
const AccessibilityPreferencesSchema = z.object({
  screenReaderEnabled: z.boolean().default(false),
  highContrastMode: z.boolean().default(false),
  largeTextMode: z.boolean().default(false),
  reduceMotion: z.boolean().default(false),
  voiceOverEnabled: z.boolean().default(false),
  hapticsEnabled: z.boolean().default(true),
  audioDescriptions: z.boolean().default(false),
  textToSpeechRate: z.number().min(0.1).max(2.0).default(1.0),
  textToSpeechPitch: z.number().min(0.5).max(2.0).default(1.0),
  textToSpeechLanguage: z.string().default('en-US'),
  colorBlindnessType: z.enum(['none', 'protanopia', 'deuteranopia', 'tritanopia']).default('none'),
});

export type AccessibilityPreferences = z.infer<typeof AccessibilityPreferencesSchema>;

// Accessibility state schema
const AccessibilityStateSchema = z.object({
  isScreenReaderEnabled: z.boolean(),
  isReduceMotionEnabled: z.boolean(),
  isReduceTransparencyEnabled: z.boolean(),
  isBoldTextEnabled: z.boolean(),
  isGrayscaleEnabled: z.boolean(),
  isInvertColorsEnabled: z.boolean(),
  prefersCrossFadeTransitions: z.boolean(),
});

export type AccessibilityState = z.infer<typeof AccessibilityStateSchema>;

/**
 * Comprehensive Accessibility Service
 * Following Rule 12: Validation and Error Handling
 * Provides accessibility features for vision and hearing impaired users
 */
export class AccessibilityService {
  private preferences: AccessibilityPreferences;
  private systemState: AccessibilityState;
  private listeners: Map<string, (state: AccessibilityState) => void> = new Map();
  private speechQueue: string[] = [];
  private isSpeaking = false;

  private readonly STORAGE_KEY = 'accessibility_preferences';

  constructor() {
    this.preferences = AccessibilityPreferencesSchema.parse({});
    this.systemState = {
      isScreenReaderEnabled: false,
      isReduceMotionEnabled: false,
      isReduceTransparencyEnabled: false,
      isBoldTextEnabled: false,
      isGrayscaleEnabled: false,
      isInvertColorsEnabled: false,
      prefersCrossFadeTransitions: false,
    };

    this.initialize();
  }

  /**
   * Initialize accessibility service
   */
  private async initialize(): Promise<void> {
    try {
      await this.loadPreferences();
      await this.detectSystemAccessibilitySettings();
      this.setupAccessibilityListeners();
      console.log('Accessibility service initialized');
    } catch (error) {
      console.warn('Failed to initialize accessibility service:', error);
    }
  }

  /**
   * Load user preferences from storage
   */
  private async loadPreferences(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const preferences = JSON.parse(stored);
        this.preferences = AccessibilityPreferencesSchema.parse({
          ...this.preferences,
          ...preferences,
        });
      }
    } catch (error) {
      console.warn('Failed to load accessibility preferences:', error);
    }
  }

  /**
   * Save user preferences to storage
   */
  private async savePreferences(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.preferences));
    } catch (error) {
      console.warn('Failed to save accessibility preferences:', error);
    }
  }

  /**
   * Detect system accessibility settings
   */
  private async detectSystemAccessibilitySettings(): Promise<void> {
    try {
      const [
        isScreenReaderEnabled,
        isReduceMotionEnabled,
        isReduceTransparencyEnabled,
        isBoldTextEnabled,
        isGrayscaleEnabled,
        isInvertColorsEnabled,
        prefersCrossFadeTransitions,
      ] = await Promise.all([
        AccessibilityInfo.isScreenReaderEnabled(),
        AccessibilityInfo.isReduceMotionEnabled(),
        AccessibilityInfo.isReduceTransparencyEnabled(),
        AccessibilityInfo.isBoldTextEnabled(),
        AccessibilityInfo.isGrayscaleEnabled(),
        AccessibilityInfo.isInvertColorsEnabled(),
        AccessibilityInfo.prefersCrossFadeTransitions(),
      ]);

      this.systemState = {
        isScreenReaderEnabled,
        isReduceMotionEnabled,
        isReduceTransparencyEnabled,
        isBoldTextEnabled,
        isGrayscaleEnabled,
        isInvertColorsEnabled,
        prefersCrossFadeTransitions,
      };

      // Update preferences based on system settings
      this.preferences.screenReaderEnabled = isScreenReaderEnabled;
      this.preferences.reduceMotion = isReduceMotionEnabled;
      this.preferences.largeTextMode = isBoldTextEnabled;

      this.notifyListeners();
    } catch (error) {
      console.warn('Failed to detect system accessibility settings:', error);
    }
  }

  /**
   * Setup accessibility event listeners
   */
  private setupAccessibilityListeners(): void {
    try {
      AccessibilityInfo.addEventListener('screenReaderChanged', (isEnabled) => {
        this.systemState.isScreenReaderEnabled = isEnabled;
        this.preferences.screenReaderEnabled = isEnabled;
        this.notifyListeners();
      });

      AccessibilityInfo.addEventListener('reduceMotionChanged', (isEnabled) => {
        this.systemState.isReduceMotionEnabled = isEnabled;
        this.preferences.reduceMotion = isEnabled;
        this.notifyListeners();
      });

      AccessibilityInfo.addEventListener('boldTextChanged', (isEnabled) => {
        this.systemState.isBoldTextEnabled = isEnabled;
        this.preferences.largeTextMode = isEnabled;
        this.notifyListeners();
      });
    } catch (error) {
      console.warn('Failed to setup accessibility listeners:', error);
    }
  }

  /**
   * Speak text using text-to-speech
   */
  async speak(text: string, options?: {
    interrupt?: boolean;
    rate?: number;
    pitch?: number;
    language?: string;
  }): Promise<void> {
    try {
      if (!this.preferences.audioDescriptions && !this.preferences.screenReaderEnabled) {
        return;
      }

      const speechOptions = {
        rate: options?.rate || this.preferences.textToSpeechRate,
        pitch: options?.pitch || this.preferences.textToSpeechPitch,
        language: options?.language || this.preferences.textToSpeechLanguage,
      };

      if (options?.interrupt) {
        await Speech.stop();
        this.speechQueue = [];
        this.isSpeaking = false;
      }

      if (this.isSpeaking) {
        this.speechQueue.push(text);
        return;
      }

      this.isSpeaking = true;
      await Speech.speak(text, speechOptions);
      this.isSpeaking = false;

      // Process queue
      if (this.speechQueue.length > 0) {
        const nextText = this.speechQueue.shift()!;
        await this.speak(nextText, options);
      }
    } catch (error) {
      console.warn('Text-to-speech failed:', error);
      this.isSpeaking = false;
    }
  }

  /**
   * Stop text-to-speech
   */
  async stopSpeaking(): Promise<void> {
    try {
      await Speech.stop();
      this.speechQueue = [];
      this.isSpeaking = false;
    } catch (error) {
      console.warn('Failed to stop speech:', error);
    }
  }

  /**
   * Provide haptic feedback
   */
  async hapticFeedback(type: 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error' = 'light'): Promise<void> {
    try {
      if (!this.preferences.hapticsEnabled) {
        return;
      }

      switch (type) {
        case 'light':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
        case 'medium':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
        case 'heavy':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;
        case 'success':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
        case 'warning':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          break;
        case 'error':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          break;
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }

  /**
   * Get accessibility-friendly colors based on preferences
   */
  getAccessibleColors(originalColors: string[]): string[] {
    if (this.preferences.colorBlindnessType === 'none' && !this.preferences.highContrastMode) {
      return originalColors;
    }

    // Apply color transformations based on accessibility needs
    return originalColors.map(color => this.transformColor(color));
  }

  /**
   * Transform color for accessibility
   */
  private transformColor(color: string): string {
    // High contrast mode
    if (this.preferences.highContrastMode) {
      // Convert to high contrast equivalents
      const lightColors = ['#FFFFFF', '#F3F4F6', '#E5E7EB'];
      const darkColors = ['#000000', '#1F2937', '#374151'];
      
      // Simple high contrast mapping
      if (color.includes('FF') || color.includes('F3') || color.includes('E5')) {
        return '#FFFFFF';
      } else if (color.includes('00') || color.includes('1F') || color.includes('37')) {
        return '#000000';
      }
    }

    // Color blindness adjustments
    if (this.preferences.colorBlindnessType !== 'none') {
      // Apply color blindness filters
      return this.applyColorBlindnessFilter(color, this.preferences.colorBlindnessType);
    }

    return color;
  }

  /**
   * Apply color blindness filter
   */
  private applyColorBlindnessFilter(color: string, type: string): string {
    // Simplified color blindness simulation
    // In a real implementation, you'd use proper color transformation matrices
    switch (type) {
      case 'protanopia':
        // Red-blind: reduce red channel
        return color.replace(/^#([0-9A-F]{2})([0-9A-F]{2})([0-9A-F]{2})$/i, 
          (match, r, g, b) => `#${Math.floor(parseInt(r, 16) * 0.3).toString(16).padStart(2, '0')}${g}${b}`);
      case 'deuteranopia':
        // Green-blind: reduce green channel
        return color.replace(/^#([0-9A-F]{2})([0-9A-F]{2})([0-9A-F]{2})$/i, 
          (match, r, g, b) => `#${r}${Math.floor(parseInt(g, 16) * 0.3).toString(16).padStart(2, '0')}${b}`);
      case 'tritanopia':
        // Blue-blind: reduce blue channel
        return color.replace(/^#([0-9A-F]{2})([0-9A-F]{2})([0-9A-F]{2})$/i, 
          (match, r, g, b) => `#${r}${g}${Math.floor(parseInt(b, 16) * 0.3).toString(16).padStart(2, '0')}`);
      default:
        return color;
    }
  }

  /**
   * Get font size multiplier based on preferences
   */
  getFontSizeMultiplier(): number {
    if (this.preferences.largeTextMode) {
      return 1.3;
    }
    return 1.0;
  }

  /**
   * Check if animations should be reduced
   */
  shouldReduceMotion(): boolean {
    return this.preferences.reduceMotion || this.systemState.isReduceMotionEnabled;
  }

  /**
   * Get animation duration based on preferences
   */
  getAnimationDuration(defaultDuration: number): number {
    if (this.shouldReduceMotion()) {
      return Math.min(defaultDuration * 0.3, 100); // Reduce to 30% or max 100ms
    }
    return defaultDuration;
  }

  /**
   * Update accessibility preferences
   */
  async updatePreferences(newPreferences: Partial<AccessibilityPreferences>): Promise<void> {
    try {
      this.preferences = AccessibilityPreferencesSchema.parse({
        ...this.preferences,
        ...newPreferences,
      });
      
      await this.savePreferences();
      this.notifyListeners();
    } catch (error) {
      console.warn('Failed to update accessibility preferences:', error);
      throw error;
    }
  }

  /**
   * Get current preferences
   */
  getPreferences(): AccessibilityPreferences {
    return { ...this.preferences };
  }

  /**
   * Get current system state
   */
  getSystemState(): AccessibilityState {
    return { ...this.systemState };
  }

  /**
   * Add state change listener
   */
  addStateListener(id: string, callback: (state: AccessibilityState) => void): void {
    this.listeners.set(id, callback);
  }

  /**
   * Remove state change listener
   */
  removeStateListener(id: string): void {
    this.listeners.delete(id);
  }

  /**
   * Notify all listeners of state changes
   */
  private notifyListeners(): void {
    this.listeners.forEach(callback => {
      try {
        callback({ ...this.systemState });
      } catch (error) {
        console.error('Error in accessibility state listener:', error);
      }
    });
  }

  /**
   * Generate accessibility announcement for screen readers
   */
  announceForScreenReader(message: string, priority: 'low' | 'high' = 'low'): void {
    if (this.preferences.screenReaderEnabled) {
      // Use platform-specific screen reader announcements
      if (Platform.OS === 'ios') {
        AccessibilityInfo.announceForAccessibility(message);
      } else {
        // For Android, we can use TTS as fallback
        this.speak(message, { interrupt: priority === 'high' });
      }
    }
  }

  /**
   * Cleanup on service destruction
   */
  destroy(): void {
    this.listeners.clear();
    this.stopSpeaking();
  }

  /**
   * Get current settings
   */
  async getSettings(): Promise<any> {
    return {
      screenReader: this.settings.screenReader,
      highContrast: this.settings.highContrast,
      largeText: this.settings.largeText,
      reduceMotion: this.settings.reduceMotion,
      voiceOver: this.settings.voiceOver,
      hapticFeedback: this.settings.hapticFeedback,
      audioDescriptions: this.settings.audioDescriptions,
      colorBlindSupport: this.settings.colorBlindSupport,
      focusIndicators: this.settings.focusIndicators,
      keyboardNavigation: this.settings.keyboardNavigation,
      gestureAlternatives: this.settings.gestureAlternatives,
      textToSpeech: this.settings.textToSpeech,
    };
  }

  /**
   * Update settings
   */
  async updateSettings(newSettings: any): Promise<void> {
    this.settings = {
      ...this.settings,
      ...newSettings,
    };
    await this.saveSettings();
  }

  /**
   * Apply settings
   */
  async applySettings(settings: any): Promise<void> {
    // Apply settings to the app
    if (settings.highContrast) {
      // Apply high contrast theme
    }

    if (settings.largeText) {
      // Apply large text scaling
    }

    if (settings.reduceMotion) {
      // Reduce animations
    }

    // Additional setting applications...
  }

  /**
   * Run accessibility audit
   */
  async runAccessibilityAudit(): Promise<{ issues: any[] }> {
    // Mock accessibility audit
    const issues: any[] = [];

    // Check for common accessibility issues
    if (!this.settings.focusIndicators) {
      issues.push({
        type: 'focus',
        severity: 'medium',
        message: 'Focus indicators are disabled',
        recommendation: 'Enable focus indicators for better keyboard navigation'
      });
    }

    if (!this.settings.screenReader) {
      issues.push({
        type: 'screen_reader',
        severity: 'low',
        message: 'Screen reader support is disabled',
        recommendation: 'Consider enabling screen reader support for better accessibility'
      });
    }

    return { issues };
  }

  /**
   * Reset to defaults
   */
  async resetToDefaults(): Promise<void> {
    this.settings = {
      screenReader: false,
      highContrast: false,
      largeText: false,
      reduceMotion: false,
      voiceOver: false,
      hapticFeedback: true,
      audioDescriptions: false,
      colorBlindSupport: false,
      focusIndicators: true,
      keyboardNavigation: true,
      gestureAlternatives: false,
      textToSpeech: false,
    };
    await this.saveSettings();
  }
}

// Export singleton instance
export const accessibilityService = new AccessibilityService();
