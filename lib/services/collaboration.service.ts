import { z } from 'zod';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Share, Alert } from 'react-native';
import * as FileSystem from 'expo-file-system';
import { CompleteKnowledgeCard } from '@/types/appwrite-v2';

// Collaboration session schema
const CollaborationSessionSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().optional(),
  createdBy: z.string(),
  participants: z.array(z.string()),
  knowledgeCards: z.array(z.string()), // Card IDs
  createdAt: z.string(),
  updatedAt: z.string(),
  isActive: z.boolean(),
  shareCode: z.string(),
});

export type CollaborationSession = z.infer<typeof CollaborationSessionSchema>;

// Share format schema
const ShareFormatSchema = z.object({
  format: z.enum(['json', 'markdown', 'pdf', 'link']),
  includeImages: z.boolean().default(true),
  includeMetadata: z.boolean().default(true),
  template: z.enum(['study-guide', 'flashcards', 'summary', 'detailed']).default('detailed'),
});

export type ShareFormat = z.infer<typeof ShareFormatSchema>;

// Share result schema
const ShareResultSchema = z.object({
  success: z.boolean(),
  shareUrl: z.string().optional(),
  filePath: z.string().optional(),
  shareCode: z.string().optional(),
  error: z.string().optional(),
});

export type ShareResult = z.infer<typeof ShareResultSchema>;

/**
 * Enhanced Sharing and Collaboration Service
 * Following Rule 11: State Management Architecture
 */
export class CollaborationService {
  private sessions: Map<string, CollaborationSession> = new Map();
  private readonly STORAGE_KEY = 'collaboration_sessions';

  constructor() {
    this.loadSessions();
  }

  /**
   * Load collaboration sessions from storage
   */
  private async loadSessions(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const sessions = JSON.parse(stored);
        sessions.forEach((session: CollaborationSession) => {
          this.sessions.set(session.id, session);
        });
      }
    } catch (error) {
      console.warn('Failed to load collaboration sessions:', error);
    }
  }

  /**
   * Save collaboration sessions to storage
   */
  private async saveSessions(): Promise<void> {
    try {
      const sessions = Array.from(this.sessions.values());
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessions));
    } catch (error) {
      console.warn('Failed to save collaboration sessions:', error);
    }
  }

  /**
   * Create a new collaboration session
   */
  async createSession(
    title: string,
    description: string,
    createdBy: string,
    knowledgeCards: string[]
  ): Promise<CollaborationSession> {
    try {
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const shareCode = this.generateShareCode();

      const session: CollaborationSession = {
        id: sessionId,
        title,
        description,
        createdBy,
        participants: [createdBy],
        knowledgeCards,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isActive: true,
        shareCode,
      };

      this.sessions.set(sessionId, session);
      await this.saveSessions();

      return session;
    } catch (error) {
      console.error('Failed to create collaboration session:', error);
      throw error;
    }
  }

  /**
   * Join a collaboration session using share code
   */
  async joinSession(shareCode: string, userId: string): Promise<CollaborationSession | null> {
    try {
      const session = Array.from(this.sessions.values()).find(s => s.shareCode === shareCode);
      
      if (!session) {
        return null;
      }

      if (!session.participants.includes(userId)) {
        session.participants.push(userId);
        session.updatedAt = new Date().toISOString();
        this.sessions.set(session.id, session);
        await this.saveSessions();
      }

      return session;
    } catch (error) {
      console.error('Failed to join collaboration session:', error);
      throw error;
    }
  }

  /**
   * Share knowledge cards in various formats
   */
  async shareKnowledgeCards(
    cards: CompleteKnowledgeCard[],
    format: ShareFormat
  ): Promise<ShareResult> {
    try {
      switch (format.format) {
        case 'json':
          return await this.shareAsJSON(cards, format);
        case 'markdown':
          return await this.shareAsMarkdown(cards, format);
        case 'link':
          return await this.shareAsLink(cards, format);
        default:
          throw new Error(`Unsupported share format: ${format.format}`);
      }
    } catch (error) {
      console.error('Failed to share knowledge cards:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Share as JSON file
   */
  private async shareAsJSON(
    cards: CompleteKnowledgeCard[],
    format: ShareFormat
  ): Promise<ShareResult> {
    try {
      const shareData = {
        title: 'LearniScan Knowledge Cards',
        exportedAt: new Date().toISOString(),
        format: 'json',
        cards: format.includeMetadata ? cards : cards.map(card => ({
          title: card.title,
          content: card.content,
          category: card.category,
          tags: card.tags,
          difficulty: card.difficulty,
        })),
        metadata: format.includeMetadata ? {
          totalCards: cards.length,
          categories: [...new Set(cards.map(c => c.category))],
          difficulties: [...new Set(cards.map(c => c.difficulty))],
        } : undefined,
      };

      const fileName = `learni-scan-cards-${Date.now()}.json`;
      const filePath = `${FileSystem.documentDirectory}${fileName}`;
      
      await FileSystem.writeAsStringAsync(
        filePath,
        JSON.stringify(shareData, null, 2),
        { encoding: FileSystem.EncodingType.UTF8 }
      );

      await Share.share({
        url: filePath,
        title: 'LearniScan Knowledge Cards',
        message: `Sharing ${cards.length} knowledge cards from LearniScan`,
      });

      return {
        success: true,
        filePath,
      };
    } catch (error) {
      throw new Error(`JSON export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Share as Markdown file
   */
  private async shareAsMarkdown(
    cards: CompleteKnowledgeCard[],
    format: ShareFormat
  ): Promise<ShareResult> {
    try {
      let markdown = `# LearniScan Knowledge Cards\n\n`;
      markdown += `*Exported on ${new Date().toLocaleDateString()}*\n\n`;

      if (format.includeMetadata) {
        markdown += `## Summary\n\n`;
        markdown += `- **Total Cards:** ${cards.length}\n`;
        markdown += `- **Categories:** ${[...new Set(cards.map(c => c.category))].join(', ')}\n`;
        markdown += `- **Difficulties:** ${[...new Set(cards.map(c => c.difficulty))].join(', ')}\n\n`;
      }

      // Group cards by category
      const cardsByCategory = cards.reduce((acc, card) => {
        if (!acc[card.category]) {
          acc[card.category] = [];
        }
        acc[card.category].push(card);
        return acc;
      }, {} as Record<string, CompleteKnowledgeCard[]>);

      Object.entries(cardsByCategory).forEach(([category, categoryCards]) => {
        markdown += `## ${category.charAt(0).toUpperCase() + category.slice(1)}\n\n`;
        
        categoryCards.forEach((card, index) => {
          markdown += `### ${index + 1}. ${card.title}\n\n`;
          markdown += `${card.content}\n\n`;
          
          if (format.includeMetadata) {
            markdown += `**Difficulty:** ${card.difficulty} | **Tags:** ${card.tags.join(', ')}\n\n`;
          }
          
          markdown += `---\n\n`;
        });
      });

      const fileName = `learni-scan-cards-${Date.now()}.md`;
      const filePath = `${FileSystem.documentDirectory}${fileName}`;
      
      await FileSystem.writeAsStringAsync(
        filePath,
        markdown,
        { encoding: FileSystem.EncodingType.UTF8 }
      );

      await Share.share({
        url: filePath,
        title: 'LearniScan Knowledge Cards',
        message: `Sharing ${cards.length} knowledge cards from LearniScan`,
      });

      return {
        success: true,
        filePath,
      };
    } catch (error) {
      throw new Error(`Markdown export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Share as shareable link
   */
  private async shareAsLink(
    cards: CompleteKnowledgeCard[],
    format: ShareFormat
  ): Promise<ShareResult> {
    try {
      // Create a collaboration session for sharing
      const session = await this.createSession(
        `Shared Knowledge Cards - ${new Date().toLocaleDateString()}`,
        `${cards.length} knowledge cards shared from LearniScan`,
        'anonymous_user',
        cards.map(c => c.$id)
      );

      const shareUrl = `learni-scan://share/${session.shareCode}`;
      const shareMessage = `Check out these ${cards.length} knowledge cards I created with LearniScan!\n\nShare Code: ${session.shareCode}\nLink: ${shareUrl}`;

      await Share.share({
        message: shareMessage,
        url: shareUrl,
        title: 'LearniScan Knowledge Cards',
      });

      return {
        success: true,
        shareUrl,
        shareCode: session.shareCode,
      };
    } catch (error) {
      throw new Error(`Link sharing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate a unique share code
   */
  private generateShareCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Get all active collaboration sessions
   */
  getActiveSessions(): CollaborationSession[] {
    return Array.from(this.sessions.values()).filter(s => s.isActive);
  }

  /**
   * Get sessions for a specific user
   */
  getUserSessions(userId: string): CollaborationSession[] {
    return Array.from(this.sessions.values()).filter(s => 
      s.participants.includes(userId) && s.isActive
    );
  }

  /**
   * Update collaboration session
   */
  async updateSession(sessionId: string, updates: Partial<CollaborationSession>): Promise<void> {
    try {
      const session = this.sessions.get(sessionId);
      if (!session) {
        throw new Error('Session not found');
      }

      const updatedSession = {
        ...session,
        ...updates,
        updatedAt: new Date().toISOString(),
      };

      this.sessions.set(sessionId, updatedSession);
      await this.saveSessions();
    } catch (error) {
      console.error('Failed to update collaboration session:', error);
      throw error;
    }
  }

  /**
   * End collaboration session
   */
  async endSession(sessionId: string): Promise<void> {
    try {
      const session = this.sessions.get(sessionId);
      if (session) {
        session.isActive = false;
        session.updatedAt = new Date().toISOString();
        this.sessions.set(sessionId, session);
        await this.saveSessions();
      }
    } catch (error) {
      console.error('Failed to end collaboration session:', error);
      throw error;
    }
  }

  /**
   * Quick share with native sharing
   */
  async quickShare(
    title: string,
    content: string,
    url?: string
  ): Promise<boolean> {
    try {
      const shareOptions: any = {
        title,
        message: content,
      };

      if (url) {
        shareOptions.url = url;
      }

      const result = await Share.share(shareOptions);
      return result.action === Share.sharedAction;
    } catch (error) {
      console.error('Quick share failed:', error);
      return false;
    }
  }

  /**
   * Export study session data
   */
  async exportStudySession(
    cards: CompleteKnowledgeCard[],
    sessionData: {
      duration: number;
      correctAnswers: number;
      totalQuestions: number;
      difficulty: string;
    }
  ): Promise<ShareResult> {
    try {
      const exportData = {
        title: 'LearniScan Study Session',
        sessionDate: new Date().toISOString(),
        performance: {
          duration: sessionData.duration,
          accuracy: (sessionData.correctAnswers / sessionData.totalQuestions) * 100,
          totalQuestions: sessionData.totalQuestions,
          correctAnswers: sessionData.correctAnswers,
          difficulty: sessionData.difficulty,
        },
        studiedCards: cards.map(card => ({
          title: card.title,
          category: card.category,
          difficulty: card.difficulty,
          studyCount: card.studyCount,
        })),
        recommendations: this.generateStudyRecommendations(sessionData),
      };

      const fileName = `learni-scan-study-session-${Date.now()}.json`;
      const filePath = `${FileSystem.documentDirectory}${fileName}`;
      
      await FileSystem.writeAsStringAsync(
        filePath,
        JSON.stringify(exportData, null, 2),
        { encoding: FileSystem.EncodingType.UTF8 }
      );

      return {
        success: true,
        filePath,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Export failed',
      };
    }
  }

  /**
   * Generate study recommendations based on session performance
   */
  private generateStudyRecommendations(sessionData: {
    correctAnswers: number;
    totalQuestions: number;
    difficulty: string;
  }): string[] {
    const accuracy = (sessionData.correctAnswers / sessionData.totalQuestions) * 100;
    const recommendations: string[] = [];

    if (accuracy < 60) {
      recommendations.push('Consider reviewing the material again before the next session');
      recommendations.push('Focus on understanding core concepts rather than memorization');
    } else if (accuracy < 80) {
      recommendations.push('Good progress! Try increasing the difficulty level');
      recommendations.push('Review incorrect answers to identify knowledge gaps');
    } else {
      recommendations.push('Excellent performance! You can move to more advanced topics');
      recommendations.push('Consider teaching others to reinforce your knowledge');
    }

    return recommendations;
  }
}

// Export singleton instance
export const collaborationService = new CollaborationService();
