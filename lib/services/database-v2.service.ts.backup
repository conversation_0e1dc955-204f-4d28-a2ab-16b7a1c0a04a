/**
 * Database Service v2 for Split Knowledge Cards Architecture
 *
 * This service provides methods to work with the new split knowledge cards schema:
 * - knowledge_cards_v2: Core card metadata
 * - knowledge_content: Large content and source data
 * - knowledge_reviews: Review data and statistics
 * - knowledge_tags: Tags and categorization
 */

import { ID, Permission, Query, Role } from "react-native-appwrite";
import type { DatabaseResponse } from "../../types/appwrite";
import type {
	AppwriteDocument,
	CompleteKnowledgeCard,
	CreateKnowledgeCardInput,
	KnowledgeCardQuery,
	KnowledgeCardResponse,
	KnowledgeCardV2,
	KnowledgeContent,
	KnowledgeReviews,
	KnowledgeTag,
	ReviewDueQuery,
	ReviewDueResponse,
	ReviewSessionInput,
	UpdateKnowledgeCardInput,
} from "../../types/appwrite-v2";
import { APPWRITE_CONFIG, databases } from "../config/appwrite";
import {
	logPermissionError,
	withPermissionErrorHandling,
} from "../utils/permissionErrorUtils";

// Updated collection IDs for v2 schema
const COLLECTIONS = {
	knowledgeCardsV2: "knowledge_cards_v2",
	knowledgeContent: "knowledge_content",
	knowledgeReviews: "knowledge_reviews",
	knowledgeTags: "knowledge_tags",
	users: "users",
	learningSessions: "learning_sessions",
	scanHistory: "scan_history",
};

export class DatabaseServiceV2 {
	private databaseId: string;

	constructor() {
		this.databaseId = APPWRITE_CONFIG.databaseId;
	}

	// KNOWLEDGE CARDS V2 METHODS

	/**
	 * Create a new knowledge card with split architecture
	 */
	async createKnowledgeCard(
		userId: string,
		input: CreateKnowledgeCardInput,
	): Promise<CompleteKnowledgeCard> {
		return withPermissionErrorHandling(async () => {
			// 1. Create core card record
			const cardData: Omit<KnowledgeCardV2, keyof AppwriteDocument> = {
				userId,
				title: input.title,
				difficulty: input.difficulty,
				category: input.category,
				sourceType: input.sourceType,
				status: "active",
				aiEnhanced: input.aiEnhanced || false,
				isPublic: input.isPublic || false,
			};

			// Determine permissions based on user type
			const isGuestUser =
				userId.startsWith("dev-guest-user") || userId.includes("anonymous");
			const permissions = isGuestUser
				? [
						Permission.read(Role.guests()),
						Permission.update(Role.guests()),
						Permission.delete(Role.guests()),
					]
				: [
						Permission.read(Role.user(userId)),
						Permission.update(Role.user(userId)),
						Permission.delete(Role.user(userId)),
					];

			const card = (await databases.createDocument(
				this.databaseId,
				COLLECTIONS.knowledgeCardsV2,
				ID.unique(),
				cardData,
				permissions,
			)) as KnowledgeCardV2;

			// 2. Create content record
			const contentData: Omit<KnowledgeContent, keyof AppwriteDocument> = {
				cardId: card.$id,
				content: input.content,
				sourceData: input.sourceData, // Already an object, no need to stringify
				summary: input.summary,
			};

			const content = (await databases.createDocument(
				this.databaseId,
				COLLECTIONS.knowledgeContent,
				ID.unique(),
				contentData,
				permissions, // Use same permissions as card
			)) as KnowledgeContent;

			// 3. Create initial review record
			const reviewData: Omit<KnowledgeReviews, keyof AppwriteDocument> = {
				cardId: card.$id,
				nextReview: new Date().toISOString(), // Due for immediate review
				interval: 1,
				easeFactor: 2.5,
				reviewCount: 0,
				correctCount: 0,
				reviewHistory: [],
			};

			const reviews = (await databases.createDocument(
				this.databaseId,
				COLLECTIONS.knowledgeReviews,
				ID.unique(),
				reviewData,
				permissions, // Use same permissions as card
			)) as KnowledgeReviews;

			// 4. Create tags if provided
			let tags: KnowledgeTag[] = [];
			if (input.tags && input.tags.length > 0) {
				tags = await this.addTagsToCard(card.$id, input.tags, "user");
			}

			// 5. Return complete knowledge card
			return {
				card,
				content,
				reviews,
				tags,
				tagNames: tags.map((t) => t.tag),
				nextReviewDate: new Date(reviews.nextReview),
				isReviewDue: new Date(reviews.nextReview) <= new Date(),
			};
	}, userId, "Knowledge card creation");
}

	/**
	 * Get a complete knowledge card by ID
	 */
	async getKnowledgeCard(
		cardId: string,
	): Promise<CompleteKnowledgeCard | null> {
		try {
			// Get core card data
			const card = (await databases.getDocument(
				this.databaseId,
				COLLECTIONS.knowledgeCardsV2,
				cardId,
			)) as KnowledgeCardV2;

			// Get related data in parallel
			const [content, reviews, tags] = await Promise.all([
				this.getCardContent(cardId),
				this.getCardReviews(cardId),
				this.getCardTags(cardId),
			]);

			return {
				card,
				content: content || undefined,
				reviews: reviews || undefined,
				tags: tags || [],
				tagNames: tags?.map((t) => t.tag) || [],
				nextReviewDate: reviews ? new Date(reviews.nextReview) : undefined,
				isReviewDue: reviews
					? new Date(reviews.nextReview) <= new Date()
					: false,
			};
		} catch (error) {
			console.error("Error getting knowledge card:", error);
			return null;
		}
	}

	/**
	 * Query knowledge cards with filters
	 */
	async queryKnowledgeCards(
		query: KnowledgeCardQuery,
	): Promise<KnowledgeCardResponse> {
		try {
			const queries: string[] = [];

			// Build query filters
			if (query.userId) {
				queries.push(Query.equal("userId", query.userId));
			}
			if (query.category) {
				queries.push(Query.equal("category", query.category));
			}
			if (query.difficulty) {
				queries.push(Query.equal("difficulty", query.difficulty));
			}
			if (query.status) {
				queries.push(Query.equal("status", query.status));
			}
			if (query.isPublic !== undefined) {
				queries.push(Query.equal("isPublic", query.isPublic));
			}
			if (query.searchTerm) {
				queries.push(Query.search("title", query.searchTerm));
			}

			// Add ordering
			if (query.orderBy) {
				const direction =
					query.orderDirection === "desc" ? Query.orderDesc : Query.orderAsc;
				switch (query.orderBy) {
					case "created":
						queries.push(direction("$createdAt"));
						break;
					case "updated":
						queries.push(direction("$updatedAt"));
						break;
					case "title":
						queries.push(direction("title"));
						break;
				}
			}

			// Add pagination
			if (query.limit) {
				queries.push(Query.limit(query.limit));
			}
			if (query.offset) {
				queries.push(Query.offset(query.offset));
			}

			// Execute query
			const response = (await databases.listDocuments(
				this.databaseId,
				COLLECTIONS.knowledgeCardsV2,
				queries,
			)) as DatabaseResponse<KnowledgeCardV2>;

			// Get complete data for each card
			const completeCards = await Promise.all(
				response.documents.map(async (card) => {
					const [content, reviews, tags] = await Promise.all([
						this.getCardContent(card.$id),
						this.getCardReviews(card.$id),
						this.getCardTags(card.$id),
					]);

					return {
						card,
						content: content || undefined,
						reviews: reviews || undefined,
						tags: tags || [],
						tagNames: tags?.map((t) => t.tag) || [],
						nextReviewDate: reviews ? new Date(reviews.nextReview) : undefined,
						isReviewDue: reviews
							? new Date(reviews.nextReview) <= new Date()
							: false,
					};
				}),
			);

			return {
				total: response.total,
				cards: completeCards,
			};
		} catch (error) {
			console.error("Error querying knowledge cards:", error);
			throw error;
		}
	}

	/**
	 * Get cards due for review
	 */
	async getCardsDueForReview(
		query: ReviewDueQuery,
	): Promise<ReviewDueResponse> {
		try {
			const queries: string[] = [
				Query.lessThanEqual(
					"nextReview",
					query.beforeDate || new Date().toISOString(),
				),
			];

			if (query.limit) {
				queries.push(Query.limit(query.limit));
			}

			// Get review records due
			const reviewsResponse = (await databases.listDocuments(
				this.databaseId,
				COLLECTIONS.knowledgeReviews,
				queries,
			)) as DatabaseResponse<KnowledgeReviews>;

			// Get complete cards for each review
			const completeCards = await Promise.all(
				reviewsResponse.documents.map(async (review) => {
					const card = await this.getKnowledgeCard(review.cardId);
					return card;
				}),
			);

			// Filter out null cards and sort by next review date
			const validCards = completeCards
				.filter((card): card is CompleteKnowledgeCard => card !== null)
				.sort((a, b) => {
					const dateA = a.nextReviewDate?.getTime() || 0;
					const dateB = b.nextReviewDate?.getTime() || 0;
					return dateA - dateB;
				});

			return {
				total: validCards.length,
				cards: validCards,
				nextReviewDate: validCards[0]?.nextReviewDate?.toISOString(),
			};
		} catch (error) {
			console.error("Error getting cards due for review:", error);
			throw error;
		}
	}

	/**
	 * Update a knowledge card
	 */
	async updateKnowledgeCard(
		cardId: string,
		input: UpdateKnowledgeCardInput,
	): Promise<CompleteKnowledgeCard | null> {
		try {
			// Update core card data if provided
			if (
				input.title ||
				input.difficulty ||
				input.category ||
				input.status !== undefined ||
				input.aiEnhanced !== undefined ||
				input.isPublic !== undefined
			) {
				const updateData: Partial<KnowledgeCardV2> = {};
				if (input.title) updateData.title = input.title;
				if (input.difficulty) updateData.difficulty = input.difficulty;
				if (input.category) updateData.category = input.category;
				if (input.status !== undefined) updateData.status = input.status;
				if (input.aiEnhanced !== undefined)
					updateData.aiEnhanced = input.aiEnhanced;
				if (input.isPublic !== undefined) updateData.isPublic = input.isPublic;

				await databases.updateDocument(
					this.databaseId,
					COLLECTIONS.knowledgeCardsV2,
					cardId,
					updateData,
				);
			}

			// Update content data if provided
			if (
				input.content ||
				input.sourceData ||
				input.processedContent ||
				input.summary
			) {
				const content = await this.getCardContent(cardId);
				if (content) {
					const updateData: Partial<KnowledgeContent> = {};
					if (input.content) updateData.content = input.content;
					if (input.sourceData)
						updateData.sourceData = input.sourceData;
					if (input.processedContent)
						updateData.processedContent = input.processedContent;
					if (input.summary) updateData.summary = input.summary;

					await databases.updateDocument(
						this.databaseId,
						COLLECTIONS.knowledgeContent,
						content.$id,
						updateData,
					);
				}
			}

			// Handle tag updates
			if (input.addTags && input.addTags.length > 0) {
				await this.addTagsToCard(cardId, input.addTags, "user");
			}
			if (input.removeTags && input.removeTags.length > 0) {
				await this.removeTagsFromCard(cardId, input.removeTags);
			}

			// Return updated complete card
			return await this.getKnowledgeCard(cardId);
		} catch (error) {
			console.error("Error updating knowledge card:", error);
			throw error;
		}
	}

	/**
	 * Record a review session for a card
	 */
	async recordReviewSession(
		input: ReviewSessionInput,
	): Promise<KnowledgeReviews | null> {
		try {
			const reviews = await this.getCardReviews(input.cardId);
			if (!reviews) {
				throw new Error("Review record not found for card");
			}

			// Get existing review history (should already be an array)
			const reviewHistory = reviews.reviewHistory || [];

			// Add new review to history
			reviewHistory.push({
				date: new Date().toISOString(),
				correct: input.correct,
				responseTime: input.responseTime,
				difficulty: input.difficulty,
			});

			// Calculate new spaced repetition values
			const { interval, easeFactor } = this.calculateSpacedRepetition(
				reviews.interval,
				reviews.easeFactor,
				input.correct,
				input.difficulty,
			);

			// Calculate next review date
			const nextReview = new Date();
			nextReview.setDate(nextReview.getDate() + interval);

			// Update review record
			const updateData = {
				nextReview: nextReview.toISOString(),
				interval,
				easeFactor,
				reviewCount: reviews.reviewCount + 1,
				correctCount: reviews.correctCount + (input.correct ? 1 : 0),
				reviewHistory: reviewHistory, // Keep as array, don't stringify
			};

			const updatedReviews = (await databases.updateDocument(
				this.databaseId,
				COLLECTIONS.knowledgeReviews,
				reviews.$id,
				updateData,
			)) as KnowledgeReviews;

			return updatedReviews;
		} catch (error) {
			console.error("Error recording review session:", error);
			throw error;
		}
	}

	// HELPER METHODS

	private async getCardContent(
		cardId: string,
	): Promise<KnowledgeContent | null> {
		try {
			const response = (await databases.listDocuments(
				this.databaseId,
				COLLECTIONS.knowledgeContent,
				[Query.equal("cardId", cardId)],
			)) as DatabaseResponse<KnowledgeContent>;

			return response.documents[0] || null;
		} catch (error) {
			return null;
		}
	}

	private async getCardReviews(
		cardId: string,
	): Promise<KnowledgeReviews | null> {
		try {
			const response = (await databases.listDocuments(
				this.databaseId,
				COLLECTIONS.knowledgeReviews,
				[Query.equal("cardId", cardId)],
			)) as DatabaseResponse<KnowledgeReviews>;

			return response.documents[0] || null;
		} catch (error) {
			return null;
		}
	}

	private async getCardTags(cardId: string): Promise<KnowledgeTag[] | null> {
		try {
			const response = (await databases.listDocuments(
				this.databaseId,
				COLLECTIONS.knowledgeTags,
				[Query.equal("cardId", cardId)],
			)) as DatabaseResponse<KnowledgeTag>;

			return response.documents;
		} catch (error) {
			return null;
		}
	}

	private async addTagsToCard(
		cardId: string,
		tags: string[],
		tagType: "user" | "system" | "ai",
	): Promise<KnowledgeTag[]> {
		const createdTags: KnowledgeTag[] = [];

		for (const tag of tags) {
			try {
				const tagData = {
					cardId,
					tag: tag.toLowerCase().trim(),
					tagType,
				};

				const createdTag = (await databases.createDocument(
					this.databaseId,
					COLLECTIONS.knowledgeTags,
					ID.unique(),
					tagData,
				)) as KnowledgeTag;

				createdTags.push(createdTag);
			} catch (error) {
				// Skip if tag already exists (unique constraint)
				console.warn(`Tag "${tag}" already exists for card ${cardId}`);
			}
		}

		return createdTags;
	}

	private async removeTagsFromCard(
		cardId: string,
		tags: string[],
	): Promise<void> {
		for (const tag of tags) {
			try {
				const response = (await databases.listDocuments(
					this.databaseId,
					COLLECTIONS.knowledgeTags,
					[
						Query.equal("cardId", cardId),
						Query.equal("tag", tag.toLowerCase().trim()),
					],
				)) as DatabaseResponse<KnowledgeTag>;

				for (const tagDoc of response.documents) {
					await databases.deleteDocument(
						this.databaseId,
						COLLECTIONS.knowledgeTags,
						tagDoc.$id,
					);
				}
			} catch (error) {
				console.warn(`Error removing tag "${tag}" from card ${cardId}:`, error);
			}
		}
	}

	private calculateSpacedRepetition(
		currentInterval: number,
		currentEaseFactor: number,
		correct: boolean,
		difficulty: "easy" | "good" | "hard" | "again",
	): { interval: number; easeFactor: number } {
		let newEaseFactor = currentEaseFactor;
		let newInterval = currentInterval;

		if (correct) {
			// Adjust ease factor based on difficulty
			switch (difficulty) {
				case "easy":
					newEaseFactor = Math.min(newEaseFactor + 0.15, 3.0);
					newInterval = Math.round(currentInterval * newEaseFactor * 1.3);
					break;
				case "good":
					newInterval = Math.round(currentInterval * newEaseFactor);
					break;
				case "hard":
					newEaseFactor = Math.max(newEaseFactor - 0.15, 1.3);
					newInterval = Math.round(currentInterval * newEaseFactor * 0.85);
					break;
			}
		} else {
			// Incorrect answer - reset interval and reduce ease factor
			newEaseFactor = Math.max(newEaseFactor - 0.2, 1.3);
			newInterval = 1; // Review again tomorrow
		}

		return {
			interval: Math.max(newInterval, 1),
			easeFactor: newEaseFactor,
		};
	}

	// DELETE METHODS

	async deleteKnowledgeCard(cardId: string): Promise<boolean> {
		try {
			// Delete in reverse order of dependencies

			// 1. Delete tags
			const tags = await this.getCardTags(cardId);
			if (tags) {
				await Promise.all(
					tags.map((tag) =>
						databases.deleteDocument(
							this.databaseId,
							COLLECTIONS.knowledgeTags,
							tag.$id,
						),
					),
				);
			}

			// 2. Delete reviews
			const reviews = await this.getCardReviews(cardId);
			if (reviews) {
				await databases.deleteDocument(
					this.databaseId,
					COLLECTIONS.knowledgeReviews,
					reviews.$id,
				);
			}

			// 3. Delete content
			const content = await this.getCardContent(cardId);
			if (content) {
				await databases.deleteDocument(
					this.databaseId,
					COLLECTIONS.knowledgeContent,
					content.$id,
				);
			}

			// 4. Delete core card
			await databases.deleteDocument(
				this.databaseId,
				COLLECTIONS.knowledgeCardsV2,
				cardId,
			);

			return true;
		} catch (error) {
			console.error("Error deleting knowledge card:", error);
			return false;
		}
	}
}

// Export singleton instance
export const databaseServiceV2 = new DatabaseServiceV2();
