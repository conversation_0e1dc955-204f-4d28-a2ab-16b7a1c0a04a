/**
 * Configurable Permission Limits
 * 
 * This configuration allows dynamic adjustment of user permission limits
 * through environment variables, making testing and deployment more flexible.
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 5: TypeScript quality and type safety
 * - Rule 11: Zod validation for all data structures
 * - Rule 12: Comprehensive error handling
 */

import { z } from 'zod';

// Zod schema for permission limits validation
const PermissionLimitsSchema = z.object({
  dailyScans: z.number().int().min(-1), // -1 means unlimited
  totalCards: z.number().int().min(-1),
  aiSearches: z.number().int().min(-1),
  aiGenerations: z.number().int().min(-1),
  storageMb: z.number().int().min(-1),
});

export type PermissionLimits = z.infer<typeof PermissionLimitsSchema>;

// Helper function to parse environment variable as number
const parseEnvNumber = (value: string | undefined, defaultValue: number): number => {
  if (!value) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
};

/**
 * Guest User Limits Configuration
 */
export const GUEST_LIMITS: PermissionLimits = PermissionLimitsSchema.parse({
  dailyScans: parseEnvNumber(process.env.EXPO_PUBLIC_GUEST_DAILY_SCANS, 5),
  totalCards: parseEnvNumber(process.env.EXPO_PUBLIC_GUEST_TOTAL_CARDS, 0),
  aiSearches: parseEnvNumber(process.env.EXPO_PUBLIC_GUEST_AI_SEARCHES, 0),
  aiGenerations: parseEnvNumber(process.env.EXPO_PUBLIC_GUEST_AI_GENERATIONS, 0),
  storageMb: parseEnvNumber(process.env.EXPO_PUBLIC_GUEST_STORAGE_MB, 0),
});

/**
 * Authenticated User Limits Configuration
 */
export const AUTHENTICATED_LIMITS: PermissionLimits = PermissionLimitsSchema.parse({
  dailyScans: parseEnvNumber(process.env.EXPO_PUBLIC_AUTH_DAILY_SCANS, 50),
  totalCards: parseEnvNumber(process.env.EXPO_PUBLIC_AUTH_TOTAL_CARDS, 100),
  aiSearches: parseEnvNumber(process.env.EXPO_PUBLIC_AUTH_AI_SEARCHES, 10),
  aiGenerations: parseEnvNumber(process.env.EXPO_PUBLIC_AUTH_AI_GENERATIONS, 20),
  storageMb: parseEnvNumber(process.env.EXPO_PUBLIC_AUTH_STORAGE_MB, 1024),
});

/**
 * Premium User Limits Configuration
 */
export const PREMIUM_LIMITS: PermissionLimits = PermissionLimitsSchema.parse({
  dailyScans: parseEnvNumber(process.env.EXPO_PUBLIC_PREMIUM_DAILY_SCANS, -1),
  totalCards: parseEnvNumber(process.env.EXPO_PUBLIC_PREMIUM_TOTAL_CARDS, -1),
  aiSearches: parseEnvNumber(process.env.EXPO_PUBLIC_PREMIUM_AI_SEARCHES, -1),
  aiGenerations: parseEnvNumber(process.env.EXPO_PUBLIC_PREMIUM_AI_GENERATIONS, -1),
  storageMb: parseEnvNumber(process.env.EXPO_PUBLIC_PREMIUM_STORAGE_MB, -1),
});

/**
 * Testing Configuration
 */
export const TESTING_CONFIG = {
  enableTestingMode: process.env.EXPO_PUBLIC_ENABLE_TESTING_MODE === 'true',
  bypassAuthInDev: process.env.EXPO_PUBLIC_BYPASS_AUTH_IN_DEV === 'true',
};

/**
 * Get limits for a specific user role
 */
export const getLimitsForRole = (role: 'guest' | 'authenticated' | 'premium'): PermissionLimits => {
  switch (role) {
    case 'guest':
      return GUEST_LIMITS;
    case 'authenticated':
      return AUTHENTICATED_LIMITS;
    case 'premium':
      return PREMIUM_LIMITS;
    default:
      return GUEST_LIMITS; // Fail safe to most restrictive
  }
};

/**
 * Check if a limit is unlimited
 */
export const isUnlimited = (limit: number): boolean => limit === -1;

/**
 * Format limit for display
 */
export const formatLimit = (limit: number): string => {
  return isUnlimited(limit) ? 'Unlimited' : limit.toString();
};

/**
 * Validate all permission limits on app startup
 */
export const validatePermissionLimits = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  try {
    PermissionLimitsSchema.parse(GUEST_LIMITS);
  } catch (error) {
    errors.push(`Guest limits validation failed: ${error}`);
  }
  
  try {
    PermissionLimitsSchema.parse(AUTHENTICATED_LIMITS);
  } catch (error) {
    errors.push(`Authenticated limits validation failed: ${error}`);
  }
  
  try {
    PermissionLimitsSchema.parse(PREMIUM_LIMITS);
  } catch (error) {
    errors.push(`Premium limits validation failed: ${error}`);
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
};

/**
 * Development helper to log current configuration
 */
export const logCurrentLimits = (): void => {
  if (__DEV__ && TESTING_CONFIG.enableTestingMode) {
    console.log('🔧 Permission Limits Configuration:');
    console.log('Guest:', GUEST_LIMITS);
    console.log('Authenticated:', AUTHENTICATED_LIMITS);
    console.log('Premium:', PREMIUM_LIMITS);
    console.log('Testing Config:', TESTING_CONFIG);
  }
};
