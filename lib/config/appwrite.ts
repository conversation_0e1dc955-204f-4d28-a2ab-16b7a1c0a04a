import { Client, Account, Databases, Storage, Functions, Messaging } from 'react-native-appwrite';
import Constants from 'expo-constants';

// Environment configuration
export const APPWRITE_CONFIG = {
  endpoint: Constants.expoConfig?.extra?.appwriteEndpoint || 'https://cloud.appwrite.io/v1',
  projectId: Constants.expoConfig?.extra?.appwriteProjectId || '',
  databaseId: Constants.expoConfig?.extra?.appwriteDatabaseId || 'learni-scan-db',
  
  // Collection IDs
  collections: {
    users: 'users',
    knowledgeCards: 'knowledge_cards', // Legacy collection
    knowledgeCardsV2: 'knowledge_cards_v2', // New core collection
    knowledgeContent: 'knowledge_content', // New content collection
    knowledgeReviews: 'knowledge_reviews', // New reviews collection
    knowledgeTags: 'knowledge_tags', // New tags collection
    learningSessions: 'learning_sessions',
    scanHistory: 'scan_history',
  },
  
  // Storage Bucket IDs
  buckets: {
    userAvatars: 'user-avatars',
    scanImages: 'scan-images',
    cardAssets: 'card-assets',
    userExports: 'user-exports',
  },
  
  // Function IDs (for future use)
  functions: {
    processDocument: 'process-document',
    generateSummary: 'generate-summary',
    enhanceContent: 'enhance-content',
  },
};

// Validate configuration
const validateConfig = () => {
  if (!APPWRITE_CONFIG.projectId) {
    throw new Error('AppWrite Project ID is required. Please set EXPO_PUBLIC_APPWRITE_PROJECT_ID in your environment variables.');
  }
  
  if (!APPWRITE_CONFIG.endpoint) {
    throw new Error('AppWrite Endpoint is required. Please set EXPO_PUBLIC_APPWRITE_ENDPOINT in your environment variables.');
  }
};

// Initialize AppWrite client
export const client = new Client();

try {
  validateConfig();
  
  client
    .setEndpoint(APPWRITE_CONFIG.endpoint)
    .setProject(APPWRITE_CONFIG.projectId)
    .setPlatform('com.learni.scan'); // Your app bundle ID
    
  console.log('AppWrite client initialized successfully');
  console.log('Endpoint:', APPWRITE_CONFIG.endpoint);
  console.log('Project ID:', APPWRITE_CONFIG.projectId);
} catch (error) {
  console.error('Failed to initialize AppWrite client:', error);
}

// Initialize services
export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);
export const functions = new Functions(client);
export const messaging = new Messaging(client);

// Export client for direct use if needed
export default client;
