/**
 * AI Service Configuration for LearniScan
 * 
 * This configuration file sets up the OpenAI-compatible AI service
 * following LearniScan development workflow rules:
 * - Rule 11: Zod validation for all state/config
 * - Rule 5: TypeScript quality and type safety
 * - Rule 12: Comprehensive error handling
 */

import { z } from 'zod';
import { createOpenAICompatible } from '@ai-sdk/openai-compatible';

// Zod schema for AI configuration validation (Rule 11)
const AIConfigSchema = z.object({
  apiKey: z.string().min(1, 'API key is required'),
  baseURL: z.string().url('Base URL must be a valid URL'),
  models: z.object({
    chat: z.string().min(1, 'Chat model name is required'),
    vision: z.string().min(1, 'Vision model name is required'),
    embedding: z.string().min(1, 'Embedding model name is required'),
  }),
  timeout: z.number().positive().default(30000),
  maxRetries: z.number().min(0).max(5).default(3),
  rateLimits: z.object({
    requestsPerMinute: z.number().positive().default(60),
    tokensPerMinute: z.number().positive().default(10000),
  }),
});

export type AIConfig = z.infer<typeof AIConfigSchema>;

// Default configuration based on user's example
const DEFAULT_AI_CONFIG: AIConfig = {
  apiKey: 'kala-0719',
  baseURL: 'http://127.0.0.1:8987/v1',
  models: {
    chat: 'gemini-2.5-flash-lite-preview-06-17',
    vision: 'gemini-2.5-flash-lite-preview-06-17', // Same model for vision tasks
    embedding: 'text-embedding-3-small', // Dedicated embedding model
  },
  timeout: 30000, // 30 seconds
  maxRetries: 3,
  rateLimits: {
    requestsPerMinute: 60,
    tokensPerMinute: 10000,
  },
};

// Environment-based configuration with fallback to defaults
function getAIConfig(): AIConfig {
  const config = {
    apiKey: process.env.EXPO_PUBLIC_AI_API_KEY || DEFAULT_AI_CONFIG.apiKey,
    baseURL: process.env.EXPO_PUBLIC_AI_BASE_URL || DEFAULT_AI_CONFIG.baseURL,
    models: {
      chat: process.env.EXPO_PUBLIC_AI_CHAT_MODEL || DEFAULT_AI_CONFIG.models.chat,
      vision: process.env.EXPO_PUBLIC_AI_VISION_MODEL || DEFAULT_AI_CONFIG.models.vision,
      embedding: process.env.EXPO_PUBLIC_AI_EMBEDDING_MODEL || DEFAULT_AI_CONFIG.models.embedding,
    },
    timeout: parseInt(process.env.EXPO_PUBLIC_AI_TIMEOUT || '30000'),
    maxRetries: parseInt(process.env.EXPO_PUBLIC_AI_MAX_RETRIES || '3'),
    rateLimits: {
      requestsPerMinute: parseInt(process.env.EXPO_PUBLIC_AI_RATE_LIMIT_RPM || '60'),
      tokensPerMinute: parseInt(process.env.EXPO_PUBLIC_AI_RATE_LIMIT_TPM || '10000'),
    },
  };

  // Validate configuration using Zod (Rule 11)
  try {
    return AIConfigSchema.parse(config);
  } catch (error) {
    console.error('AI Configuration validation failed:', error);
    console.warn('Falling back to default AI configuration');
    return DEFAULT_AI_CONFIG;
  }
}

// Export validated configuration
export const AI_CONFIG = getAIConfig();

// Define model types for better TypeScript support and V2 compatibility
type ChatModelIds =
  | 'gemini-2.5-flash-lite-preview-06-17'
  | 'gpt-4o'
  | 'gpt-4o-mini'
  | (string & {});

type EmbeddingModelIds =
  | 'text-embedding-3-small'
  | 'text-embedding-3-large'
  | 'text-embedding-ada-002'
  | (string & {});

// Create OpenAI-compatible provider instance with V2 specification
export const aiProvider = createOpenAICompatible<
  ChatModelIds,
  ChatModelIds, // completion models (same as chat for our use case)
  EmbeddingModelIds
>({
  name: 'learni-scan-ai',
  apiKey: AI_CONFIG.apiKey,
  baseURL: AI_CONFIG.baseURL,
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'LearniScan-AI/1.0',
  },
  // Add fetch middleware for better error handling
  fetch: async (input, init) => {
    try {
      const response = await fetch(input, init);
      if (!response.ok) {
        console.error(`AI API Error: ${response.status} ${response.statusText}`);
      }
      return response;
    } catch (error) {
      console.error('AI API Fetch Error:', error);
      throw error;
    }
  },
});

// Pre-configured model instances for different use cases
export const aiModels = {
  // For general chat and text processing
  chat: aiProvider.chatModel(AI_CONFIG.models.chat),

  // For vision tasks (OCR, image analysis)
  vision: aiProvider.chatModel(AI_CONFIG.models.vision),

  // For embeddings and semantic search - use dedicated embedding model method
  embedding: aiProvider.textEmbeddingModel(AI_CONFIG.models.embedding),
} as const;

/**
 * Validate AI configuration and provide helpful error messages for V2 compatibility
 */
export function validateAIConfig(): { isValid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!AI_CONFIG.apiKey) {
    errors.push('AI API key is missing. Set EXPO_PUBLIC_AI_API_KEY environment variable.');
  }

  if (!AI_CONFIG.baseURL) {
    errors.push('AI base URL is missing. Set EXPO_PUBLIC_AI_BASE_URL environment variable.');
  }

  if (!AI_CONFIG.models.embedding) {
    errors.push('Embedding model is not configured.');
  }

  // Check if embedding model is appropriate for embeddings
  if (AI_CONFIG.models.embedding === AI_CONFIG.models.chat) {
    warnings.push('Using chat model for embeddings. Consider using a dedicated embedding model like text-embedding-3-small');
  }

  // Test embedding model initialization
  try {
    const embeddingModel = aiModels.embedding;
    // Just verify the model can be created without errors
    if (!embeddingModel) {
      errors.push('Failed to create embedding model instance');
    }
  } catch (error) {
    errors.push(`Failed to initialize embedding model: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// AI operation types for type safety (Rule 5)
export const AIOperationSchema = z.object({
  id: z.string(),
  type: z.enum(['ocr', 'translation', 'card-generation', 'graph-generation', 'chat']),
  status: z.enum(['pending', 'processing', 'completed', 'failed', 'cancelled']),
  progress: z.number().min(0).max(100),
  startTime: z.date(),
  endTime: z.date().optional(),
  error: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export type AIOperation = z.infer<typeof AIOperationSchema>;

// Rate limiting state schema (Rule 11)
const RateLimitStateSchema = z.object({
  requests: z.array(z.number()),
  tokens: z.number(),
  lastReset: z.number(),
});

export type RateLimitState = z.infer<typeof RateLimitStateSchema>;

// AI service health check function
export async function checkAIServiceHealth(): Promise<{
  isHealthy: boolean;
  latency?: number;
  error?: string;
}> {
  const startTime = Date.now();
  
  try {
    // Simple health check with minimal token usage
    const response = await fetch(`${AI_CONFIG.baseURL}/models`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${AI_CONFIG.apiKey}`,
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(AI_CONFIG.timeout),
    });

    if (!response.ok) {
      throw new Error(`Health check failed: ${response.status} ${response.statusText}`);
    }

    const latency = Date.now() - startTime;
    return {
      isHealthy: true,
      latency,
    };
  } catch (error) {
    return {
      isHealthy: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Export configuration for debugging and monitoring
export const getAIConfigInfo = () => ({
  baseURL: AI_CONFIG.baseURL,
  models: AI_CONFIG.models,
  timeout: AI_CONFIG.timeout,
  maxRetries: AI_CONFIG.maxRetries,
  rateLimits: AI_CONFIG.rateLimits,
  // Don't expose API key for security
});

// Development mode helpers
export const isDevelopment = process.env.NODE_ENV === 'development';

if (isDevelopment) {
  console.log('🤖 AI Service Configuration:', getAIConfigInfo());
}
