/**
 * AppWrite Permissions Configuration
 * 
 * Defines database collections, permissions, and indexes for the permission system:
 * - User permissions and roles
 * - Usage tracking across features
 * - Subscription management
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 11: Zod validation for all data structures
 * - Rule 12: Comprehensive error handling
 * - Rule 5: TypeScript quality and type safety
 */

import { z } from 'zod';
import { Permission, Role } from 'react-native-appwrite';

// Database collection IDs
export const COLLECTIONS = {
  USER_PERMISSIONS: 'user_permissions',
  USAGE_TRACKING: 'usage_tracking',
  SUBSCRIPTION_PLANS: 'subscription_plans',
  FEATURE_FLAGS: 'feature_flags',
} as const;

// Zod schemas for data validation (Rule 11)
export const UserPermissionSchema = z.object({
  userId: z.string(),
  role: z.enum(['guest', 'authenticated', 'premium', 'admin']),
  subscription: z.object({
    plan: z.string(),
    status: z.enum(['active', 'expired', 'cancelled', 'trial']),
    expiresAt: z.string().optional(),
    features: z.array(z.string()).default([]),
  }).optional(),
  permissions: z.array(z.string()).default([]),
  metadata: z.record(z.any()).optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const UsageTrackingSchema = z.object({
  userId: z.string(),
  feature: z.string(),
  date: z.string(), // YYYY-MM-DD format
  usage: z.number().min(0),
  limit: z.number().min(-1), // -1 for unlimited
  resetPeriod: z.enum(['daily', 'weekly', 'monthly']).default('daily'),
  metadata: z.record(z.any()).optional(),
  timestamp: z.string(),
});

export const SubscriptionPlanSchema = z.object({
  planId: z.string(),
  name: z.string(),
  description: z.string(),
  price: z.number().min(0),
  currency: z.string().default('USD'),
  interval: z.enum(['monthly', 'yearly', 'lifetime']),
  features: z.array(z.string()),
  limits: z.record(z.number()), // feature -> limit mapping
  isActive: z.boolean().default(true),
  sortOrder: z.number().default(0),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// TypeScript types
export type UserPermission = z.infer<typeof UserPermissionSchema>;
export type UsageTracking = z.infer<typeof UsageTrackingSchema>;
export type SubscriptionPlan = z.infer<typeof SubscriptionPlanSchema>;

/**
 * AppWrite Database Collection Configurations
 */
export const DATABASE_CONFIG = {
  // User Permissions Collection
  [COLLECTIONS.USER_PERMISSIONS]: {
    name: 'User Permissions',
    attributes: [
      { key: 'userId', type: 'string', size: 255, required: true },
      { key: 'role', type: 'string', size: 50, required: true },
      { key: 'subscription', type: 'string', size: 1000, required: false }, // JSON string
      { key: 'permissions', type: 'string', size: 2000, required: false }, // JSON array
      { key: 'metadata', type: 'string', size: 1000, required: false }, // JSON object
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'updatedAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'userId_index', type: 'key', attributes: ['userId'] },
      { key: 'role_index', type: 'key', attributes: ['role'] },
      { key: 'created_index', type: 'key', attributes: ['createdAt'] },
    ],
    permissions: {
      read: [
        Permission.read(Role.any()), // Users can read their own permissions
      ],
      write: [
        Permission.write(Role.label('admin')), // Only admins can modify permissions
        Permission.write(Role.label('system')), // System processes can update
      ],
      create: [
        Permission.create(Role.label('system')),
      ],
      update: [
        Permission.update(Role.label('admin')),
        Permission.update(Role.label('system')),
      ],
      delete: [
        Permission.delete(Role.label('admin')),
      ],
    },
  },

  // Usage Tracking Collection
  [COLLECTIONS.USAGE_TRACKING]: {
    name: 'Usage Tracking',
    attributes: [
      { key: 'userId', type: 'string', size: 255, required: true },
      { key: 'feature', type: 'string', size: 100, required: true },
      { key: 'date', type: 'string', size: 20, required: true }, // YYYY-MM-DD
      { key: 'usage', type: 'integer', required: true },
      { key: 'limit', type: 'integer', required: true },
      { key: 'resetPeriod', type: 'string', size: 20, required: true },
      { key: 'metadata', type: 'string', size: 1000, required: false },
      { key: 'timestamp', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'user_feature_date', type: 'key', attributes: ['userId', 'feature', 'date'] },
      { key: 'user_date', type: 'key', attributes: ['userId', 'date'] },
      { key: 'feature_date', type: 'key', attributes: ['feature', 'date'] },
      { key: 'timestamp_index', type: 'key', attributes: ['timestamp'] },
    ],
    permissions: {
      read: [
        Permission.read(Role.any()), // Users can read their own usage data
      ],
      write: [
        Permission.write(Role.any()), // Users can write their own usage data
      ],
      create: [
        Permission.create(Role.any()),
      ],
      update: [
        Permission.update(Role.any()),
      ],
      delete: [
        Permission.delete(Role.label('admin')), // Only admins can delete usage data
      ],
    },
  },

  // Subscription Plans Collection
  [COLLECTIONS.SUBSCRIPTION_PLANS]: {
    name: 'Subscription Plans',
    attributes: [
      { key: 'planId', type: 'string', size: 100, required: true },
      { key: 'name', type: 'string', size: 100, required: true },
      { key: 'description', type: 'string', size: 500, required: true },
      { key: 'price', type: 'double', required: true },
      { key: 'currency', type: 'string', size: 10, required: true },
      { key: 'interval', type: 'string', size: 20, required: true },
      { key: 'features', type: 'string', size: 2000, required: true }, // JSON array
      { key: 'limits', type: 'string', size: 1000, required: true }, // JSON object
      { key: 'isActive', type: 'boolean', required: true },
      { key: 'sortOrder', type: 'integer', required: true },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'updatedAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'planId_index', type: 'key', attributes: ['planId'] },
      { key: 'active_plans', type: 'key', attributes: ['isActive'] },
      { key: 'sort_order', type: 'key', attributes: ['sortOrder'] },
    ],
    permissions: {
      read: [
        Permission.read(Role.any()), // Everyone can read plan information
      ],
      write: [
        Permission.write(Role.label('admin')), // Only admins can modify plans
      ],
      create: [
        Permission.create(Role.label('admin')),
      ],
      update: [
        Permission.update(Role.label('admin')),
      ],
      delete: [
        Permission.delete(Role.label('admin')),
      ],
    },
  },

  // Feature Flags Collection
  [COLLECTIONS.FEATURE_FLAGS]: {
    name: 'Feature Flags',
    attributes: [
      { key: 'flagKey', type: 'string', size: 100, required: true },
      { key: 'name', type: 'string', size: 100, required: true },
      { key: 'description', type: 'string', size: 500, required: false },
      { key: 'isEnabled', type: 'boolean', required: true },
      { key: 'targetRoles', type: 'string', size: 500, required: false }, // JSON array
      { key: 'targetUsers', type: 'string', size: 1000, required: false }, // JSON array
      { key: 'rolloutPercentage', type: 'integer', required: true }, // 0-100
      { key: 'metadata', type: 'string', size: 1000, required: false },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'updatedAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'flag_key', type: 'key', attributes: ['flagKey'] },
      { key: 'enabled_flags', type: 'key', attributes: ['isEnabled'] },
    ],
    permissions: {
      read: [
        Permission.read(Role.any()), // Everyone can read feature flags
      ],
      write: [
        Permission.write(Role.label('admin')), // Only admins can modify flags
      ],
      create: [
        Permission.create(Role.label('admin')),
      ],
      update: [
        Permission.update(Role.label('admin')),
      ],
      delete: [
        Permission.delete(Role.label('admin')),
      ],
    },
  },
};

/**
 * Default subscription plans
 */
export const DEFAULT_SUBSCRIPTION_PLANS: Omit<SubscriptionPlan, 'createdAt' | 'updatedAt'>[] = [
  {
    planId: 'free',
    name: 'Free',
    description: 'Perfect for getting started with LearniScan',
    price: 0,
    currency: 'USD',
    interval: 'monthly',
    features: [
      'Basic document scanning',
      'Manual knowledge cards',
      'Basic study mode',
      'Local storage',
      'Limited AI features',
    ],
    limits: {
      daily_scans: 50,
      total_cards: 100,
      ai_searches: 10,
      ai_generations: 20,
      storage_mb: 1024,
    },
    isActive: true,
    sortOrder: 1,
  },
  {
    planId: 'premium_monthly',
    name: 'Premium',
    description: 'Unlock the full power of AI-enhanced learning',
    price: 9.99,
    currency: 'USD',
    interval: 'monthly',
    features: [
      'Unlimited document scanning',
      'AI camera enhancement',
      'Unlimited knowledge cards',
      'AI study sessions',
      'Knowledge graph intelligence',
      'Unlimited cloud storage',
      'Priority support',
      'Advanced analytics',
    ],
    limits: {
      daily_scans: -1,
      total_cards: -1,
      ai_searches: -1,
      ai_generations: -1,
      storage_mb: -1,
    },
    isActive: true,
    sortOrder: 2,
  },
  {
    planId: 'premium_yearly',
    name: 'Premium (Yearly)',
    description: 'Save 20% with annual billing',
    price: 99.99,
    currency: 'USD',
    interval: 'yearly',
    features: [
      'Everything in Premium',
      '2 months free',
      'Early access to new features',
    ],
    limits: {
      daily_scans: -1,
      total_cards: -1,
      ai_searches: -1,
      ai_generations: -1,
      storage_mb: -1,
    },
    isActive: true,
    sortOrder: 3,
  },
];

/**
 * Database initialization function
 */
export const initializePermissionDatabase = async (databases: any, databaseId: string) => {
  try {
    // Create collections if they don't exist
    for (const [collectionId, config] of Object.entries(DATABASE_CONFIG)) {
      try {
        await databases.createCollection(
          databaseId,
          collectionId,
          config.name,
          config.permissions.read,
          config.permissions.write
        );

        // Create attributes
        for (const attr of config.attributes) {
          if (attr.type === 'string') {
            await databases.createStringAttribute(
              databaseId,
              collectionId,
              attr.key,
              attr.size,
              attr.required
            );
          } else if (attr.type === 'integer') {
            await databases.createIntegerAttribute(
              databaseId,
              collectionId,
              attr.key,
              attr.required
            );
          } else if (attr.type === 'double') {
            await databases.createFloatAttribute(
              databaseId,
              collectionId,
              attr.key,
              attr.required
            );
          } else if (attr.type === 'boolean') {
            await databases.createBooleanAttribute(
              databaseId,
              collectionId,
              attr.key,
              attr.required
            );
          } else if (attr.type === 'datetime') {
            await databases.createDatetimeAttribute(
              databaseId,
              collectionId,
              attr.key,
              attr.required
            );
          }
        }

        // Create indexes
        for (const index of config.indexes) {
          await databases.createIndex(
            databaseId,
            collectionId,
            index.key,
            index.type,
            index.attributes
          );
        }

        console.log(`✅ Created collection: ${config.name}`);
      } catch (error: any) {
        if (error.code === 409) {
          console.log(`ℹ️ Collection already exists: ${config.name}`);
        } else {
          console.error(`❌ Failed to create collection ${config.name}:`, error);
        }
      }
    }

    console.log('✅ Permission database initialization complete');
  } catch (error) {
    console.error('❌ Permission database initialization failed:', error);
    throw error;
  }
};

export default DATABASE_CONFIG;
