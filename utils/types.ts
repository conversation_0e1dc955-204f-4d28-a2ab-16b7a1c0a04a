import type { ITemplateConfig } from '../interfaces/ITemplateConfig';
import { CompletionParams } from '@pocketpalai/llama.rn';

// Common types used across multiple stores
export enum ModelOrigin {
  PRESET = 'PRESET',
  HF = 'HF',
  LOCAL = 'LOCAL',
}

export enum CacheType {
  F16 = 0,
  F32 = 1,
  Q4_0 = 2,
  Q4_1 = 3,
  Q5_0 = 4,
  Q5_1 = 5,
  Q8_0 = 6,
  AUTO = 7,
}

// Model types
export interface ModelFile {
  rfilename: string;
  url: string;
  size?: number;
  oid?: string;
  lfs?: {
    oid: string;
    size: number;
    pointerSize: number;
  };
  canFitInStorage?: boolean;
}

export interface ChatTemplateConfig extends ITemplateConfig {
  addGenerationPrompt: boolean;
  systemPrompt?: string;
  name: string;
}

export interface Model {
  id: string;
  author: string;
  name: string;
  type: string;
  description: string;
  size: number;
  params?: number;
  isDownloaded: boolean;
  downloadUrl: string;
  hfUrl?: string;
  progress: number;
  filename: string;
  isLocal: boolean;
  origin: ModelOrigin;
  chatTemplate: ChatTemplateConfig;
  defaultChatTemplate: ChatTemplateConfig;
  completionSettings: CompletionParams;
  defaultCompletionSettings: CompletionParams;
  stopWords: string[];
  defaultStopWords: string[];
  hfModelFile?: ModelFile;
  hash?: string;
  fullPath?: string;
}

// Hugging Face model interface
export interface HuggingFaceModel {
  id: string;
  author: string;
  type: string;
  downloads?: number;
  likes?: number;
  siblings: ModelFile[];
  url?: string;
  specs?: any;
}

// Benchmark types
export interface BenchmarkResult {
  uuid: string;
  timestamp: string;
  modelId: string;
  modelName: string;
  deviceInfo: {
    brand: string;
    model: string;
    systemVersion: string;
  };
  contextSize: number;
  loadTime: number;
  tokenTime: number[];
  tokenCount: number;
  tokensPerSecond: number;
  submitted?: boolean;
}

export const MessageType = {
  Text: 'text',
  Image: 'image',
  Audio: 'audio',
  Video: 'video',
  File: 'file',
} as const;

// Message types
export namespace MessageType {
  export interface Base {
    id: string;
    author: {
      name: string;
    };
    createdAt: number;
    type: string;
    metadata?: Record<string, any>;
  }

  export interface Text extends Base {
    type: typeof MessageType.Text;
    text: string;
  }

  export interface Image extends Base {
    type: typeof MessageType.Image;
    uri: string;
    width: number;
    height: number;
  }

  export interface Audio extends Base {
    type: typeof MessageType.Audio;
    uri: string;
    duration: number;
  }

  export interface Video extends Base {
    type: typeof MessageType.Video;
    uri: string;
    thumbnail?: string;
    duration: number;
  }

  export interface File extends Base {
    type: typeof MessageType.File;
    uri: string;
    name: string;
    size: number;
    mimeType: string;
  }

  export type Any = Text | Image | Audio | Video | File;
}

export type ChatMessage = {
  role: 'system' | 'assistant' | 'user';
  content: string;
};
