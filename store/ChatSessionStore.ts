import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { CompletionParams, LlamaContext } from '@pocketpalai/llama.rn';
import * as RNFS from '@dr.pogodin/react-native-fs';
import { format, isToday, isYesterday } from 'date-fns';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { assistant, defaultCompletionParams } from '../utils/chat';
import { ISessionMetaData } from '@/interfaces/types';
import { MessageType } from '@/interfaces/types';

const NEW_SESSION_TITLE = 'New Session';
const TITLE_LIMIT = 40;

export const defaultCompletionSettings = {...defaultCompletionParams};
delete defaultCompletionSettings.prompt;
delete defaultCompletionSettings.stop;

interface SessionGroup {
  [key: string]: ISessionMetaData[];
}

interface ChatSessionState {
  sessions: ISessionMetaData[];
  activeSessionId: string | null;
  isEditMode: boolean;
  editingMessageId: string | null;
  isGenerating: boolean;
  newChatCompletionSettings: CompletionParams;
  newChatPalId: string | undefined;
  
  // Computed properties
  shouldShowHeaderDivider: boolean;
  currentSessionMessages: MessageType.Any[];
  groupedSessions: SessionGroup;
  activePalId: string | undefined;
}

interface ChatSessionActions {
  setIsGenerating: (value: boolean) => void;
  loadSessionList: () => Promise<void>;
  deleteSession: (id: string) => Promise<void>;
  duplicateSession: (id: string) => Promise<void>;
  resetActiveSession: () => void;
  setActiveSession: (sessionId: string) => void;
  updateSessionTitleBySessionId: (sessionId: string, newTitle: string) => void;
  updateSessionTitle: (session: ISessionMetaData) => void;
  addMessageToCurrentSession: (message: MessageType.Any) => void;
  saveSessionsMetadata: () => Promise<void>;
  setNewChatCompletionSettings: (settings: CompletionParams) => void;
  resetNewChatCompletionSettings: () => void;
  createNewSession: (title: string, initialMessages?: MessageType.Any[], completionSettings?: CompletionParams) => Promise<void>;
  updateMessage: (id: string, update: Partial<MessageType.IText>) => void;
  updateSessionCompletionSettings: (settings: CompletionParams) => void;
  updateMessageToken: (data: any, createdAt: number, id: string, context: LlamaContext) => void;
  enterEditMode: (messageId: string) => void;
  exitEditMode: () => void;
  commitEdit: () => void;
  removeMessagesFromId: (messageId: string, includeMessage?: boolean) => void;
  setActivePal: (palId: string | undefined) => void;
}

export const useChatSessionStore = create<ChatSessionState & ChatSessionActions>()(
  persist(
    (set, get) => ({
      // State
      sessions: [],
      activeSessionId: null,
      isEditMode: false,
      editingMessageId: null,
      isGenerating: false,
      newChatCompletionSettings: defaultCompletionSettings,
      newChatPalId: undefined,
      
      // Computed properties
      get shouldShowHeaderDivider() {
        const state = get();
        return (
          !state.activeSessionId ||
          (state.currentSessionMessages.length === 0 &&
            !state.isGenerating &&
            !state.isEditMode)
        );
      },
      
      get currentSessionMessages() {
        const state = get();
        if (state.activeSessionId) {
          const session = state.sessions.find(s => s.id === state.activeSessionId);
          if (session) {
            if (state.isEditMode && state.editingMessageId) {
              const messageIndex = session.messages.findIndex(
                msg => msg.id === state.editingMessageId,
              );
              if (messageIndex >= 0) {
                return session.messages.slice(messageIndex + 1);
              }
            }
            return session.messages;
          }
        }
        return [];
      },
      
      get activePalId() {
        const state = get();
        if (state.activeSessionId) {
          const session = state.sessions.find(s => s.id === state.activeSessionId);
          return session?.activePalId;
        }
        return state.newChatPalId;
      },
      
      get groupedSessions() {
        const sessions = get().sessions;
        const groups: SessionGroup = sessions.reduce(
          (acc: SessionGroup, session) => {
            const date = new Date(session.date);
            let dateKey: string = format(date, 'MMMM dd, yyyy');
            const today = new Date();
            const daysAgo = Math.ceil(
              (today.getTime() - date.getTime()) / (1000 * 3600 * 24),
            );
    
            if (isToday(date)) {
              dateKey = 'Today';
            } else if (isYesterday(date)) {
              dateKey = 'Yesterday';
            } else if (daysAgo <= 6) {
              dateKey = 'This week';
            } else if (daysAgo <= 13) {
              dateKey = 'Last week';
            } else if (daysAgo <= 20) {
              dateKey = '2 weeks ago';
            } else if (daysAgo <= 27) {
              dateKey = '3 weeks ago';
            } else if (daysAgo <= 34) {
              dateKey = '4 weeks ago';
            } else if (daysAgo <= 60) {
              dateKey = 'Last month';
            } else {
              dateKey = 'Older';
            }
    
            if (!acc[dateKey]) {
              acc[dateKey] = [];
            }
            acc[dateKey].push(session);
            return acc;
          },
          {},
        );
    
        // Define the order of keys
        const orderedKeys = [
          'Today',
          'Yesterday',
          'This week',
          'Last week',
          '2 weeks ago',
          '3 weeks ago',
          '4 weeks ago',
          'Last month',
          'Older',
        ];
    
        // Create a new object with keys in the desired order
        const orderedGroups: SessionGroup = {};
        orderedKeys.forEach(key => {
          if (groups[key]) {
            orderedGroups[key] = groups[key].sort(
              (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
            );
          }
        });
    
        // Add any remaining keys that weren't in our predefined list
        Object.keys(groups).forEach(key => {
          if (!orderedGroups[key]) {
            orderedGroups[key] = groups[key].sort(
              (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
            );
          }
        });
    
        return orderedGroups;
      },
      
      // Actions
      setIsGenerating: (value) => set({ isGenerating: value }),
      
      loadSessionList: async () => {
        const path = `${RNFS.DocumentDirectoryPath}/session-metadata.json`;
        try {
          const data = await RNFS.readFile(path);
          set({ sessions: JSON.parse(data) });
        } catch (error) {
          console.error('Failed to load session list:', error);
        }
      },
      
      deleteSession: async (id) => {
        try {
          const sessionFile = `${RNFS.DocumentDirectoryPath}/${id}.llama-session.bin`;
          const fileExists = await RNFS.exists(sessionFile);
          if (fileExists) {
            await RNFS.unlink(sessionFile);
          }
    
          const state = get();
          if (id === state.activeSessionId) {
            state.resetActiveSession();
          }
    
          set(state => ({ 
            sessions: state.sessions.filter(session => session.id !== id) 
          }));
    
          await get().saveSessionsMetadata();
        } catch (error) {
          console.error('Failed to delete session:', error);
        }
      },
      
      duplicateSession: async (id) => {
        const session = get().sessions.find(s => s.id === id);
        if (session) {
          await get().createNewSession(
            `${session.title} - Copy`,
            session.messages,
            session.completionSettings,
          );
        }
      },
      
      resetActiveSession: () => {
        const activePalId = get().activePalId;
        set(state => ({
          newChatPalId: activePalId,
          activeSessionId: null,
          isEditMode: false,
          editingMessageId: null,
        }));
      },
      
      setActiveSession: (sessionId) => {
        set({
          activeSessionId: sessionId,
          isEditMode: false,
          editingMessageId: null,
          newChatCompletionSettings: defaultCompletionSettings,
          newChatPalId: undefined
        });
      },
      
      updateSessionTitleBySessionId: (sessionId, newTitle) => {
        set(state => ({
          sessions: state.sessions.map(session => 
            session.id === sessionId 
              ? { ...session, title: newTitle } 
              : session
          )
        }));
        get().saveSessionsMetadata();
      },
      
      updateSessionTitle: (session) => {
        if (session.messages.length > 0) {
          const message = session.messages[session.messages.length - 1];
          if (session.title === NEW_SESSION_TITLE && message.type === 'text') {
            const newTitle = message.text.length > TITLE_LIMIT
              ? `${message.text.substring(0, TITLE_LIMIT)}...`
              : message.text;
            
            set(state => ({
              sessions: state.sessions.map(s => 
                s.id === session.id 
                  ? { ...s, title: newTitle } 
                  : s
              )
            }));
          }
        }
      },
      
      addMessageToCurrentSession: (message) => {
        const state = get();
        if (state.activeSessionId) {
          const sessionIndex = state.sessions.findIndex(s => s.id === state.activeSessionId);
          if (sessionIndex !== -1) {
            const updatedSession = { 
              ...state.sessions[sessionIndex],
              messages: [message, ...state.sessions[sessionIndex].messages]
            };
            
            set(state => ({
              sessions: [
                ...state.sessions.slice(0, sessionIndex),
                updatedSession,
                ...state.sessions.slice(sessionIndex + 1)
              ]
            }));
            
            get().updateSessionTitle(updatedSession);
            get().saveSessionsMetadata();
          }
        } else {
          get().createNewSession(NEW_SESSION_TITLE, [message]);
        }
      },
      
      saveSessionsMetadata: async () => {
        try {
          await RNFS.writeFile(
            `${RNFS.DocumentDirectoryPath}/session-metadata.json`,
            JSON.stringify(get().sessions),
          );
        } catch (error) {
          console.error('Failed to save sessions metadata:', error);
        }
      },
      
      setNewChatCompletionSettings: (settings) => {
        set({ newChatCompletionSettings: settings });
      },
      
      resetNewChatCompletionSettings: () => {
        set({ newChatCompletionSettings: defaultCompletionSettings });
      },
      
      createNewSession: async (title, initialMessages = [], completionSettings = defaultCompletionSettings) => {
        const id = new Date().toISOString();
        const metaData: ISessionMetaData = {
          id,
          title,
          date: id,
          messages: initialMessages,
          completionSettings,
        };
    
        const state = get();
        if (Object.keys(state.newChatCompletionSettings).length > 0) {
          metaData.completionSettings = state.newChatCompletionSettings;
          set({ newChatCompletionSettings: defaultCompletionSettings });
        }
    
        if (state.newChatPalId) {
          metaData.activePalId = state.newChatPalId;
          set({ newChatPalId: undefined });
        }
    
        get().updateSessionTitle(metaData);
        
        set(state => ({
          sessions: [...state.sessions, metaData],
          activeSessionId: id,
        }));
        
        await RNFS.writeFile(
          `${RNFS.DocumentDirectoryPath}/session-metadata.json`,
          JSON.stringify(get().sessions),
        );
      },
      
      updateMessage: (id, update) => {
        const state = get();
        if (state.activeSessionId) {
          const sessionIndex = state.sessions.findIndex(s => s.id === state.activeSessionId);
          if (sessionIndex !== -1) {
            const messageIndex = state.sessions[sessionIndex].messages.findIndex(msg => msg.id === id);
            if (messageIndex >= 0 && state.sessions[sessionIndex].messages[messageIndex].type === 'text') {
              set(state => {
                const updatedSessions = [...state.sessions];
                const updatedMessages = [...updatedSessions[sessionIndex].messages];
                updatedMessages[messageIndex] = {
                  ...updatedMessages[messageIndex],
                  ...update,
                } as MessageType.IText;
                updatedSessions[sessionIndex] = {
                  ...updatedSessions[sessionIndex],
                  messages: updatedMessages
                };
                return { sessions: updatedSessions };
              });
              
              get().saveSessionsMetadata();
            }
          }
        }
      },
      
      updateSessionCompletionSettings: (settings) => {
        const state = get();
        if (state.activeSessionId) {
          set(state => ({
            sessions: state.sessions.map(session => 
              session.id === state.activeSessionId 
                ? { ...session, completionSettings: settings } 
                : session
            )
          }));
          get().saveSessionsMetadata();
        }
      },
      
      updateMessageToken: (data, createdAt, id, context) => {
        const {token} = data;
        const state = get();
        
        if (state.activeSessionId) {
          const sessionIndex = state.sessions.findIndex(s => s.id === state.activeSessionId);
          if (sessionIndex !== -1) {
            const index = state.sessions[sessionIndex].messages.findIndex(msg => msg.id === id);
            
            if (index >= 0) {
              // Update existing message
              set(state => {
                const updatedSessions = [...state.sessions];
                const updatedMessages = [...updatedSessions[sessionIndex].messages];
                
                if (updatedMessages[index].type === 'text') {
                  updatedMessages[index] = {
                    ...updatedMessages[index],
                    text: (updatedMessages[index].text + token).replace(/^\s+/, ''),
                  } as MessageType.IText;
                  
                  updatedSessions[sessionIndex] = {
                    ...updatedSessions[sessionIndex],
                    messages: updatedMessages
                  };
                }
                
                return { sessions: updatedSessions };
              });
            } else {
              // Add new message
              set(state => {
                const updatedSessions = [...state.sessions];
                const newMessage: MessageType.IText = {
                  author: assistant,
                  createdAt,
                  id,
                  text: token,
                  type: 'text',
                  metadata: {contextId: context?.id, copyable: true},
                };
                
                updatedSessions[sessionIndex] = {
                  ...updatedSessions[sessionIndex],
                  messages: [newMessage, ...updatedSessions[sessionIndex].messages]
                };
                
                return { sessions: updatedSessions };
              });
            }
            
            get().saveSessionsMetadata();
          }
        }
      },
      
      enterEditMode: (messageId) => {
        const state = get();
        if (state.activeSessionId) {
          const session = state.sessions.find(s => s.id === state.activeSessionId);
          if (session) {
            const messageIndex = session.messages.findIndex(
              msg => msg.id === messageId,
            );
            if (messageIndex >= 0) {
              set({ isEditMode: true, editingMessageId: messageId });
            }
          }
        }
      },
      
      exitEditMode: () => {
        set({ isEditMode: false, editingMessageId: null });
      },
      
      commitEdit: () => {
        const state = get();
        if (state.activeSessionId && state.editingMessageId) {
          const sessionIndex = state.sessions.findIndex(s => s.id === state.activeSessionId);
          if (sessionIndex >= 0) {
            const session = state.sessions[sessionIndex];
            const messageIndex = session.messages.findIndex(
              msg => msg.id === state.editingMessageId,
            );
            if (messageIndex >= 0) {
              set(state => {
                const updatedSessions = [...state.sessions];
                updatedSessions[sessionIndex] = {
                  ...updatedSessions[sessionIndex],
                  messages: updatedSessions[sessionIndex].messages.slice(messageIndex + 1)
                };
                
                return { 
                  sessions: updatedSessions,
                  isEditMode: false,
                  editingMessageId: null
                };
              });
              
              get().saveSessionsMetadata();
            }
          }
        }
      },
      
      removeMessagesFromId: (messageId, includeMessage = true) => {
        const state = get();
        if (state.activeSessionId) {
          const sessionIndex = state.sessions.findIndex(s => s.id === state.activeSessionId);
          if (sessionIndex >= 0) {
            const session = state.sessions[sessionIndex];
            const messageIndex = session.messages.findIndex(
              msg => msg.id === messageId,
            );
            if (messageIndex >= 0) {
              set(state => {
                const updatedSessions = [...state.sessions];
                updatedSessions[sessionIndex] = {
                  ...updatedSessions[sessionIndex],
                  messages: updatedSessions[sessionIndex].messages.slice(
                    includeMessage ? messageIndex + 1 : messageIndex,
                  )
                };
                
                return { sessions: updatedSessions };
              });
              
              get().saveSessionsMetadata();
            }
          }
        }
      },
      
      setActivePal: (palId) => {
        const state = get();
        if (state.activeSessionId) {
          set(state => ({
            sessions: state.sessions.map(session => 
              session.id === state.activeSessionId 
                ? { ...session, activePalId: palId } 
                : session
            )
          }));
          get().saveSessionsMetadata();
        } else {
          set({ newChatPalId: palId });
        }
      }
    }),
    {
      name: 'chat-session-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        sessions: state.sessions,
        activeSessionId: state.activeSessionId,
        newChatPalId: state.newChatPalId,
      }),
    }
  )
);

// For backward compatibility
export const chatSessionStore = {
  get sessions() {
    return useChatSessionStore.getState().sessions;
  },
  get activeSessionId() {
    return useChatSessionStore.getState().activeSessionId;
  },
  get isEditMode() {
    return useChatSessionStore.getState().isEditMode;
  },
  get editingMessageId() {
    return useChatSessionStore.getState().editingMessageId;
  },
  get isGenerating() {
    return useChatSessionStore.getState().isGenerating;
  },
  get shouldShowHeaderDivider() {
    return useChatSessionStore.getState().shouldShowHeaderDivider;
  },
  get currentSessionMessages() {
    return useChatSessionStore.getState().currentSessionMessages;
  },
  get activePalId() {
    return useChatSessionStore.getState().activePalId;
  },
  get groupedSessions() {
    return useChatSessionStore.getState().groupedSessions;
  },
  get newChatCompletionSettings() {
    return useChatSessionStore.getState().newChatCompletionSettings;
  },
  
  // Methods
  setIsGenerating: (value: boolean) => 
    useChatSessionStore.getState().setIsGenerating(value),
  loadSessionList: () => 
    useChatSessionStore.getState().loadSessionList(),
  deleteSession: (id: string) => 
    useChatSessionStore.getState().deleteSession(id),
  duplicateSession: (id: string) => 
    useChatSessionStore.getState().duplicateSession(id),
  resetActiveSession: () => 
    useChatSessionStore.getState().resetActiveSession(),
  setActiveSession: (id: string) => 
    useChatSessionStore.getState().setActiveSession(id),
  updateSessionTitleBySessionId: (id: string, title: string) => 
    useChatSessionStore.getState().updateSessionTitleBySessionId(id, title),
  updateSessionTitle: (session: ISessionMetaData) => 
    useChatSessionStore.getState().updateSessionTitle(session),
  addMessageToCurrentSession: (message: MessageType.Any) => 
    useChatSessionStore.getState().addMessageToCurrentSession(message),
  saveSessionsMetadata: () => 
    useChatSessionStore.getState().saveSessionsMetadata(),
  createNewSession: (title: string, initialMessages: MessageType.Any[] = [], completionSettings = defaultCompletionSettings) => 
    useChatSessionStore.getState().createNewSession(title, initialMessages, completionSettings),
  updateMessage: (id: string, update: Partial<MessageType.IText>) => 
    useChatSessionStore.getState().updateMessage(id, update),
  updateSessionCompletionSettings: (settings: CompletionParams) => 
    useChatSessionStore.getState().updateSessionCompletionSettings(settings),
  updateMessageToken: (data: any, createdAt: number, id: string, context: LlamaContext) => 
    useChatSessionStore.getState().updateMessageToken(data, createdAt, id, context),
  enterEditMode: (id: string) => 
    useChatSessionStore.getState().enterEditMode(id),
  exitEditMode: () => 
    useChatSessionStore.getState().exitEditMode(),
  commitEdit: () => 
    useChatSessionStore.getState().commitEdit(),
  removeMessagesFromId: (id: string, includeMessage: boolean = true) => 
    useChatSessionStore.getState().removeMessagesFromId(id, includeMessage),
  setActivePal: (id: string | undefined) => 
    useChatSessionStore.getState().setActivePal(id),
  setNewChatCompletionSettings: (settings: CompletionParams) =>
    useChatSessionStore.getState().setNewChatCompletionSettings(settings),
  resetNewChatCompletionSettings: () =>
    useChatSessionStore.getState().resetNewChatCompletionSettings(),
};
