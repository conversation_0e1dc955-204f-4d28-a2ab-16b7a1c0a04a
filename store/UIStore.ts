import {Appearance} from 'react-native';
import {create} from 'zustand';
import {createJSONStorage, persist} from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define constants
const GROUP_KEYS = {
  READY_TO_USE: 'ready_to_use',
  AVAILABLE_TO_DOWNLOAD: 'available_to_download',
} as const;

// Define the store interface
interface UIStoreState {
  pageStates: {
    modelsScreen: {
      filters: string[];
      expandedGroups: {
        [key: string]: boolean;
      };
    };
  };
  autoNavigatetoChat: boolean;
  colorScheme: 'light' | 'dark';
  displayMemUsage: boolean;
  iOSBackgroundDownloading: boolean;
  benchmarkShareDialog: {
    shouldShow: boolean;
  };
  
  // Actions
  setValue: <T extends keyof UIStoreState['pageStates']>(
    page: T,
    key: keyof UIStoreState['pageStates'][T],
    value: any,
  ) => void;
  setColorScheme: (colorScheme: 'light' | 'dark') => void;
  setAutoNavigateToChat: (value: boolean) => void;
  setDisplayMemUsage: (value: boolean) => void;
  setiOSBackgroundDownloading: (value: boolean) => void;
  setBenchmarkShareDialogPreference: (shouldShow: boolean) => void;
  showError: (message: string) => void;
}

// Create the Zustand store with persistence
export const useUIStore = create<UIStoreState>()(
  persist(
    (set) => ({
      pageStates: {
        modelsScreen: {
          filters: [],
          expandedGroups: {
            [GROUP_KEYS.READY_TO_USE]: true,
          },
        },
      },
      autoNavigatetoChat: true,
      colorScheme: Appearance.getColorScheme() ?? 'light',
      displayMemUsage: false,
      iOSBackgroundDownloading: true,
      benchmarkShareDialog: {
        shouldShow: true,
      },
      
      // Actions implementation
      setValue: (page, key, value) => 
        set((state) => ({
          pageStates: {
            ...state.pageStates,
            [page]: {
              ...state.pageStates[page],
              [key]: value,
            },
          },
        })),
        
      setColorScheme: (colorScheme) => 
        set({ colorScheme }),
        
      setAutoNavigateToChat: (value) => 
        set({ autoNavigatetoChat: value }),
        
      setDisplayMemUsage: (value) => 
        set({ displayMemUsage: value }),
        
      setiOSBackgroundDownloading: (value) => 
        set({ iOSBackgroundDownloading: value }),
        
      setBenchmarkShareDialogPreference: (shouldShow) => 
        set((state) => ({
          benchmarkShareDialog: {
            ...state.benchmarkShareDialog,
            shouldShow,
          },
        })),
        
      showError: (message) => {
        // TODO: Implement error display logic (e.g., toast, alert, etc.)
        console.error(message);
      },
    }),
    {
      name: 'ui-store',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

// For backward compatibility
export const uiStore = {
  // Expose GROUP_KEYS as a static property
  GROUP_KEYS,
  
  // Properties with getters
  get pageStates() {
    return useUIStore.getState().pageStates;
  },
  
  get autoNavigatetoChat() {
    return useUIStore.getState().autoNavigatetoChat;
  },
  
  get colorScheme() {
    return useUIStore.getState().colorScheme;
  },
  
  get displayMemUsage() {
    return useUIStore.getState().displayMemUsage;
  },
  
  get iOSBackgroundDownloading() {
    return useUIStore.getState().iOSBackgroundDownloading;
  },
  
  get benchmarkShareDialog() {
    return useUIStore.getState().benchmarkShareDialog;
  },
  
  // Methods
  setValue: <T extends keyof UIStoreState['pageStates']>(
    page: T,
    key: keyof UIStoreState['pageStates'][T],
    value: any,
  ) => useUIStore.getState().setValue(page, key, value),
  
  setColorScheme: (colorScheme: 'light' | 'dark') => 
    useUIStore.getState().setColorScheme(colorScheme),
  
  setAutoNavigateToChat: (value: boolean) => 
    useUIStore.getState().setAutoNavigateToChat(value),
  
  setDisplayMemUsage: (value: boolean) => 
    useUIStore.getState().setDisplayMemUsage(value),
  
  setiOSBackgroundDownloading: (value: boolean) => 
    useUIStore.getState().setiOSBackgroundDownloading(value),
  
  setBenchmarkShareDialogPreference: (shouldShow: boolean) => 
    useUIStore.getState().setBenchmarkShareDialogPreference(shouldShow),
    
  showError: (message: string) => useUIStore.getState().showError(message),
};
