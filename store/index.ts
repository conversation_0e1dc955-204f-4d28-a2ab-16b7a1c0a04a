// Export all stores from a central location
import { todoStore, useTodoStore } from './todoStore';
import { useThemeStore } from './theme';
import { palStore, usePalStore } from './PalStore';
import { modelStore, useModelStore } from './ModelStore';
import { hfStore, useHFStore } from './HFStore';
import { chatSessionStore, useChatSessionStore } from './ChatSessionStore';
import { benchmarkStore, useBenchmarkStore } from './BenchmarkStore';
import { initializeZustandStore } from '@/lib/helpers/storeHelpers';

// Export individual stores
export {
  // Instance objects for backward compatibility
  todoStore,
  palStore,
  modelStore,
  hfStore,
  chatSessionStore,
  benchmarkStore,
  
  // Zustand hooks
  useTodoStore,
  useThemeStore,
  usePalStore,
  useModelStore,
  useHFStore,
  useChatSessionStore,
  useBenchmarkStore,
};

// Export store initialization function
export const initializeStores = async () => {
  // Add any store initialization that should happen at app start
  try {
    await initializeZustandStore(() => 
      useModelStore.getState().initializeStore());
    return true;
  } catch (error) {
    console.error("Failed to initialize stores:", error);
    return false;
  }
};

// Export store cleanup function
export const cleanupStores = async () => {
  // Release resources, save state, etc.
  try {
    if (useModelStore.getState().context) {
      await useModelStore.getState().releaseContext();
    }
    return true;
  } catch (error) {
    console.error("Failed to clean up stores:", error);
    return false;
  }
};
