import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { IBenchmarkResult } from '@/interfaces/types';

interface BenchmarkStore {
  results: IBenchmarkResult[];
  addResult: (result: IBenchmarkResult) => void;
  removeResult: (timestamp: string) => void;
  clearResults: () => void;
  getResultsByModel: (modelId: string) => IBenchmarkResult[];
  latestResult: IBenchmarkResult | undefined;
  markAsSubmitted: (uuid: string) => void;
}

export const useBenchmarkStore = create<BenchmarkStore>()(
  persist(
    (set, get) => ({
      results: [],
      
      addResult: (result) => 
        set((state) => ({
          results: [result, ...state.results] // Add new result at the beginning
        })),
      
      removeResult: (timestamp) =>
        set((state) => ({
          results: state.results.filter(result => result.timestamp !== timestamp)
        })),
      
      clearResults: () => set({ results: [] }),
      
      getResultsByModel: (modelId) => 
        get().results.filter(result => result.modelId === modelId),
      
      get latestResult() {
        return get().results.length > 0 ? get().results[0] : undefined;
      },
      
      markAsSubmitted: (uuid) =>
        set((state) => ({
          results: state.results.map(result => 
            result.uuid === uuid 
              ? { ...result, submitted: true } 
              : result
          )
        })),
    }),
    {
      name: 'benchmark-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

// For backward compatibility with existing code that uses the benchmarkStore object
export const benchmarkStore = {
  get results() {
    return useBenchmarkStore.getState().results;
  },
  addResult: (result: IBenchmarkResult) => 
    useBenchmarkStore.getState().addResult(result),
  removeResult: (timestamp: string) => 
    useBenchmarkStore.getState().removeResult(timestamp),
  clearResults: () => 
    useBenchmarkStore.getState().clearResults(),
  getResultsByModel: (modelId: string) => 
    useBenchmarkStore.getState().getResultsByModel(modelId),
  get latestResult() {
    return useBenchmarkStore.getState().latestResult;
  },
  markAsSubmitted: (uuid: string) => 
    useBenchmarkStore.getState().markAsSubmitted(uuid),
};
