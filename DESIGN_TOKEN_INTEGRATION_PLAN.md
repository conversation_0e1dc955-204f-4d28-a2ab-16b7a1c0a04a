# Design Token Integration Plan

## Overview
This document outlines the specific implementation plan for integrating LearniScan's candy color design tokens into the Gluestack UI configuration system.

## Current Gluestack UI Configuration Analysis

### Current Config Structure (`components/ui/gluestack-ui-provider/config.ts`)
```typescript
export const config = {
  light: vars({
    '--color-primary-0': '255 255 255',
    '--color-primary-50': '250 250 250', 
    '--color-primary-500': '51 51 51',    // Current primary (dark gray)
    '--color-secondary-500': '217 217 219', // Current secondary (light gray)
    // ... more standard colors
  }),
  dark: vars({
    '--color-primary-500': '230 230 230',  // Light gray for dark mode
    // ... dark mode colors
  })
}
```

### Issues with Current Configuration
1. **Color Scheme**: Uses blue/gray theme instead of candy colors
2. **Limited Palette**: Missing candy pink, purple, blue variants
3. **No Glass Effects**: No CSS variables for glass morphism
4. **Typography**: No white text color variables for dark backgrounds
5. **Missing Semantic Colors**: No success, warning, error in candy colors

## LearniScan Design Token Requirements

### Candy Color Palette
```typescript
// Primary brand colors from design/design-tokens.js
const candyColors = {
  pink: '#FF6B9D',     // Primary brand color
  purple: '#A855F7',   // Secondary brand color  
  blue: '#3B82F6',     // Tertiary brand color
  cyan: '#06B6D4',     // Accent color
  green: '#10B981',    // Success color
  yellow: '#F59E0B',   // Warning color
  orange: '#F97316',   // Info color
  red: '#EF4444',      // Error color
};
```

### Glass Effect Colors
```typescript
// Glass morphism colors from design system
const glassColors = {
  bg: {
    primary: 'rgba(255, 255, 255, 0.1)',
    secondary: 'rgba(255, 255, 255, 0.05)',
    card: 'rgba(255, 255, 255, 0.08)',
  },
  border: {
    primary: 'rgba(255, 255, 255, 0.2)',
    secondary: 'rgba(255, 255, 255, 0.1)',
    accent: 'rgba(255, 107, 157, 0.3)',
  }
};
```

## Implementation Plan

### Step 1: Create New LearniScan Configuration

#### File: `components/ui/gluestack-ui-provider/learni-config.ts`
```typescript
import { vars } from 'nativewind';

export const learniConfig = {
  light: vars({
    // === CANDY COLOR PALETTE ===
    // Primary: Candy Pink (main brand color)
    '--color-primary-0': '255 245 250',     // Very light pink
    '--color-primary-50': '255 235 245',    // Light pink
    '--color-primary-100': '255 225 240',   // Lighter pink
    '--color-primary-200': '255 205 230',   // Light pink
    '--color-primary-300': '255 185 220',   // Medium light pink
    '--color-primary-400': '255 145 200',   // Medium pink
    '--color-primary-500': '255 107 157',   // Candy Pink (main)
    '--color-primary-600': '230 96 141',    // Darker pink
    '--color-primary-700': '204 85 125',    // Dark pink
    '--color-primary-800': '179 74 109',    // Darker pink
    '--color-primary-900': '153 64 94',     // Very dark pink
    '--color-primary-950': '128 53 78',     // Darkest pink

    // Secondary: Candy Purple
    '--color-secondary-0': '250 245 255',   // Very light purple
    '--color-secondary-50': '245 235 255',  // Light purple
    '--color-secondary-100': '240 225 255', // Lighter purple
    '--color-secondary-200': '230 205 255', // Light purple
    '--color-secondary-300': '220 185 255', // Medium light purple
    '--color-secondary-400': '200 145 255', // Medium purple
    '--color-secondary-500': '168 85 247',  // Candy Purple (main)
    '--color-secondary-600': '151 76 222',  // Darker purple
    '--color-secondary-700': '134 68 197',  // Dark purple
    '--color-secondary-800': '117 59 172',  // Darker purple
    '--color-secondary-900': '100 51 147',  // Very dark purple
    '--color-secondary-950': '84 42 123',   // Darkest purple

    // Tertiary: Candy Blue
    '--color-tertiary-0': '240 248 255',    // Very light blue
    '--color-tertiary-50': '230 242 255',   // Light blue
    '--color-tertiary-100': '220 235 255',  // Lighter blue
    '--color-tertiary-200': '200 220 255',  // Light blue
    '--color-tertiary-300': '180 205 255',  // Medium light blue
    '--color-tertiary-400': '140 175 255',  // Medium blue
    '--color-tertiary-500': '59 130 246',   // Candy Blue (main)
    '--color-tertiary-600': '53 117 221',   // Darker blue
    '--color-tertiary-700': '47 104 196',   // Dark blue
    '--color-tertiary-800': '41 91 171',    // Darker blue
    '--color-tertiary-900': '35 78 147',    // Very dark blue
    '--color-tertiary-950': '29 65 122',    // Darkest blue

    // === GLASS EFFECT COLORS ===
    '--color-glass-bg-primary': '255 255 255 / 0.1',      // rgba(255, 255, 255, 0.1)
    '--color-glass-bg-secondary': '255 255 255 / 0.05',   // rgba(255, 255, 255, 0.05)
    '--color-glass-bg-card': '255 255 255 / 0.08',        // rgba(255, 255, 255, 0.08)
    '--color-glass-border-primary': '255 255 255 / 0.2',  // rgba(255, 255, 255, 0.2)
    '--color-glass-border-secondary': '255 255 255 / 0.1', // rgba(255, 255, 255, 0.1)
    '--color-glass-border-accent': '255 107 157 / 0.3',   // rgba(255, 107, 157, 0.3)

    // === SEMANTIC COLORS (using candy palette) ===
    '--color-success-500': '16 185 129',   // Candy Green
    '--color-warning-500': '245 158 11',   // Candy Yellow
    '--color-error-500': '239 68 68',      // Candy Red
    '--color-info-500': '6 182 212',       // Candy Cyan

    // === TYPOGRAPHY COLORS ===
    '--color-typography-0': '255 255 255',         // White (100%)
    '--color-typography-50': '255 255 255 / 0.95', // 95% white
    '--color-typography-100': '255 255 255 / 0.9', // 90% white
    '--color-typography-200': '255 255 255 / 0.8', // 80% white
    '--color-typography-300': '255 255 255 / 0.7', // 70% white
    '--color-typography-400': '255 255 255 / 0.6', // 60% white
    '--color-typography-500': '255 255 255 / 0.5', // 50% white
    '--color-typography-600': '255 255 255 / 0.4', // 40% white
    '--color-typography-700': '255 255 255 / 0.3', // 30% white
    '--color-typography-800': '255 255 255 / 0.2', // 20% white
    '--color-typography-900': '255 255 255 / 0.1', // 10% white
    '--color-typography-950': '255 255 255 / 0.05', // 5% white

    // === BACKGROUND COLORS ===
    '--color-background-0': '0 0 0',               // Black
    '--color-background-50': '18 18 18',           // Very dark
    '--color-background-100': '31 31 31',          // Dark
    '--color-background-200': '45 45 45',          // Medium dark
    '--color-background-300': '64 64 64',          // Medium
    '--color-background-400': '82 82 82',          // Medium light
    '--color-background-500': '115 115 115',       // Light
    '--color-background-muted': '255 255 255 / 0.05', // Glass muted

    // === BORDER COLORS ===
    '--color-border-primary': '255 255 255 / 0.2',   // Primary border
    '--color-border-secondary': '255 255 255 / 0.1', // Secondary border
    '--color-border-muted': '255 255 255 / 0.05',    // Muted border

    // === SHADOW COLORS ===
    '--color-shadow-primary': '31 38 135 / 0.37',    // Glass shadow
    '--color-shadow-candy-pink': '255 107 157 / 0.3', // Pink shadow
    '--color-shadow-candy-purple': '168 85 247 / 0.3', // Purple shadow
    '--color-shadow-candy-blue': '59 130 246 / 0.3',  // Blue shadow
  }),
  
  dark: vars({
    // Dark mode uses same candy colors but adjusted opacity
    '--color-primary-500': '255 107 157',   // Candy Pink (same)
    '--color-secondary-500': '168 85 247',  // Candy Purple (same)
    '--color-tertiary-500': '59 130 246',   // Candy Blue (same)
    
    // Glass effects for dark mode (slightly more opaque)
    '--color-glass-bg-primary': '255 255 255 / 0.08',
    '--color-glass-bg-secondary': '255 255 255 / 0.04',
    '--color-glass-bg-card': '255 255 255 / 0.06',
    '--color-glass-border-primary': '255 255 255 / 0.15',
    '--color-glass-border-secondary': '255 255 255 / 0.08',
    
    // Typography for dark mode (same as light)
    '--color-typography-0': '255 255 255',
    '--color-typography-100': '255 255 255 / 0.9',
    '--color-typography-200': '255 255 255 / 0.8',
    '--color-typography-300': '255 255 255 / 0.7',
    '--color-typography-400': '255 255 255 / 0.6',
    '--color-typography-500': '255 255 255 / 0.5',
    '--color-typography-600': '255 255 255 / 0.4',
    '--color-typography-700': '255 255 255 / 0.3',
    
    // Background colors for dark mode
    '--color-background-0': '0 0 0',
    '--color-background-50': '12 12 12',
    '--color-background-100': '24 24 24',
    '--color-background-muted': '255 255 255 / 0.03',
  })
};
```

### Step 2: Update Main Configuration File

#### File: `components/ui/gluestack-ui-provider/config.ts`
```typescript
// Replace current config with LearniScan config
import { learniConfig } from './learni-config';

export const config = learniConfig;
```

### Step 3: Update Tailwind Configuration

#### File: `tailwind.config.js`
```typescript
// Add LearniScan color mappings
module.exports = {
  content: [
    "./App.{js,jsx,ts,tsx}",
    "./app/**/*.{js,jsx,ts,tsx}",
    "./components/**/*.{js,jsx,ts,tsx}",
  ],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        // Map CSS variables to Tailwind classes
        primary: {
          50: 'rgb(var(--color-primary-50))',
          100: 'rgb(var(--color-primary-100))',
          200: 'rgb(var(--color-primary-200))',
          300: 'rgb(var(--color-primary-300))',
          400: 'rgb(var(--color-primary-400))',
          500: 'rgb(var(--color-primary-500))',
          600: 'rgb(var(--color-primary-600))',
          700: 'rgb(var(--color-primary-700))',
          800: 'rgb(var(--color-primary-800))',
          900: 'rgb(var(--color-primary-900))',
          950: 'rgb(var(--color-primary-950))',
        },
        secondary: {
          50: 'rgb(var(--color-secondary-50))',
          500: 'rgb(var(--color-secondary-500))',
          600: 'rgb(var(--color-secondary-600))',
          700: 'rgb(var(--color-secondary-700))',
          // ... full scale
        },
        tertiary: {
          50: 'rgb(var(--color-tertiary-50))',
          500: 'rgb(var(--color-tertiary-500))',
          600: 'rgb(var(--color-tertiary-600))',
          700: 'rgb(var(--color-tertiary-700))',
          // ... full scale
        },
        glass: {
          'bg-primary': 'rgba(var(--color-glass-bg-primary))',
          'bg-secondary': 'rgba(var(--color-glass-bg-secondary))',
          'bg-card': 'rgba(var(--color-glass-bg-card))',
          'border-primary': 'rgba(var(--color-glass-border-primary))',
          'border-secondary': 'rgba(var(--color-glass-border-secondary))',
          'border-accent': 'rgba(var(--color-glass-border-accent))',
        },
        typography: {
          0: 'rgba(var(--color-typography-0))',
          50: 'rgba(var(--color-typography-50))',
          100: 'rgba(var(--color-typography-100))',
          200: 'rgba(var(--color-typography-200))',
          300: 'rgba(var(--color-typography-300))',
          400: 'rgba(var(--color-typography-400))',
          500: 'rgba(var(--color-typography-500))',
          600: 'rgba(var(--color-typography-600))',
          700: 'rgba(var(--color-typography-700))',
          800: 'rgba(var(--color-typography-800))',
          900: 'rgba(var(--color-typography-900))',
          950: 'rgba(var(--color-typography-950))',
        }
      },
      backdropBlur: {
        'glass': '12px',
        'glass-light': '8px',
        'glass-heavy': '16px',
      },
      boxShadow: {
        'glass': '0 8px 32px 0 rgba(var(--color-shadow-primary))',
        'candy-pink': '0 8px 32px 0 rgba(var(--color-shadow-candy-pink))',
        'candy-purple': '0 8px 32px 0 rgba(var(--color-shadow-candy-purple))',
        'candy-blue': '0 8px 32px 0 rgba(var(--color-shadow-candy-blue))',
      }
    },
  },
  plugins: [
    // Custom plugin for glass morphism utilities
    function({ addUtilities, theme }) {
      addUtilities({
        '.glass-morphism': {
          backgroundColor: 'rgba(var(--color-glass-bg-primary))',
          backdropFilter: 'blur(12px)',
          WebkitBackdropFilter: 'blur(12px)',
          borderColor: 'rgba(var(--color-glass-border-primary))',
          borderWidth: '1px',
          boxShadow: '0 8px 32px 0 rgba(var(--color-shadow-primary))',
        },
        '.glass-card': {
          background: 'linear-gradient(135deg, rgba(var(--color-glass-bg-primary)), rgba(var(--color-glass-bg-secondary)))',
          backdropFilter: 'blur(12px)',
          WebkitBackdropFilter: 'blur(12px)',
          borderColor: 'rgba(var(--color-glass-border-secondary))',
          borderWidth: '1px',
          boxShadow: '0 8px 32px 0 rgba(var(--color-shadow-primary))',
        },
        '.btn-candy-pink': {
          background: 'linear-gradient(135deg, rgb(var(--color-primary-500)), rgba(255, 107, 157, 0.8))',
          color: 'rgb(var(--color-typography-0))',
          boxShadow: '0 8px 32px 0 rgba(var(--color-shadow-candy-pink))',
        },
        '.btn-candy-purple': {
          background: 'linear-gradient(135deg, rgb(var(--color-secondary-500)), rgba(168, 85, 247, 0.8))',
          color: 'rgb(var(--color-typography-0))',
          boxShadow: '0 8px 32px 0 rgba(var(--color-shadow-candy-purple))',
        },
        '.btn-candy-blue': {
          background: 'linear-gradient(135deg, rgb(var(--color-tertiary-500)), rgba(59, 130, 246, 0.8))',
          color: 'rgb(var(--color-typography-0))',
          boxShadow: '0 8px 32px 0 rgba(var(--color-shadow-candy-blue))',
        },
      });
    }
  ],
};
```

## Implementation Steps

### Phase 1: Backup & Preparation
1. **Backup Current Config**: Copy existing `config.ts` to `config.backup.ts`
2. **Create LearniScan Config**: Create new `learni-config.ts` file
3. **Test Environment**: Ensure development environment is ready

### Phase 2: Configuration Replacement
1. **Replace Config**: Update `config.ts` to use `learniConfig`
2. **Update Tailwind**: Add LearniScan color mappings
3. **Test Basic Colors**: Verify primary, secondary, tertiary colors work

### Phase 3: Validation & Testing
1. **Component Testing**: Test all existing components with new colors
2. **Platform Testing**: Verify colors work on iOS, Android, Web
3. **Accessibility Testing**: Ensure proper contrast ratios
4. **Performance Testing**: Measure any performance impact

### Phase 4: Documentation & Cleanup
1. **Update Documentation**: Document new color system
2. **Create Examples**: Provide usage examples for new colors
3. **Clean Up**: Remove backup files if everything works

## Risk Mitigation

### Potential Issues
1. **Breaking Changes**: Existing components may look different
2. **Color Contrast**: White text may not be readable on all backgrounds
3. **Platform Differences**: Glass effects may not work consistently
4. **Performance**: CSS variables may impact performance

### Mitigation Strategies
1. **Gradual Rollout**: Test one component at a time
2. **Fallback Colors**: Provide fallback colors for accessibility
3. **Platform Testing**: Test on all target platforms
4. **Performance Monitoring**: Monitor render performance

## Success Criteria

### Visual Quality
- [ ] All candy colors render correctly
- [ ] Glass effects work on all platforms
- [ ] Typography is readable on gradient backgrounds
- [ ] Color transitions are smooth

### Technical Quality
- [ ] No TypeScript errors
- [ ] No runtime errors
- [ ] Performance impact < 5%
- [ ] Bundle size increase < 20KB

### Compatibility
- [ ] Works on iOS, Android, Web
- [ ] Light/dark mode switching works
- [ ] Accessibility standards met
- [ ] Existing component APIs unchanged

This design token integration plan provides a systematic approach to replacing the default Gluestack UI color scheme with LearniScan's candy color aesthetic while maintaining compatibility and performance.