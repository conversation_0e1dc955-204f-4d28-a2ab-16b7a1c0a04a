# Component-by-Component Alignment Strategy

## Overview
This document provides detailed alignment strategies for each installed Gluestack UI component to match LearniScan's candy color design system and glass morphism aesthetic.

## Component Analysis & Alignment Plan

### 1. Button Component (`components/ui/button/`)

#### Current State
```typescript
// Current buttonStyle variants
action: {
  primary: 'bg-primary-500 data-[hover=true]:bg-primary-600',
  secondary: 'bg-secondary-500 data-[hover=true]:bg-secondary-600',
  positive: 'bg-success-500',
  negative: 'bg-error-500',
  default: 'bg-transparent',
}
```

#### LearniScan Requirements
- Candy pink, purple, blue button variants
- Glass morphism button variant
- Gradient button effects
- White text on colored backgrounds

#### Alignment Strategy
```typescript
// Add new action variants
action: {
  // Keep existing for compatibility
  primary: 'bg-primary-500 data-[hover=true]:bg-primary-600',
  secondary: 'bg-secondary-500 data-[hover=true]:bg-secondary-600',
  
  // Add LearniScan candy variants
  candyPink: 'bg-primary-500 data-[hover=true]:bg-primary-600 data-[active=true]:bg-primary-700 text-white shadow-candy-pink',
  candyPurple: 'bg-secondary-500 data-[hover=true]:bg-secondary-600 data-[active=true]:bg-secondary-700 text-white shadow-candy-purple',
  candyBlue: 'bg-tertiary-500 data-[hover=true]:bg-tertiary-600 data-[active=true]:bg-tertiary-700 text-white shadow-candy-blue',
  
  // Add glass variant
  glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md text-white',
  glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md text-white',
}
```

#### Implementation Priority: **CRITICAL**
#### Files to Modify:
- `components/ui/button/index.tsx` (add variants)
- Update ButtonText default color to white

---

### 2. Input Component (`components/ui/input/`)

#### Current State
```typescript
// Current inputStyle variants
variant: {
  underlined: 'rounded-none border-b',
  outline: 'rounded border',
  rounded: 'rounded-full border',
}
```

#### LearniScan Requirements
- Glass morphism input fields
- Candy color focus states
- White text and placeholder text
- Translucent backgrounds

#### Alignment Strategy
```typescript
// Add new variants
variant: {
  // Keep existing
  underlined: 'rounded-none border-b',
  outline: 'rounded border',
  rounded: 'rounded-full border',
  
  // Add LearniScan glass variants
  glass: 'bg-glass-bg-secondary border border-glass-border-primary backdrop-blur-md text-white placeholder:text-white/60 rounded-lg',
  glassRounded: 'bg-glass-bg-secondary border border-glass-border-primary backdrop-blur-md text-white placeholder:text-white/60 rounded-full',
  
  // Add candy focus variants
  candyFocus: 'border border-background-300 data-[focus=true]:border-primary-500 data-[focus=true]:shadow-candy-pink bg-glass-bg-secondary text-white',
}
```

#### Implementation Priority: **HIGH**
#### Files to Modify:
- `components/ui/input/index.tsx` (add variants)
- Update InputField text color to white

---

### 3. Text Component (`components/ui/text/`)

#### Current State
```typescript
// Current textStyle base
base: 'text-typography-700 font-body'
```

#### LearniScan Requirements
- White text for dark gradient backgrounds
- Multiple opacity levels (primary, secondary, tertiary, muted)
- Candy color accent text options

#### Alignment Strategy
```typescript
// Update base and add color variants
base: 'text-white font-body', // Change from typography-700 to white

variants: {
  // Keep existing variants
  size: { /* existing sizes */ },
  bold: { true: 'font-bold' },
  
  // Add LearniScan color variants
  color: {
    primary: 'text-white',           // 100% opacity
    secondary: 'text-white/80',      // 80% opacity  
    tertiary: 'text-white/60',       // 60% opacity
    muted: 'text-white/40',          // 40% opacity
    candyPink: 'text-primary-500',   // Candy pink accent
    candyPurple: 'text-secondary-500', // Candy purple accent
    candyBlue: 'text-tertiary-500',  // Candy blue accent
  }
}
```

#### Implementation Priority: **HIGH**
#### Files to Modify:
- `components/ui/text/styles.tsx` (update base and add color variants)

---

### 4. Box Component (`components/ui/box/`)

#### Current State
```typescript
// Basic container component with minimal styling
```

#### LearniScan Requirements
- Glass morphism card containers
- Candy color accent borders
- Backdrop blur effects

#### Alignment Strategy
```typescript
// Add glass and candy variants
const boxStyle = tva({
  base: 'flex',
  variants: {
    variant: {
      default: '',
      glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md rounded-xl',
      glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-2xl p-6',
      candyBorder: 'border-2 border-primary-500 rounded-xl',
    }
  }
});
```

#### Implementation Priority: **MEDIUM**
#### Files to Modify:
- `components/ui/box/index.tsx` (add styling variants)

---

### 5. Layout Components (`vstack`, `hstack`, `center`)

#### Current State
```typescript
// Basic flex layout components
```

#### LearniScan Requirements
- Consistent spacing using design tokens
- Glass container variants
- Responsive gap sizing

#### Alignment Strategy
```typescript
// Add spacing and glass variants
const stackStyle = tva({
  base: 'flex',
  variants: {
    space: {
      xs: 'gap-1',    // 4px
      sm: 'gap-2',    // 8px  
      md: 'gap-4',    // 16px
      lg: 'gap-6',    // 24px
      xl: 'gap-8',    // 32px
    },
    variant: {
      default: '',
      glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md rounded-xl p-4',
    }
  }
});
```

#### Implementation Priority: **MEDIUM**
#### Files to Modify:
- `components/ui/vstack/index.tsx`
- `components/ui/hstack/index.tsx`
- `components/ui/center/index.tsx`

---

### 6. Feedback Components (`spinner`, `toast`)

#### Current State
```typescript
// Standard loading and notification components
```

#### LearniScan Requirements
- Candy color spinners
- Glass morphism toast backgrounds
- White text in notifications

#### Alignment Strategy

**Spinner:**
```typescript
// Add candy color variants
const spinnerStyle = tva({
  variants: {
    color: {
      default: 'text-primary-500',
      candyPink: 'text-primary-500',
      candyPurple: 'text-secondary-500', 
      candyBlue: 'text-tertiary-500',
    }
  }
});
```

**Toast:**
```typescript
// Add glass background variant
const toastStyle = tva({
  variants: {
    variant: {
      glass: 'bg-glass-bg-card border border-glass-border-primary backdrop-blur-md',
    }
  }
});
```

#### Implementation Priority: **LOW**
#### Files to Modify:
- `components/ui/spinner/index.tsx`
- `components/ui/toast/index.tsx`

---

### 7. Icon Component (`components/ui/icon/`)

#### Current State
```typescript
// Basic icon wrapper component
```

#### LearniScan Requirements
- Candy color icon variants
- White icons for dark backgrounds
- Consistent sizing

#### Alignment Strategy
```typescript
// Add color variants
const iconStyle = tva({
  variants: {
    color: {
      white: 'text-white',
      candyPink: 'text-primary-500',
      candyPurple: 'text-secondary-500',
      candyBlue: 'text-tertiary-500',
    }
  }
});
```

#### Implementation Priority: **LOW**
#### Files to Modify:
- `components/ui/icon/index.tsx`

## Implementation Order

### Phase 1: Critical Components (Week 1)
1. **Config.ts** - Replace color scheme
2. **Button** - Add candy and glass variants
3. **Text** - Update to white text with color variants
4. **Input** - Add glass and candy focus variants

### Phase 2: Layout Components (Week 2)  
5. **Box** - Add glass and candy variants
6. **VStack/HStack/Center** - Add spacing and glass variants

### Phase 3: Feedback Components (Week 3)
7. **Spinner** - Add candy color variants
8. **Toast** - Add glass background variant
9. **Icon** - Add color variants

## Testing Strategy

### Per Component Testing
1. **Visual Testing**: Compare with LearniScan design specifications
2. **Interaction Testing**: Test hover, focus, active states
3. **Platform Testing**: Verify on iOS, Android, Web
4. **Accessibility Testing**: Ensure proper contrast and touch targets

### Integration Testing
1. **Component Combinations**: Test components used together
2. **Theme Switching**: Test light/dark mode compatibility
3. **Performance Testing**: Measure render performance impact
4. **Bundle Size**: Monitor JavaScript bundle size increase

## Success Metrics

### Visual Alignment
- [ ] All components match LearniScan color palette
- [ ] Glass effects render correctly on all platforms
- [ ] Typography is readable on gradient backgrounds
- [ ] Component spacing follows design tokens

### Technical Quality
- [ ] No breaking changes to existing API
- [ ] TypeScript types remain accurate
- [ ] Performance impact < 5% render time increase
- [ ] Bundle size increase < 50KB

### Developer Experience
- [ ] New variants are discoverable via TypeScript
- [ ] Component documentation is updated
- [ ] Usage examples are provided
- [ ] Migration guide is available

This component-by-component strategy ensures systematic alignment while maintaining backward compatibility and code quality.