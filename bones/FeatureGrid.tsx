import React from 'react';
import { View } from 'react-native';
import { VStack } from '../components/ui/vstack';
import { FeatureCard } from './FeatureCard';
import { CameraIcon, LightbulbIcon, ShareIcon } from 'lucide-react-native';
import { useRouter } from 'expo-router';

export const FeatureGrid: React.FC = () => {
  const router = useRouter();

  const features = [
    {
      title: "Smart Scanning",
      description: "Instantly digitize any document with advanced OCR technology",
      icon: CameraIcon,
      iconColor: "rgb(255, 107, 157)",
      iconBackgroundColor: "rgba(255, 107, 157, 0.2)",
      onPress: () => router.push('/(tabs)/scan')
    },
    {
      title: "AI Learning",
      description: "Generate knowledge cards and personalized study paths",
      icon: LightbulbIcon,
      iconColor: "rgb(168, 85, 247)",
      iconBackgroundColor: "rgba(168, 85, 247, 0.2)",
      onPress: () => {}
    },
    {
      title: "Social Learning",
      description: "Share knowledge cards and join study workshops",
      icon: ShareIcon,
      iconColor: "rgb(59, 130, 246)",
      iconBackgroundColor: "rgba(59, 130, 246, 0.2)",
      onPress: () => {}
    }
  ];

  return (
    <View className="w-full mb-12 px-2">
      <VStack space="lg" className="md:flex-row md:space-x-6">
        {features.map((feature, index) => (
          <FeatureCard
            key={index}
            title={feature.title}
            description={feature.description}
            icon={feature.icon}
            iconColor={feature.iconColor}
            iconBackgroundColor={feature.iconBackgroundColor}
            onPress={feature.onPress}
            className="flex-1 mb-4 md:mb-0"
          />
        ))}
      </VStack>
    </View>
  );
};
