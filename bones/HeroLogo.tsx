import React from 'react';
import { Box } from '../components/ui/box';
import { LightbulbIcon } from 'lucide-react-native';

interface HeroLogoProps {
  size?: number;
  iconSize?: number;
}

export const HeroLogo: React.FC<HeroLogoProps> = ({
  size = 128,
  iconSize = 64
}) => {
  return (
    <Box className="mb-8 items-center">
      <Box
        className="bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md rounded-full justify-center items-center shadow-lg"
        style={{
          width: size,
          height: size,
        }}
      >
        <LightbulbIcon size={iconSize} color="#FFFFFF" />
      </Box>
    </Box>
  );
};
