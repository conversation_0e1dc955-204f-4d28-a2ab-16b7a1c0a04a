import React from 'react';
import { View, Pressable } from 'react-native';
import { HStack } from '../components/ui/hstack';
import { Box } from '../components/ui/box';
import { Text } from '../components/ui/text';

interface HomeHeaderProps {
  onSkipTour?: () => void;
}

export const HomeHeader: React.FC<HomeHeaderProps> = ({ onSkipTour }) => {
  return (
    <View className="px-6 pt-4 pb-2">
      <HStack className="justify-between items-center">
        <Box className="bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md rounded-lg px-4 py-2 shadow-sm">
          <Text color="primary" size="lg" className="font-semibold">
            LearniScan
          </Text>
        </Box>
        <Pressable onPress={onSkipTour}>
          <Box className="bg-glass-bg-secondary border border-glass-border-secondary backdrop-blur-md rounded-lg px-4 py-2 shadow-sm">
            <Text color="primary" size="sm" className="opacity-90">
              Skip Tour
            </Text>
          </Box>
        </Pressable>
      </HStack>
    </View>
  );
};
