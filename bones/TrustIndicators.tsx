import React from 'react';
import { View } from 'react-native';
import { HStack } from '../components/ui/hstack';
import { Box } from '../components/ui/box';
import { Text } from '../components/ui/text';

interface TrustIndicator {
  label: string;
  value: string;
}

interface TrustIndicatorsProps {
  title?: string;
  indicators?: TrustIndicator[];
}

export const TrustIndicators: React.FC<TrustIndicatorsProps> = ({
  title = "Trusted by students and professionals worldwide",
  indicators = [
    { label: "Users", value: "10K+" },
    { label: "Languages", value: "50+" },
    { label: "AI Powered", value: "AI Powered" }
  ]
}) => {
  return (
    <View className="w-full pt-8 border-t border-white/10">
      <Text color="secondary" size="sm" className="text-center mb-6 opacity-70">
        {title}
      </Text>
      <HStack className="justify-center items-center space-x-6 flex-wrap">
        {indicators.map((indicator, index) => (
          <Box
            key={index}
            className="bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md rounded-lg px-4 py-2 shadow-sm"
          >
            <Text color="primary" size="sm" className="font-semibold">
              {indicator.value}
            </Text>
          </Box>
        ))}
      </HStack>
    </View>
  );
};
