import React from 'react';
import { View } from 'react-native';
import { HeroLogo } from './HeroLogo';
import { HeroHeading } from './HeroHeading';
import { FeatureGrid } from './FeatureGrid';
import { CTAButtons } from './CTAButtons';
import { TrustIndicators } from './TrustIndicators';

interface HeroSectionProps {
  onGetStarted?: () => void;
  onWatchDemo?: () => void;
}

export const HeroSection: React.FC<HeroSectionProps> = ({
  onGetStarted,
  onWatchDemo
}) => {
  return (
    <View className="flex-1 items-center justify-center px-6">
      <View className="max-w-4xl mx-auto items-center">
        <HeroLogo />
        <HeroHeading />
        <FeatureGrid />
        <CTAButtons 
          onGetStarted={onGetStarted}
          onWatchDemo={onWatchDemo}
        />
        <TrustIndicators />
      </View>
    </View>
  );
};
