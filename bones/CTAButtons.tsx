import React from 'react';
import { View } from 'react-native';
import { VStack } from '../components/ui/vstack';
import { HStack } from '../components/ui/hstack';
import { Button, ButtonText } from '../components/ui/button';
import { ArrowRightIcon, PlayIcon } from 'lucide-react-native';
import { useRouter } from 'expo-router';

interface CTAButtonsProps {
  onGetStarted?: () => void;
  onWatchDemo?: () => void;
}

export const CTAButtons: React.FC<CTAButtonsProps> = ({
  onGetStarted,
  onWatchDemo
}) => {
  const router = useRouter();

  const handleGetStarted = () => {
    if (onGetStarted) {
      onGetStarted();
    } else {
      router.push('/(tabs)/scan');
    }
  };

  return (
    <View className="w-full mb-12 px-4">
      <VStack space="md" className="sm:flex-row sm:space-x-6 items-center justify-center">
        <Button
          action="primary"
          size="lg"
          onPress={handleGetStarted}
          className="w-full sm:w-auto bg-primary-500"
        >
          <HStack space="sm" className="items-center">
            <ButtonText className="font-semibold">
              Get Started
            </ButtonText>
            <ArrowRightIcon size={20} color="#FFFFFF" />
          </HStack>
        </Button>

        <Button
          action="secondary"
          variant="outline"
          size="lg"
          onPress={onWatchDemo}
          className="w-full sm:w-auto"
        >
          <HStack space="sm" className="items-center">
            <ButtonText className="font-semibold">
              Watch Demo
            </ButtonText>
            <PlayIcon size={20} color="#FFFFFF" />
          </HStack>
        </Button>
      </VStack>
    </View>
  );
};
