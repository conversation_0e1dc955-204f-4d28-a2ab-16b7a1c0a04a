import React from 'react';
import { Pressable } from 'react-native';
import { Box } from '../components/ui/box';
import { VStack } from '../components/ui/vstack';
import { Text } from '../components/ui/text';
import { LucideIcon } from 'lucide-react-native';

interface FeatureCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  iconColor: string;
  iconBackgroundColor: string;
  onPress?: () => void;
  className?: string;
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon: Icon,
  iconColor,
  iconBackgroundColor,
  onPress,
  className = "flex-1"
}) => {
  return (
    <Pressable onPress={onPress} className={className}>
      <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-6 shadow-lg">
        <VStack space="md" className="items-center">
          <Box
            className="w-12 h-12 rounded-lg justify-center items-center"
            style={{ backgroundColor: iconBackgroundColor }}
          >
            <Icon size={24} color={iconColor} />
          </Box>
          <VStack space="xs" className="items-center">
            <Text color="primary" size="lg" className="font-semibold text-center">
              {title}
            </Text>
            <Text color="secondary" size="sm" className="text-center leading-relaxed">
              {description}
            </Text>
          </VStack>
        </VStack>
      </Box>
    </Pressable>
  );
};
