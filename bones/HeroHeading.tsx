import React from 'react';
import { VStack } from '../components/ui/vstack';
import { Text } from '../components/ui/text';
import { GradientText } from '@/components/learni-scan/GradientText';
import { Dimensions } from 'react-native';

const { width: screenWidth } = Dimensions.get('window');

interface HeroHeadingProps {
  welcomeText?: string;
  appName?: string;
  tagline?: string;
  description?: string;
}

export const HeroHeading: React.FC<HeroHeadingProps> = ({
  welcomeText = "Welcome to",
  appName = "LearniScan",
  tagline = "Scan. Learn. Share.",
  description = "Transform any document into an interactive learning experience with AI-powered knowledge cards, translations, and personalized study roadmaps."
}) => {
  return (
    <>
      {/* Main Heading */}
      <VStack space="md" className="items-center mb-6">
        <Text
          color="primary"
          size="5xl"
          className="text-center font-bold"
        >
          {welcomeText}
        </Text>
        <GradientText
          fontSize={screenWidth >= 768 ? 72 : 48}
          fontWeight="bold"
          colors={['#FF6B9D', '#C44CEA', '#4F46E5', '#06B6D4'] as const }
        >
          {appName}
        </GradientText>
      </VStack>

      {/* Tagline & Description */}
      <VStack space="md" className="items-center mb-12">
        <Text
          color="primary"
          size="xl"
          className="text-center font-medium"
        >
          {tagline}
        </Text>
        <Text
          color="secondary"
          size="lg"
          className="text-center leading-relaxed max-w-2xl px-4"
        >
          {description}
        </Text>
      </VStack>
    </>
  );
};
