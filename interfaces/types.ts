import { CompletionParams } from '@pocketpalai/llama.rn';

// Common enums used across multiple stores
export enum ModelOrigin {
  PRESET = 'PRESET',
  HF = 'HF',
  LOCAL = 'LOCAL',
}

export enum CacheType {
  F16 = 0,
  F32 = 1,
  Q4_0 = 2,
  Q4_1 = 3,
  Q5_0 = 4,
  Q5_1 = 5,
  Q8_0 = 6,
  AUTO = 7,
}

export enum PalType {
  ASSISTANT = 'ASSISTANT',
  ROLEPLAY = 'ROLEPLAY',
}

// Model interfaces
export interface IModelFile {
  rfilename: string;
  url: string;
  size?: number;
  oid?: string;
  lfs?: {
    oid: string;
    size: number;
    pointerSize: number;
  };
  canFitInStorage?: boolean;
}

export interface IChatTemplateConfig {
  chat_template: string;
  system_prefix?: string;
  add_generation_prompt?: boolean;
  roles?: {
    system?: string;
    user?: string;
    assistant?: string;
  };
}

export interface IModel {
  id: string;
  author: string;
  name: string;
  type: string;
  description: string;
  size: number;
  params?: number;
  isDownloaded: boolean;
  downloadUrl: string;
  hfUrl?: string;
  progress: number;
  filename: string;
  isLocal: boolean;
  origin: ModelOrigin;
  chatTemplate: IChatTemplateConfig;
  defaultChatTemplate: IChatTemplateConfig;
  completionSettings: CompletionParams;
  defaultCompletionSettings: CompletionParams;
  stopWords: string[];
  defaultStopWords: string[];
  hfModelFile?: IModelFile;
  hash?: string;
}

// Hugging Face model interface
export interface IHuggingFaceModel {
  id: string;
  author: string;
  type: string;
  downloads?: number;
  likes?: number;
  siblings: IModelFile[];
  url?: string;
  specs?: any;
}

// Benchmark interface
export interface IBenchmarkResult {
  uuid: string;
  timestamp: string;
  modelId: string;
  modelName: string;
  deviceInfo: {
    brand: string;
    model: string;
    systemVersion: string;
  };
  contextSize: number;
  loadTime: number;
  tokenTime: number[];
  tokenCount: number;
  tokensPerSecond: number;
  submitted?: boolean;
}

// Todo interface
export interface ITodo {
  id: string;
  text: string;
  completed: boolean;
  createdAt: Date;
}

// Pal interfaces
export interface IAssistantFormData {
  palType: PalType.ASSISTANT;
  name: string;
  description: string;
  systemPrompt: string;
  avatar?: string;
}

export interface IRoleplayFormData {
  palType: PalType.ROLEPLAY;
  name: string;
  description: string;
  personality: string;
  scenario: string;
  exampleConversation: string;
  avatar?: string;
}

export type IPal = {id: string} & (IAssistantFormData | IRoleplayFormData);
export type IAssistantPal = IPal & {palType: PalType.ASSISTANT};
export type IRoleplayPal = IPal & {palType: PalType.ROLEPLAY};

// Message types
export const MessageType = {
  Text: 'text',
  Image: 'image',
  Audio: 'audio',
  Video: 'video',
  File: 'file',
} as const;

export namespace MessageType {
  export interface IBase {
    id: string;
    author: {
      name: string;
    };
    createdAt: number;
    type: string;
    metadata?: Record<string, any>;
  }

  export interface IText extends IBase {
    type: typeof MessageType.Text;
    text: string;
  }

  export interface IImage extends IBase {
    type: typeof MessageType.Image;
    uri: string;
    width: number;
    height: number;
  }

  export interface IAudio extends IBase {
    type: typeof MessageType.Audio;
    uri: string;
    duration: number;
  }

  export interface IVideo extends IBase {
    type: typeof MessageType.Video;
    uri: string;
    thumbnail?: string;
    duration: number;
  }

  export interface IFile extends IBase {
    type: typeof MessageType.File;
    uri: string;
    name: string;
    size: number;
    mimeType: string;
  }

  export type Any = IText | IImage | IAudio | IVideo | IFile;
}

// Chat session interface
export interface ISessionMetaData {
  id: string;
  title: string;
  date: string;
  messages: MessageType.Any[];
  completionSettings: CompletionParams;
  activePalId?: string;
}
