import type { IConversation } from './IConversation';
import type { ITemplateConfig } from './ITemplateConfig';
interface ApplyTemplateOptions {
    templateKey?: string;
    customTemplate?: ITemplateConfig;
    addGenerationPrompt?: boolean;
    isBeginningOfSequence?: boolean;
    isEndOfSequence?: boolean;
}
declare const applyTemplate: (conversation: IConversation | IConversation[], options?: ApplyTemplateOptions) => string | string[];