export interface IBenchmarkResult {
  config: BenchmarkConfig;
  modelDesc: string;
  modelSize: number;
  modelNParams: number;
  ppAvg: number;
  ppStd: number;
  tgAvg: number;
  tgStd: number;
  timestamp: string;
  modelId: string;
  modelName: string;
  oid?: string;
  rfilename?: string;
  filename?: string;
  peakMemoryUsage?: {
    total: number;
    used: number;
    percentage: number;
  };
  wallTimeMs?: number;
  uuid: string;
  submitted?: boolean;
  initSettings?: {
    n_context: number;
    n_batch: number;
    n_ubatch: number;
    n_threads: number;
    flash_attn: boolean;
    cache_type_k: CacheType;
    cache_type_v: CacheType;
    n_gpu_layers: number;
  };
}