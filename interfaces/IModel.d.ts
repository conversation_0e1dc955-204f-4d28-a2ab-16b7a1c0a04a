export interface IModel {
  id: string;
  author: string;
  name: string;
  type?: string;
  description: string;
  size: number; // Size in bytes
  params: number;
  isDownloaded: boolean;
  downloadUrl: string;
  hfUrl: string;
  progress: number; // Progress as a percentage
  downloadSpeed?: string;
  filename: string;
  fullPath?: string; // Full path for local models
  /**
   * @deprecated Use 'origin' instead.
   */
  isLocal: boolean; // this need to be deprecated
  origin: ModelOrigin;
  defaultChatTemplate: ChatTemplateConfig;
  chatTemplate: ChatTemplateConfig;
  defaultStopWords: CompletionParams['stop'];
  stopWords: CompletionParams['stop'];
  defaultCompletionSettings: CompletionParams;
  completionSettings: CompletionParams;
  hfModelFile?: ModelFile;
  hfModel?: HuggingFaceModel;
  hash?: string;
}