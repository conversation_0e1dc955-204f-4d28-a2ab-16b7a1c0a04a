export interface IUtilities {
  randId: () => string;
  l10n: {
    modelNotLoaded: string;
    networkError: string;
    conversationReset: string;
  };
  activateKeepAwake: () => void;
  deactivateKeepAwake: () => void;
  applyChatTemplate: (
    messages: Array<{ role: string; content: string }>,
    model: any,
    context: LlamaContext
  ) => Promise<string>;
  convertToChatMessages: (messages: MessageType.Any[]) => Array<{ role: string; content: string }>;
}
