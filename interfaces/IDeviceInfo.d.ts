export type IDeviceInfo = {
  model: string;
  systemName: string;
  systemVersion: string;
  brand: string;
  cpuArch: string[];
  isEmulator: boolean;
  version: string;
  buildNumber: string;
  device: string;
  deviceId: string;
  totalMemory: number;
  chipset: string;
  cpu: string;
  cpuDetails: {
    cores: number;
    processors: Array<{
      processor: string;
      'model name': string;
      'cpu MHz': string;
      vendor_id: string;
    }>;
    socModel: string;
    features: string[];
    hasFp16: boolean;
    hasDotProd: boolean;
    hasSve: boolean;
    hasI8mm: boolean;
  };
};