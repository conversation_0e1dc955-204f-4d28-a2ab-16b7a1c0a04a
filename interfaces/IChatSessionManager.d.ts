import { LlamaContext, CompletionParams } from '@pocketpalai/llama.rn';
import type { IMessageType } from './IMessageType';
import type { IUser } from './IUser';

export interface IChatSessionManager {
  addMessageToCurrentSession: (message: IMessageType.Any) => void;
  updateMessageToken: (data: any, createdAt: number, id: string, context: LlamaContext) => void;
  updateMessage: (id: string, update: Partial<IMessageType.Text>) => void;
  setIsGenerating: (value: boolean) => void;
  currentSessionMessages: IMessageType.Any[];
  sessions: Array<{
    id: string;
    completionSettings?: CompletionParams;
    activePalId?: string;
  }>;
  activeSessionId: string | null;
}
