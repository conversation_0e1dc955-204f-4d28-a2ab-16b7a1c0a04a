# LearniScan Design System - Implementation Guide

This guide will help you implement the LearniScan design system in your React Native app using NativeWind.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# Install NativeWind and dependencies
npm install nativewind tailwindcss react-native-reanimated react-native-svg

# For Expo projects
npx expo install react-native-reanimated react-native-svg
```

### 2. Setup NativeWind Configuration

Create `tailwind.config.js` in your project root:

```javascript
const { nativeWindConfig } = require('./design/design-tokens');

module.exports = nativeWindConfig;
```

### 3. Configure Metro

Update your `metro.config.js`:

```javascript
const { getDefaultConfig } = require("expo/metro-config");
const { withNativeWind } = require('nativewind/metro');

const config = getDefaultConfig(__dirname);

module.exports = withNativeWind(config, { 
  input: './global.css',
  inlineRem: 16 
});
```

### 4. Setup Babel

Update your `babel.config.js`:

```javascript
module.exports = function (api) {
  api.cache(true);
  return {
    presets: [
      ["babel-preset-expo", { jsxImportSource: "nativewind" }],
      "nativewind/babel",
    ],
    plugins: [
      "react-native-reanimated/plugin",
    ],
  };
};
```

### 5. Create Global CSS

Create `global.css` in your project root:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom glass morphism utilities */
.glass-morphism {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}
```

## 🎨 Using Design Tokens

### Colors

```jsx
import { View, Text } from 'react-native';

export function ColorExample() {
  return (
    <View className="bg-candy-pink p-4 rounded-xl">
      <Text className="text-white font-semibold">Candy Pink Background</Text>
    </View>
  );
}
```

### Glass Morphism Components

```jsx
import { View, Text, Pressable } from 'react-native';

export function GlassCard({ children }) {
  return (
    <View className="glass-card p-6 rounded-2xl">
      {children}
    </View>
  );
}

export function GlassButton({ onPress, children }) {
  return (
    <Pressable 
      className="glass-button py-3 px-6 rounded-xl active:scale-95"
      onPress={onPress}
    >
      <Text className="text-white font-semibold text-center">
        {children}
      </Text>
    </Pressable>
  );
}
```

### Candy Buttons

```jsx
import { Pressable, Text } from 'react-native';

export function CandyButton({ variant = 'pink', onPress, children }) {
  const variantClasses = {
    pink: 'btn-candy-pink',
    purple: 'btn-candy-purple', 
    blue: 'btn-candy-blue',
  };

  return (
    <Pressable 
      className={`${variantClasses[variant]} py-3 px-6 rounded-xl active:scale-95`}
      onPress={onPress}
    >
      <Text className="text-white font-semibold text-center">
        {children}
      </Text>
    </Pressable>
  );
}
```

### Responsive Layout

```jsx
import { View, Text } from 'react-native';

export function ResponsiveGrid() {
  return (
    <View className="p-4">
      <View className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <View className="glass-card p-4 rounded-xl">
          <Text className="text-white font-semibold">Card 1</Text>
        </View>
        <View className="glass-card p-4 rounded-xl">
          <Text className="text-white font-semibold">Card 2</Text>
        </View>
        <View className="glass-card p-4 rounded-xl">
          <Text className="text-white font-semibold">Card 3</Text>
        </View>
      </View>
    </View>
  );
}
```

### Typography Scale

```jsx
import { View, Text } from 'react-native';

export function TypographyExample() {
  return (
    <View className="p-4 space-y-4">
      <Text className="text-4xl font-bold text-white">Heading 1</Text>
      <Text className="text-2xl font-semibold text-white">Heading 2</Text>
      <Text className="text-lg text-white/80">Body text with proper scaling</Text>
      <Text className="text-sm text-white/60">Small text for captions</Text>
    </View>
  );
}
```

## 🔧 Advanced Usage

### Custom Glass Components with cssInterop

```jsx
import { View } from 'react-native';
import { cssInterop } from 'nativewind';

// Enable className prop for custom components
const GlassView = cssInterop(View, {
  className: {
    target: "style",
    nativeStyleToProp: {
      backgroundColor: true,
      borderRadius: true,
      borderWidth: true,
      borderColor: true,
    }
  }
});

export function CustomGlassComponent() {
  return (
    <GlassView className="glass-morphism p-6 rounded-2xl">
      {/* Content */}
    </GlassView>
  );
}
```

### Platform-Specific Styling

```jsx
import { View, Text } from 'react-native';
import { Platform } from 'react-native';

export function PlatformSpecific() {
  return (
    <View className={`
      p-4 rounded-xl
      ${Platform.OS === 'ios' ? 'glass-card' : 'bg-white/10'}
    `}>
      <Text className="text-white">Platform-optimized styling</Text>
    </View>
  );
}
```

### Animated Components

```jsx
import { Pressable, Text } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring 
} from 'react-native-reanimated';

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export function AnimatedButton({ onPress, children }) {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  return (
    <AnimatedPressable
      className="btn-candy-pink py-3 px-6 rounded-xl"
      style={animatedStyle}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={onPress}
    >
      <Text className="text-white font-semibold text-center">
        {children}
      </Text>
    </AnimatedPressable>
  );
}
```

## 📱 Touch Targets & Accessibility

```jsx
import { Pressable, Text } from 'react-native';

export function AccessibleButton({ onPress, children, accessibilityLabel }) {
  return (
    <Pressable
      className="glass-button min-h-[44px] min-w-[44px] py-3 px-6 rounded-xl justify-center items-center"
      onPress={onPress}
      accessibilityRole="button"
      accessibilityLabel={accessibilityLabel}
      accessible={true}
    >
      <Text className="text-white font-semibold text-center">
        {children}
      </Text>
    </Pressable>
  );
}
```

## 🎯 Best Practices

1. **Use Design Tokens**: Always use the predefined color, spacing, and typography tokens
2. **Touch Targets**: Ensure interactive elements are at least 44px in height/width
3. **Responsive Design**: Use responsive utilities for different screen sizes
4. **Platform Optimization**: Leverage platform-specific styling when needed
5. **Accessibility**: Include proper accessibility props and labels
6. **Performance**: Use `cssInterop` sparingly and prefer built-in NativeWind utilities

## 🔍 Troubleshooting

### Common Issues:

1. **Styles not applying**: Ensure NativeWind preset is included in tailwind.config.js
2. **Glass effects not working**: Check that backdrop-filter is supported on your platform
3. **Colors not showing**: Verify design tokens are properly imported
4. **Responsive not working**: Ensure proper screen breakpoints are configured

### Debug Mode:

Add to your metro.config.js for debugging:

```javascript
module.exports = withNativeWind(config, { 
  input: './global.css',
  inlineRem: 16,
  // Enable debug mode
  debug: __DEV__
});
```

## 📚 Resources

- [NativeWind Documentation](https://www.nativewind.dev/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)
- [React Native Reanimated](https://docs.swmansion.com/react-native-reanimated/)
- [LearniScan Design System Documentation](./documentation.html)

Ready to build beautiful, consistent UIs with the LearniScan design system! 🚀
