<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LearniScan - Design System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        // Candy Color Palette
                        candy: {
                            pink: '#FF6B9D',
                            purple: '#A855F7',
                            blue: '#3B82F6',
                            cyan: '#06B6D4',
                            green: '#10B981',
                            yellow: '#F59E0B',
                            orange: '#F97316',
                            red: '#EF4444',
                            // Liquid Glass variants
                            'pink-glass': 'rgba(255, 107, 157, 0.1)',
                            'purple-glass': 'rgba(168, 85, 247, 0.1)',
                            'blue-glass': 'rgba(59, 130, 246, 0.1)',
                            'cyan-glass': 'rgba(6, 182, 212, 0.1)',
                            'green-glass': 'rgba(16, 185, 129, 0.1)',
                            'yellow-glass': 'rgba(245, 158, 11, 0.1)',
                            'orange-glass': 'rgba(249, 115, 22, 0.1)',
                            'red-glass': 'rgba(239, 68, 68, 0.1)',
                        },
                        glass: {
                            white: 'rgba(255, 255, 255, 0.1)',
                            black: 'rgba(0, 0, 0, 0.1)',
                            gray: 'rgba(107, 114, 128, 0.1)',
                        }
                    },
                    backdropBlur: {
                        'xs': '2px',
                        'glass': '12px',
                        'heavy': '24px',
                    },
                    boxShadow: {
                        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
                        'glass-inset': 'inset 0 1px 0 0 rgba(255, 255, 255, 0.05)',
                        'candy': '0 10px 25px -5px rgba(255, 107, 157, 0.4)',
                        'candy-lg': '0 20px 40px -10px rgba(255, 107, 157, 0.3)',
                    },
                    borderRadius: {
                        'glass': '16px',
                        'card': '20px',
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'shimmer': 'shimmer 2s linear infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 20px rgba(255, 107, 157, 0.5)' },
                            '100%': { boxShadow: '0 0 30px rgba(255, 107, 157, 0.8)' },
                        },
                        shimmer: {
                            '0%': { backgroundPosition: '-200% 0' },
                            '100%': { backgroundPosition: '200% 0' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Custom Liquid Glass Effects */
        .glass-morphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .glass-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        
        .candy-gradient {
            background: linear-gradient(135deg, #FF6B9D, #A855F7, #3B82F6);
        }
        
        .shimmer-effect {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            background-size: 200% 100%;
        }
        
        /* Smooth transitions */
        * {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-candy-purple via-candy-pink to-candy-blue">
    <!-- Design System Showcase -->
    <div class="container mx-auto px-4 py-8">
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-4 animate-float">
                LearniScan Design System
            </h1>
            <p class="text-xl text-white/80 max-w-2xl mx-auto">
                Liquid Glass effects with Candy colors for an engaging learning experience
            </p>
        </header>

        <!-- Color Palette Section -->
        <section class="mb-16">
            <div class="glass-card rounded-card p-8 mb-8">
                <h2 class="text-2xl font-bold text-white mb-6">Candy Color Palette</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-candy-pink rounded-full mx-auto mb-2 shadow-candy"></div>
                        <span class="text-white text-sm">Pink</span>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-candy-purple rounded-full mx-auto mb-2"></div>
                        <span class="text-white text-sm">Purple</span>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-candy-blue rounded-full mx-auto mb-2"></div>
                        <span class="text-white text-sm">Blue</span>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-candy-cyan rounded-full mx-auto mb-2"></div>
                        <span class="text-white text-sm">Cyan</span>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-candy-green rounded-full mx-auto mb-2"></div>
                        <span class="text-white text-sm">Green</span>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-candy-yellow rounded-full mx-auto mb-2"></div>
                        <span class="text-white text-sm">Yellow</span>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-candy-orange rounded-full mx-auto mb-2"></div>
                        <span class="text-white text-sm">Orange</span>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-candy-red rounded-full mx-auto mb-2"></div>
                        <span class="text-white text-sm">Red</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Component Examples -->
        <section class="mb-16">
            <div class="glass-card rounded-card p-8">
                <h2 class="text-2xl font-bold text-white mb-6">Glass Components</h2>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Button Examples -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-white">Buttons</h3>
                        <button class="w-full py-3 px-6 bg-candy-pink hover:bg-candy-pink/80 text-white font-semibold rounded-glass shadow-candy hover:shadow-candy-lg transform hover:scale-105">
                            Primary Button
                        </button>
                        <button class="w-full py-3 px-6 glass-morphism text-white font-semibold rounded-glass hover:bg-white/20">
                            Glass Button
                        </button>
                        <button class="w-full py-3 px-6 border-2 border-candy-purple text-candy-purple hover:bg-candy-purple hover:text-white font-semibold rounded-glass">
                            Outline Button
                        </button>
                    </div>
                    
                    <!-- Card Examples -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-white">Cards</h3>
                        <div class="glass-card rounded-card p-4">
                            <h4 class="font-semibold text-white mb-2">Knowledge Card</h4>
                            <p class="text-white/70 text-sm">This is a sample knowledge card with glass morphism effect.</p>
                        </div>
                        <div class="bg-candy-blue/20 backdrop-blur-glass rounded-card p-4 border border-candy-blue/30">
                            <h4 class="font-semibold text-white mb-2">Colored Glass Card</h4>
                            <p class="text-white/70 text-sm">Card with colored glass background.</p>
                        </div>
                    </div>
                    
                    <!-- Input Examples -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-white">Inputs</h3>
                        <input type="text" placeholder="Glass Input" class="w-full py-3 px-4 glass-morphism text-white placeholder-white/50 rounded-glass focus:ring-2 focus:ring-candy-pink focus:outline-none">
                        <textarea placeholder="Glass Textarea" rows="3" class="w-full py-3 px-4 glass-morphism text-white placeholder-white/50 rounded-glass focus:ring-2 focus:ring-candy-purple focus:outline-none resize-none"></textarea>
                    </div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
