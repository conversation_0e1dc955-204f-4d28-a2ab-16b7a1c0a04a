<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LearniScan - Component Library</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../styles/design-system.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        candy: {
                            pink: '#FF6B9D',
                            purple: '#A855F7',
                            blue: '#3B82F6',
                            cyan: '#06B6D4',
                            green: '#10B981',
                            yellow: '#F59E0B',
                            orange: '#F97316',
                            red: '#EF4444',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen gradient-candy">
    <div class="container mx-auto px-4 py-8">
        <header class="text-center mb-12">
            <h1 class="text-4xl font-bold text-white mb-4 text-shadow animate-float">
                Component Library
            </h1>
            <p class="text-lg text-white/80">Reusable UI components for LearniScan</p>
        </header>

        <!-- Navigation Component -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-6">Navigation</h2>
            <div class="glass-card p-6">
                <nav class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="glass-button p-2 rounded-lg">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>
                        <h3 class="text-xl font-semibold text-white">LearniScan</h3>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="glass-button p-2 rounded-lg">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </button>
                        <button class="glass-button p-2 rounded-lg">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                            </svg>
                        </button>
                    </div>
                </nav>
            </div>
        </section>

        <!-- Button Components -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-6">Buttons</h2>
            <div class="glass-card p-6">
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-white">Primary Buttons</h3>
                        <button class="btn-candy-pink w-full py-3 px-6 rounded-lg font-semibold">
                            Scan Document
                        </button>
                        <button class="btn-candy-purple w-full py-3 px-6 rounded-lg font-semibold">
                            Generate Cards
                        </button>
                        <button class="btn-candy-blue w-full py-3 px-6 rounded-lg font-semibold">
                            Start Chat
                        </button>
                    </div>
                    
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-white">Glass Buttons</h3>
                        <button class="glass-button w-full">
                            View Graph
                        </button>
                        <button class="glass-button w-full">
                            Export Roadmap
                        </button>
                        <button class="glass-button w-full">
                            Share Card
                        </button>
                    </div>
                    
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-white">Icon Buttons</h3>
                        <button class="glass-button p-3 rounded-lg inline-flex items-center justify-center mr-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </button>
                        <button class="glass-button p-3 rounded-lg inline-flex items-center justify-center mr-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                            </svg>
                        </button>
                        <button class="glass-button p-3 rounded-lg inline-flex items-center justify-center">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Card Components -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-6">Cards</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Knowledge Card -->
                <div class="knowledge-card">
                    <div class="flex items-start justify-between mb-3">
                        <h3 class="text-lg font-semibold text-white">Machine Learning Basics</h3>
                        <span class="text-xs bg-candy-pink/20 text-candy-pink px-2 py-1 rounded-full">AI</span>
                    </div>
                    <p class="text-white/70 text-sm mb-4">
                        Introduction to supervised and unsupervised learning algorithms with practical examples.
                    </p>
                    <div class="flex items-center justify-between">
                        <span class="text-xs text-white/50">5 min read</span>
                        <button class="text-candy-pink hover:text-candy-pink/80">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Scan Card -->
                <div class="scan-card">
                    <div class="w-16 h-16 bg-candy-blue/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-candy-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-white mb-2">Scan New Document</h3>
                    <p class="text-white/70 text-sm mb-4">Capture or upload a document to start learning</p>
                    <button class="btn-candy-blue py-2 px-4 rounded-lg text-sm font-semibold">
                        Start Scanning
                    </button>
                </div>

                <!-- Progress Card -->
                <div class="glass-card p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-white">Learning Progress</h3>
                        <span class="text-candy-green font-semibold">75%</span>
                    </div>
                    <div class="w-full bg-white/10 rounded-full h-2 mb-4">
                        <div class="bg-gradient-to-r from-candy-green to-candy-cyan h-2 rounded-full" style="width: 75%"></div>
                    </div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between text-white/70">
                            <span>Cards Created</span>
                            <span>12/16</span>
                        </div>
                        <div class="flex justify-between text-white/70">
                            <span>Quizzes Completed</span>
                            <span>8/10</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Input Components -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-6">Form Elements</h2>
            <div class="glass-card p-6">
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-white">Text Inputs</h3>
                        <input type="text" placeholder="Search knowledge cards..." class="glass-input">
                        <input type="email" placeholder="Enter your email" class="glass-input">
                        <textarea placeholder="Add your notes..." rows="4" class="glass-input resize-none"></textarea>
                    </div>
                    
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-white">Select & Options</h3>
                        <select class="glass-input">
                            <option>Select Language</option>
                            <option>English</option>
                            <option>Spanish</option>
                            <option>French</option>
                            <option>German</option>
                        </select>
                        
                        <div class="space-y-2">
                            <label class="flex items-center space-x-3 text-white cursor-pointer">
                                <input type="checkbox" class="w-5 h-5 text-candy-pink bg-transparent border-2 border-white/30 rounded focus:ring-candy-pink">
                                <span>Enable voice narration</span>
                            </label>
                            <label class="flex items-center space-x-3 text-white cursor-pointer">
                                <input type="checkbox" class="w-5 h-5 text-candy-purple bg-transparent border-2 border-white/30 rounded focus:ring-candy-purple">
                                <span>Auto-generate flashcards</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Chat Components -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-6">Chat Interface</h2>
            <div class="glass-card p-6 max-w-2xl mx-auto">
                <div class="space-y-4 mb-6 max-h-64 overflow-y-auto">
                    <!-- User Message -->
                    <div class="flex justify-end">
                        <div class="bg-candy-pink/20 backdrop-blur-sm rounded-lg px-4 py-2 max-w-xs">
                            <p class="text-white text-sm">Can you explain machine learning in simple terms?</p>
                        </div>
                    </div>

                    <!-- AI Response -->
                    <div class="flex justify-start">
                        <div class="glass-morphism rounded-lg px-4 py-2 max-w-xs">
                            <p class="text-white text-sm">Machine learning is like teaching a computer to recognize patterns, just like how you learn to recognize faces or voices!</p>
                        </div>
                    </div>

                    <!-- User Message -->
                    <div class="flex justify-end">
                        <div class="bg-candy-pink/20 backdrop-blur-sm rounded-lg px-4 py-2 max-w-xs">
                            <p class="text-white text-sm">That's helpful! Can you give me an example?</p>
                        </div>
                    </div>
                </div>

                <!-- Chat Input -->
                <div class="flex items-center space-x-3">
                    <input type="text" placeholder="Ask me anything about your document..." class="glass-input flex-1">
                    <button class="btn-candy-pink p-3 rounded-lg">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                    </button>
                    <button class="glass-button p-3 rounded-lg">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </section>

        <!-- Modal Components -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-6">Modals & Overlays</h2>
            <div class="grid md:grid-cols-2 gap-6">
                <!-- Modal Trigger -->
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Modal Examples</h3>
                    <div class="space-y-3">
                        <button class="btn-candy-pink w-full py-2 px-4 rounded-lg" onclick="showModal('confirm-modal')">
                            Confirmation Modal
                        </button>
                        <button class="btn-candy-purple w-full py-2 px-4 rounded-lg" onclick="showModal('info-modal')">
                            Info Modal
                        </button>
                        <button class="btn-candy-blue w-full py-2 px-4 rounded-lg" onclick="showModal('form-modal')">
                            Form Modal
                        </button>
                    </div>
                </div>

                <!-- Toast Notifications -->
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Toast Notifications</h3>
                    <div class="space-y-3">
                        <button class="btn-candy-green w-full py-2 px-4 rounded-lg text-sm" onclick="showToast('success')">
                            Success Toast
                        </button>
                        <button class="btn-candy-yellow w-full py-2 px-4 rounded-lg text-sm" onclick="showToast('warning')">
                            Warning Toast
                        </button>
                        <button class="btn-candy-red w-full py-2 px-4 rounded-lg text-sm" onclick="showToast('error')">
                            Error Toast
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Progress Components -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-6">Progress Indicators</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Progress Bars -->
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Progress Bars</h3>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between text-sm text-white/70 mb-1">
                                <span>Learning Progress</span>
                                <span>75%</span>
                            </div>
                            <div class="w-full bg-white/10 rounded-full h-2">
                                <div class="bg-gradient-to-r from-candy-pink to-candy-purple h-2 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>

                        <div>
                            <div class="flex justify-between text-sm text-white/70 mb-1">
                                <span>Quiz Score</span>
                                <span>60%</span>
                            </div>
                            <div class="w-full bg-white/10 rounded-full h-2">
                                <div class="bg-gradient-to-r from-candy-blue to-candy-cyan h-2 rounded-full" style="width: 60%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Circular Progress -->
                <div class="glass-card p-6 text-center">
                    <h3 class="text-lg font-semibold text-white mb-4">Circular Progress</h3>
                    <div class="relative w-20 h-20 mx-auto">
                        <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 100 100">
                            <circle cx="50" cy="50" r="40" stroke="rgba(255,255,255,0.1)" stroke-width="8" fill="none"/>
                            <circle cx="50" cy="50" r="40" stroke="#FF6B9D" stroke-width="8" fill="none"
                                    stroke-dasharray="251" stroke-dashoffset="75" stroke-linecap="round"/>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-white font-bold">70%</span>
                        </div>
                    </div>
                </div>

                <!-- Loading Spinners -->
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Loading Spinners</h3>
                    <div class="space-y-4 text-center">
                        <div class="w-8 h-8 border-4 border-candy-pink border-t-transparent rounded-full animate-spin mx-auto"></div>
                        <div class="flex justify-center space-x-1">
                            <div class="w-2 h-2 bg-candy-purple rounded-full animate-bounce"></div>
                            <div class="w-2 h-2 bg-candy-pink rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                            <div class="w-2 h-2 bg-candy-blue rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Badge & Tag Components -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-6">Badges & Tags</h2>
            <div class="glass-card p-6">
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-white mb-4">Status Badges</h3>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-candy-green/20 text-candy-green px-3 py-1 rounded-full text-sm font-medium">Completed</span>
                            <span class="bg-candy-yellow/20 text-candy-yellow px-3 py-1 rounded-full text-sm font-medium">In Progress</span>
                            <span class="bg-candy-red/20 text-candy-red px-3 py-1 rounded-full text-sm font-medium">Failed</span>
                            <span class="bg-candy-blue/20 text-candy-blue px-3 py-1 rounded-full text-sm font-medium">New</span>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold text-white mb-4">Category Tags</h3>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-white/10 text-white px-3 py-1 rounded-lg text-sm border border-white/20">Machine Learning</span>
                            <span class="bg-white/10 text-white px-3 py-1 rounded-lg text-sm border border-white/20">AI Basics</span>
                            <span class="bg-white/10 text-white px-3 py-1 rounded-lg text-sm border border-white/20">Data Science</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Modal Templates -->
    <div id="confirm-modal" class="modal-overlay hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div class="glass-card rounded-2xl p-6 max-w-md w-full">
            <h3 class="text-xl font-bold text-white mb-4">Confirm Action</h3>
            <p class="text-white/80 mb-6">Are you sure you want to proceed with this action?</p>
            <div class="flex space-x-3">
                <button class="btn-candy-red flex-1 py-2 px-4 rounded-lg" onclick="hideModal('confirm-modal')">Cancel</button>
                <button class="btn-candy-green flex-1 py-2 px-4 rounded-lg" onclick="hideModal('confirm-modal')">Confirm</button>
            </div>
        </div>
    </div>

    <div id="info-modal" class="modal-overlay hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div class="glass-card rounded-2xl p-6 max-w-md w-full">
            <div class="flex items-center space-x-3 mb-4">
                <div class="w-10 h-10 bg-candy-blue/20 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-candy-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-white">Information</h3>
            </div>
            <p class="text-white/80 mb-6">This is an informational modal with important details about the current operation.</p>
            <button class="btn-candy-blue w-full py-2 px-4 rounded-lg" onclick="hideModal('info-modal')">Got it</button>
        </div>
    </div>

    <div id="form-modal" class="modal-overlay hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div class="glass-card rounded-2xl p-6 max-w-md w-full">
            <h3 class="text-xl font-bold text-white mb-4">Create New Card</h3>
            <div class="space-y-4 mb-6">
                <input type="text" placeholder="Card title..." class="glass-input w-full">
                <textarea placeholder="Card description..." rows="3" class="glass-input w-full resize-none"></textarea>
                <select class="glass-input w-full">
                    <option>Select category</option>
                    <option>Machine Learning</option>
                    <option>Data Science</option>
                    <option>AI Basics</option>
                </select>
            </div>
            <div class="flex space-x-3">
                <button class="glass-button flex-1 py-2 px-4 rounded-lg" onclick="hideModal('form-modal')">Cancel</button>
                <button class="btn-candy-pink flex-1 py-2 px-4 rounded-lg" onclick="hideModal('form-modal')">Create</button>
            </div>
        </div>
    </div>

    <script>
        // Modal functionality
        function showModal(modalId) {
            document.getElementById(modalId).classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function hideModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Toast notification functionality
        function showToast(type) {
            const toastContainer = document.getElementById('toast-container') || createToastContainer();

            const toast = document.createElement('div');
            toast.className = `glass-card rounded-lg p-4 mb-3 transform translate-x-full transition-transform duration-300`;

            let icon, message, bgColor;
            switch(type) {
                case 'success':
                    icon = '<svg class="w-5 h-5 text-candy-green" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
                    message = 'Operation completed successfully!';
                    bgColor = 'border-l-4 border-candy-green';
                    break;
                case 'warning':
                    icon = '<svg class="w-5 h-5 text-candy-yellow" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path></svg>';
                    message = 'Please check your input and try again.';
                    bgColor = 'border-l-4 border-candy-yellow';
                    break;
                case 'error':
                    icon = '<svg class="w-5 h-5 text-candy-red" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
                    message = 'An error occurred. Please try again.';
                    bgColor = 'border-l-4 border-candy-red';
                    break;
            }

            toast.innerHTML = `
                <div class="flex items-center space-x-3 ${bgColor}">
                    <div class="flex-shrink-0">${icon}</div>
                    <p class="text-white text-sm">${message}</p>
                    <button onclick="removeToast(this)" class="flex-shrink-0 text-white/50 hover:text-white">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `;

            toastContainer.appendChild(toast);

            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);

            // Auto remove after 5 seconds
            setTimeout(() => {
                removeToast(toast);
            }, 5000);
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'fixed top-4 right-4 z-50 max-w-sm';
            document.body.appendChild(container);
            return container;
        }

        function removeToast(element) {
            const toast = element.closest('.glass-card') || element;
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }

        // Close modals when clicking outside
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                hideModal(e.target.id);
            }
        });

        // Close modals with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal-overlay:not(.hidden)');
                if (openModal) {
                    hideModal(openModal.id);
                }
            }
        });
    </script>
</body>
</html>
