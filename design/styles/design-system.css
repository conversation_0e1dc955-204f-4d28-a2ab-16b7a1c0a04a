/* LearniScan Design System - Liquid Glass & Candy Colors */

/* Root Variables for Candy Colors */
:root {
  /* Candy Color Palette */
  --candy-pink: #FF6B9D;
  --candy-purple: #A855F7;
  --candy-blue: #3B82F6;
  --candy-cyan: #06B6D4;
  --candy-green: #10B981;
  --candy-yellow: #F59E0B;
  --candy-orange: #F97316;
  --candy-red: #EF4444;
  
  /* Glass Colors */
  --candy-pink-glass: rgba(255, 107, 157, 0.1);
  --candy-purple-glass: rgba(168, 85, 247, 0.1);
  --candy-blue-glass: rgba(59, 130, 246, 0.1);
  --candy-cyan-glass: rgba(6, 182, 212, 0.1);
  --candy-green-glass: rgba(16, 185, 129, 0.1);
  --candy-yellow-glass: rgba(245, 158, 11, 0.1);
  --candy-orange-glass: rgba(249, 115, 22, 0.1);
  --candy-red-glass: rgba(239, 68, 68, 0.1);
  
  /* Glass Base Colors */
  --glass-white: rgba(255, 255, 255, 0.1);
  --glass-black: rgba(0, 0, 0, 0.1);
  --glass-gray: rgba(107, 114, 128, 0.1);
  
  /* Glass Effects */
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-blur: blur(12px);
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-2xl: 24px;
  
  /* Transitions */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Styles */
* {
  box-sizing: border-box;
  transition: all var(--transition-normal);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  color: white;
  background: linear-gradient(135deg, var(--candy-purple), var(--candy-pink), var(--candy-blue));
  min-height: 100vh;
}

/* Glass Morphism Base Classes */
.glass-morphism {
  background: var(--glass-white);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.glass-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  border-radius: var(--radius-xl);
}

.glass-button {
  background: var(--glass-white);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  color: white;
  font-weight: 600;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: var(--glass-shadow), 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Candy Button Variants */
.btn-candy-pink {
  background: var(--candy-pink);
  color: white;
  border: none;
  box-shadow: 0 10px 25px -5px rgba(255, 107, 157, 0.4);
}

.btn-candy-pink:hover {
  background: #ff5a8a;
  box-shadow: 0 20px 40px -10px rgba(255, 107, 157, 0.6);
  transform: translateY(-3px);
}

.btn-candy-purple {
  background: var(--candy-purple);
  color: white;
  border: none;
  box-shadow: 0 10px 25px -5px rgba(168, 85, 247, 0.4);
}

.btn-candy-purple:hover {
  background: #9333ea;
  box-shadow: 0 20px 40px -10px rgba(168, 85, 247, 0.6);
  transform: translateY(-3px);
}

.btn-candy-blue {
  background: var(--candy-blue);
  color: white;
  border: none;
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
}

.btn-candy-blue:hover {
  background: #2563eb;
  box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.6);
  transform: translateY(-3px);
}

/* Glass Input Styles */
.glass-input {
  background: var(--glass-white);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  width: 100%;
  font-size: 1rem;
}

.glass-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.glass-input:focus {
  outline: none;
  border-color: var(--candy-pink);
  box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

/* Card Variants */
.knowledge-card {
  background: linear-gradient(135deg, var(--candy-pink-glass), var(--candy-purple-glass));
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.knowledge-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: var(--glass-shadow), 0 20px 40px rgba(255, 107, 157, 0.2);
}

.scan-card {
  background: linear-gradient(135deg, var(--candy-blue-glass), var(--candy-cyan-glass));
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  text-align: center;
}

/* Animation Classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
}

/* Keyframes */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0% { box-shadow: 0 0 20px rgba(255, 107, 157, 0.5); }
  100% { box-shadow: 0 0 30px rgba(255, 107, 157, 0.8); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Utility Classes */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.backdrop-blur-heavy {
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
}

.gradient-candy {
  background: linear-gradient(135deg, var(--candy-pink), var(--candy-purple), var(--candy-blue));
}

.gradient-candy-reverse {
  background: linear-gradient(135deg, var(--candy-blue), var(--candy-purple), var(--candy-pink));
}

/* Responsive Design */
@media (max-width: 768px) {
  .glass-card {
    padding: var(--spacing-md);
    margin: var(--spacing-sm);
  }
  
  .knowledge-card {
    padding: var(--spacing-md);
  }
  
  .scan-card {
    padding: var(--spacing-lg);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for accessibility */
.glass-button:focus,
.glass-input:focus {
  outline: 2px solid var(--candy-pink);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-morphism,
  .glass-card,
  .glass-button {
    border-width: 2px;
    border-color: white;
  }
}
