<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LearniScan Design System Documentation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles/design-system.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        candy: {
                            pink: '#FF6B9D',
                            purple: '#A855F7',
                            blue: '#3B82F6',
                            cyan: '#06B6D4',
                            green: '#10B981',
                            yellow: '#F59E0B',
                            orange: '#F97316',
                            red: '#EF4444',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 16px;
            font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: auto;
        }
        
        .nav-sticky {
            position: sticky;
            top: 20px;
        }
        
        .section-link {
            display: block;
            padding: 8px 16px;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s ease;
        }
        
        .section-link:hover {
            background: rgba(255, 107, 157, 0.1);
            color: #FF6B9D;
        }
        
        .section-link.active {
            background: rgba(255, 107, 157, 0.2);
            color: #FF6B9D;
            border-left: 3px solid #FF6B9D;
        }
    </style>
</head>
<body class="min-h-screen gradient-candy">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="p-6 border-b border-white/10">
            <div class="max-w-7xl mx-auto">
                <h1 class="text-3xl lg:text-4xl font-bold text-white mb-2">LearniScan Design System</h1>
                <p class="text-white/70 text-lg">Complete documentation for the Liquid Glass + Candy Colors design system</p>
            </div>
        </header>

        <div class="max-w-7xl mx-auto p-6">
            <div class="grid lg:grid-cols-4 gap-8">
                <!-- Navigation Sidebar -->
                <nav class="lg:col-span-1">
                    <div class="nav-sticky glass-card rounded-xl p-4">
                        <h3 class="text-lg font-semibold text-white mb-4">Contents</h3>
                        <div class="space-y-1">
                            <a href="#overview" class="section-link">Overview</a>
                            <a href="#colors" class="section-link">Color Palette</a>
                            <a href="#typography" class="section-link">Typography</a>
                            <a href="#spacing" class="section-link">Spacing</a>
                            <a href="#glass-effects" class="section-link">Liquid Glass Effects</a>
                            <a href="#components" class="section-link">Components</a>
                            <a href="#responsive" class="section-link">Responsive Design</a>
                            <a href="#accessibility" class="section-link">Accessibility</a>
                            <a href="#implementation" class="section-link">Implementation</a>
                        </div>
                    </div>
                </nav>

                <!-- Main Content -->
                <main class="lg:col-span-3 space-y-12">
                    <!-- Overview -->
                    <section id="overview" class="glass-card rounded-2xl p-8">
                        <h2 class="text-2xl font-bold text-white mb-6">Overview</h2>
                        <div class="space-y-4 text-white/80">
                            <p>The LearniScan design system combines Apple's Liquid Glass aesthetic with a vibrant Candy color palette to create an engaging, modern learning experience.</p>
                            
                            <div class="grid md:grid-cols-2 gap-6 mt-6">
                                <div>
                                    <h3 class="text-lg font-semibold text-white mb-3">Design Principles</h3>
                                    <ul class="space-y-2 text-sm">
                                        <li>• <strong>Clarity:</strong> Clean, minimal interfaces</li>
                                        <li>• <strong>Depth:</strong> Liquid glass effects create visual hierarchy</li>
                                        <li>• <strong>Vibrancy:</strong> Candy colors inspire engagement</li>
                                        <li>• <strong>Accessibility:</strong> WCAG AA compliant</li>
                                        <li>• <strong>Responsiveness:</strong> Mobile-first approach</li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-white mb-3">Key Features</h3>
                                    <ul class="space-y-2 text-sm">
                                        <li>• Backdrop blur effects</li>
                                        <li>• Translucent backgrounds</li>
                                        <li>• Smooth animations</li>
                                        <li>• Touch-friendly interactions</li>
                                        <li>• Consistent component library</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Color Palette -->
                    <section id="colors" class="glass-card rounded-2xl p-8">
                        <h2 class="text-2xl font-bold text-white mb-6">Candy Color Palette</h2>
                        
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                            <div class="text-center">
                                <div class="w-20 h-20 bg-candy-pink rounded-2xl mx-auto mb-3 shadow-lg"></div>
                                <div class="text-white font-medium">Pink</div>
                                <div class="text-white/60 text-sm">#FF6B9D</div>
                            </div>
                            <div class="text-center">
                                <div class="w-20 h-20 bg-candy-purple rounded-2xl mx-auto mb-3 shadow-lg"></div>
                                <div class="text-white font-medium">Purple</div>
                                <div class="text-white/60 text-sm">#A855F7</div>
                            </div>
                            <div class="text-center">
                                <div class="w-20 h-20 bg-candy-blue rounded-2xl mx-auto mb-3 shadow-lg"></div>
                                <div class="text-white font-medium">Blue</div>
                                <div class="text-white/60 text-sm">#3B82F6</div>
                            </div>
                            <div class="text-center">
                                <div class="w-20 h-20 bg-candy-cyan rounded-2xl mx-auto mb-3 shadow-lg"></div>
                                <div class="text-white font-medium">Cyan</div>
                                <div class="text-white/60 text-sm">#06B6D4</div>
                            </div>
                            <div class="text-center">
                                <div class="w-20 h-20 bg-candy-green rounded-2xl mx-auto mb-3 shadow-lg"></div>
                                <div class="text-white font-medium">Green</div>
                                <div class="text-white/60 text-sm">#10B981</div>
                            </div>
                            <div class="text-center">
                                <div class="w-20 h-20 bg-candy-yellow rounded-2xl mx-auto mb-3 shadow-lg"></div>
                                <div class="text-white font-medium">Yellow</div>
                                <div class="text-white/60 text-sm">#F59E0B</div>
                            </div>
                            <div class="text-center">
                                <div class="w-20 h-20 bg-candy-orange rounded-2xl mx-auto mb-3 shadow-lg"></div>
                                <div class="text-white font-medium">Orange</div>
                                <div class="text-white/60 text-sm">#F97316</div>
                            </div>
                            <div class="text-center">
                                <div class="w-20 h-20 bg-candy-red rounded-2xl mx-auto mb-3 shadow-lg"></div>
                                <div class="text-white font-medium">Red</div>
                                <div class="text-white/60 text-sm">#EF4444</div>
                            </div>
                        </div>

                        <div class="code-block text-white/90">
<pre>/* Tailwind CSS Configuration */
colors: {
  candy: {
    pink: '#FF6B9D',
    purple: '#A855F7',
    blue: '#3B82F6',
    cyan: '#06B6D4',
    green: '#10B981',
    yellow: '#F59E0B',
    orange: '#F97316',
    red: '#EF4444',
  }
}</pre>
                        </div>
                    </section>

                    <!-- Liquid Glass Effects -->
                    <section id="glass-effects" class="glass-card rounded-2xl p-8">
                        <h2 class="text-2xl font-bold text-white mb-6">Liquid Glass Effects</h2>
                        
                        <div class="grid md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4">Glass Morphism</h3>
                                <div class="glass-morphism rounded-xl p-4 mb-4">
                                    <p class="text-white text-sm">Basic glass morphism effect with backdrop blur and translucent background.</p>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4">Glass Card</h3>
                                <div class="glass-card rounded-xl p-4 mb-4">
                                    <p class="text-white text-sm">Enhanced glass card with gradient background and subtle shadows.</p>
                                </div>
                            </div>
                        </div>

                        <div class="code-block text-white/90">
<pre>/* Glass Morphism CSS */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-card {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1), 
    rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}</pre>
                        </div>
                    </section>

                    <!-- Typography -->
                    <section id="typography" class="glass-card rounded-2xl p-8">
                        <h2 class="text-2xl font-bold text-white mb-6">Typography</h2>
                        
                        <div class="space-y-6">
                            <div>
                                <h1 class="text-4xl font-bold text-white mb-2">Heading 1</h1>
                                <p class="text-white/60 text-sm">text-4xl font-bold</p>
                            </div>
                            <div>
                                <h2 class="text-3xl font-bold text-white mb-2">Heading 2</h2>
                                <p class="text-white/60 text-sm">text-3xl font-bold</p>
                            </div>
                            <div>
                                <h3 class="text-2xl font-semibold text-white mb-2">Heading 3</h3>
                                <p class="text-white/60 text-sm">text-2xl font-semibold</p>
                            </div>
                            <div>
                                <p class="text-lg text-white mb-2">Body Large - Used for important content and descriptions</p>
                                <p class="text-white/60 text-sm">text-lg</p>
                            </div>
                            <div>
                                <p class="text-base text-white mb-2">Body Regular - Standard body text for most content</p>
                                <p class="text-white/60 text-sm">text-base</p>
                            </div>
                            <div>
                                <p class="text-sm text-white/80 mb-2">Body Small - Secondary information and captions</p>
                                <p class="text-white/60 text-sm">text-sm text-white/80</p>
                            </div>
                        </div>
                    </section>

                    <!-- Components -->
                    <section id="components" class="glass-card rounded-2xl p-8">
                        <h2 class="text-2xl font-bold text-white mb-6">Component Library</h2>
                        
                        <div class="space-y-8">
                            <!-- Buttons -->
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4">Buttons</h3>
                                <div class="flex flex-wrap gap-3 mb-4">
                                    <button class="btn-candy-pink py-2 px-4 rounded-lg font-semibold">Primary</button>
                                    <button class="btn-candy-purple py-2 px-4 rounded-lg font-semibold">Secondary</button>
                                    <button class="glass-button py-2 px-4 rounded-lg font-semibold">Glass</button>
                                </div>
                                <div class="code-block text-white/90 text-xs">
<pre>&lt;button class="btn-candy-pink py-2 px-4 rounded-lg font-semibold"&gt;Primary&lt;/button&gt;
&lt;button class="glass-button py-2 px-4 rounded-lg font-semibold"&gt;Glass&lt;/button&gt;</pre>
                                </div>
                            </div>

                            <!-- Cards -->
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4">Cards</h3>
                                <div class="grid md:grid-cols-2 gap-4 mb-4">
                                    <div class="knowledge-card p-4 rounded-xl">
                                        <h4 class="font-semibold text-white mb-2">Knowledge Card</h4>
                                        <p class="text-white/70 text-sm">Interactive learning card with hover effects</p>
                                    </div>
                                    <div class="glass-card p-4 rounded-xl">
                                        <h4 class="font-semibold text-white mb-2">Glass Card</h4>
                                        <p class="text-white/70 text-sm">Standard glass morphism card</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Elements -->
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4">Form Elements</h3>
                                <div class="space-y-3 mb-4">
                                    <input type="text" placeholder="Glass input field" class="glass-input w-full">
                                    <textarea placeholder="Glass textarea" rows="3" class="glass-input w-full resize-none"></textarea>
                                    <select class="glass-input w-full">
                                        <option>Glass select dropdown</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Responsive Design -->
                    <section id="responsive" class="glass-card rounded-2xl p-8">
                        <h2 class="text-2xl font-bold text-white mb-6">Responsive Breakpoints</h2>
                        
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4">Breakpoint System</h3>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-white">XS (Extra Small)</span>
                                        <span class="text-white/60">&lt; 475px</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-white">SM (Small)</span>
                                        <span class="text-white/60">640px+</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-white">MD (Medium)</span>
                                        <span class="text-white/60">768px+</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-white">LG (Large)</span>
                                        <span class="text-white/60">1024px+</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-white">XL (Extra Large)</span>
                                        <span class="text-white/60">1280px+</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-white">2XL (2X Large)</span>
                                        <span class="text-white/60">1536px+</span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4">Touch Targets</h3>
                                <div class="space-y-2 text-sm text-white/80">
                                    <p>• Minimum 44px height for touch elements</p>
                                    <p>• Adequate spacing between interactive elements</p>
                                    <p>• Larger touch targets on mobile devices</p>
                                    <p>• Hover states for desktop interactions</p>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Implementation -->
                    <section id="implementation" class="glass-card rounded-2xl p-8">
                        <h2 class="text-2xl font-bold text-white mb-6">Implementation Guide</h2>
                        
                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-3">Getting Started</h3>
                                <div class="code-block text-white/90">
<pre>1. Include Tailwind CSS
2. Add design-system.css
3. Configure candy colors in Tailwind config
4. Use glass-morphism and glass-card classes
5. Apply responsive breakpoints</pre>
                                </div>
                            </div>
                            
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-3">File Structure</h3>
                                <div class="code-block text-white/90 text-sm">
<pre>design/
├── index.html              # Design system showcase
├── styles/
│   └── design-system.css   # Core CSS system
├── components/
│   └── components.html     # Component library
├── pages/
│   ├── welcome.html        # Welcome screen
│   ├── scan.html          # Document scanning
│   ├── text-review.html   # Text review & translation
│   ├── knowledge-cards.html # Knowledge cards
│   ├── knowledge-graph.html # Knowledge graph
│   ├── ai-chat.html       # AI tutor chat
│   ├── quiz.html          # Quiz & workshop
│   └── social-share.html  # Social sharing
├── responsive-test.html    # Responsive testing
└── documentation.html      # This documentation</pre>
                                </div>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </div>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('.section-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Update active navigation link on scroll
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.section-link');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                if (window.pageYOffset >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
