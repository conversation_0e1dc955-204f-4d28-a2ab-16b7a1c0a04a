/**
 * LearniScan Design Tokens for NativeWind
 * 
 * This file contains all design tokens extracted from the LearniScan design system
 * for use with NativeWind in React Native applications.
 * 
 * Usage:
 * 1. Import this file in your tailwind.config.js
 * 2. Extend the theme with these tokens
 * 3. Use the utility classes in your React Native components
 */

const { platformSelect, hairlineWidth, pixelRatio, fontScale } = require("nativewind");

// ===== COLOR TOKENS =====
const colors = {
  // Candy Color Palette - Primary brand colors
  candy: {
    pink: '#FF6B9D',
    purple: '#A855F7', 
    blue: '#3B82F6',
    cyan: '#06B6D4',
    green: '#10B981',
    yellow: '#F59E0B',
    orange: '#F97316',
    red: '#EF4444',
  },
  
  // Glass Effect Colors - For liquid glass morphism
  glass: {
    // Translucent backgrounds
    bg: {
      primary: 'rgba(255, 255, 255, 0.1)',
      secondary: 'rgba(255, 255, 255, 0.05)',
      card: 'rgba(255, 255, 255, 0.08)',
    },
    // Border colors
    border: {
      primary: 'rgba(255, 255, 255, 0.2)',
      secondary: 'rgba(255, 255, 255, 0.1)',
      accent: 'rgba(255, 107, 157, 0.3)',
    },
    // Shadow colors
    shadow: {
      primary: 'rgba(31, 38, 135, 0.37)',
      accent: 'rgba(255, 107, 157, 0.3)',
    }
  },
  
  // Semantic Colors - For status and feedback
  semantic: {
    success: '#10B981',
    warning: '#F59E0B', 
    error: '#EF4444',
    info: '#3B82F6',
  },
  
  // Text Colors - For typography
  text: {
    primary: '#FFFFFF',
    secondary: 'rgba(255, 255, 255, 0.8)',
    tertiary: 'rgba(255, 255, 255, 0.6)',
    disabled: 'rgba(255, 255, 255, 0.4)',
  }
};

// ===== TYPOGRAPHY TOKENS =====
const typography = {
  fontFamily: {
    // System fonts optimized for each platform
    sans: platformSelect({
      ios: ['SF Pro Display', 'system'],
      android: ['Roboto', 'sans-serif'],
      default: ['system-ui', 'sans-serif']
    }),
    mono: platformSelect({
      ios: ['SF Mono', 'Monaco'],
      android: ['Roboto Mono', 'monospace'],
      default: ['ui-monospace', 'monospace']
    })
  },
  
  fontSize: {
    // Responsive font sizes using fontScale for accessibility
    xs: fontScale(12),
    sm: fontScale(14),
    base: fontScale(16),
    lg: fontScale(18),
    xl: fontScale(20),
    '2xl': fontScale(24),
    '3xl': fontScale(30),
    '4xl': fontScale(36),
    '5xl': fontScale(48),
  },
  
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
  
  letterSpacing: {
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
  }
};

// ===== SPACING TOKENS =====
const spacing = {
  // Base spacing scale using pixelRatio for crisp rendering
  0: 0,
  1: pixelRatio(4),
  2: pixelRatio(8),
  3: pixelRatio(12),
  4: pixelRatio(16),
  5: pixelRatio(20),
  6: pixelRatio(24),
  8: pixelRatio(32),
  10: pixelRatio(40),
  12: pixelRatio(48),
  16: pixelRatio(64),
  20: pixelRatio(80),
  24: pixelRatio(96),
  32: pixelRatio(128),
  40: pixelRatio(160),
  48: pixelRatio(192),
  56: pixelRatio(224),
  64: pixelRatio(256),
  
  // Touch targets - minimum 44px for accessibility
  touchTarget: 44,
  touchTargetLarge: 56,
};

// ===== BORDER RADIUS TOKENS =====
const borderRadius = {
  none: 0,
  sm: 4,
  DEFAULT: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  '3xl': 32,
  full: 9999,
  
  // Glass-specific radius for liquid glass effects
  glass: {
    sm: 8,
    DEFAULT: 12,
    lg: 16,
    xl: 20,
  }
};

// ===== SHADOW TOKENS =====
const boxShadow = {
  // Glass morphism shadows
  glass: {
    sm: '0 4px 16px 0 rgba(31, 38, 135, 0.2)',
    DEFAULT: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
    lg: '0 16px 48px 0 rgba(31, 38, 135, 0.4)',
  },
  
  // Candy accent shadows
  candy: {
    pink: '0 8px 32px 0 rgba(255, 107, 157, 0.3)',
    purple: '0 8px 32px 0 rgba(168, 85, 247, 0.3)',
    blue: '0 8px 32px 0 rgba(59, 130, 246, 0.3)',
  },
  
  // Standard shadows
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
};

// ===== BORDER WIDTH TOKENS =====
const borderWidth = {
  0: 0,
  DEFAULT: 1,
  2: 2,
  4: 4,
  8: 8,
  hairline: hairlineWidth(), // Platform-specific hairline width
};

// ===== ANIMATION TOKENS =====
const animation = {
  // Duration
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
  },
  
  // Timing functions
  timing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
  
  // Transform values
  scale: {
    95: '0.95',
    100: '1',
    105: '1.05',
    110: '1.1',
  }
};

// ===== BREAKPOINT TOKENS =====
const screens = {
  xs: '475px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};

// ===== BACKDROP BLUR TOKENS =====
const backdropBlur = {
  none: '0',
  sm: '4px',
  DEFAULT: '8px',
  md: '12px',
  lg: '16px',
  xl: '24px',
  '2xl': '40px',
  '3xl': '64px',
  
  // Glass-specific blur values
  glass: '12px',
  glassLight: '8px',
  glassHeavy: '16px',
};

// ===== COMPONENT-SPECIFIC TOKENS =====
const components = {
  // Button variants
  button: {
    height: {
      sm: 32,
      DEFAULT: 40,
      lg: 48,
      xl: 56,
    },
    padding: {
      sm: { x: 12, y: 6 },
      DEFAULT: { x: 16, y: 8 },
      lg: { x: 20, y: 12 },
      xl: { x: 24, y: 16 },
    }
  },
  
  // Card variants
  card: {
    padding: {
      sm: 12,
      DEFAULT: 16,
      lg: 20,
      xl: 24,
    },
    gap: {
      sm: 8,
      DEFAULT: 12,
      lg: 16,
    }
  },
  
  // Input variants
  input: {
    height: {
      sm: 36,
      DEFAULT: 44, // Touch-friendly
      lg: 52,
    },
    padding: {
      x: 12,
      y: 8,
    }
  }
};

// ===== EXPORT DESIGN TOKENS =====
module.exports = {
  colors,
  typography,
  spacing,
  borderRadius,
  boxShadow,
  borderWidth,
  animation,
  screens,
  backdropBlur,
  components,

  // Utility functions for custom implementations
  utils: {
    platformSelect,
    hairlineWidth,
    pixelRatio,
    fontScale,
  }
};

// ===== NATIVEWIND TAILWIND CONFIG =====
// Use this configuration in your tailwind.config.js file
const nativeWindConfig = {
  content: [
    "./App.{js,jsx,ts,tsx}",
    "./app/**/*.{js,jsx,ts,tsx}",
    "./components/**/*.{js,jsx,ts,tsx}",
    "./screens/**/*.{js,jsx,ts,tsx}",
    "./pages/**/*.{js,jsx,ts,tsx}",
  ],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        ...colors.candy,
        glass: colors.glass,
        semantic: colors.semantic,
        text: colors.text,
      },
      fontFamily: typography.fontFamily,
      fontSize: typography.fontSize,
      fontWeight: typography.fontWeight,
      lineHeight: typography.lineHeight,
      letterSpacing: typography.letterSpacing,
      spacing: spacing,
      borderRadius: borderRadius,
      boxShadow: boxShadow,
      borderWidth: borderWidth,
      screens: screens,
      backdropBlur: backdropBlur,
      transitionDuration: animation.duration,
      transitionTimingFunction: animation.timing,
      scale: animation.scale,
    },
  },
  plugins: [
    // Custom plugin for glass morphism utilities
    function({ addUtilities, theme }) {
      const glassUtilities = {
        '.glass-morphism': {
          backgroundColor: theme('colors.glass.bg.primary'),
          backdropFilter: `blur(${theme('backdropBlur.glass')})`,
          WebkitBackdropFilter: `blur(${theme('backdropBlur.glass')})`,
          border: `1px solid ${theme('colors.glass.border.primary')}`,
          boxShadow: theme('boxShadow.glass.DEFAULT'),
        },
        '.glass-card': {
          background: `linear-gradient(135deg, ${theme('colors.glass.bg.primary')}, ${theme('colors.glass.bg.secondary')})`,
          backdropFilter: `blur(${theme('backdropBlur.glass')})`,
          WebkitBackdropFilter: `blur(${theme('backdropBlur.glass')})`,
          border: `1px solid ${theme('colors.glass.border.secondary')}`,
          boxShadow: theme('boxShadow.glass.DEFAULT'),
        },
        '.glass-button': {
          backgroundColor: theme('colors.glass.bg.card'),
          backdropFilter: `blur(${theme('backdropBlur.glassLight')})`,
          WebkitBackdropFilter: `blur(${theme('backdropBlur.glassLight')})`,
          border: `1px solid ${theme('colors.glass.border.primary')}`,
        },
        '.glass-input': {
          backgroundColor: theme('colors.glass.bg.secondary'),
          backdropFilter: `blur(${theme('backdropBlur.glass')})`,
          WebkitBackdropFilter: `blur(${theme('backdropBlur.glass')})`,
          border: `1px solid ${theme('colors.glass.border.primary')}`,
          color: theme('colors.text.primary'),
        },
        '.btn-candy-pink': {
          background: `linear-gradient(135deg, ${theme('colors.candy.pink')}, rgba(255, 107, 157, 0.8))`,
          color: theme('colors.text.primary'),
          boxShadow: theme('boxShadow.candy.pink'),
        },
        '.btn-candy-purple': {
          background: `linear-gradient(135deg, ${theme('colors.candy.purple')}, rgba(168, 85, 247, 0.8))`,
          color: theme('colors.text.primary'),
          boxShadow: theme('boxShadow.candy.purple'),
        },
        '.btn-candy-blue': {
          background: `linear-gradient(135deg, ${theme('colors.candy.blue')}, rgba(59, 130, 246, 0.8))`,
          color: theme('colors.text.primary'),
          boxShadow: theme('boxShadow.candy.blue'),
        },
        '.gradient-candy': {
          background: `linear-gradient(135deg,
            rgba(255, 107, 157, 0.9) 0%,
            rgba(168, 85, 247, 0.8) 50%,
            rgba(59, 130, 246, 0.9) 100%)`,
        },
      };

      addUtilities(glassUtilities);
    },
  ],
};

// Export the NativeWind config for easy import
module.exports.nativeWindConfig = nativeWindConfig;
