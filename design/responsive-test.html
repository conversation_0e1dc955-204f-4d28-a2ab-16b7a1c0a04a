<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Breakpoints Test - LearniScan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles/design-system.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        candy: {
                            pink: '#FF6B9D',
                            purple: '#A855F7',
                            blue: '#3B82F6',
                            cyan: '#06B6D4',
                            green: '#10B981',
                            yellow: '#F59E0B',
                            orange: '#F97316',
                            red: '#EF4444',
                        }
                    },
                    screens: {
                        'xs': '475px',
                        'sm': '640px',
                        'md': '768px',
                        'lg': '1024px',
                        'xl': '1280px',
                        '2xl': '1536px',
                    }
                }
            }
        }
    </script>
    <style>
        .breakpoint-indicator {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .grid-demo {
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
                        linear-gradient(-45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%),
                        linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .touch-target {
            min-height: 44px;
            min-width: 44px;
        }
        
        @media (max-width: 474px) {
            .breakpoint-indicator::after { content: "XS (< 475px)"; }
        }
        @media (min-width: 475px) and (max-width: 639px) {
            .breakpoint-indicator::after { content: "XS (475px+)"; }
        }
        @media (min-width: 640px) and (max-width: 767px) {
            .breakpoint-indicator::after { content: "SM (640px+)"; }
        }
        @media (min-width: 768px) and (max-width: 1023px) {
            .breakpoint-indicator::after { content: "MD (768px+)"; }
        }
        @media (min-width: 1024px) and (max-width: 1279px) {
            .breakpoint-indicator::after { content: "LG (1024px+)"; }
        }
        @media (min-width: 1280px) and (max-width: 1535px) {
            .breakpoint-indicator::after { content: "XL (1280px+)"; }
        }
        @media (min-width: 1536px) {
            .breakpoint-indicator::after { content: "2XL (1536px+)"; }
        }
    </style>
</head>
<body class="min-h-screen gradient-candy">
    <!-- Breakpoint Indicator -->
    <div class="breakpoint-indicator"></div>

    <div class="min-h-screen">
        <!-- Header - Responsive Navigation -->
        <header class="p-4 lg:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2 lg:space-x-4">
                    <button class="glass-button p-2 rounded-lg touch-target lg:hidden">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                    <h1 class="text-lg sm:text-xl lg:text-2xl font-bold text-white">Responsive Test</h1>
                </div>
                
                <div class="flex items-center space-x-2">
                    <button class="glass-button p-2 rounded-lg touch-target">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                    <button class="glass-button p-2 rounded-lg touch-target">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-5a7.5 7.5 0 0 1 15 0v5z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="px-4 lg:px-6 pb-6">
            <!-- Grid Layout Test -->
            <section class="mb-8">
                <h2 class="text-xl lg:text-2xl font-bold text-white mb-6">Responsive Grid Layout</h2>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-6">
                    <div class="glass-card p-4 lg:p-6 grid-demo">
                        <h3 class="text-lg font-semibold text-white mb-2">Card 1</h3>
                        <p class="text-white/70 text-sm">1 col on mobile, 2 on tablet, 3 on desktop, 4 on xl</p>
                    </div>
                    <div class="glass-card p-4 lg:p-6 grid-demo">
                        <h3 class="text-lg font-semibold text-white mb-2">Card 2</h3>
                        <p class="text-white/70 text-sm">Responsive grid system demonstration</p>
                    </div>
                    <div class="glass-card p-4 lg:p-6 grid-demo">
                        <h3 class="text-lg font-semibold text-white mb-2">Card 3</h3>
                        <p class="text-white/70 text-sm">Touch-friendly on mobile devices</p>
                    </div>
                    <div class="glass-card p-4 lg:p-6 grid-demo">
                        <h3 class="text-lg font-semibold text-white mb-2">Card 4</h3>
                        <p class="text-white/70 text-sm">Optimized for all screen sizes</p>
                    </div>
                </div>
            </section>

            <!-- Typography Scale -->
            <section class="mb-8">
                <h2 class="text-xl lg:text-2xl font-bold text-white mb-6">Typography Scale</h2>
                <div class="glass-card p-4 lg:p-6 space-y-4">
                    <h1 class="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-white">Heading 1</h1>
                    <h2 class="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-white">Heading 2</h2>
                    <h3 class="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-semibold text-white">Heading 3</h3>
                    <p class="text-sm sm:text-base lg:text-lg text-white/80">Body text scales appropriately across devices for optimal readability.</p>
                    <p class="text-xs sm:text-sm lg:text-base text-white/60">Small text remains legible on all screen sizes.</p>
                </div>
            </section>

            <!-- Button Sizes & Touch Targets -->
            <section class="mb-8">
                <h2 class="text-xl lg:text-2xl font-bold text-white mb-6">Touch-Friendly Buttons</h2>
                <div class="glass-card p-4 lg:p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="space-y-3">
                            <h3 class="text-lg font-semibold text-white">Mobile Optimized</h3>
                            <button class="btn-candy-pink w-full py-3 px-4 rounded-lg font-semibold touch-target">
                                Large Touch Target
                            </button>
                            <button class="btn-candy-purple w-full py-3 px-4 rounded-lg font-semibold touch-target">
                                44px Min Height
                            </button>
                        </div>
                        
                        <div class="space-y-3">
                            <h3 class="text-lg font-semibold text-white">Icon Buttons</h3>
                            <div class="flex space-x-3">
                                <button class="glass-button p-3 rounded-lg touch-target">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                </button>
                                <button class="glass-button p-3 rounded-lg touch-target">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                    </svg>
                                </button>
                                <button class="glass-button p-3 rounded-lg touch-target">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        
                        <div class="space-y-3">
                            <h3 class="text-lg font-semibold text-white">Responsive Sizing</h3>
                            <button class="btn-candy-blue w-full py-2 sm:py-3 px-3 sm:px-4 lg:px-6 rounded-lg font-semibold text-sm sm:text-base">
                                Adaptive Size
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Form Elements -->
            <section class="mb-8">
                <h2 class="text-xl lg:text-2xl font-bold text-white mb-6">Responsive Forms</h2>
                <div class="glass-card p-4 lg:p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <input type="text" placeholder="Full width on mobile, half on desktop" 
                                   class="glass-input w-full py-3 px-4 text-base">
                            <textarea placeholder="Responsive textarea with proper touch targets" 
                                      rows="4" class="glass-input w-full py-3 px-4 text-base resize-none"></textarea>
                        </div>
                        <div class="space-y-4">
                            <select class="glass-input w-full py-3 px-4 text-base">
                                <option>Responsive select dropdown</option>
                                <option>Option 1</option>
                                <option>Option 2</option>
                            </select>
                            <div class="flex flex-col sm:flex-row gap-3">
                                <button class="btn-candy-green flex-1 py-3 px-4 rounded-lg font-semibold touch-target">
                                    Submit
                                </button>
                                <button class="glass-button flex-1 py-3 px-4 rounded-lg font-semibold touch-target">
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Spacing & Layout -->
            <section class="mb-8">
                <h2 class="text-xl lg:text-2xl font-bold text-white mb-6">Responsive Spacing</h2>
                <div class="space-y-4 lg:space-y-6">
                    <div class="glass-card p-3 sm:p-4 lg:p-6 xl:p-8">
                        <h3 class="text-lg font-semibold text-white mb-2 lg:mb-4">Adaptive Padding</h3>
                        <p class="text-white/70 text-sm lg:text-base">
                            Padding scales from 12px on mobile to 32px on xl screens for optimal content density.
                        </p>
                    </div>
                    
                    <div class="flex flex-col sm:flex-row gap-3 lg:gap-6">
                        <div class="glass-card p-4 lg:p-6 flex-1">
                            <h4 class="font-semibold text-white">Flexible Layout</h4>
                            <p class="text-white/70 text-sm">Stacks on mobile, side-by-side on tablet+</p>
                        </div>
                        <div class="glass-card p-4 lg:p-6 flex-1">
                            <h4 class="font-semibold text-white">Equal Width</h4>
                            <p class="text-white/70 text-sm">Responsive gap spacing</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Breakpoint Information -->
            <section class="mb-8">
                <h2 class="text-xl lg:text-2xl font-bold text-white mb-6">Breakpoint Reference</h2>
                <div class="glass-card p-4 lg:p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                        <div class="space-y-2">
                            <h4 class="font-semibold text-white">Mobile First</h4>
                            <div class="text-white/70">
                                <div>XS: &lt; 475px</div>
                                <div>SM: 640px+</div>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <h4 class="font-semibold text-white">Tablet & Desktop</h4>
                            <div class="text-white/70">
                                <div>MD: 768px+</div>
                                <div>LG: 1024px+</div>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <h4 class="font-semibold text-white">Large Screens</h4>
                            <div class="text-white/70">
                                <div>XL: 1280px+</div>
                                <div>2XL: 1536px+</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        // Touch interaction enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Add touch feedback for buttons
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });
                
                button.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Prevent zoom on double tap for form inputs on iOS
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('touchend', function(e) {
                    e.preventDefault();
                    this.focus();
                });
            });

            // Log current breakpoint for debugging
            function logBreakpoint() {
                const width = window.innerWidth;
                let breakpoint;
                
                if (width < 475) breakpoint = 'XS';
                else if (width < 640) breakpoint = 'XS+';
                else if (width < 768) breakpoint = 'SM';
                else if (width < 1024) breakpoint = 'MD';
                else if (width < 1280) breakpoint = 'LG';
                else if (width < 1536) breakpoint = 'XL';
                else breakpoint = '2XL';
                
                console.log(`Current breakpoint: ${breakpoint} (${width}px)`);
            }

            // Log breakpoint on load and resize
            logBreakpoint();
            window.addEventListener('resize', logBreakpoint);
        });
    </script>
</body>
</html>
