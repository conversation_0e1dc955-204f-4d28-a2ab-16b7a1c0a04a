<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scan Document - LearniScan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../styles/design-system.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        candy: {
                            pink: '#FF6B9D',
                            purple: '#A855F7',
                            blue: '#3B82F6',
                            cyan: '#06B6D4',
                            green: '#10B981',
                            yellow: '#F59E0B',
                            orange: '#F97316',
                            red: '#EF4444',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .viewfinder {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 2px dashed rgba(255, 107, 157, 0.5);
            position: relative;
            overflow: hidden;
        }
        
        .viewfinder::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 49%, rgba(255, 107, 157, 0.1) 50%, transparent 51%);
            animation: scan-line 3s linear infinite;
        }
        
        @keyframes scan-line {
            0% { transform: translateY(-100%); }
            100% { transform: translateY(100vh); }
        }
        
        .corner-frame {
            position: absolute;
            width: 30px;
            height: 30px;
            border: 3px solid #FF6B9D;
        }
        
        .corner-tl { top: 20px; left: 20px; border-right: none; border-bottom: none; }
        .corner-tr { top: 20px; right: 20px; border-left: none; border-bottom: none; }
        .corner-bl { bottom: 20px; left: 20px; border-right: none; border-top: none; }
        .corner-br { bottom: 20px; right: 20px; border-left: none; border-top: none; }
        
        .upload-zone {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        
        .upload-zone:hover {
            border-color: #FF6B9D;
            background: rgba(255, 107, 157, 0.05);
        }
        
        .upload-zone.dragover {
            border-color: #A855F7;
            background: rgba(168, 85, 247, 0.1);
            transform: scale(1.02);
        }
    </style>
</head>
<body class="min-h-screen gradient-candy">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="p-4 md:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button class="glass-button p-2 rounded-lg hover:scale-105 transition-transform">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <h1 class="text-xl md:text-2xl font-bold text-white">Scan Document</h1>
                </div>
                
                <div class="flex items-center space-x-2">
                    <button class="glass-button p-2 rounded-lg" title="Flash">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </button>
                    <button class="glass-button p-2 rounded-lg" title="Settings">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 px-4 md:px-6 pb-6">
            <!-- Scan Methods Tabs -->
            <div class="mb-6">
                <div class="glass-card p-1 rounded-xl inline-flex">
                    <button class="scan-tab active px-4 py-2 rounded-lg bg-candy-pink text-white font-semibold transition-all">
                        Camera
                    </button>
                    <button class="scan-tab px-4 py-2 rounded-lg text-white/70 hover:text-white hover:bg-white/10 font-semibold transition-all">
                        Upload
                    </button>
                    <button class="scan-tab px-4 py-2 rounded-lg text-white/70 hover:text-white hover:bg-white/10 font-semibold transition-all">
                        Gallery
                    </button>
                </div>
            </div>

            <!-- Camera View -->
            <div id="camera-view" class="scan-content">
                <div class="max-w-md mx-auto">
                    <!-- Viewfinder -->
                    <div class="viewfinder aspect-[3/4] rounded-2xl mb-6 relative flex items-center justify-center">
                        <!-- Corner Frames -->
                        <div class="corner-frame corner-tl"></div>
                        <div class="corner-frame corner-tr"></div>
                        <div class="corner-frame corner-bl"></div>
                        <div class="corner-frame corner-br"></div>
                        
                        <!-- Center Content -->
                        <div class="text-center z-10">
                            <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                </svg>
                            </div>
                            <p class="text-white/80 text-sm">Position document within frame</p>
                            <p class="text-white/60 text-xs mt-1">Auto-crop will detect edges</p>
                        </div>
                    </div>

                    <!-- Camera Controls -->
                    <div class="flex items-center justify-center space-x-6 mb-6">
                        <button class="glass-button p-3 rounded-full">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                        
                        <button class="btn-candy-pink p-6 rounded-full hover:scale-110 transition-transform animate-pulse">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                            </svg>
                        </button>
                        
                        <button class="glass-button p-3 rounded-full">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Upload View -->
            <div id="upload-view" class="scan-content hidden">
                <div class="max-w-md mx-auto">
                    <div class="upload-zone glass-card rounded-2xl p-8 text-center mb-6 cursor-pointer">
                        <div class="w-16 h-16 bg-candy-blue/20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-candy-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-2">Upload Document</h3>
                        <p class="text-white/70 text-sm mb-4">Drag and drop your file here or click to browse</p>
                        <p class="text-white/50 text-xs">Supports: PDF, JPG, PNG, HEIC</p>
                        <input type="file" class="hidden" accept=".pdf,.jpg,.jpeg,.png,.heic" multiple>
                    </div>
                </div>
            </div>

            <!-- Gallery View -->
            <div id="gallery-view" class="scan-content hidden">
                <div class="max-w-md mx-auto">
                    <div class="glass-card rounded-2xl p-6 mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Recent Photos</h3>
                        <div class="grid grid-cols-3 gap-3">
                            <div class="aspect-square bg-white/10 rounded-lg flex items-center justify-center cursor-pointer hover:bg-white/20 transition-colors">
                                <svg class="w-6 h-6 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div class="aspect-square bg-white/10 rounded-lg flex items-center justify-center cursor-pointer hover:bg-white/20 transition-colors">
                                <svg class="w-6 h-6 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div class="aspect-square bg-white/10 rounded-lg flex items-center justify-center cursor-pointer hover:bg-white/20 transition-colors">
                                <svg class="w-6 h-6 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tips Section -->
            <div class="max-w-md mx-auto">
                <div class="glass-card rounded-xl p-4">
                    <h4 class="text-sm font-semibold text-white mb-2 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-candy-yellow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        Scanning Tips
                    </h4>
                    <ul class="text-white/70 text-xs space-y-1">
                        <li>• Ensure good lighting for better OCR results</li>
                        <li>• Keep the document flat and within the frame</li>
                        <li>• Multiple pages? Scan them one by one</li>
                        <li>• Auto-crop will detect document edges</li>
                    </ul>
                </div>
            </div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab switching functionality
            const tabs = document.querySelectorAll('.scan-tab');
            const contents = document.querySelectorAll('.scan-content');
            
            tabs.forEach((tab, index) => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    tabs.forEach(t => {
                        t.classList.remove('active', 'bg-candy-pink');
                        t.classList.add('text-white/70');
                    });
                    
                    // Add active class to clicked tab
                    this.classList.add('active', 'bg-candy-pink');
                    this.classList.remove('text-white/70');
                    this.classList.add('text-white');
                    
                    // Hide all content
                    contents.forEach(content => content.classList.add('hidden'));
                    
                    // Show corresponding content
                    contents[index].classList.remove('hidden');
                });
            });

            // File upload functionality
            const uploadZone = document.querySelector('.upload-zone');
            const fileInput = uploadZone.querySelector('input[type="file"]');
            
            uploadZone.addEventListener('click', () => fileInput.click());
            
            uploadZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadZone.classList.add('dragover');
            });
            
            uploadZone.addEventListener('dragleave', () => {
                uploadZone.classList.remove('dragover');
            });
            
            uploadZone.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');
                const files = e.dataTransfer.files;
                handleFiles(files);
            });
            
            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });
            
            function handleFiles(files) {
                console.log('Files selected:', files);
                // Here you would handle the file upload and processing
            }

            // Camera capture simulation
            document.querySelector('.btn-candy-pink').addEventListener('click', function() {
                this.classList.add('animate-pulse');
                setTimeout(() => {
                    this.classList.remove('animate-pulse');
                    // Navigate to text review page
                    console.log('Document captured, processing...');
                }, 500);
            });
        });
    </script>
</body>
</html>
