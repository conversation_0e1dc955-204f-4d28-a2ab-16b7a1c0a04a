<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Graph - LearniScan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <link rel="stylesheet" href="../styles/design-system.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        candy: {
                            pink: '#FF6B9D',
                            purple: '#A855F7',
                            blue: '#3B82F6',
                            cyan: '#06B6D4',
                            green: '#10B981',
                            yellow: '#F59E0B',
                            orange: '#F97316',
                            red: '#EF4444',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .mermaid-container {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            overflow: hidden;
        }
        
        .mermaid {
            background: transparent !important;
        }
        
        .mermaid svg {
            max-width: 100%;
            height: auto;
        }
        
        /* Custom Mermaid styling */
        .node rect {
            fill: rgba(255, 107, 157, 0.2) !important;
            stroke: #FF6B9D !important;
            stroke-width: 2px !important;
        }
        
        .node text {
            fill: white !important;
            font-weight: 600 !important;
        }
        
        .edgePath path {
            stroke: #A855F7 !important;
            stroke-width: 2px !important;
        }
        
        .edgeLabel {
            background: rgba(168, 85, 247, 0.2) !important;
            color: white !important;
            border-radius: 8px !important;
            padding: 4px 8px !important;
        }
        
        .zoom-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 10;
        }
        
        .minimap {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 200px;
            height: 150px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            z-index: 10;
        }
        
        .graph-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
        }
    </style>
</head>
<body class="min-h-screen gradient-candy">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="p-4 md:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button class="glass-button p-2 rounded-lg hover:scale-105 transition-transform">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <div>
                        <h1 class="text-xl md:text-2xl font-bold text-white">Knowledge Graph</h1>
                        <p class="text-white/70 text-sm">Machine Learning Concepts</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <button class="glass-button p-2 rounded-lg" title="Fullscreen">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                        </svg>
                    </button>
                    <button class="glass-button p-2 rounded-lg" title="Export">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </button>
                    <button class="glass-button p-2 rounded-lg" title="Settings">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Graph Stats -->
        <div class="px-4 md:px-6 mb-6">
            <div class="glass-card p-4">
                <div class="graph-stats">
                    <div class="text-center">
                        <div class="text-lg font-bold text-candy-pink mb-1">8</div>
                        <div class="text-white/70 text-xs">Concepts</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-bold text-candy-purple mb-1">12</div>
                        <div class="text-white/70 text-xs">Connections</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-bold text-candy-blue mb-1">3</div>
                        <div class="text-white/70 text-xs">Clusters</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-bold text-candy-green mb-1">2</div>
                        <div class="text-white/70 text-xs">Levels</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graph Controls -->
        <div class="px-4 md:px-6 mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex items-center space-x-3">
                    <div class="glass-card p-1 rounded-xl inline-flex">
                        <button class="layout-btn active px-3 py-2 rounded-lg bg-candy-pink text-white text-sm font-semibold transition-all">
                            Flowchart
                        </button>
                        <button class="layout-btn px-3 py-2 rounded-lg text-white/70 hover:text-white hover:bg-white/10 text-sm font-semibold transition-all">
                            Mind Map
                        </button>
                        <button class="layout-btn px-3 py-2 rounded-lg text-white/70 hover:text-white hover:bg-white/10 text-sm font-semibold transition-all">
                            Network
                        </button>
                    </div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <button class="btn-candy-purple py-2 px-4 rounded-lg text-sm font-semibold hover:scale-105 transition-transform">
                        Edit Nodes
                    </button>
                    <button class="btn-candy-blue py-2 px-4 rounded-lg text-sm font-semibold hover:scale-105 transition-transform">
                        Auto Layout
                    </button>
                    <button class="glass-button py-2 px-4 rounded-lg text-sm font-semibold hover:scale-105 transition-transform">
                        Export PNG
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Graph Container -->
        <main class="flex-1 px-4 md:px-6 pb-6">
            <div class="relative h-full min-h-96">
                <div class="mermaid-container h-full p-6 relative">
                    <!-- Zoom Controls -->
                    <div class="zoom-controls flex flex-col space-y-2">
                        <button class="glass-button p-2 rounded-lg hover:scale-105 transition-transform" onclick="zoomIn()">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </button>
                        <button class="glass-button p-2 rounded-lg hover:scale-105 transition-transform" onclick="zoomOut()">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6"></path>
                            </svg>
                        </button>
                        <button class="glass-button p-2 rounded-lg hover:scale-105 transition-transform" onclick="resetZoom()">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Mermaid Diagram -->
                    <div class="mermaid" id="knowledge-graph">
                        graph TD
                            A[Machine Learning] --> B[Supervised Learning]
                            A --> C[Unsupervised Learning]
                            A --> D[Reinforcement Learning]
                            
                            B --> E[Classification]
                            B --> F[Regression]
                            
                            C --> G[Clustering]
                            C --> H[Dimensionality Reduction]
                            
                            E --> I[Decision Trees]
                            E --> J[Neural Networks]
                            F --> K[Linear Regression]
                            F --> L[Polynomial Regression]
                            
                            G --> M[K-Means]
                            H --> N[PCA]
                            
                            style A fill:#FF6B9D,stroke:#FF6B9D,stroke-width:3px,color:#fff
                            style B fill:#A855F7,stroke:#A855F7,stroke-width:2px,color:#fff
                            style C fill:#3B82F6,stroke:#3B82F6,stroke-width:2px,color:#fff
                            style D fill:#06B6D4,stroke:#06B6D4,stroke-width:2px,color:#fff
                    </div>

                    <!-- Minimap -->
                    <div class="minimap glass-morphism">
                        <div class="p-2 text-center">
                            <span class="text-white text-xs font-medium">Overview</span>
                        </div>
                        <div class="flex-1 relative">
                            <div class="absolute inset-2 border border-candy-pink rounded opacity-50"></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Bottom Actions -->
        <div class="px-4 md:px-6 pb-6">
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button class="btn-candy-pink py-3 px-6 rounded-xl font-semibold hover:scale-105 transition-transform">
                    Generate Learning Path
                    <svg class="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                    </svg>
                </button>
                
                <button class="btn-candy-purple py-3 px-6 rounded-xl font-semibold hover:scale-105 transition-transform">
                    Start Quiz
                    <svg class="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </button>
                
                <button class="glass-button py-3 px-6 rounded-xl font-semibold hover:scale-105 transition-transform">
                    Back to Cards
                    <svg class="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0l-4-4m4 4l-4 4"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'dark',
            themeVariables: {
                primaryColor: '#FF6B9D',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#FF6B9D',
                lineColor: '#A855F7',
                secondaryColor: '#A855F7',
                tertiaryColor: '#3B82F6',
                background: 'transparent',
                mainBkg: 'rgba(255, 107, 157, 0.2)',
                secondBkg: 'rgba(168, 85, 247, 0.2)',
                tertiaryBkg: 'rgba(59, 130, 246, 0.2)'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // Zoom functionality
        let currentZoom = 1;
        const zoomStep = 0.2;
        const minZoom = 0.5;
        const maxZoom = 3;

        function zoomIn() {
            if (currentZoom < maxZoom) {
                currentZoom += zoomStep;
                applyZoom();
            }
        }

        function zoomOut() {
            if (currentZoom > minZoom) {
                currentZoom -= zoomStep;
                applyZoom();
            }
        }

        function resetZoom() {
            currentZoom = 1;
            applyZoom();
        }

        function applyZoom() {
            const mermaidElement = document.querySelector('.mermaid svg');
            if (mermaidElement) {
                mermaidElement.style.transform = `scale(${currentZoom})`;
                mermaidElement.style.transformOrigin = 'center center';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Layout switching
            const layoutBtns = document.querySelectorAll('.layout-btn');
            layoutBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    layoutBtns.forEach(b => {
                        b.classList.remove('active', 'bg-candy-pink');
                        b.classList.add('text-white/70');
                    });
                    this.classList.add('active', 'bg-candy-pink');
                    this.classList.remove('text-white/70');
                    this.classList.add('text-white');
                    
                    // Here you would switch the mermaid diagram type
                    console.log('Switching to layout:', this.textContent);
                });
            });

            // Node click handling
            setTimeout(() => {
                const nodes = document.querySelectorAll('.node');
                nodes.forEach(node => {
                    node.addEventListener('click', function() {
                        console.log('Node clicked:', this.textContent);
                        // Show node details or navigate to related content
                    });
                });
            }, 1000);
        });
    </script>
</body>
</html>
