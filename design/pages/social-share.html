<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Share Knowledge Card - LearniScan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../styles/design-system.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        candy: {
                            pink: '#FF6B9D',
                            purple: '#A855F7',
                            blue: '#3B82F6',
                            cyan: '#06B6D4',
                            green: '#10B981',
                            yellow: '#F59E0B',
                            orange: '#F97316',
                            red: '#EF4444',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .share-card-preview {
            background: linear-gradient(135deg, rgba(255, 107, 157, 0.2), rgba(168, 85, 247, 0.2));
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(255, 107, 157, 0.3);
        }

        .social-button {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .social-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .social-button.instagram {
            background: linear-gradient(135deg, #E4405F, #C13584, #833AB4);
        }

        .social-button.twitter {
            background: linear-gradient(135deg, #1DA1F2, #0d8bd9);
        }

        .social-button.linkedin {
            background: linear-gradient(135deg, #0077B5, #005885);
        }

        .social-button.facebook {
            background: linear-gradient(135deg, #1877F2, #166FE5);
        }

        .social-button.whatsapp {
            background: linear-gradient(135deg, #25D366, #128C7E);
        }

        .social-button.telegram {
            background: linear-gradient(135deg, #0088CC, #006699);
        }

        .customization-panel {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .color-picker {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .color-picker:hover {
            transform: scale(1.1);
            border-color: rgba(255, 255, 255, 0.6);
        }

        .color-picker.selected {
            border-color: #FF6B9D;
            box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.3);
        }

        .template-option {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(8px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .template-option:hover {
            border-color: rgba(255, 107, 157, 0.5);
        }

        .template-option.selected {
            border-color: #FF6B9D;
            background: rgba(255, 107, 157, 0.1);
        }

        .copy-success {
            animation: copySuccess 2s ease-in-out;
        }

        @keyframes copySuccess {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); background-color: rgba(16, 185, 129, 0.2); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body class="min-h-screen gradient-candy">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="p-4 md:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button class="glass-button p-2 rounded-lg hover:scale-105 transition-transform">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <div>
                        <h1 class="text-xl md:text-2xl font-bold text-white">Share Knowledge Card</h1>
                        <p class="text-white/70 text-sm">Spread the learning</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <button class="glass-button p-2 rounded-lg" title="Download">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </button>
                    <button class="glass-button p-2 rounded-lg" title="Settings">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 px-4 md:px-6 pb-6">
            <div class="max-w-6xl mx-auto">
                <div class="grid lg:grid-cols-2 gap-8">
                    <!-- Card Preview -->
                    <div class="space-y-6">
                        <h2 class="text-xl font-bold text-white">Preview</h2>

                        <!-- Share Card -->
                        <div class="share-card-preview rounded-2xl p-8 text-center" id="share-card">
                            <div class="mb-6">
                                <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                </div>
                                <span class="text-xs bg-white/20 text-white px-3 py-1 rounded-full">Machine Learning</span>
                            </div>

                            <h3 class="text-2xl font-bold text-white mb-4" id="card-title">
                                What is Supervised Learning?
                            </h3>

                            <p class="text-white/80 text-lg leading-relaxed mb-6" id="card-description">
                                Learning with labeled examples to make predictions on new, unseen data. Essential for classification and regression tasks.
                            </p>

                            <div class="flex items-center justify-center space-x-4 text-white/60 text-sm">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                    <span>LearniScan</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <span>Today</span>
                                </div>
                            </div>
                        </div>

                        <!-- Format Options -->
                        <div class="glass-card rounded-xl p-4">
                            <h3 class="text-lg font-semibold text-white mb-4">Format</h3>
                            <div class="grid grid-cols-3 gap-3">
                                <div class="template-option selected rounded-lg p-3 text-center">
                                    <div class="text-white text-sm font-medium">Square</div>
                                    <div class="text-white/60 text-xs">1080x1080</div>
                                </div>
                                <div class="template-option rounded-lg p-3 text-center">
                                    <div class="text-white text-sm font-medium">Story</div>
                                    <div class="text-white/60 text-xs">1080x1920</div>
                                </div>
                                <div class="template-option rounded-lg p-3 text-center">
                                    <div class="text-white text-sm font-medium">Wide</div>
                                    <div class="text-white/60 text-xs">1200x630</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Customization Panel -->
                    <div class="space-y-6">
                        <h2 class="text-xl font-bold text-white">Customize</h2>

                        <!-- Edit Content -->
                        <div class="customization-panel rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-white mb-4">Edit Content</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="text-white text-sm font-medium mb-2 block">Title</label>
                                    <input type="text" value="What is Supervised Learning?"
                                           class="glass-input w-full" id="title-input">
                                </div>
                                <div>
                                    <label class="text-white text-sm font-medium mb-2 block">Description</label>
                                    <textarea rows="3" class="glass-input w-full resize-none" id="description-input">Learning with labeled examples to make predictions on new, unseen data. Essential for classification and regression tasks.</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Color Theme -->
                        <div class="customization-panel rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-white mb-4">Color Theme</h3>
                            <div class="grid grid-cols-4 gap-3">
                                <div class="color-picker selected" style="background: linear-gradient(135deg, #FF6B9D, #A855F7);" data-theme="pink-purple"></div>
                                <div class="color-picker" style="background: linear-gradient(135deg, #3B82F6, #06B6D4);" data-theme="blue-cyan"></div>
                                <div class="color-picker" style="background: linear-gradient(135deg, #10B981, #F59E0B);" data-theme="green-yellow"></div>
                                <div class="color-picker" style="background: linear-gradient(135deg, #F97316, #EF4444);" data-theme="orange-red"></div>
                            </div>
                        </div>

                        <!-- Social Media Platforms -->
                        <div class="customization-panel rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-white mb-4">Share To</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <button class="social-button instagram rounded-xl p-4 flex items-center space-x-3 hover:scale-105 transition-transform">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                    </svg>
                                    <span class="text-white font-medium">Instagram</span>
                                </button>

                                <button class="social-button twitter rounded-xl p-4 flex items-center space-x-3 hover:scale-105 transition-transform">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                    </svg>
                                    <span class="text-white font-medium">Twitter</span>
                                </button>

                                <button class="social-button linkedin rounded-xl p-4 flex items-center space-x-3 hover:scale-105 transition-transform">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                    <span class="text-white font-medium">LinkedIn</span>
                                </button>

                                <button class="social-button facebook rounded-xl p-4 flex items-center space-x-3 hover:scale-105 transition-transform">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                    </svg>
                                    <span class="text-white font-medium">Facebook</span>
                                </button>
                            </div>
                        </div>

                        <!-- Copy Link -->
                        <div class="customization-panel rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-white mb-4">Share Link</h3>
                            <div class="flex items-center space-x-3">
                                <input type="text" value="https://learniscan.app/card/ml-supervised-learning"
                                       class="glass-input flex-1" readonly id="share-link">
                                <button class="btn-candy-pink py-2 px-4 rounded-lg font-semibold hover:scale-105 transition-transform"
                                        onclick="copyLink()">
                                    Copy
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Content editing
            const titleInput = document.getElementById('title-input');
            const descriptionInput = document.getElementById('description-input');
            const cardTitle = document.getElementById('card-title');
            const cardDescription = document.getElementById('card-description');

            titleInput.addEventListener('input', function() {
                cardTitle.textContent = this.value;
            });

            descriptionInput.addEventListener('input', function() {
                cardDescription.textContent = this.value;
            });

            // Color theme selection
            const colorPickers = document.querySelectorAll('.color-picker');
            const shareCard = document.getElementById('share-card');

            colorPickers.forEach(picker => {
                picker.addEventListener('click', function() {
                    colorPickers.forEach(p => p.classList.remove('selected'));
                    this.classList.add('selected');

                    const theme = this.dataset.theme;
                    updateCardTheme(theme);
                });
            });

            // Template selection
            const templateOptions = document.querySelectorAll('.template-option');
            templateOptions.forEach(option => {
                option.addEventListener('click', function() {
                    templateOptions.forEach(t => t.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });

            // Social media sharing
            const socialButtons = document.querySelectorAll('.social-button');
            socialButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const platform = this.querySelector('span').textContent.toLowerCase();
                    shareToSocial(platform);
                });
            });
        });

        function updateCardTheme(theme) {
            const shareCard = document.getElementById('share-card');

            switch(theme) {
                case 'pink-purple':
                    shareCard.style.background = 'linear-gradient(135deg, rgba(255, 107, 157, 0.2), rgba(168, 85, 247, 0.2))';
                    break;
                case 'blue-cyan':
                    shareCard.style.background = 'linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(6, 182, 212, 0.2))';
                    break;
                case 'green-yellow':
                    shareCard.style.background = 'linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(245, 158, 11, 0.2))';
                    break;
                case 'orange-red':
                    shareCard.style.background = 'linear-gradient(135deg, rgba(249, 115, 22, 0.2), rgba(239, 68, 68, 0.2))';
                    break;
            }
        }

        function shareToSocial(platform) {
            const title = document.getElementById('card-title').textContent;
            const description = document.getElementById('card-description').textContent;
            const url = document.getElementById('share-link').value;

            let shareUrl = '';

            switch(platform) {
                case 'twitter':
                    shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(title + ' - ' + description)}&url=${encodeURIComponent(url)}`;
                    break;
                case 'facebook':
                    shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
                    break;
                case 'linkedin':
                    shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
                    break;
                case 'instagram':
                    // Instagram doesn't support direct URL sharing, so we'll copy to clipboard
                    copyToClipboard(title + '\n\n' + description + '\n\n' + url);
                    showNotification('Content copied! Paste it in Instagram.');
                    return;
            }

            if (shareUrl) {
                window.open(shareUrl, '_blank', 'width=600,height=400');
            }
        }

        function copyLink() {
            const linkInput = document.getElementById('share-link');
            copyToClipboard(linkInput.value);

            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Copied!';
            button.classList.add('copy-success');

            setTimeout(() => {
                button.textContent = originalText;
                button.classList.remove('copy-success');
            }, 2000);
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                console.log('Copied to clipboard');
            }).catch(err => {
                console.error('Failed to copy: ', err);
            });
        }

        function showNotification(message) {
            // Create a simple notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-candy-green text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>
</body>
</html>