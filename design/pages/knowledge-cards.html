<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Cards - LearniScan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../styles/design-system.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        candy: {
                            pink: '#FF6B9D',
                            purple: '#A855F7',
                            blue: '#3B82F6',
                            cyan: '#06B6D4',
                            green: '#10B981',
                            yellow: '#F59E0B',
                            orange: '#F97316',
                            red: '#EF4444',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .card-flip {
            perspective: 1000px;
            height: 200px;
        }
        
        .card-flip-inner {
            position: relative;
            width: 100%;
            height: 100%;
            text-align: center;
            transition: transform 0.6s;
            transform-style: preserve-3d;
        }
        
        .card-flip.flipped .card-flip-inner {
            transform: rotateY(180deg);
        }
        
        .card-front, .card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            border-radius: 16px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .card-back {
            transform: rotateY(180deg);
        }
        
        .masonry-grid {
            column-count: 1;
            column-gap: 1rem;
        }
        
        @media (min-width: 640px) {
            .masonry-grid {
                column-count: 2;
            }
        }
        
        @media (min-width: 1024px) {
            .masonry-grid {
                column-count: 3;
            }
        }
        
        .masonry-item {
            break-inside: avoid;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body class="min-h-screen gradient-candy">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="p-4 md:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button class="glass-button p-2 rounded-lg hover:scale-105 transition-transform">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <div>
                        <h1 class="text-xl md:text-2xl font-bold text-white">Knowledge Cards</h1>
                        <p class="text-white/70 text-sm">Machine Learning Basics</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <button class="glass-button p-2 rounded-lg" title="Search Cards">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                    <button class="glass-button p-2 rounded-lg" title="Add Card">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Stats Overview -->
        <div class="px-4 md:px-6 mb-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="glass-card p-4 text-center">
                    <div class="text-2xl font-bold text-candy-pink mb-1">12</div>
                    <div class="text-white/70 text-sm">Total Cards</div>
                </div>
                <div class="glass-card p-4 text-center">
                    <div class="text-2xl font-bold text-candy-green mb-1">8</div>
                    <div class="text-white/70 text-sm">Mastered</div>
                </div>
                <div class="glass-card p-4 text-center">
                    <div class="text-2xl font-bold text-candy-yellow mb-1">3</div>
                    <div class="text-white/70 text-sm">Learning</div>
                </div>
                <div class="glass-card p-4 text-center">
                    <div class="text-2xl font-bold text-candy-blue mb-1">1</div>
                    <div class="text-white/70 text-sm">New</div>
                </div>
            </div>
        </div>

        <!-- View Toggle -->
        <div class="px-4 md:px-6 mb-6">
            <div class="flex items-center justify-between">
                <div class="glass-card p-1 rounded-xl inline-flex">
                    <button class="view-toggle active px-4 py-2 rounded-lg bg-candy-pink text-white font-semibold transition-all">
                        Grid
                    </button>
                    <button class="view-toggle px-4 py-2 rounded-lg text-white/70 hover:text-white hover:bg-white/10 font-semibold transition-all">
                        Study
                    </button>
                </div>
                
                <div class="flex items-center space-x-3">
                    <button class="btn-candy-purple py-2 px-4 rounded-lg text-sm font-semibold hover:scale-105 transition-transform">
                        View Graph
                    </button>
                    <button class="btn-candy-blue py-2 px-4 rounded-lg text-sm font-semibold hover:scale-105 transition-transform">
                        Roadmap
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1 px-4 md:px-6 pb-6">
            <!-- Grid View -->
            <div id="grid-view" class="view-content">
                <div class="masonry-grid">
                    <!-- Knowledge Card 1 -->
                    <div class="masonry-item">
                        <div class="card-flip" onclick="flipCard(this)">
                            <div class="card-flip-inner">
                                <div class="card-front knowledge-card">
                                    <div class="flex items-start justify-between mb-3">
                                        <span class="text-xs bg-candy-pink/20 text-candy-pink px-2 py-1 rounded-full">AI Basics</span>
                                        <div class="flex items-center space-x-1">
                                            <svg class="w-4 h-4 text-candy-green" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span class="text-xs text-candy-green">Mastered</span>
                                        </div>
                                    </div>
                                    <h3 class="text-lg font-bold text-white mb-2">What is Machine Learning?</h3>
                                    <p class="text-white/70 text-sm mb-4 flex-1">A subset of AI that enables systems to learn and improve from experience without explicit programming.</p>
                                    <div class="flex items-center justify-between text-xs text-white/50">
                                        <span>Created 2 days ago</span>
                                        <span>Click to flip</span>
                                    </div>
                                </div>
                                <div class="card-back glass-card">
                                    <h3 class="text-lg font-bold text-white mb-3">Key Points</h3>
                                    <ul class="text-white/80 text-sm space-y-2 text-left">
                                        <li>• Learns from data patterns</li>
                                        <li>• No explicit programming needed</li>
                                        <li>• Improves with more data</li>
                                        <li>• Used in recommendations, predictions</li>
                                    </ul>
                                    <div class="mt-4 pt-3 border-t border-white/10">
                                        <span class="text-xs text-white/50">Click to flip back</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Knowledge Card 2 -->
                    <div class="masonry-item">
                        <div class="card-flip" onclick="flipCard(this)">
                            <div class="card-flip-inner">
                                <div class="card-front knowledge-card">
                                    <div class="flex items-start justify-between mb-3">
                                        <span class="text-xs bg-candy-purple/20 text-candy-purple px-2 py-1 rounded-full">Learning Types</span>
                                        <div class="flex items-center space-x-1">
                                            <svg class="w-4 h-4 text-candy-yellow" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span class="text-xs text-candy-yellow">Learning</span>
                                        </div>
                                    </div>
                                    <h3 class="text-lg font-bold text-white mb-2">Supervised Learning</h3>
                                    <p class="text-white/70 text-sm mb-4 flex-1">Learning with labeled examples to make predictions on new, unseen data.</p>
                                    <div class="flex items-center justify-between text-xs text-white/50">
                                        <span>Created 1 day ago</span>
                                        <span>Click to flip</span>
                                    </div>
                                </div>
                                <div class="card-back glass-card">
                                    <h3 class="text-lg font-bold text-white mb-3">Examples</h3>
                                    <ul class="text-white/80 text-sm space-y-2 text-left">
                                        <li>• Email spam detection</li>
                                        <li>• Image classification</li>
                                        <li>• Price prediction</li>
                                        <li>• Medical diagnosis</li>
                                    </ul>
                                    <div class="mt-4 pt-3 border-t border-white/10">
                                        <span class="text-xs text-white/50">Click to flip back</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Add New Card -->
                    <div class="masonry-item">
                        <div class="glass-card p-6 text-center cursor-pointer hover:scale-105 transition-transform border-2 border-dashed border-white/30 hover:border-candy-pink">
                            <div class="w-12 h-12 bg-candy-pink/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-6 h-6 text-candy-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-white mb-2">Create New Card</h3>
                            <p class="text-white/70 text-sm">Add a new knowledge card to your collection</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Study View -->
            <div id="study-view" class="view-content hidden">
                <div class="max-w-2xl mx-auto">
                    <div class="glass-card rounded-2xl p-8 text-center mb-6">
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-4">
                                <span class="text-white/70 text-sm">Card 1 of 12</span>
                                <span class="text-xs bg-candy-blue/20 text-candy-blue px-2 py-1 rounded-full">Study Mode</span>
                            </div>
                            <div class="w-full bg-white/10 rounded-full h-2 mb-6">
                                <div class="bg-gradient-to-r from-candy-pink to-candy-purple h-2 rounded-full" style="width: 8.33%"></div>
                            </div>
                        </div>
                        
                        <h2 class="text-2xl font-bold text-white mb-4">What is Machine Learning?</h2>
                        <p class="text-white/80 text-lg mb-8">A subset of AI that enables systems to learn and improve from experience without explicit programming.</p>
                        
                        <div class="flex justify-center space-x-4">
                            <button class="btn-candy-red py-3 px-6 rounded-xl font-semibold">
                                Don't Know
                            </button>
                            <button class="btn-candy-yellow py-3 px-6 rounded-xl font-semibold">
                                Partially
                            </button>
                            <button class="btn-candy-green py-3 px-6 rounded-xl font-semibold">
                                Know Well
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function flipCard(card) {
            card.classList.toggle('flipped');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // View toggle functionality
            const toggles = document.querySelectorAll('.view-toggle');
            const views = document.querySelectorAll('.view-content');
            
            toggles.forEach((toggle, index) => {
                toggle.addEventListener('click', function() {
                    // Remove active class from all toggles
                    toggles.forEach(t => {
                        t.classList.remove('active', 'bg-candy-pink');
                        t.classList.add('text-white/70');
                    });
                    
                    // Add active class to clicked toggle
                    this.classList.add('active', 'bg-candy-pink');
                    this.classList.remove('text-white/70');
                    this.classList.add('text-white');
                    
                    // Hide all views
                    views.forEach(view => view.classList.add('hidden'));
                    
                    // Show corresponding view
                    views[index].classList.remove('hidden');
                });
            });

            // Study mode navigation
            const studyButtons = document.querySelectorAll('#study-view button');
            studyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Simulate moving to next card
                    console.log('Study response:', this.textContent);
                    // Here you would implement the spaced repetition logic
                });
            });
        });
    </script>
</body>
</html>
