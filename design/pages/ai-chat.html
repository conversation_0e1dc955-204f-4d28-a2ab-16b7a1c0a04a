<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Tutor Chat - LearniScan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../styles/design-system.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        candy: {
                            pink: '#FF6B9D',
                            purple: '#A855F7',
                            blue: '#3B82F6',
                            cyan: '#06B6D4',
                            green: '#10B981',
                            yellow: '#F59E0B',
                            orange: '#F97316',
                            red: '#EF4444',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .chat-container {
            height: calc(100vh - 200px);
            min-height: 400px;
        }
        
        .message-bubble {
            max-width: 80%;
            word-wrap: break-word;
            animation: slideIn 0.3s ease-out;
        }
        
        .user-message {
            background: linear-gradient(135deg, rgba(255, 107, 157, 0.2), rgba(255, 107, 157, 0.1));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 107, 157, 0.3);
            margin-left: auto;
        }
        
        .ai-message {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .typing-indicator {
            display: flex;
            align-items: center;
            space-x: 1;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        .voice-recording {
            animation: pulse 1.5s infinite;
        }
        
        .suggestion-chip {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.2s ease;
        }
        
        .suggestion-chip:hover {
            background: rgba(255, 107, 157, 0.2);
            border-color: rgba(255, 107, 157, 0.4);
            transform: translateY(-2px);
        }
        
        .chat-input-container {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="min-h-screen gradient-candy">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="p-4 md:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button class="glass-button p-2 rounded-lg hover:scale-105 transition-transform">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-candy-purple to-candy-pink rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-white">AI Tutor</h1>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-candy-green rounded-full animate-pulse"></div>
                                <span class="text-white/70 text-sm">Online</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <button class="glass-button p-2 rounded-lg" title="Voice Settings">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path>
                        </svg>
                    </button>
                    <button class="glass-button p-2 rounded-lg" title="Clear Chat">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Context Info -->
        <div class="px-4 md:px-6 mb-4">
            <div class="glass-card p-4 rounded-xl">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-candy-blue/20 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-candy-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <span class="text-white font-medium text-sm">Discussing: Machine Learning Basics</span>
                            <p class="text-white/60 text-xs">12 knowledge cards • 8 concepts</p>
                        </div>
                    </div>
                    <button class="text-candy-pink hover:text-candy-pink/80 text-sm font-medium">
                        Change Topic
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Messages -->
        <main class="flex-1 px-4 md:px-6">
            <div class="chat-container overflow-y-auto space-y-4 pb-4">
                <!-- Welcome Message -->
                <div class="flex justify-start">
                    <div class="message-bubble ai-message rounded-2xl p-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-r from-candy-purple to-candy-pink rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-white text-sm leading-relaxed">
                                    Hi! I'm your AI tutor. I've analyzed your Machine Learning document and I'm ready to help you understand the concepts better. What would you like to explore first?
                                </p>
                                <span class="text-white/50 text-xs mt-2 block">Just now</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Message -->
                <div class="flex justify-end">
                    <div class="message-bubble user-message rounded-2xl p-4">
                        <p class="text-white text-sm leading-relaxed">
                            Can you explain the difference between supervised and unsupervised learning?
                        </p>
                        <span class="text-white/50 text-xs mt-2 block text-right">2 min ago</span>
                    </div>
                </div>

                <!-- AI Response -->
                <div class="flex justify-start">
                    <div class="message-bubble ai-message rounded-2xl p-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-r from-candy-purple to-candy-pink rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-white text-sm leading-relaxed mb-3">
                                    Great question! Let me break this down for you:
                                </p>
                                <div class="space-y-3">
                                    <div class="bg-candy-pink/10 rounded-lg p-3">
                                        <h4 class="text-candy-pink font-semibold text-sm mb-1">Supervised Learning</h4>
                                        <p class="text-white/80 text-sm">Uses labeled data to learn patterns. Like showing a child pictures of cats labeled "cat" to teach recognition.</p>
                                    </div>
                                    <div class="bg-candy-blue/10 rounded-lg p-3">
                                        <h4 class="text-candy-blue font-semibold text-sm mb-1">Unsupervised Learning</h4>
                                        <p class="text-white/80 text-sm">Finds hidden patterns in unlabeled data. Like giving a child mixed animal pictures and letting them group similar ones.</p>
                                    </div>
                                </div>
                                <span class="text-white/50 text-xs mt-3 block">1 min ago</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Typing Indicator -->
                <div class="flex justify-start" id="typing-indicator" style="display: none;">
                    <div class="message-bubble ai-message rounded-2xl p-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-r from-candy-purple to-candy-pink rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <div class="typing-indicator">
                                <span class="text-white/70 text-sm mr-2">AI is typing</span>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Quick Suggestions -->
        <div class="px-4 md:px-6 mb-4">
            <div class="flex flex-wrap gap-2">
                <button class="suggestion-chip px-3 py-2 rounded-full text-white text-sm hover:scale-105 transition-transform">
                    Give me examples
                </button>
                <button class="suggestion-chip px-3 py-2 rounded-full text-white text-sm hover:scale-105 transition-transform">
                    Create a quiz
                </button>
                <button class="suggestion-chip px-3 py-2 rounded-full text-white text-sm hover:scale-105 transition-transform">
                    Explain neural networks
                </button>
                <button class="suggestion-chip px-3 py-2 rounded-full text-white text-sm hover:scale-105 transition-transform">
                    Show learning path
                </button>
            </div>
        </div>

        <!-- Chat Input -->
        <div class="px-4 md:px-6 pb-6">
            <div class="chat-input-container rounded-2xl p-4">
                <div class="flex items-end space-x-3">
                    <div class="flex-1">
                        <textarea 
                            id="chat-input" 
                            placeholder="Ask me anything about machine learning..." 
                            rows="1"
                            class="w-full bg-transparent text-white placeholder-white/50 resize-none focus:outline-none text-sm leading-relaxed"
                        ></textarea>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <button id="voice-btn" class="glass-button p-3 rounded-full hover:scale-105 transition-transform" title="Voice Input">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                            </svg>
                        </button>
                        
                        <button id="send-btn" class="btn-candy-pink p-3 rounded-full hover:scale-105 transition-transform" title="Send Message">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chat-input');
            const sendBtn = document.getElementById('send-btn');
            const voiceBtn = document.getElementById('voice-btn');
            const typingIndicator = document.getElementById('typing-indicator');
            const chatContainer = document.querySelector('.chat-container');
            
            let isRecording = false;

            // Auto-resize textarea
            chatInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // Send message
            function sendMessage() {
                const message = chatInput.value.trim();
                if (message) {
                    addUserMessage(message);
                    chatInput.value = '';
                    chatInput.style.height = 'auto';
                    
                    // Show typing indicator
                    showTypingIndicator();
                    
                    // Simulate AI response
                    setTimeout(() => {
                        hideTypingIndicator();
                        addAIMessage("I understand your question. Let me help you with that...");
                    }, 2000);
                }
            }

            sendBtn.addEventListener('click', sendMessage);
            
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Voice recording
            voiceBtn.addEventListener('click', function() {
                if (!isRecording) {
                    startRecording();
                } else {
                    stopRecording();
                }
            });

            function startRecording() {
                isRecording = true;
                voiceBtn.classList.add('voice-recording', 'bg-candy-red');
                voiceBtn.innerHTML = '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a2 2 0 114 0v4a2 2 0 11-4 0V7z" clip-rule="evenodd"></path></svg>';
            }

            function stopRecording() {
                isRecording = false;
                voiceBtn.classList.remove('voice-recording', 'bg-candy-red');
                voiceBtn.innerHTML = '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path></svg>';
                
                // Simulate voice-to-text
                chatInput.value = "What are some real-world applications of machine learning?";
                chatInput.focus();
            }

            // Suggestion chips
            document.querySelectorAll('.suggestion-chip').forEach(chip => {
                chip.addEventListener('click', function() {
                    chatInput.value = this.textContent;
                    chatInput.focus();
                });
            });

            function addUserMessage(message) {
                const messageHTML = `
                    <div class="flex justify-end">
                        <div class="message-bubble user-message rounded-2xl p-4">
                            <p class="text-white text-sm leading-relaxed">${message}</p>
                            <span class="text-white/50 text-xs mt-2 block text-right">Just now</span>
                        </div>
                    </div>
                `;
                chatContainer.insertAdjacentHTML('beforeend', messageHTML);
                scrollToBottom();
            }

            function addAIMessage(message) {
                const messageHTML = `
                    <div class="flex justify-start">
                        <div class="message-bubble ai-message rounded-2xl p-4">
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 bg-gradient-to-r from-candy-purple to-candy-pink rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-white text-sm leading-relaxed">${message}</p>
                                    <span class="text-white/50 text-xs mt-2 block">Just now</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                chatContainer.insertAdjacentHTML('beforeend', messageHTML);
                scrollToBottom();
            }

            function showTypingIndicator() {
                typingIndicator.style.display = 'flex';
                scrollToBottom();
            }

            function hideTypingIndicator() {
                typingIndicator.style.display = 'none';
            }

            function scrollToBottom() {
                setTimeout(() => {
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }, 100);
            }
        });
    </script>
</body>
</html>
