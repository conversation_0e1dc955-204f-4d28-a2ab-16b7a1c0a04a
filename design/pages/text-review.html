<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Review - LearniScan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../styles/design-system.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        candy: {
                            pink: '#FF6B9D',
                            purple: '#A855F7',
                            blue: '#3B82F6',
                            cyan: '#06B6D4',
                            green: '#10B981',
                            yellow: '#F59E0B',
                            orange: '#F97316',
                            red: '#EF4444',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .text-content {
            line-height: 1.7;
            font-size: 16px;
        }
        
        .highlight-word {
            background: linear-gradient(120deg, rgba(255, 107, 157, 0.3) 0%, rgba(255, 107, 157, 0.3) 100%);
            padding: 2px 4px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .highlight-word:hover {
            background: linear-gradient(120deg, rgba(255, 107, 157, 0.5) 0%, rgba(255, 107, 157, 0.5) 100%);
        }
        
        .translation-toggle {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .translation-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            transition: .4s;
            border-radius: 34px;
            backdrop-filter: blur(10px);
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background: #FF6B9D;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .confidence-bar {
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #EF4444, #F59E0B, #10B981);
            border-radius: 2px;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body class="min-h-screen gradient-candy">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="p-4 md:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button class="glass-button p-2 rounded-lg hover:scale-105 transition-transform">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <h1 class="text-xl md:text-2xl font-bold text-white">Extracted Text</h1>
                </div>
                
                <div class="flex items-center space-x-2">
                    <button class="glass-button p-2 rounded-lg" title="Edit Text">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                    </button>
                    <button class="glass-button p-2 rounded-lg" title="Share">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- OCR Confidence Indicator -->
        <div class="px-4 md:px-6 mb-4">
            <div class="glass-card rounded-xl p-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-white text-sm font-medium">OCR Confidence</span>
                    <span class="text-candy-green text-sm font-semibold">94%</span>
                </div>
                <div class="confidence-bar">
                    <div class="confidence-fill" style="width: 94%"></div>
                </div>
            </div>
        </div>

        <!-- Translation Controls -->
        <div class="px-4 md:px-6 mb-6">
            <div class="glass-card rounded-xl p-4">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                    <div class="flex items-center space-x-4">
                        <label class="text-white text-sm font-medium">Translate to:</label>
                        <select class="glass-input text-sm py-2 px-3 rounded-lg min-w-32">
                            <option value="">Select Language</option>
                            <option value="es">Spanish</option>
                            <option value="fr">French</option>
                            <option value="de">German</option>
                            <option value="it">Italian</option>
                            <option value="pt">Portuguese</option>
                            <option value="zh">Chinese</option>
                            <option value="ja">Japanese</option>
                            <option value="ko">Korean</option>
                        </select>
                        <button class="btn-candy-purple py-2 px-4 rounded-lg text-sm font-semibold">
                            Translate
                        </button>
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        <span class="text-white text-sm">Original</span>
                        <label class="translation-toggle">
                            <input type="checkbox" id="translation-toggle">
                            <span class="slider"></span>
                        </label>
                        <span class="text-white text-sm">Translation</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1 px-4 md:px-6 pb-6">
            <div class="max-w-4xl mx-auto">
                <!-- Original Text -->
                <div id="original-text" class="glass-card rounded-xl p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-white">Original Text</h2>
                        <div class="flex items-center space-x-2">
                            <button class="glass-button p-2 rounded-lg text-xs" title="Read Aloud">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path>
                                </svg>
                            </button>
                            <button class="glass-button p-2 rounded-lg text-xs" title="Copy Text">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <div class="text-content text-white/90 leading-relaxed">
                        <p class="mb-4">
                            <span class="highlight-word">Machine learning</span> is a subset of artificial intelligence (AI) that provides systems the ability to automatically learn and improve from experience without being explicitly programmed. Machine learning focuses on the development of computer programs that can access data and use it to learn for themselves.
                        </p>
                        
                        <p class="mb-4">
                            The process of learning begins with observations or data, such as examples, direct experience, or instruction, in order to look for patterns in data and make better decisions in the future based on the examples that we provide. The primary aim is to allow the computers to learn automatically without human intervention or assistance and adjust actions accordingly.
                        </p>
                        
                        <p>
                            Some <span class="highlight-word">machine learning</span> methods include <span class="highlight-word">supervised learning</span>, <span class="highlight-word">unsupervised learning</span>, and <span class="highlight-word">reinforcement learning</span>. Each method has its own approach to analyzing data and making predictions or decisions.
                        </p>
                    </div>
                </div>

                <!-- Translated Text (Hidden by default) -->
                <div id="translated-text" class="glass-card rounded-xl p-6 mb-6 hidden">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-white">Spanish Translation</h2>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs bg-candy-green/20 text-candy-green px-2 py-1 rounded-full">Translated</span>
                            <button class="glass-button p-2 rounded-lg text-xs" title="Read Aloud">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path>
                                </svg>
                            </button>
                            <button class="glass-button p-2 rounded-lg text-xs" title="Copy Text">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <div class="text-content text-white/90 leading-relaxed">
                        <p class="mb-4">
                            El <span class="highlight-word">aprendizaje automático</span> es un subconjunto de la inteligencia artificial (IA) que proporciona a los sistemas la capacidad de aprender automáticamente y mejorar a partir de la experiencia sin ser programados explícitamente.
                        </p>
                        
                        <p class="mb-4">
                            El proceso de aprendizaje comienza con observaciones o datos, como ejemplos, experiencia directa o instrucción, para buscar patrones en los datos y tomar mejores decisiones en el futuro basándose en los ejemplos que proporcionamos.
                        </p>
                        
                        <p>
                            Algunos métodos de <span class="highlight-word">aprendizaje automático</span> incluyen <span class="highlight-word">aprendizaje supervisado</span>, <span class="highlight-word">aprendizaje no supervisado</span> y <span class="highlight-word">aprendizaje por refuerzo</span>.
                        </p>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="btn-candy-pink py-3 px-6 rounded-xl font-semibold hover:scale-105 transition-transform">
                        Generate Knowledge Cards
                        <svg class="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </button>
                    
                    <button class="btn-candy-blue py-3 px-6 rounded-xl font-semibold hover:scale-105 transition-transform">
                        Create Learning Path
                        <svg class="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                        </svg>
                    </button>
                    
                    <button class="glass-button py-3 px-6 rounded-xl font-semibold hover:scale-105 transition-transform">
                        Start AI Chat
                        <svg class="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Translation toggle functionality
            const toggle = document.getElementById('translation-toggle');
            const originalText = document.getElementById('original-text');
            const translatedText = document.getElementById('translated-text');
            
            toggle.addEventListener('change', function() {
                if (this.checked) {
                    originalText.classList.add('hidden');
                    translatedText.classList.remove('hidden');
                } else {
                    originalText.classList.remove('hidden');
                    translatedText.classList.add('hidden');
                }
            });

            // Translate button functionality
            document.querySelector('.btn-candy-purple').addEventListener('click', function() {
                const select = document.querySelector('select');
                if (select.value) {
                    // Simulate translation process
                    this.innerHTML = '<svg class="w-4 h-4 animate-spin inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Translating...';
                    
                    setTimeout(() => {
                        this.innerHTML = 'Translate';
                        toggle.checked = true;
                        toggle.dispatchEvent(new Event('change'));
                    }, 2000);
                }
            });

            // Highlight word interactions
            document.querySelectorAll('.highlight-word').forEach(word => {
                word.addEventListener('click', function() {
                    // Show definition or more info
                    console.log('Show definition for:', this.textContent);
                });
            });

            // Copy text functionality
            document.querySelectorAll('[title="Copy Text"]').forEach(button => {
                button.addEventListener('click', function() {
                    const textContent = this.closest('.glass-card').querySelector('.text-content').textContent;
                    navigator.clipboard.writeText(textContent).then(() => {
                        // Show success feedback
                        this.innerHTML = '<svg class="w-4 h-4 text-candy-green" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
                        setTimeout(() => {
                            this.innerHTML = '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg>';
                        }, 1000);
                    });
                });
            });

            // Text-to-speech simulation
            document.querySelectorAll('[title="Read Aloud"]').forEach(button => {
                button.addEventListener('click', function() {
                    this.classList.add('animate-pulse');
                    setTimeout(() => {
                        this.classList.remove('animate-pulse');
                    }, 3000);
                });
            });
        });
    </script>
</body>
</html>
