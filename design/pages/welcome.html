<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to LearniScan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../styles/design-system.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        candy: {
                            pink: '#FF6B9D',
                            purple: '#A855F7',
                            blue: '#3B82F6',
                            cyan: '#06B6D4',
                            green: '#10B981',
                            yellow: '#F59E0B',
                            orange: '#F97316',
                            red: '#EF4444',
                        }
                    },
                    animation: {
                        'bounce-slow': 'bounce 3s infinite',
                        'pulse-slow': 'pulse 4s infinite',
                        'spin-slow': 'spin 8s linear infinite',
                    }
                }
            }
        }
    </script>
    <style>
        .hero-gradient {
            background: linear-gradient(135deg, 
                rgba(255, 107, 157, 0.9) 0%, 
                rgba(168, 85, 247, 0.8) 50%, 
                rgba(59, 130, 246, 0.9) 100%);
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }
        
        .shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .shape-1 {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape-2 {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 15%;
            animation: float 8s ease-in-out infinite reverse;
        }
        
        .shape-3 {
            width: 60px;
            height: 60px;
            bottom: 30%;
            left: 20%;
            animation: float 7s ease-in-out infinite;
        }
        
        .shape-4 {
            width: 100px;
            height: 100px;
            top: 10%;
            right: 30%;
            animation: float 9s ease-in-out infinite reverse;
        }
    </style>
</head>
<body class="min-h-screen hero-gradient relative">
    <!-- Floating Background Shapes -->
    <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
    </div>

    <!-- Main Content -->
    <div class="relative z-10 min-h-screen flex flex-col">
        <!-- Header -->
        <header class="p-6">
            <div class="flex justify-between items-center">
                <div class="glass-morphism rounded-lg px-4 py-2">
                    <span class="text-white font-semibold text-lg">LearniScan</span>
                </div>
                <button class="glass-button text-sm px-4 py-2 rounded-lg">
                    Skip Tour
                </button>
            </div>
        </header>

        <!-- Hero Section -->
        <main class="flex-1 flex items-center justify-center px-6">
            <div class="max-w-4xl mx-auto text-center">
                <!-- Logo/Icon -->
                <div class="mb-8">
                    <div class="w-32 h-32 mx-auto glass-card rounded-full flex items-center justify-center animate-pulse-slow">
                        <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                </div>

                <!-- Main Heading -->
                <h1 class="text-5xl md:text-7xl font-bold text-white mb-6 text-shadow animate-float">
                    Welcome to
                    <span class="bg-gradient-to-r from-candy-pink via-candy-purple to-candy-blue bg-clip-text text-transparent">
                        LearniScan
                    </span>
                </h1>

                <!-- Tagline -->
                <p class="text-xl md:text-2xl text-white/90 mb-4 font-medium">
                    Scan. Learn. Share.
                </p>
                
                <p class="text-lg text-white/70 mb-12 max-w-2xl mx-auto leading-relaxed">
                    Transform any document into an interactive learning experience with AI-powered knowledge cards, 
                    translations, and personalized study roadmaps.
                </p>

                <!-- Feature Highlights -->
                <div class="grid md:grid-cols-3 gap-6 mb-12">
                    <div class="glass-card p-6 text-center hover:scale-105 transition-transform">
                        <div class="w-12 h-12 bg-candy-pink/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-candy-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-2">Smart Scanning</h3>
                        <p class="text-white/70 text-sm">Instantly digitize any document with advanced OCR technology</p>
                    </div>

                    <div class="glass-card p-6 text-center hover:scale-105 transition-transform">
                        <div class="w-12 h-12 bg-candy-purple/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-candy-purple" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-2">AI Learning</h3>
                        <p class="text-white/70 text-sm">Generate knowledge cards and personalized study paths</p>
                    </div>

                    <div class="glass-card p-6 text-center hover:scale-105 transition-transform">
                        <div class="w-12 h-12 bg-candy-blue/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-candy-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-2">Social Learning</h3>
                        <p class="text-white/70 text-sm">Share knowledge cards and join study workshops</p>
                    </div>
                </div>

                <!-- Call to Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <button class="btn-candy-pink py-4 px-8 rounded-xl font-semibold text-lg hover:scale-105 transition-transform animate-glow">
                        Get Started
                        <svg class="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </button>
                    
                    <button class="glass-button py-4 px-8 rounded-xl font-semibold text-lg hover:scale-105 transition-transform">
                        Watch Demo
                        <svg class="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1"></path>
                        </svg>
                    </button>
                </div>

                <!-- Trust Indicators -->
                <div class="mt-12 pt-8 border-t border-white/10">
                    <p class="text-white/50 text-sm mb-4">Trusted by students and professionals worldwide</p>
                    <div class="flex justify-center items-center space-x-8 opacity-60">
                        <div class="glass-morphism rounded-lg px-4 py-2">
                            <span class="text-white text-sm font-medium">10K+ Users</span>
                        </div>
                        <div class="glass-morphism rounded-lg px-4 py-2">
                            <span class="text-white text-sm font-medium">50+ Languages</span>
                        </div>
                        <div class="glass-morphism rounded-lg px-4 py-2">
                            <span class="text-white text-sm font-medium">AI Powered</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="p-6 text-center">
            <div class="glass-morphism rounded-lg inline-block px-6 py-3">
                <p class="text-white/70 text-sm">
                    Ready to transform your learning experience?
                </p>
            </div>
        </footer>
    </div>

    <script>
        // Add some interactive animations
        document.addEventListener('DOMContentLoaded', function() {
            // Animate elements on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all cards
            document.querySelectorAll('.glass-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });

            // Add click handlers for buttons
            document.querySelector('.btn-candy-pink').addEventListener('click', function() {
                // Navigate to onboarding or main app
                console.log('Starting onboarding...');
            });

            document.querySelector('.glass-button').addEventListener('click', function() {
                // Show demo video or tour
                console.log('Starting demo...');
            });
        });
    </script>
</body>
</html>
