<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz - LearniScan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../styles/design-system.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        candy: {
                            pink: '#FF6B9D',
                            purple: '#A855F7',
                            blue: '#3B82F6',
                            cyan: '#06B6D4',
                            green: '#10B981',
                            yellow: '#F59E0B',
                            orange: '#F97316',
                            red: '#EF4444',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .quiz-option {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .quiz-option:hover {
            border-color: rgba(255, 107, 157, 0.5);
            background: rgba(255, 107, 157, 0.1);
            transform: translateY(-2px);
        }
        
        .quiz-option.selected {
            border-color: #FF6B9D;
            background: rgba(255, 107, 157, 0.2);
        }
        
        .quiz-option.correct {
            border-color: #10B981;
            background: rgba(16, 185, 129, 0.2);
        }
        
        .quiz-option.incorrect {
            border-color: #EF4444;
            background: rgba(239, 68, 68, 0.2);
        }
        
        .progress-circle {
            transform: rotate(-90deg);
        }
        
        .progress-circle-bg {
            stroke: rgba(255, 255, 255, 0.1);
        }
        
        .progress-circle-fill {
            stroke: #FF6B9D;
            stroke-linecap: round;
            transition: stroke-dashoffset 0.5s ease;
        }
        
        .timer-circle {
            animation: countdown 30s linear infinite;
        }
        
        @keyframes countdown {
            from {
                stroke-dashoffset: 0;
            }
            to {
                stroke-dashoffset: 283;
            }
        }
        
        .workshop-participant {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .leaderboard-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .rank-badge {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #000;
            font-weight: bold;
        }
        
        .rank-badge.silver {
            background: linear-gradient(135deg, #C0C0C0, #A0A0A0);
        }
        
        .rank-badge.bronze {
            background: linear-gradient(135deg, #CD7F32, #B8860B);
        }
    </style>
</head>
<body class="min-h-screen gradient-candy">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="p-4 md:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button class="glass-button p-2 rounded-lg hover:scale-105 transition-transform">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <div>
                        <h1 class="text-xl md:text-2xl font-bold text-white">Machine Learning Quiz</h1>
                        <p class="text-white/70 text-sm">Test your knowledge</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- Timer -->
                    <div class="relative w-12 h-12">
                        <svg class="w-12 h-12 progress-circle" viewBox="0 0 100 100">
                            <circle cx="50" cy="50" r="45" class="progress-circle-bg" stroke-width="8" fill="none"/>
                            <circle cx="50" cy="50" r="45" class="timer-circle" stroke="#F59E0B" stroke-width="8" fill="none" 
                                    stroke-dasharray="283" stroke-dashoffset="0"/>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-white text-xs font-bold" id="timer">30</span>
                        </div>
                    </div>
                    
                    <button class="glass-button p-2 rounded-lg" title="Pause Quiz">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Progress Bar -->
        <div class="px-4 md:px-6 mb-6">
            <div class="glass-card p-4 rounded-xl">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-white font-medium">Question 3 of 10</span>
                    <span class="text-candy-green font-semibold">Score: 180</span>
                </div>
                <div class="w-full bg-white/10 rounded-full h-3">
                    <div class="bg-gradient-to-r from-candy-pink to-candy-purple h-3 rounded-full transition-all duration-500" style="width: 30%"></div>
                </div>
            </div>
        </div>

        <!-- Quiz Mode Toggle -->
        <div class="px-4 md:px-6 mb-6">
            <div class="glass-card p-1 rounded-xl inline-flex">
                <button class="mode-toggle active px-4 py-2 rounded-lg bg-candy-pink text-white font-semibold transition-all">
                    Solo Quiz
                </button>
                <button class="mode-toggle px-4 py-2 rounded-lg text-white/70 hover:text-white hover:bg-white/10 font-semibold transition-all">
                    Workshop Mode
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1 px-4 md:px-6 pb-6">
            <!-- Solo Quiz View -->
            <div id="solo-quiz" class="quiz-mode-content">
                <div class="max-w-3xl mx-auto">
                    <!-- Question Card -->
                    <div class="glass-card rounded-2xl p-8 mb-8">
                        <div class="text-center mb-8">
                            <div class="w-16 h-16 bg-candy-blue/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-candy-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h2 class="text-2xl font-bold text-white mb-4">
                                Which of the following is NOT a type of machine learning?
                            </h2>
                            <p class="text-white/70">Select the best answer</p>
                        </div>

                        <!-- Answer Options -->
                        <div class="space-y-4">
                            <div class="quiz-option rounded-xl p-4" onclick="selectOption(this, 'A')">
                                <div class="flex items-center space-x-4">
                                    <div class="w-8 h-8 bg-candy-pink/20 rounded-full flex items-center justify-center">
                                        <span class="text-candy-pink font-bold">A</span>
                                    </div>
                                    <span class="text-white font-medium">Supervised Learning</span>
                                </div>
                            </div>
                            
                            <div class="quiz-option rounded-xl p-4" onclick="selectOption(this, 'B')">
                                <div class="flex items-center space-x-4">
                                    <div class="w-8 h-8 bg-candy-purple/20 rounded-full flex items-center justify-center">
                                        <span class="text-candy-purple font-bold">B</span>
                                    </div>
                                    <span class="text-white font-medium">Unsupervised Learning</span>
                                </div>
                            </div>
                            
                            <div class="quiz-option rounded-xl p-4" onclick="selectOption(this, 'C')">
                                <div class="flex items-center space-x-4">
                                    <div class="w-8 h-8 bg-candy-blue/20 rounded-full flex items-center justify-center">
                                        <span class="text-candy-blue font-bold">C</span>
                                    </div>
                                    <span class="text-white font-medium">Reinforcement Learning</span>
                                </div>
                            </div>
                            
                            <div class="quiz-option rounded-xl p-4" onclick="selectOption(this, 'D')">
                                <div class="flex items-center space-x-4">
                                    <div class="w-8 h-8 bg-candy-green/20 rounded-full flex items-center justify-center">
                                        <span class="text-candy-green font-bold">D</span>
                                    </div>
                                    <span class="text-white font-medium">Deterministic Learning</span>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="text-center mt-8">
                            <button id="submit-btn" class="btn-candy-pink py-3 px-8 rounded-xl font-semibold hover:scale-105 transition-transform" disabled>
                                Submit Answer
                            </button>
                        </div>
                    </div>

                    <!-- Explanation (Hidden initially) -->
                    <div id="explanation" class="glass-card rounded-2xl p-6 hidden">
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-candy-green/20 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-candy-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-white mb-2">Correct! Well done!</h3>
                                <p class="text-white/80 text-sm leading-relaxed">
                                    "Deterministic Learning" is not a recognized type of machine learning. The three main types are Supervised, Unsupervised, and Reinforcement Learning, each with distinct approaches to learning from data.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Workshop Mode View -->
            <div id="workshop-mode" class="quiz-mode-content hidden">
                <div class="max-w-6xl mx-auto">
                    <div class="grid lg:grid-cols-3 gap-6">
                        <!-- Main Quiz Area -->
                        <div class="lg:col-span-2">
                            <div class="glass-card rounded-2xl p-6 mb-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-xl font-bold text-white">Workshop: ML Fundamentals</h3>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-3 h-3 bg-candy-green rounded-full animate-pulse"></div>
                                        <span class="text-white/70 text-sm">Live</span>
                                    </div>
                                </div>
                                
                                <div class="text-center py-8">
                                    <h2 class="text-xl font-bold text-white mb-4">
                                        What is the primary goal of supervised learning?
                                    </h2>
                                    
                                    <div class="space-y-3 mt-6">
                                        <div class="quiz-option rounded-lg p-3">
                                            <span class="text-white">A) Find hidden patterns in data</span>
                                        </div>
                                        <div class="quiz-option rounded-lg p-3">
                                            <span class="text-white">B) Learn from labeled examples to make predictions</span>
                                        </div>
                                        <div class="quiz-option rounded-lg p-3">
                                            <span class="text-white">C) Maximize rewards through trial and error</span>
                                        </div>
                                        <div class="quiz-option rounded-lg p-3">
                                            <span class="text-white">D) Reduce data dimensionality</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Participants & Leaderboard -->
                        <div class="space-y-6">
                            <!-- Participants -->
                            <div class="glass-card rounded-xl p-4">
                                <h4 class="text-lg font-bold text-white mb-4">Participants (8)</h4>
                                <div class="space-y-3">
                                    <div class="workshop-participant rounded-lg p-3 flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-candy-pink rounded-full flex items-center justify-center">
                                                <span class="text-white text-sm font-bold">A</span>
                                            </div>
                                            <span class="text-white text-sm">Alex Chen</span>
                                        </div>
                                        <div class="w-2 h-2 bg-candy-green rounded-full"></div>
                                    </div>
                                    
                                    <div class="workshop-participant rounded-lg p-3 flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-candy-purple rounded-full flex items-center justify-center">
                                                <span class="text-white text-sm font-bold">S</span>
                                            </div>
                                            <span class="text-white text-sm">Sarah Kim</span>
                                        </div>
                                        <div class="w-2 h-2 bg-candy-yellow rounded-full"></div>
                                    </div>
                                    
                                    <div class="workshop-participant rounded-lg p-3 flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-candy-blue rounded-full flex items-center justify-center">
                                                <span class="text-white text-sm font-bold">M</span>
                                            </div>
                                            <span class="text-white text-sm">Mike Johnson</span>
                                        </div>
                                        <div class="w-2 h-2 bg-candy-red rounded-full"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Live Leaderboard -->
                            <div class="glass-card rounded-xl p-4">
                                <h4 class="text-lg font-bold text-white mb-4">Leaderboard</h4>
                                <div class="space-y-3">
                                    <div class="leaderboard-item rounded-lg p-3 flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="rank-badge w-6 h-6 rounded-full flex items-center justify-center text-xs">1</div>
                                            <span class="text-white text-sm font-medium">Alex Chen</span>
                                        </div>
                                        <span class="text-candy-green font-bold">240</span>
                                    </div>
                                    
                                    <div class="leaderboard-item rounded-lg p-3 flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="rank-badge silver w-6 h-6 rounded-full flex items-center justify-center text-xs">2</div>
                                            <span class="text-white text-sm font-medium">Sarah Kim</span>
                                        </div>
                                        <span class="text-candy-blue font-bold">220</span>
                                    </div>
                                    
                                    <div class="leaderboard-item rounded-lg p-3 flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="rank-badge bronze w-6 h-6 rounded-full flex items-center justify-center text-xs">3</div>
                                            <span class="text-white text-sm font-medium">You</span>
                                        </div>
                                        <span class="text-candy-purple font-bold">180</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Bottom Actions -->
        <div class="px-4 md:px-6 pb-6">
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button class="glass-button py-3 px-6 rounded-xl font-semibold hover:scale-105 transition-transform">
                    Skip Question
                </button>
                <button class="btn-candy-blue py-3 px-6 rounded-xl font-semibold hover:scale-105 transition-transform">
                    Next Question
                    <svg class="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        let selectedOption = null;
        let timer = 30;
        let timerInterval;

        document.addEventListener('DOMContentLoaded', function() {
            startTimer();
            
            // Mode toggle functionality
            const modeToggles = document.querySelectorAll('.mode-toggle');
            const modeContents = document.querySelectorAll('.quiz-mode-content');
            
            modeToggles.forEach((toggle, index) => {
                toggle.addEventListener('click', function() {
                    modeToggles.forEach(t => {
                        t.classList.remove('active', 'bg-candy-pink');
                        t.classList.add('text-white/70');
                    });
                    
                    this.classList.add('active', 'bg-candy-pink');
                    this.classList.remove('text-white/70');
                    this.classList.add('text-white');
                    
                    modeContents.forEach(content => content.classList.add('hidden'));
                    modeContents[index].classList.remove('hidden');
                });
            });

            // Submit button
            document.getElementById('submit-btn').addEventListener('click', function() {
                if (selectedOption) {
                    showAnswer();
                }
            });
        });

        function selectOption(element, option) {
            // Remove previous selection
            document.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            // Add selection to clicked option
            element.classList.add('selected');
            selectedOption = option;
            
            // Enable submit button
            document.getElementById('submit-btn').disabled = false;
            document.getElementById('submit-btn').classList.remove('opacity-50');
        }

        function showAnswer() {
            // Stop timer
            clearInterval(timerInterval);
            
            // Show correct/incorrect answers
            document.querySelectorAll('.quiz-option').forEach((opt, index) => {
                if (index === 3) { // D is correct
                    opt.classList.add('correct');
                } else if (opt.classList.contains('selected') && index !== 3) {
                    opt.classList.add('incorrect');
                }
            });
            
            // Show explanation
            document.getElementById('explanation').classList.remove('hidden');
            
            // Update submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.textContent = 'Next Question';
            submitBtn.onclick = nextQuestion;
        }

        function nextQuestion() {
            // Reset for next question
            document.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('selected', 'correct', 'incorrect');
            });
            
            document.getElementById('explanation').classList.add('hidden');
            selectedOption = null;
            
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.textContent = 'Submit Answer';
            submitBtn.disabled = true;
            submitBtn.classList.add('opacity-50');
            submitBtn.onclick = null;
            
            // Reset timer
            timer = 30;
            startTimer();
            
            // Update progress (simulate)
            const progressBar = document.querySelector('.bg-gradient-to-r');
            const currentWidth = parseInt(progressBar.style.width) || 30;
            progressBar.style.width = Math.min(currentWidth + 10, 100) + '%';
        }

        function startTimer() {
            const timerElement = document.getElementById('timer');
            timerInterval = setInterval(() => {
                timer--;
                timerElement.textContent = timer;
                
                if (timer <= 0) {
                    clearInterval(timerInterval);
                    // Auto-submit or move to next question
                    if (!selectedOption) {
                        // Auto-select a random option or skip
                        console.log('Time up! Moving to next question...');
                    }
                }
            }, 1000);
        }
    </script>
</body>
</html>
