# API Keys (Required to enable respective provider)
ANTHROPIC_API_KEY="your_anthropic_api_key_here"       # Required: Format: sk-ant-api03-...
PERPLEXITY_API_KEY="your_perplexity_api_key_here"     # Optional: Format: pplx-...
OPENAI_API_KEY="your_openai_api_key_here"             # Optional, for OpenAI/OpenRouter models. Format: sk-proj-...
GOOGLE_API_KEY="your_google_api_key_here"             # Optional, for Google Gemini models.
MISTRAL_API_KEY="your_mistral_key_here"               # Optional, for Mistral AI models.
XAI_API_KEY="YOUR_XAI_KEY_HERE"                       # Optional, for xAI AI models.
AZURE_OPENAI_API_KEY="your_azure_key_here"            # Optional, for Azure OpenAI models (requires endpoint in .taskmaster/config.json).
OLLAMA_API_KEY="your_ollama_api_key_here"             # Optional: For remote Ollama servers that require authentication.
GITHUB_API_KEY="your_github_api_key_here"             # Optional: For GitHub import/export features. Format: ghp_... or github_pat_...

# GitHub OAuth Configuration
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# AppWrite Configuration
EXPO_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
EXPO_PUBLIC_APPWRITE_PROJECT_ID=your_appwrite_project_id
EXPO_PUBLIC_APPWRITE_DATABASE_ID=learni-scan-db

# AI Service Configuration (OpenAI-Compatible)
EXPO_PUBLIC_AI_API_KEY=kala-0719
EXPO_PUBLIC_AI_BASE_URL=http://127.0.0.1:8987/v1
EXPO_PUBLIC_AI_CHAT_MODEL=gemini-2.5-flash-lite-preview-06-17
EXPO_PUBLIC_AI_VISION_MODEL=gemini-2.5-flash-lite-preview-06-17
EXPO_PUBLIC_AI_EMBEDDING_MODEL=text-embedding-3-small
EXPO_PUBLIC_AI_TIMEOUT=30000
EXPO_PUBLIC_AI_MAX_RETRIES=3
EXPO_PUBLIC_AI_RATE_LIMIT_RPM=60
EXPO_PUBLIC_AI_RATE_LIMIT_TPM=10000

# Development Environment
NODE_ENV=development

# User Permission Limits Configuration
# Guest User Limits
EXPO_PUBLIC_GUEST_DAILY_SCANS=5
EXPO_PUBLIC_GUEST_TOTAL_CARDS=0
EXPO_PUBLIC_GUEST_AI_SEARCHES=0
EXPO_PUBLIC_GUEST_AI_GENERATIONS=0
EXPO_PUBLIC_GUEST_STORAGE_MB=0

# Authenticated User Limits
EXPO_PUBLIC_AUTH_DAILY_SCANS=50
EXPO_PUBLIC_AUTH_TOTAL_CARDS=100
EXPO_PUBLIC_AUTH_AI_SEARCHES=10
EXPO_PUBLIC_AUTH_AI_GENERATIONS=20
EXPO_PUBLIC_AUTH_STORAGE_MB=1024

# Premium User Limits (-1 means unlimited)
EXPO_PUBLIC_PREMIUM_DAILY_SCANS=-1
EXPO_PUBLIC_PREMIUM_TOTAL_CARDS=-1
EXPO_PUBLIC_PREMIUM_AI_SEARCHES=-1
EXPO_PUBLIC_PREMIUM_AI_GENERATIONS=-1
EXPO_PUBLIC_PREMIUM_STORAGE_MB=-1

# Testing Configuration
EXPO_PUBLIC_ENABLE_TESTING_MODE=true
EXPO_PUBLIC_BYPASS_AUTH_IN_DEV=false