import React, { useRef, useCallback } from 'react';
import { throttle } from 'radash';
import { toJS } from 'mobx';
import type { IUser } from '../interfaces/IUser';
import type { IMessageType } from '../interfaces/IMessageType';
import { CompletionParams } from '@pocketpalai/llama.rn';
import type { IChatSessionManager } from '../interfaces/IChatSessionManager';
import type { IModelManager } from '../interfaces/IModelManager';
import type { IUtilities } from '../interfaces/IUtilities';
import type { IPalManager } from '../interfaces/IPalManager';

export interface UseChatSessionDependencies {
  chatSessionManager: IChatSessionManager;
  modelManager: IModelManager;
  palManager: IPalManager;
  utilities: IUtilities;
}

export const useChatSession = (
  currentMessageInfo: React.MutableRefObject<{
    createdAt: number;
    id: string;
  } | null>,
  user: IUser,
  assistant: IUser,
  dependencies: UseChatSessionDependencies
) => {
  const { chatSessionManager, modelManager, palManager, utilities } = dependencies;
  const { randId, l10n, activateKeepAwake, deactivateKeepAwake, applyChatTemplate, convertToChatMessages } = utilities;
  
  const conversationIdRef = useRef<string>(randId());

  // Token buffer to accumulate tokens and avoid excessive UI updates
  const tokenBufferRef = useRef<string>('');
  const updateInterval = 150; // Interval for flushing token buffer (in ms)

  // Function to flush the token buffer and update the chat message
  const flushTokenBuffer = useCallback((createdAt: number, id: string) => {
    const context = modelManager.context;
    if (tokenBufferRef.current.length > 0 && context) {
      chatSessionManager.updateMessageToken(
        {token: tokenBufferRef.current},
        createdAt,
        id,
        context,
      );
      tokenBufferRef.current = ''; // Reset the token buffer
    }
  }, [chatSessionManager, modelManager]);

  // Throttled version of flushTokenBuffer
  const throttledFlushTokenBuffer = throttle(
    {interval: updateInterval},
    (createdAt: number, id: string) => {
      flushTokenBuffer(createdAt, id);
    }
  );

  const addMessage = useCallback((message: IMessageType.Any) => {
    chatSessionManager.addMessageToCurrentSession(message);
  }, [chatSessionManager]);

  const addSystemMessage = useCallback((text: string, metadata = {}) => {
    const textMessage: IMessageType.Text = {
      author: assistant,
      createdAt: Date.now(),
      id: randId(),
      text,
      type: 'text',
      metadata: {system: true, ...metadata},
    };
    addMessage(textMessage);
  }, [assistant, addMessage, randId]);

  const handleSendPress = useCallback(async (message: IMessageType.PartialText) => {
    const context = modelManager.context;
    if (!context) {
      addSystemMessage(l10n.modelNotLoaded);
      return;
    }

    const textMessage: IMessageType.Text = {
      author: user,
      createdAt: Date.now(),
      id: randId(),
      text: message.text,
      type: 'text',
      metadata: {
        contextId: context.id,
        conversationId: conversationIdRef.current,
        copyable: true,
      },
    };
    addMessage(textMessage);
    modelManager.setInferencing(true);
    modelManager.setIsStreaming(false);
    chatSessionManager.setIsGenerating(true);

    // Keep screen awake during completion
    try {
      activateKeepAwake();
    } catch (error) {
      console.error('Failed to activate keep awake during chat:', error);
      // Continue with chat even if keep awake fails
    }

    const id = randId();
    const createdAt = Date.now();
    currentMessageInfo.current = {createdAt, id};

    const activeSession = chatSessionManager.sessions.find(
      s => s.id === chatSessionManager.activeSessionId,
    );
    let systemPrompt = '';
    if (activeSession?.activePalId) {
      const pal = palManager.pals.find(p => p.id === activeSession.activePalId);
      if (pal?.systemPrompt) {
        systemPrompt = pal.systemPrompt;
      }
    }

    const getSystemMessage = () => {
      // If no system prompt is available at all, return empty array
      if (
        !systemPrompt &&
        !modelManager.activeModel?.chatTemplate?.systemPrompt?.trim()
      ) {
        return [];
      }

      // Prefer custom system prompt, fall back to template's system prompt
      const finalSystemPrompt =
        systemPrompt ||
        modelManager.activeModel?.chatTemplate?.systemPrompt ||
        '';

      if (finalSystemPrompt?.trim() === '') {
        return [];
      }
      return [
        {
          role: 'system' as 'system',
          content: finalSystemPrompt,
        },
      ];
    };

    const chatMessages = [
      ...getSystemMessage(),
      ...convertToChatMessages([
        textMessage,
        ...chatSessionManager.currentSessionMessages.filter(
          msg => msg.id !== textMessage.id,
        ),
      ]),
    ];

    let prompt = await applyChatTemplate(
      chatMessages,
      modelManager.activeModel ?? null,
      context,
    );

    const sessionCompletionSettings = toJS(activeSession?.completionSettings);
    const stopWords = toJS(modelManager.activeModel?.stopWords);
    const completionParams = {
      ...sessionCompletionSettings,
      prompt,
      stop: stopWords,
    };
    try {
      const result = await context.completion(
        completionParams as CompletionParams,
        data => {
          if (data.token && currentMessageInfo.current) {
            if (!modelManager.isStreaming) {
              modelManager.setIsStreaming(true);
            }
            tokenBufferRef.current += data.token;
            throttledFlushTokenBuffer(
              currentMessageInfo.current.createdAt,
              currentMessageInfo.current.id,
            );
          }
        },
      );

      // Flush any remaining tokens after completion
      if (
        currentMessageInfo.current?.createdAt &&
        currentMessageInfo.current?.id
      ) {
        flushTokenBuffer(
          currentMessageInfo.current.createdAt,
          currentMessageInfo.current.id,
        );
      }

      chatSessionManager.updateMessage(id, {
        metadata: {timings: result.timings, copyable: true},
      });
      modelManager.setInferencing(false);
      modelManager.setIsStreaming(false);
      chatSessionManager.setIsGenerating(false);
    } catch (error) {
      modelManager.setInferencing(false);
      modelManager.setIsStreaming(false);
      chatSessionManager.setIsGenerating(false);
      const errorMessage = (error as Error).message;
      if (errorMessage.includes('network')) {
        addSystemMessage(l10n.networkError);
      } else {
        addSystemMessage(`Completion failed: ${errorMessage}`);
      }
    } finally {
      // Always try to deactivate keep awake in finally block
      try {
        deactivateKeepAwake();
      } catch (error) {
        console.error('Failed to deactivate keep awake after chat:', error);
      }
    }
  }, [
    modelManager, 
    chatSessionManager, 
    palManager, 
    addSystemMessage, 
    addMessage, 
    l10n,
    user,
    randId, 
    activateKeepAwake, 
    deactivateKeepAwake,
    applyChatTemplate,
    convertToChatMessages,
    throttledFlushTokenBuffer,
    flushTokenBuffer
  ]);

  const handleResetConversation = useCallback(() => {
    conversationIdRef.current = randId();
    addSystemMessage(l10n.conversationReset);
  }, [randId, addSystemMessage, l10n]);

  const handleStopPress = useCallback(() => {
    const context = modelManager.context;
    if (modelManager.inferencing && context) {
      context.stopCompletion();
    }
    if (
      currentMessageInfo.current?.createdAt &&
      currentMessageInfo.current?.id
    ) {
      flushTokenBuffer(
        currentMessageInfo.current.createdAt,
        currentMessageInfo.current.id,
      );
    }
    modelManager.setInferencing(false);
    modelManager.setIsStreaming(false);
    // Deactivate keep awake when stopping completion
    try {
      deactivateKeepAwake();
    } catch (error) {
      console.error(
        'Failed to deactivate keep awake after stopping chat:',
        error,
      );
    }
  }, [modelManager, flushTokenBuffer, deactivateKeepAwake]);

  return {
    handleSendPress,
    handleResetConversation,
    handleStopPress,
  };
};