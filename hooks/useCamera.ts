import { useState, useRef, useEffect } from 'react';
import { CameraView, CameraType, useCameraPermissions, FlashMode } from 'expo-camera';
import * as MediaLibrary from 'expo-media-library';

export interface CameraSettings {
  quality: number;
  base64: boolean;
  exif: boolean;
}

export interface UseCamera {
  // Camera ref
  cameraRef: React.RefObject<CameraView>;
  
  // Permissions
  cameraPermission: any;
  requestCameraPermission: () => Promise<any>;
  mediaLibraryPermission: any;
  requestMediaLibraryPermission: () => Promise<any>;
  
  // Camera state
  facing: CameraType;
  flash: FlashMode;
  isReady: boolean;
  
  // Camera controls
  toggleCameraFacing: () => void;
  toggleFlash: () => void;
  setFlashMode: (mode: FlashMode) => void;
  
  // Capture functions
  takePicture: (settings?: Partial<CameraSettings>) => Promise<string | null>;
  
  // Utility functions
  hasPermissions: boolean;
  isPermissionDenied: boolean;
}

export const useCamera = (): UseCamera => {
  const cameraRef = useRef<CameraView>(null);
  
  // Permissions
  const [cameraPermission, requestCameraPermission] = useCameraPermissions();
  const [mediaLibraryPermission, requestMediaLibraryPermission] = MediaLibrary.usePermissions();
  
  // Camera state
  const [facing, setFacing] = useState<CameraType>('back');
  const [flash, setFlash] = useState<FlashMode>('off');
  const [isReady, setIsReady] = useState(false);

  // Request permissions on mount
  useEffect(() => {
    const requestPermissions = async () => {
      if (!cameraPermission?.granted) {
        await requestCameraPermission();
      }
      if (!mediaLibraryPermission?.granted) {
        await requestMediaLibraryPermission();
      }
    };
    requestPermissions();
  }, []);

  // Camera controls
  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const toggleFlash = () => {
    setFlash(current => {
      switch (current) {
        case 'off': return 'on';
        case 'on': return 'auto';
        case 'auto': return 'off';
        default: return 'off';
      }
    });
  };

  const setFlashMode = (mode: FlashMode) => {
    setFlash(mode);
  };

  // Capture function
  const takePicture = async (settings: Partial<CameraSettings> = {}): Promise<string | null> => {
    if (!cameraRef.current || !isReady) {
      throw new Error('Camera not ready');
    }

    try {
      const defaultSettings: CameraSettings = {
        quality: 0.8,
        base64: false,
        exif: true,
      };

      const photo = await cameraRef.current.takePictureAsync({
        ...defaultSettings,
        ...settings,
      });

      return photo?.uri || null;
    } catch (error) {
      console.error('Error taking picture:', error);
      throw error;
    }
  };

  // Utility computed values
  const hasPermissions = Boolean(cameraPermission?.granted && mediaLibraryPermission?.granted);
  const isPermissionDenied = cameraPermission?.granted === false;

  return {
    cameraRef,
    cameraPermission,
    requestCameraPermission,
    mediaLibraryPermission,
    requestMediaLibraryPermission,
    facing,
    flash,
    isReady: Boolean(isReady && hasPermissions),
    toggleCameraFacing,
    toggleFlash,
    setFlashMode,
    takePicture,
    hasPermissions,
    isPermissionDenied,
  };
};

export default useCamera;
