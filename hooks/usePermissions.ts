/**
 * usePermissions Hook - Permission Management for React Components
 * 
 * Provides easy-to-use permission checking and usage tracking for React components:
 * - Role-based permission checking
 * - Usage limit validation
 * - Automatic usage tracking
 * - Upgrade prompt triggers
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 11: Zod validation for all inputs/outputs
 * - Rule 12: Comprehensive error handling with early returns
 * - Rule 5: TypeScript quality and type safety
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { 
  permissionsService, 
  PERMISSIONS, 
  USER_ROLES,
  type PermissionCheckResult 
} from '@/lib/services/permissions.service';

export interface UsePermissionsReturn {
  // Permission checking
  hasPermission: (permission: string) => boolean;
  checkFeatureAccess: (feature: string, permission: string) => Promise<PermissionCheckResult>;
  
  // Usage tracking
  trackUsage: (feature: string) => Promise<boolean>;
  getCurrentUsage: (feature: string) => Promise<number>;
  getUsageLimit: (feature: string) => number;
  
  // User info
  userRole: string;
  isPremium: boolean;
  isAuthenticated: boolean;
  
  // Plan information
  planInfo: {
    plan: string;
    features: string[];
    limits: Record<string, number>;
  };
  
  // Loading states
  isLoading: boolean;
  error: string | null;
}

export const usePermissions = (): UsePermissionsReturn => {
  const { user, userProfile, isAuthenticated, isLoading: authLoading } = useAuth();
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Determine user role
  const userRole = userProfile?.subscription?.plan === 'premium' 
    ? USER_ROLES.PREMIUM 
    : user 
      ? USER_ROLES.AUTHENTICATED 
      : USER_ROLES.GUEST;

  const isPremium = userRole === USER_ROLES.PREMIUM;

  // Get plan information
  const planInfo = permissionsService.getUserPlanInfo(userRole);

  /**
   * Check if user has a specific permission
   */
  const hasPermission = useCallback((permission: string): boolean => {
    try {
      return permissionsService.hasPermission(permission, userRole);
    } catch (error) {
      console.error('Permission check error:', error);
      setError('Failed to check permission');
      return false;
    }
  }, [userRole]);

  /**
   * Check feature access with usage limits
   */
  const checkFeatureAccess = useCallback(async (
    feature: string, 
    permission: string
  ): Promise<PermissionCheckResult> => {
    if (!user) {
      return {
        hasPermission: false,
        reason: 'User not authenticated',
        upgradeRequired: true
      };
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await permissionsService.checkPermissionWithUsage(
        user.$id,
        permission,
        feature,
        userRole
      );

      return result;
    } catch (error) {
      console.error('Feature access check error:', error);
      setError('Failed to check feature access');
      return {
        hasPermission: false,
        reason: 'Access check failed'
      };
    } finally {
      setIsLoading(false);
    }
  }, [user, userRole]);

  /**
   * Track usage for a feature
   */
  const trackUsage = useCallback(async (feature: string): Promise<boolean> => {
    if (!user) {
      console.warn('Cannot track usage for unauthenticated user');
      return false;
    }

    try {
      const success = await permissionsService.trackUsage(user.$id, feature);
      if (!success) {
        setError('Failed to track usage');
      }
      return success;
    } catch (error) {
      console.error('Usage tracking error:', error);
      setError('Failed to track usage');
      return false;
    }
  }, [user]);

  /**
   * Get current usage for a feature
   */
  const getCurrentUsage = useCallback(async (feature: string): Promise<number> => {
    if (!user) return 0;

    try {
      // This is a private method, so we'll implement it here
      const today = new Date().toISOString().split('T')[0];
      const key = `usage_${user.$id}_${feature}_${today}`;
      
      const AsyncStorage = await import('@react-native-async-storage/async-storage');
      const currentUsage = await AsyncStorage.default.getItem(key);
      
      return currentUsage ? parseInt(currentUsage) : 0;
    } catch (error) {
      console.error('Get current usage error:', error);
      return 0;
    }
  }, [user]);

  /**
   * Get usage limit for a feature
   */
  const getUsageLimit = useCallback((feature: string): number => {
    return permissionsService.getUsageLimit(feature, userRole);
  }, [userRole]);

  // Clear error when user changes
  useEffect(() => {
    setError(null);
  }, [user]);

  return {
    // Permission checking
    hasPermission,
    checkFeatureAccess,
    
    // Usage tracking
    trackUsage,
    getCurrentUsage,
    getUsageLimit,
    
    // User info
    userRole,
    isPremium,
    isAuthenticated,
    
    // Plan information
    planInfo,
    
    // Loading states
    isLoading: isLoading || authLoading,
    error
  };
};

/**
 * Hook for specific feature permission checking
 */
export const useFeaturePermission = (feature: string, permission: string) => {
  const { checkFeatureAccess, trackUsage, hasPermission } = usePermissions();
  const [permissionResult, setPermissionResult] = useState<PermissionCheckResult | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  const checkAccess = useCallback(async () => {
    setIsChecking(true);
    try {
      const result = await checkFeatureAccess(feature, permission);
      setPermissionResult(result);
      return result;
    } finally {
      setIsChecking(false);
    }
  }, [checkFeatureAccess, feature, permission]);

  const useFeature = useCallback(async () => {
    const result = await checkAccess();
    if (result.hasPermission) {
      await trackUsage(feature);
      return true;
    }
    return false;
  }, [checkAccess, trackUsage, feature]);

  // Check access on mount and when dependencies change
  useEffect(() => {
    checkAccess();
  }, [checkAccess]);

  return {
    hasAccess: permissionResult?.hasPermission || false,
    permissionResult,
    isChecking,
    checkAccess,
    useFeature,
    hasBasicPermission: hasPermission(permission)
  };
};

/**
 * Hook for usage-based features (with limits)
 */
export const useUsageLimitedFeature = (feature: string) => {
  const { getCurrentUsage, getUsageLimit, trackUsage, userRole } = usePermissions();
  const [usage, setUsage] = useState<{ current: number; limit: number; remaining: number }>({
    current: 0,
    limit: 0,
    remaining: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  const refreshUsage = useCallback(async () => {
    setIsLoading(true);
    try {
      const current = await getCurrentUsage(feature);
      const limit = getUsageLimit(feature);
      const remaining = limit === -1 ? -1 : Math.max(0, limit - current);

      setUsage({ current, limit, remaining });
    } catch (error) {
      console.error('Failed to refresh usage:', error);
    } finally {
      setIsLoading(false);
    }
  }, [getCurrentUsage, getUsageLimit, feature]);

  const canUseFeature = usage.limit === -1 || usage.remaining > 0;

  const useFeature = useCallback(async (): Promise<boolean> => {
    if (!canUseFeature) {
      return false;
    }

    const success = await trackUsage(feature);
    if (success) {
      await refreshUsage(); // Refresh usage after tracking
    }
    return success;
  }, [canUseFeature, trackUsage, feature, refreshUsage]);

  // Refresh usage on mount and when user role changes
  useEffect(() => {
    refreshUsage();
  }, [refreshUsage, userRole]);

  return {
    usage,
    canUseFeature,
    useFeature,
    refreshUsage,
    isLoading,
    isUnlimited: usage.limit === -1
  };
};

// Export permission constants for easy access
export { PERMISSIONS, USER_ROLES };
