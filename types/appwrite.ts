// AppWrite Document Base Interface
export interface AppwriteDocument {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
}

// User Interface
export interface AppwriteUser extends AppwriteDocument {
  email: string;
  name: string;
  avatar?: string;
  preferences: {
    language: string;
    theme: 'light' | 'dark';
    notifications: boolean;
  };
  learningStats: {
    totalCards: number;
    totalScans: number;
    streakDays: number;
    lastActive: string;
  };
  subscription?: {
    plan: 'free' | 'premium';
    expiresAt?: string;
  };
}

// Knowledge Card Interface
export interface KnowledgeCard extends AppwriteDocument {
  userId: string;
  title: string;
  content: string;
  sourceType: 'scan' | 'manual' | 'ai_generated';
  sourceData?: {
    originalText?: string;
    imageUrl?: string;
    documentUrl?: string;
  };
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  reviewData: {
    nextReview: string;
    interval: number;
    easeFactor: number;
    reviewCount: number;
    correctCount: number;
  };
  aiEnhanced: boolean;
  isPublic: boolean;
}

// Learning Session Interface
export interface LearningSession extends AppwriteDocument {
  userId: string;
  sessionType: 'review' | 'study' | 'scan';
  cardsReviewed: string[];
  performance: {
    totalCards: number;
    correctAnswers: number;
    averageTime: number;
    accuracy: number;
  };
  duration: number;
  startedAt: string;
  completedAt: string;
}

// Scan History Interface
export interface ScanHistory extends AppwriteDocument {
  userId: string;
  originalImageId: string;
  extractedText: string;
  processedContent?: string;
  knowledgeCardId?: string;
  scanType: 'document' | 'handwriting' | 'book' | 'whiteboard';
  confidence: number;
  language: string;
  metadata: {
    imageSize: number;
    dimensions: {
      width: number;
      height: number;
    };
    processingTime: number;
  };
}

// File Upload Interfaces
export interface FileUploadProgress {
  progress: number;
  total: number;
  loaded: number;
}

export interface FileUploadResult {
  $id: string;
  name: string;
  signature: string;
  mimeType: string;
  sizeOriginal: number;
  $createdAt: string;
  $updatedAt: string;
}

// Real-time Event Interfaces
export interface RealtimeEvent {
  events: string[];
  channels: string[];
  timestamp: number;
  payload: any;
}

export type RealtimeCallback = (event: RealtimeEvent) => void;

// Error Interfaces
export interface AppwriteError {
  code: number;
  message: string;
  type: string;
  version: string;
}

// Offline Operation Interface
export interface OfflineOperation {
  id: string;
  type: 'create' | 'update' | 'delete';
  collection: string;
  data: any;
  timestamp: number;
  retryCount: number;
}

// Authentication Interfaces
export interface AuthUser {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  name: string;
  email: string;
  emailVerification: boolean;
  phone: string;
  phoneVerification: boolean;
  prefs: Record<string, any>;
  status: boolean;
  labels: string[];
  accessedAt: string;
}

export interface AuthSession {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  userId: string;
  expire: string;
  provider: string;
  providerUid: string;
  providerAccessToken: string;
  providerAccessTokenExpiry: string;
  providerRefreshToken: string;
  ip: string;
  osCode: string;
  osName: string;
  osVersion: string;
  clientType: string;
  clientCode: string;
  clientName: string;
  clientVersion: string;
  clientEngine: string;
  clientEngineVersion: string;
  deviceName: string;
  deviceBrand: string;
  deviceModel: string;
  countryCode: string;
  countryName: string;
  current: boolean;
}

// Service Response Types
export interface DatabaseResponse<T> {
  total: number;
  documents: T[];
}

export interface StorageResponse {
  total: number;
  files: FileUploadResult[];
}

// Query Builder Types
export type QueryType = 'equal' | 'notEqual' | 'lessThan' | 'lessThanEqual' | 'greaterThan' | 'greaterThanEqual' | 'search' | 'orderAsc' | 'orderDesc' | 'limit' | 'offset';

export interface QueryBuilder {
  attribute: string;
  method: QueryType;
  values?: any[];
}

// Permission Types
export type PermissionType = 'read' | 'write' | 'create' | 'update' | 'delete';
export type RoleType = 'any' | 'user' | 'users' | 'guests' | 'team' | 'member' | 'label';

// Configuration Types
export interface AppwriteConfig {
  endpoint: string;
  projectId: string;
  databaseId: string;
  collections: {
    users: string;
    knowledgeCards: string;
    learningSessions: string;
    scanHistory: string;
  };
  buckets: {
    userAvatars: string;
    scanImages: string;
    cardAssets: string;
    userExports: string;
  };
  functions: {
    processDocument: string;
    generateSummary: string;
    enhanceContent: string;
  };
}
