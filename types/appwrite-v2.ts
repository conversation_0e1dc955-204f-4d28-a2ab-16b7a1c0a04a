// AppWrite v2 Schema Types for Split Knowledge Cards Architecture
// This file contains updated interfaces for the new split knowledge cards schema

// Base AppWrite Document Interface (unchanged)
export interface AppwriteDocument {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
}

// User Interface (unchanged)
export interface AppwriteUser extends AppwriteDocument {
  email: string;
  name: string;
  avatar?: string;
  preferences: {
    language: string;
    theme: 'light' | 'dark';
    notifications: boolean;
  };
  learningStats: {
    totalCards: number;
    totalScans: number;
    streakDays: number;
    lastActive: string;
  };
  subscription?: {
    plan: 'free' | 'premium';
    expiresAt?: string;
  };
}

// Learning Session Interface (unchanged)
export interface LearningSession extends AppwriteDocument {
  userId: string;
  sessionType: 'review' | 'study' | 'scan';
  cardsReviewed: string[];
  performance: {
    totalCards: number;
    correctAnswers: number;
    averageTime: number;
    accuracy: number;
  };
  duration: number;
  startedAt: string;
  completedAt: string;
}

// Scan History Interface (unchanged)
export interface ScanHistory extends AppwriteDocument {
  userId: string;
  originalImageId: string;
  extractedText: string;
  processedContent?: string;
  knowledgeCardId?: string;
  scanType: 'document' | 'handwriting' | 'book' | 'whiteboard';
  confidence: number;
  language: string;
  metadata: {
    imageSize: number;
    dimensions: {
      width: number;
      height: number;
    };
    processingTime: number;
  };
}

// NEW: Split Knowledge Cards Architecture

// 1. Knowledge Card v2 (Core metadata only)
export interface KnowledgeCardV2 extends AppwriteDocument {
  userId: string;
  title: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  sourceType: 'scan' | 'manual' | 'ai_generated';
  status: 'active' | 'archived' | 'deleted';
  aiEnhanced: boolean;
  isPublic: boolean;
}

// 2. Knowledge Content (Large content and source data)
export interface KnowledgeContent extends AppwriteDocument {
  cardId: string; // Reference to KnowledgeCardV2.$id
  content: string; // Main content (up to 50,000 chars)
  sourceData?: {
    originalText?: string;
    imageUrl?: string;
    documentUrl?: string;
    metadata?: Record<string, any>;
  };
  processedContent?: string; // AI-processed/enhanced content
  summary?: string; // Content summary for quick reference
}

// 3. Knowledge Reviews (Review data and statistics)
export interface KnowledgeReviews extends AppwriteDocument {
  cardId: string; // Reference to KnowledgeCardV2.$id
  nextReview: string; // ISO datetime string
  interval: number; // Days until next review
  easeFactor: number; // Spaced repetition ease factor
  reviewCount: number; // Total number of reviews
  correctCount: number; // Number of correct answers
  reviewHistory: {
    date: string;
    correct: boolean;
    responseTime: number;
    difficulty: 'easy' | 'good' | 'hard' | 'again';
  }[];
}

// 4. Knowledge Tags (Tags and categorization)
export interface KnowledgeTag extends AppwriteDocument {
  cardId: string; // Reference to KnowledgeCardV2.$id
  tag: string; // Individual tag name
  tagType: 'user' | 'system' | 'ai'; // Tag source
  confidence?: string; // AI confidence for auto-generated tags
}

// Composite interfaces for working with complete knowledge cards

// Complete Knowledge Card (aggregated from all related collections)
export interface CompleteKnowledgeCard {
  // Core data from knowledge_cards_v2
  card: KnowledgeCardV2;
  
  // Content data from knowledge_content
  content?: KnowledgeContent;
  
  // Review data from knowledge_reviews
  reviews?: KnowledgeReviews;
  
  // Tags from knowledge_tags
  tags?: KnowledgeTag[];
  
  // Computed properties
  tagNames?: string[]; // Array of tag names for easy access
  nextReviewDate?: Date; // Parsed next review date
  isReviewDue?: boolean; // Whether the card is due for review
}

// Knowledge Card Creation Input
export interface CreateKnowledgeCardInput {
  // Required core data
  title: string;
  content: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  sourceType: 'scan' | 'manual' | 'ai_generated';
  
  // Optional data
  sourceData?: {
    originalText?: string;
    imageUrl?: string;
    documentUrl?: string;
    metadata?: Record<string, any>;
  };
  tags?: string[];
  aiEnhanced?: boolean;
  isPublic?: boolean;
  summary?: string;
}

// Knowledge Card Update Input
export interface UpdateKnowledgeCardInput {
  // Core data updates
  title?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  category?: string;
  status?: 'active' | 'archived' | 'deleted';
  aiEnhanced?: boolean;
  isPublic?: boolean;
  
  // Content updates
  content?: string;
  sourceData?: {
    originalText?: string;
    imageUrl?: string;
    documentUrl?: string;
    metadata?: Record<string, any>;
  };
  processedContent?: string;
  summary?: string;
  
  // Tags updates
  addTags?: string[];
  removeTags?: string[];
}

// Review Session Input
export interface ReviewSessionInput {
  cardId: string;
  correct: boolean;
  responseTime: number; // in milliseconds
  difficulty: 'easy' | 'good' | 'hard' | 'again';
}

// Database Query Interfaces
export interface KnowledgeCardQuery {
  userId?: string;
  category?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  status?: 'active' | 'archived' | 'deleted';
  isPublic?: boolean;
  tags?: string[];
  searchTerm?: string;
  limit?: number;
  offset?: number;
  orderBy?: 'created' | 'updated' | 'title' | 'nextReview';
  orderDirection?: 'asc' | 'desc';
}

// Cards due for review query
export interface ReviewDueQuery {
  userId: string;
  beforeDate?: string; // ISO datetime string
  limit?: number;
}

// Updated AppWrite Configuration
export interface AppwriteConfigV2 {
  endpoint: string;
  projectId: string;
  databaseId: string;
  collections: {
    users: string;
    knowledgeCardsV2: string; // Updated collection name
    knowledgeContent: string; // New collection
    knowledgeReviews: string; // New collection
    knowledgeTags: string; // New collection
    learningSessions: string;
    scanHistory: string;
  };
  buckets: {
    userAvatars: string;
    scanImages: string;
    cardAssets: string;
    userExports: string;
  };
  functions: {
    processDocument: string;
    generateSummary: string;
    enhanceContent: string;
  };
}

// Service Response Types (updated)
export interface KnowledgeCardResponse {
  total: number;
  cards: CompleteKnowledgeCard[];
}

export interface ReviewDueResponse {
  total: number;
  cards: CompleteKnowledgeCard[];
  nextReviewDate?: string;
}

// Migration Types (for transitioning from old schema)
export interface MigrationStatus {
  totalCards: number;
  migratedCards: number;
  failedCards: number;
  errors: string[];
  completed: boolean;
}

// Legacy Knowledge Card Interface (for migration compatibility)
export interface LegacyKnowledgeCard extends AppwriteDocument {
  userId: string;
  title: string;
  content: string;
  sourceType: 'scan' | 'manual' | 'ai_generated';
  sourceData?: {
    originalText?: string;
    imageUrl?: string;
    documentUrl?: string;
  };
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  reviewData: {
    nextReview: string;
    interval: number;
    easeFactor: number;
    reviewCount: number;
    correctCount: number;
  };
  aiEnhanced: boolean;
  isPublic: boolean;
}

// All interfaces are already exported above with the 'export' keyword
// TypeScript compilation fix - ensure clean exports