Add the following 12 mandatory development workflow rules that must be followed throughout the entire LearniScan app development process. These rules build upon our established design system (candy colors, glass morphism, Gluestack UI components) and existing project structure:

### **Rule 1: Interactive Feedback Protocol**

- **ALWAYS** use the `interactive_feedback_mcp-feedback-enhanced` tool after completing any significant development milestone (component implementation, screen completion, feature addition, or bug fixes)
- Provide comprehensive summaries including: specific work completed, current implementation status, files modified, next planned steps, and any technical blockers encountered
- **WAIT** for explicit user approval before proceeding with major architectural changes, new feature implementations, or design system modifications
- Include the absolute project directory path (`/Users/<USER>/Documents/Code/Robot/pocket-manus`) and detailed progress summaries in all feedback requests
- Set appropriate timeout (default: 600 seconds) for user response

### **Rule 2: Expo Process Management**

- **BEFORE** starting any new Expo development server, ALWAYS check for existing running processes using `lsof -i:8081` command via `launch-process` tool
- **ONLY** launch new Expo server using `npx expo start` if no existing process is found or if the existing process has terminated
- This prevents port 8081 conflicts and maintains development session continuity

### **Rule 3: Library Context Acquisition**

- **ALWAYS** use `resolve-library-id_context7-mcp` to obtain proper Context7-compatible library IDs before using `get-library-docs_context7-mcp`
- Focus documentation requests on specific topics relevant to the current task (e.g., 'hooks', 'routing', 'components', 'styling', 'animations')
- Set appropriate token limits (default: 10000) based on complexity of the feature being implemented
- Use library documentation to understand proper usage patterns before implementing features with external libraries like react-native-reanimated, expo-linear-gradient, or Gluestack UI components

### **Rule 4: Planning and Implementation with Serena MCP**

- **ALWAYS** use Serena MCP tools for all code planning, analysis, and implementation tasks
- Call `initial_instructions_serena` at the beginning of each development session
- Use `think_about_task_adherence_serena` before making any code changes to ensure alignment with project goals
- Save important context, progress updates, and implementation decisions using `write_memory_serena` with descriptive memory names
- Use Serena's symbolic editing tools (`replace_symbol_body_serena`, `insert_after_symbol_serena`, `find_symbol_serena`) for precise code modifications instead of manual file editing
- Call `think_about_whether_you_are_done_serena` before completing tasks

### **Rule 5: Code Quality and Type Safety Assurance**

- **ALWAYS** run TypeScript compilation check using `npx tsc --noEmit --skipLibCheck` via `launch-process` after making any code changes to React Native components
- Fix ALL TypeScript errors, type compatibility issues, missing property definitions, and import/export problems before proceeding to next steps
- Ensure all React components have proper TypeScript interfaces and default exports
- Verify that Gluestack UI component props match their expected TypeScript definitions
- Use `diagnostics` tool to check for IDE-reported issues in modified files
- **CONTINUOUSLY** view Expo development server outputs using `tail -n 20 running_outputs.log`
- **IMMEDIATELY** address any runtime errors, Metro bundler warnings, or React Native bridge errors that appear in development logs

### **Rule 6: UI Design Consistency and Component Alignment**

- **ALWAYS** reference the HTML design files in the `design/` folder (especially `design/pages/welcome.html` and `design/styles/design-system.css`) for visual fidelity targets
- **PRIORITIZE** reusing pre-defined design tokens from our established system:
    - Candy colors: primary-500 (pink), secondary-500 (purple), tertiary-500 (blue)
    - Glass morphism: `bg-glass-bg-primary`, `border-glass-border-primary`, `backdrop-blur-md`
    - Typography: `color="primary"`, `color="secondary"`, `color="candyPink"`
    - Spacing: consistent padding, margins, and gap values
- **REUSE** optimized UI components from `components/ui/` directory (Button, Text, Box, VStack, HStack) with proper action variants (`candyPink`, `candyPurple`, `candyBlue`, `glass`, `glassCard`)
- **Follow** the COMPONENT_ALIGNMENT_STRATEGY.md for consistent component usage patterns
- Prefer `Tailwind CSS` than `Styles`, use `StyleSheet.create` to create static styles to avoid unnecessary re-rendering when you have to use `Styles`
- **VERIFY** UI implementation alignment using #ios-simulator MCP tools

### **Rule 7: Expo Development Monitoring**

- **Kill** process using #desktop-commander `kill_process` with appropriate process ID to terminate the process temporarily while it’s conflict with another upcoming command
- **TRACK** bundle compilation status, route loading performance, and component rendering errors
- **RESPOND** promptly to TypeScript compilation errors, missing dependency warnings, and platform-specific issues

### **Rule 8: Animation Implementation Standards**

- **ALWAYS** use `react-native-reanimated` (v3+) for performant animations instead of basic Animated API
- Leverage `react-native-gesture-handler` for touch interactions and gesture-based animations
- Implement entrance animations using `useSharedValue`, `useAnimatedStyle`, and `withTiming`/`withSpring`
- Use `runOnJS` for callbacks that need to interact with JavaScript thread
- Optimize animations for 60fps performance using native driver capabilities
- Reference existing animation patterns in `docs/` directory for consistency

### **Rule 9: Safe Area Management**

- Use `SafeAreaProvider` from `react-native-safe-area-context` to manage safe areas **globally** at the app root level
- Wrap screen-level components with `SafeAreaView` to handle device notches, status bars, and home indicators on both iOS and Android
- Use `SafeAreaScrollView` for scrollable content to ensure proper safe area boundary respect
- **AVOID** hardcoding padding or margins for safe areas; rely on SafeAreaView context and hooks (`useSafeAreaInsets`)
- Test safe area handling on devices with different screen configurations (iPhone with notch, Android with navigation bars)

### **Rule 10: Performance Optimization**

- **MINIMIZE** excessive `useState` and `useEffect` usage; prefer React Context and useReducer for complex state management
- Use Expo's `AppLoading` and `SplashScreen` APIs for optimized app startup experience
- **OPTIMIZE** images: use WebP format where supported, include explicit size dimensions, implement lazy loading with `expo-image`
- Implement code splitting and lazy loading for non-critical components using React's `Suspense` and dynamic imports
- **PROFILE** performance using React Native's built-in Flipper integration and Expo's debugging features
- Prevent unnecessary re-renders by memoizing components with `React.memo` and using `useMemo`/`useCallback` appropriately
- Use React `startTransition`/`useTransition` for non-urgent state updates that don't block user interactions
- Leverage `@tanstack/react-query` for efficient data fetching, caching, and background updates

### **Rule 11: State Management Architecture**

- Use `Zod` to declare / define any state appearing in App, for example

```tsx
const CameraStateSchema = z.object({
  activeTab: z.enum(['camera', 'upload', 'gallery']),
  facing: z.enum(['back', 'front']),
  flash: z.enum(['off', 'on']),
  isCapturing: z.boolean(),
  hasPermission: z.boolean().nullable(),
});
type ICameraState = z.infer<typeof CameraStateSchema>;
const [hasPermission, setHasPermission] = useState<ICameraState['hasPermission']>(null);
```

- Use React Context API for managing global application state (user preferences, theme, authentication)
- Implement React `Suspense` + `use` hook pattern for declarative loading state presentation
- Use React `startTransition`/`useTransition` to optimize state updates and prevent UI blocking
- For complex state logic, consider `Zustand` for lightweight, TypeScript-friendly state management
- Handle URL parameters and deep linking using `expo-linking` and `expo-router` navigation state
- Maintain state immutability and avoid direct mutations

### **Rule 12: Validation and Error Handling**

- Use `Zod` for runtime type validation, API response validation, and form input validation
- **PRIORITIZE** comprehensive error handling and edge case management:
    - Handle error conditions at the beginning of functions using early returns
    - Use **early return pattern** to avoid deeply nested conditional statements
    - Avoid unnecessary `else` statements; prefer if-return pattern for cleaner code flow
    - Implement global error boundaries using React Error Boundary components to catch unexpected errors
    - Provide user-friendly error messages and recovery options
    - Log errors appropriately for debugging while protecting user privacy

### **Implementation Requirements:**

1. **SAVE / UPDATE** these rules in Serena memory using `write_memory_serena` with memory name "learni-scan-development-workflow-rules"
2. **REFERENCE** these rules before starting any development task
3. **FOLLOW** all 12 rules consistently throughout the LearniScan app development lifecycle
4. **UPDATE** the rules in memory if modifications are needed based on project evolution

These enhanced rules ensure proper communication flow, efficient development server management, code quality maintenance, design system consistency, and performance optimization during the LearniScan app development process.