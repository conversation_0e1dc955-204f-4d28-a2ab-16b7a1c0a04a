import { act } from '@testing-library/react-native';
import { useUIStore, uiStore } from '../../store/UIStore';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}));

describe('UIStore', () => {
  beforeEach(() => {
    // Reset the store
    act(() => {
      useUIStore.setState({
        pageStates: {
          modelsScreen: {
            filters: [],
            expandedGroups: {
              ready_to_use: true,
            },
          },
        },
        autoNavigatetoChat: true,
        colorScheme: 'light',
        displayMemUsage: false,
        iOSBackgroundDownloading: true,
        benchmarkShareDialog: {
          shouldShow: true,
        },
      });
    });
  });

  test('initial state is correct', () => {
    const state = useUIStore.getState();
    
    expect(state.pageStates.modelsScreen.filters).toEqual([]);
    expect(state.pageStates.modelsScreen.expandedGroups).toEqual({ ready_to_use: true });
    expect(state.autoNavigatetoChat).toBe(true);
    expect(state.colorScheme).toBe('light');
    expect(state.displayMemUsage).toBe(false);
    expect(state.iOSBackgroundDownloading).toBe(true);
    expect(state.benchmarkShareDialog.shouldShow).toBe(true);
  });

  test('setValue updates pageState correctly', () => {
    act(() => {
      useUIStore.getState().setValue('modelsScreen', 'filters', ['test']);
    });
    
    expect(useUIStore.getState().pageStates.modelsScreen.filters).toEqual(['test']);
    expect(uiStore.pageStates.modelsScreen.filters).toEqual(['test']);
  });

  test('setColorScheme updates colorScheme correctly', () => {
    act(() => {
      useUIStore.getState().setColorScheme('dark');
    });
    
    expect(useUIStore.getState().colorScheme).toBe('dark');
    expect(uiStore.colorScheme).toBe('dark');
  });

  test('setAutoNavigateToChat updates autoNavigatetoChat correctly', () => {
    act(() => {
      useUIStore.getState().setAutoNavigateToChat(false);
    });
    
    expect(useUIStore.getState().autoNavigatetoChat).toBe(false);
    expect(uiStore.autoNavigatetoChat).toBe(false);
  });

  test('setDisplayMemUsage updates displayMemUsage correctly', () => {
    act(() => {
      useUIStore.getState().setDisplayMemUsage(true);
    });
    
    expect(useUIStore.getState().displayMemUsage).toBe(true);
    expect(uiStore.displayMemUsage).toBe(true);
  });

  test('setiOSBackgroundDownloading updates iOSBackgroundDownloading correctly', () => {
    act(() => {
      useUIStore.getState().setiOSBackgroundDownloading(false);
    });
    
    expect(useUIStore.getState().iOSBackgroundDownloading).toBe(false);
    expect(uiStore.iOSBackgroundDownloading).toBe(false);
  });

  test('setBenchmarkShareDialogPreference updates benchmarkShareDialog correctly', () => {
    act(() => {
      useUIStore.getState().setBenchmarkShareDialogPreference(false);
    });
    
    expect(useUIStore.getState().benchmarkShareDialog.shouldShow).toBe(false);
    expect(uiStore.benchmarkShareDialog.shouldShow).toBe(false);
  });

  test('compatibility layer works correctly', () => {
    // Set via direct store
    act(() => {
      useUIStore.setState({
        colorScheme: 'dark',
        autoNavigatetoChat: false,
      });
    });
    
    // Read via compatibility layer
    expect(uiStore.colorScheme).toBe('dark');
    expect(uiStore.autoNavigatetoChat).toBe(false);
    
    // Set via compatibility layer
    act(() => {
      uiStore.setColorScheme('light');
    });
    
    // Read via direct store
    expect(useUIStore.getState().colorScheme).toBe('light');
  });

  test('showError logs to console', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    
    act(() => {
      useUIStore.getState().showError('Test error');
    });
    
    expect(consoleSpy).toHaveBeenCalledWith('Test error');
    consoleSpy.mockRestore();
  });
});
