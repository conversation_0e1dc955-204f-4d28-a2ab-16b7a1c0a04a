/**
 * Camera Service Tests
 * 
 * Comprehensive test suite for the camera service functionality
 * including photo capture, image processing, upload, and database integration.
 */

import { CameraService } from '../../lib/services/camera.service';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import { storage } from '../../lib/config/appwrite';

// Mock dependencies
jest.mock('expo-file-system');
jest.mock('expo-image-manipulator');
jest.mock('../../lib/config/appwrite');
jest.mock('react-native-appwrite', () => ({
  ID: {
    unique: jest.fn(() => 'mock-unique-id'),
  },
}));

const mockFileSystem = FileSystem as jest.Mocked<typeof FileSystem>;
const mockImageManipulator = ImageManipulator as jest.Mocked<typeof ImageManipulator>;
const mockStorage = storage as jest.Mocked<typeof storage>;

describe('CameraService', () => {
  let cameraService: CameraService;
  let mockCameraRef: any;

  beforeEach(() => {
    cameraService = new CameraService();
    
    // Mock camera ref
    mockCameraRef = {
      current: {
        takePictureAsync: jest.fn(),
      },
    };

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('capturePhoto', () => {
    it('should capture and process photo successfully', async () => {
      // Arrange
      const mockPhoto = {
        uri: 'file://mock-photo.jpg',
        width: 1920,
        height: 1080,
        base64: null,
      };

      const mockFileInfo = {
        exists: true,
        size: 1024000, // 1MB
        isDirectory: false,
        modificationTime: Date.now(),
        uri: 'file://mock-photo.jpg',
      };

      const mockProcessedImage = {
        uri: 'file://processed-photo.jpg',
        width: 1920,
        height: 1080,
      };

      mockCameraRef.current.takePictureAsync.mockResolvedValue(mockPhoto);
      mockFileSystem.getInfoAsync.mockResolvedValue(mockFileInfo);
      mockImageManipulator.manipulateAsync.mockResolvedValue(mockProcessedImage);

      // Act
      const result = await cameraService.capturePhoto(mockCameraRef);

      // Assert
      expect(mockCameraRef.current.takePictureAsync).toHaveBeenCalledWith({
        quality: 0.8,
        skipProcessing: false,
        base64: false,
      });
      expect(mockFileSystem.getInfoAsync).toHaveBeenCalledWith(mockPhoto.uri);
      expect(mockImageManipulator.manipulateAsync).toHaveBeenCalled();
      expect(result).toEqual({
        uri: mockProcessedImage.uri,
        width: mockProcessedImage.width,
        height: mockProcessedImage.height,
        fileSize: mockFileInfo.size,
        base64: mockPhoto.base64,
      });
    });

    it('should throw error when camera ref is not available', async () => {
      // Arrange
      const invalidCameraRef = { current: null };

      // Act & Assert
      await expect(cameraService.capturePhoto(invalidCameraRef)).rejects.toThrow(
        'Camera reference is not available'
      );
    });

    it('should throw error when photo capture fails', async () => {
      // Arrange
      mockCameraRef.current.takePictureAsync.mockRejectedValue(new Error('Camera error'));

      // Act & Assert
      await expect(cameraService.capturePhoto(mockCameraRef)).rejects.toThrow(
        'Failed to capture photo: Camera error'
      );
    });

    it('should throw error when captured file does not exist', async () => {
      // Arrange
      const mockPhoto = {
        uri: 'file://mock-photo.jpg',
        width: 1920,
        height: 1080,
      };

      const mockFileInfo = {
        exists: false,
        size: 0,
        isDirectory: false,
        modificationTime: Date.now(),
        uri: 'file://mock-photo.jpg',
      };

      mockCameraRef.current.takePictureAsync.mockResolvedValue(mockPhoto);
      mockFileSystem.getInfoAsync.mockResolvedValue(mockFileInfo);

      // Act & Assert
      await expect(cameraService.capturePhoto(mockCameraRef)).rejects.toThrow(
        'Captured photo file does not exist'
      );
    });

    it('should use custom capture options', async () => {
      // Arrange
      const customOptions = {
        quality: 0.9,
        skipProcessing: true,
        base64: true,
      };

      const mockPhoto = {
        uri: 'file://mock-photo.jpg',
        width: 1920,
        height: 1080,
        base64: 'mock-base64-data',
      };

      const mockFileInfo = {
        exists: true,
        size: 1024000,
        isDirectory: false,
        modificationTime: Date.now(),
        uri: 'file://mock-photo.jpg',
      };

      const mockProcessedImage = {
        uri: 'file://processed-photo.jpg',
        width: 1920,
        height: 1080,
      };

      mockCameraRef.current.takePictureAsync.mockResolvedValue(mockPhoto);
      mockFileSystem.getInfoAsync.mockResolvedValue(mockFileInfo);
      mockImageManipulator.manipulateAsync.mockResolvedValue(mockProcessedImage);

      // Act
      await cameraService.capturePhoto(mockCameraRef, customOptions);

      // Assert
      expect(mockCameraRef.current.takePictureAsync).toHaveBeenCalledWith(customOptions);
    });
  });

  describe('processImage', () => {
    it('should process image with default options', async () => {
      // Arrange
      const imageUri = 'file://test-image.jpg';
      const mockResult = {
        uri: 'file://processed-image.jpg',
        width: 1920,
        height: 1080,
      };

      mockImageManipulator.manipulateAsync.mockResolvedValue(mockResult);

      // Act
      const result = await cameraService.processImage(imageUri);

      // Assert
      expect(mockImageManipulator.manipulateAsync).toHaveBeenCalledWith(
        imageUri,
        [],
        {
          compress: 0.8,
          format: ImageManipulator.SaveFormat.JPEG,
          base64: false,
        }
      );
      expect(result).toEqual(mockResult);
    });

    it('should process image with custom options', async () => {
      // Arrange
      const imageUri = 'file://test-image.jpg';
      const customOptions = {
        compress: 0.9,
        format: ImageManipulator.SaveFormat.PNG,
        resize: { width: 1024, height: 768 },
      };

      const mockResult = {
        uri: 'file://processed-image.png',
        width: 1024,
        height: 768,
      };

      mockImageManipulator.manipulateAsync.mockResolvedValue(mockResult);

      // Act
      const result = await cameraService.processImage(imageUri, customOptions);

      // Assert
      expect(mockImageManipulator.manipulateAsync).toHaveBeenCalledWith(
        imageUri,
        [{ resize: customOptions.resize }],
        {
          compress: customOptions.compress,
          format: customOptions.format,
          base64: false,
        }
      );
      expect(result).toEqual(mockResult);
    });

    it('should throw error when image processing fails', async () => {
      // Arrange
      const imageUri = 'file://test-image.jpg';
      mockImageManipulator.manipulateAsync.mockRejectedValue(new Error('Processing error'));

      // Act & Assert
      await expect(cameraService.processImage(imageUri)).rejects.toThrow(
        'Failed to process image: Processing error'
      );
    });
  });

  describe('uploadPhoto', () => {
    it('should upload photo successfully', async () => {
      // Arrange
      const photoUri = 'file://test-photo.jpg';
      const fileName = 'test-photo.jpg';

      const mockFileInfo = {
        exists: true,
        size: 1024000,
        isDirectory: false,
        modificationTime: Date.now(),
        uri: photoUri,
      };

      const mockUploadResult = {
        $id: 'uploaded-file-id',
        name: fileName,
        signature: 'mock-signature',
        mimeType: 'image/jpeg',
        sizeOriginal: 1024000,
        $createdAt: new Date().toISOString(),
        $updatedAt: new Date().toISOString(),
      };

      const mockFileUrl = 'https://cloud.appwrite.io/v1/storage/buckets/scan-images/files/uploaded-file-id/view';

      mockFileSystem.getInfoAsync.mockResolvedValue(mockFileInfo);
      mockStorage.createFile.mockResolvedValue(mockUploadResult);
      mockStorage.getFileView.mockReturnValue(new URL(mockFileUrl));

      // Act
      const result = await cameraService.uploadPhoto(photoUri, fileName);

      // Assert
      expect(mockFileSystem.getInfoAsync).toHaveBeenCalledWith(photoUri);
      expect(mockStorage.createFile).toHaveBeenCalled();
      expect(mockStorage.getFileView).toHaveBeenCalledWith('scan-images', 'uploaded-file-id');
      expect(result).toEqual({
        fileId: 'uploaded-file-id',
        fileUrl: mockFileUrl,
      });
    });

    it('should generate unique filename when not provided', async () => {
      // Arrange
      const photoUri = 'file://test-photo.jpg';

      const mockFileInfo = {
        exists: true,
        size: 1024000,
        isDirectory: false,
        modificationTime: Date.now(),
        uri: photoUri,
      };

      const mockUploadResult = {
        $id: 'uploaded-file-id',
        name: 'scan_123456_mock-unique-id.jpg',
        signature: 'mock-signature',
        mimeType: 'image/jpeg',
        sizeOriginal: 1024000,
        $createdAt: new Date().toISOString(),
        $updatedAt: new Date().toISOString(),
      };

      mockFileSystem.getInfoAsync.mockResolvedValue(mockFileInfo);
      mockStorage.createFile.mockResolvedValue(mockUploadResult);
      mockStorage.getFileView.mockReturnValue(new URL('https://example.com/file'));

      // Act
      await cameraService.uploadPhoto(photoUri);

      // Assert
      expect(mockStorage.createFile).toHaveBeenCalledWith(
        'scan-images',
        'mock-unique-id',
        expect.objectContaining({
          name: expect.stringMatching(/^scan_\d+_mock-unique-id\.jpg$/),
          type: 'image/jpeg',
          size: 1024000,
          uri: photoUri,
        }),
        undefined,
        undefined
      );
    });

    it('should call progress callback during upload', async () => {
      // Arrange
      const photoUri = 'file://test-photo.jpg';
      const onProgress = jest.fn();

      const mockFileInfo = {
        exists: true,
        size: 1024000,
        isDirectory: false,
        modificationTime: Date.now(),
        uri: photoUri,
      };

      const mockUploadResult = {
        $id: 'uploaded-file-id',
        name: 'test.jpg',
        signature: 'mock-signature',
        mimeType: 'image/jpeg',
        sizeOriginal: 1024000,
        $createdAt: new Date().toISOString(),
        $updatedAt: new Date().toISOString(),
      };

      mockFileSystem.getInfoAsync.mockResolvedValue(mockFileInfo);
      mockStorage.createFile.mockImplementation((bucketId, fileId, file, permissions, progressCallback) => {
        // Simulate progress callback
        if (progressCallback) {
          progressCallback({ loaded: 512000, total: 1024000 });
          progressCallback({ loaded: 1024000, total: 1024000 });
        }
        return Promise.resolve(mockUploadResult);
      });
      mockStorage.getFileView.mockReturnValue(new URL('https://example.com/file'));

      // Act
      await cameraService.uploadPhoto(photoUri, 'test.jpg', onProgress);

      // Assert
      expect(onProgress).toHaveBeenCalledWith({
        progress: 50,
        total: 1024000,
        loaded: 512000,
      });
      expect(onProgress).toHaveBeenCalledWith({
        progress: 100,
        total: 1024000,
        loaded: 1024000,
      });
    });

    it('should throw error when file does not exist', async () => {
      // Arrange
      const photoUri = 'file://nonexistent-photo.jpg';

      const mockFileInfo = {
        exists: false,
        size: 0,
        isDirectory: false,
        modificationTime: Date.now(),
        uri: photoUri,
      };

      mockFileSystem.getInfoAsync.mockResolvedValue(mockFileInfo);

      // Act & Assert
      await expect(cameraService.uploadPhoto(photoUri)).rejects.toThrow(
        'Photo file does not exist'
      );
    });

    it('should throw error when upload fails', async () => {
      // Arrange
      const photoUri = 'file://test-photo.jpg';

      const mockFileInfo = {
        exists: true,
        size: 1024000,
        isDirectory: false,
        modificationTime: Date.now(),
        uri: photoUri,
      };

      mockFileSystem.getInfoAsync.mockResolvedValue(mockFileInfo);
      mockStorage.createFile.mockRejectedValue(new Error('Upload failed'));

      // Act & Assert
      await expect(cameraService.uploadPhoto(photoUri)).rejects.toThrow(
        'Failed to upload photo: Upload failed'
      );
    });
  });

  describe('completeScanWorkflow', () => {
    it('should complete full scan workflow successfully', async () => {
      // Arrange
      const userId = 'user-123';
      const scanType = 'document';

      const mockProcessedPhoto = {
        uri: 'file://processed-photo.jpg',
        width: 1920,
        height: 1080,
        fileSize: 1024000,
      };

      const mockUploadResult = {
        fileId: 'uploaded-file-id',
        fileUrl: 'https://example.com/file',
      };

      const mockScanHistory = {
        $id: 'scan-history-id',
        $createdAt: new Date().toISOString(),
        $updatedAt: new Date().toISOString(),
        $permissions: [],
        $collectionId: 'scan_history',
        $databaseId: 'learni-scan-db',
        userId,
        originalImageId: 'uploaded-file-id',
        extractedText: '',
        scanType,
        confidence: 0.95,
        language: 'en',
        metadata: JSON.stringify({
          imageSize: 1024000,
          dimensions: { width: 1920, height: 1080 },
          processingTime: expect.any(Number),
        }),
      };

      // Mock the methods
      jest.spyOn(cameraService, 'capturePhoto').mockResolvedValue(mockProcessedPhoto);
      jest.spyOn(cameraService, 'uploadPhoto').mockResolvedValue(mockUploadResult);
      jest.spyOn(cameraService, 'createScanHistory').mockResolvedValue(mockScanHistory as any);

      const onProgress = jest.fn();

      // Act
      const result = await cameraService.completeScanWorkflow(
        mockCameraRef,
        userId,
        scanType,
        { onProgress }
      );

      // Assert
      expect(cameraService.capturePhoto).toHaveBeenCalledWith(mockCameraRef, undefined);
      expect(cameraService.uploadPhoto).toHaveBeenCalledWith(
        mockProcessedPhoto.uri,
        undefined,
        expect.any(Function)
      );
      expect(cameraService.createScanHistory).toHaveBeenCalledWith({
        userId,
        originalImageId: 'uploaded-file-id',
        scanType,
        language: 'en',
        confidence: 0.95,
        metadata: {
          imageSize: 1024000,
          dimensions: { width: 1920, height: 1080 },
          processingTime: expect.any(Number),
        },
      });

      expect(onProgress).toHaveBeenCalledWith('capturing');
      expect(onProgress).toHaveBeenCalledWith('uploading');
      expect(onProgress).toHaveBeenCalledWith('saving');
      expect(onProgress).toHaveBeenCalledWith('complete');

      expect(result).toEqual({
        photoId: 'uploaded-file-id',
        photoUrl: 'https://example.com/file',
        scanHistoryId: 'scan-history-id',
        processedPhoto: mockProcessedPhoto,
      });
    });

    it('should handle errors in workflow', async () => {
      // Arrange
      const userId = 'user-123';
      jest.spyOn(cameraService, 'capturePhoto').mockRejectedValue(new Error('Capture failed'));

      // Act & Assert
      await expect(
        cameraService.completeScanWorkflow(mockCameraRef, userId)
      ).rejects.toThrow('Capture failed');
    });
  });

  describe('utility methods', () => {
    it('should delete photo successfully', async () => {
      // Arrange
      const fileId = 'file-to-delete';
      mockStorage.deleteFile.mockResolvedValue(undefined);

      // Act
      const result = await cameraService.deletePhoto(fileId);

      // Assert
      expect(mockStorage.deleteFile).toHaveBeenCalledWith('scan-images', fileId);
      expect(result).toBe(true);
    });

    it('should handle delete photo error', async () => {
      // Arrange
      const fileId = 'file-to-delete';
      mockStorage.deleteFile.mockRejectedValue(new Error('Delete failed'));

      // Act
      const result = await cameraService.deletePhoto(fileId);

      // Assert
      expect(result).toBe(false);
    });

    it('should get photo URL', () => {
      // Arrange
      const fileId = 'test-file-id';
      const mockUrl = 'https://example.com/file';
      mockStorage.getFileView.mockReturnValue(new URL(mockUrl));

      // Act
      const result = cameraService.getPhotoUrl(fileId);

      // Assert
      expect(mockStorage.getFileView).toHaveBeenCalledWith('scan-images', fileId);
      expect(result).toBe(mockUrl);
    });

    it('should get photo download URL', () => {
      // Arrange
      const fileId = 'test-file-id';
      const mockUrl = 'https://example.com/download';
      mockStorage.getFileDownload.mockReturnValue(new URL(mockUrl));

      // Act
      const result = cameraService.getPhotoDownloadUrl(fileId);

      // Assert
      expect(mockStorage.getFileDownload).toHaveBeenCalledWith('scan-images', fileId);
      expect(result).toBe(mockUrl);
    });
  });
});

// Integration tests
describe('CameraService Integration', () => {
  let cameraService: CameraService;

  beforeEach(() => {
    cameraService = new CameraService();
  });

  it('should have correct bucket configuration', () => {
    // This test verifies the service is configured with the correct bucket
    expect(cameraService['bucketId']).toBe('scan-images');
  });

  it('should handle concurrent photo captures gracefully', async () => {
    // This test would verify that multiple simultaneous captures are handled properly
    // Implementation would depend on actual concurrency requirements
    expect(true).toBe(true); // Placeholder
  });
});

export {};