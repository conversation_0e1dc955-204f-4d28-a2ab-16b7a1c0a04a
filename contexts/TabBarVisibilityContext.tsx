import React, { createContext, useContext, useState, ReactNode } from 'react';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming,
  runOnJS
} from 'react-native-reanimated';

interface TabBarVisibilityContextType {
  isVisible: boolean;
  setIsVisible: (visible: boolean) => void;
  hideTabBar: () => void;
  showTabBar: () => void;
  tabBarOpacity: Animated.SharedValue<number>;
  tabBarTranslateY: Animated.SharedValue<number>;
}

const TabBarVisibilityContext = createContext<TabBarVisibilityContextType | undefined>(undefined);

interface TabBarVisibilityProviderProps {
  children: ReactNode;
}

export const TabBarVisibilityProvider: React.FC<TabBarVisibilityProviderProps> = ({ children }) => {
  const [isVisible, setIsVisible] = useState(true);
  const tabBarOpacity = useSharedValue(1);
  const tabBarTranslateY = useSharedValue(0);

  const hideTabBar = () => {
    tabBarOpacity.value = withTiming(0, { duration: 300 });
    tabBarTranslateY.value = withTiming(100, { duration: 300 }, () => {
      runOnJS(setIsVisible)(false);
    });
  };

  const showTabBar = () => {
    runOnJS(setIsVisible)(true);
    tabBarOpacity.value = withTiming(1, { duration: 300 });
    tabBarTranslateY.value = withTiming(0, { duration: 300 });
  };

  const value: TabBarVisibilityContextType = {
    isVisible,
    setIsVisible,
    hideTabBar,
    showTabBar,
    tabBarOpacity,
    tabBarTranslateY,
  };

  return (
    <TabBarVisibilityContext.Provider value={value}>
      {children}
    </TabBarVisibilityContext.Provider>
  );
};

export const useTabBarVisibility = (): TabBarVisibilityContextType => {
  const context = useContext(TabBarVisibilityContext);
  if (context === undefined) {
    throw new Error('useTabBarVisibility must be used within a TabBarVisibilityProvider');
  }
  return context;
};
