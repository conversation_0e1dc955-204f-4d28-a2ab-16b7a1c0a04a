import React, { createContext, useContext, useState, ReactNode, useRef, useCallback, useEffect } from 'react';
import { NativeScrollEvent, NativeSyntheticEvent, AppState, AppStateStatus } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS
} from 'react-native-reanimated';

interface TabBarVisibilityContextType {
  isVisible: boolean;
  setIsVisible: (visible: boolean) => void;
  hideTabBar: () => void;
  showTabBar: () => void;
  tabBarOpacity: Animated.SharedValue<number>;
  tabBarTranslateY: Animated.SharedValue<number>;
  // Auto-hide functionality
  autoHideEnabled: boolean;
  setAutoHideEnabled: (enabled: boolean) => void;
  handleScroll: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  // Guaranteed restoration
  guaranteeTabBarVisible: () => void;
  registerHideRequest: (screenName: string) => void;
  unregisterHideRequest: (screenName: string) => void;
}

const TabBarVisibilityContext = createContext<TabBarVisibilityContextType | undefined>(undefined);

interface TabBarVisibilityProviderProps {
  children: ReactNode;
}

export const TabBarVisibilityProvider: React.FC<TabBarVisibilityProviderProps> = ({ children }) => {
  const [isVisible, setIsVisible] = useState(true);
  const [autoHideEnabled, setAutoHideEnabled] = useState(true);
  const tabBarOpacity = useSharedValue(1);
  const tabBarTranslateY = useSharedValue(0);

  // Guaranteed restoration mechanism
  const hideRequestsRef = useRef<Set<string>>(new Set());
  const restorationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Scroll tracking for auto-hide
  const lastScrollY = useRef(0);
  const scrollThreshold = 5; // Minimum scroll distance to trigger hide/show

  const hideTabBar = () => {
    tabBarOpacity.value = withTiming(0, { duration: 300 });
    tabBarTranslateY.value = withTiming(100, { duration: 300 }, () => {
      runOnJS(setIsVisible)(false);
    });
  };

  const showTabBar = () => {
    runOnJS(setIsVisible)(true);
    tabBarOpacity.value = withTiming(1, { duration: 300 });
    tabBarTranslateY.value = withTiming(0, { duration: 300 });
  };

  // Handle scroll events for auto-hide functionality
  const handleScroll = useCallback((event: NativeSyntheticEvent<NativeScrollEvent>) => {
    if (!autoHideEnabled) return;

    const currentScrollY = event.nativeEvent.contentOffset.y;
    const scrollDelta = currentScrollY - lastScrollY.current;

    // Only trigger hide/show if scroll delta is significant
    if (Math.abs(scrollDelta) > scrollThreshold) {
      if (scrollDelta < 0) {
        // Scrolling UP (content moving down) - hide tab bar (user wants to focus on content)
        hideTabBar();
      } else if (scrollDelta > 0 && currentScrollY > 50) {
        // Scrolling DOWN (content moving up) and past threshold - show tab bar (user might want to navigate)
        showTabBar();
      }
    }

    lastScrollY.current = currentScrollY;
  }, [autoHideEnabled, scrollThreshold]);

  const value: TabBarVisibilityContextType = {
    isVisible,
    setIsVisible,
    hideTabBar,
    showTabBar,
    tabBarOpacity,
    tabBarTranslateY,
    autoHideEnabled,
    setAutoHideEnabled,
    handleScroll,
  };

  return (
    <TabBarVisibilityContext.Provider value={value}>
      {children}
    </TabBarVisibilityContext.Provider>
  );
};

export const useTabBarVisibility = (): TabBarVisibilityContextType => {
  const context = useContext(TabBarVisibilityContext);
  if (context === undefined) {
    throw new Error('useTabBarVisibility must be used within a TabBarVisibilityProvider');
  }
  return context;
};
