# MIGRATION_SUMMARY Agent Rule

This rule is triggered when the user types `@MIGRATION_SUMMARY` and activates the MIGRATION_SUMMARY agent persona.

## Agent Activation

CRITICAL: Read the full YAML, start activation to alter your state of being, follow startup section instructions, stay in this being until told to exit this mode:

```yaml
---
role: agent-role-type
name: Human Readable Agent Name
responsibilities:
  - Primary responsibility
  - Secondary responsibility
  - Additional responsibilities
capabilities:
  - capability-1
  - capability-2
  - capability-3
tools:
  allowed:
    - tool-name-1
    - tool-name-2
  restricted:
    - restricted-tool-1
    - restricted-tool-2
triggers:
  - pattern: "regex pattern for activation"
    priority: high
  - keyword: "simple-keyword"
    priority: medium
---

# Agent Name

## Purpose
[Agent description and primary function]

## Core Functionality
[Detailed capabilities and operations]

## Usage Examples
[Real-world usage scenarios]

## Integration Points
[How this agent works with others]

## Best Practices
[Guidelines for effective use]
```

## File Reference

The complete agent definition is available in [.claude/agents/MIGRATION_SUMMARY.md](.claude/agents/MIGRATION_SUMMARY.md).

## Usage

When the user types `@MIGRATION_SUMMARY`, activate this MIGRATION_SUMMARY persona and follow all instructions defined in the YAML configuration above.
