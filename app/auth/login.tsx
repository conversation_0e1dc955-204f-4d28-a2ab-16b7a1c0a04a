/**
 * Login Screen Component
 *
 * Professional login screen with email/password authentication,
 * OAuth integration, and modern glass morphism design.
 */

import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React, { useState } from "react";
import { Alert, StatusBar, StyleSheet } from "react-native";
import Animated, { FadeInDown, FadeInUp } from "react-native-reanimated";
import { useSafeAreaInsets } from "react-native-safe-area-context";

// Import UI components following Rule #6
import { Box } from "@/components/ui/box";
import { Button, ButtonText } from "@/components/ui/button";
import { HStack } from "@/components/ui/hstack";
import { Input, InputField } from "@/components/ui/input";
import { Pressable } from "@/components/ui/pressable";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";

// Import authentication context
import { useAuth } from "@/lib/contexts/AuthContext";

export default function LoginScreen() {
  const insets = useSafeAreaInsets();
  const { signIn, createAnonymousSession } = useAuth();

  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Form validation
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const isFormValid = email.trim() !== '' && password.trim() !== '' && isValidEmail(email);

  // Handle login
  const handleLogin = async () => {
    if (!isFormValid) {
      Alert.alert('Invalid Input', 'Please enter a valid email and password.');
      return;
    }

    try {
      setIsLoading(true);
      console.log('🔐 Attempting login with email:', email);

      await signIn(email.trim(), password);

      console.log('✅ Login successful, redirecting to home...');
      router.replace('/(tabs)/home');

    } catch (error: any) {
      console.error('❌ Login failed:', error);
      Alert.alert(
        'Login Failed',
        error.message || 'Invalid email or password. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OAuth login
  const handleOAuthLogin = async (provider: 'auth0' | 'apple') => {
    try {
      setIsLoading(true);
      console.log(`🔐 Attempting ${provider} OAuth login...`);

      // TODO: Implement OAuth login
      Alert.alert(
        'OAuth Login',
        `${provider} login will be implemented in the next phase.`,
        [{ text: 'OK' }]
      );

    } catch (error: any) {
      console.error(`❌ ${provider} OAuth login failed:`, error);
      Alert.alert('OAuth Login Failed', error.message || 'Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle guest login
  const handleGuestLogin = async () => {
    try {
      setIsLoading(true);
      console.log('🔐 Creating anonymous session...');

      await createAnonymousSession();
      console.log('✅ Anonymous session created successfully');

      // Navigate to home screen
      router.replace('/(tabs)/home');

    } catch (error: any) {
      console.error('❌ Guest login failed:', error);
      Alert.alert('Guest Login Failed', error.message || 'Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box className="flex-1">
      <StatusBar barStyle="light-content" />

      {/* Candy Gradient Background */}
      <LinearGradient
        colors={['#FF6B9D', '#A855F7', '#3B82F6']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={StyleSheet.absoluteFill}
      />

      {/* Content */}
      <Box className="flex-1" style={{ paddingTop: insets.top }}>
        <VStack className="flex-1 px-6">
          {/* Header */}
          <Animated.View entering={FadeInUp.delay(200)}>
            <HStack className="items-center justify-between mt-4 mb-8">
              <Pressable onPress={() => router.back()}>
                <Box className="w-10 h-10 bg-white/20 rounded-full items-center justify-center">
                  <MaterialIcons name="arrow-back" size={24} color="white" />
                </Box>
              </Pressable>

              <Text size="lg" className="font-semibold text-white">
                Welcome Back
              </Text>

              <Box className="w-10" />
            </HStack>
          </Animated.View>

          {/* Login Form */}
          <Animated.View entering={FadeInDown.delay(400)} className="flex-1 justify-center">
            <Box className="bg-white/10 backdrop-blur-md rounded-3xl p-6 border border-white/20">
              <VStack space="lg">
                {/* Title */}
                <VStack space="sm" className="items-center mb-4">
                  <Text size="2xl" className="font-bold text-white">
                    Sign In
                  </Text>
                  <Text size="sm" className="text-white/80 text-center">
                    Enter your credentials to access your account
                  </Text>
                </VStack>

                {/* Email Input */}
                <VStack space="xs">
                  <Text size="sm" className="font-medium text-white/90">
                    Email Address
                  </Text>
                  <Input
                    variant="outline"
                    size="lg"
                    className="bg-white/20 border-white/30"
                  >
                    <InputField
                      placeholder="Enter your email"
                      placeholderTextColor="rgba(255,255,255,0.6)"
                      value={email}
                      onChangeText={setEmail}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      autoCorrect={false}
                      className="text-white"
                    />
                  </Input>
                </VStack>

                {/* Password Input */}
                <VStack space="xs">
                  <Text size="sm" className="font-medium text-white/90">
                    Password
                  </Text>
                  <Input
                    variant="outline"
                    size="lg"
                    className="bg-white/20 border-white/30"
                  >
                    <InputField
                      placeholder="Enter your password"
                      placeholderTextColor="rgba(255,255,255,0.6)"
                      value={password}
                      onChangeText={setPassword}
                      secureTextEntry={!showPassword}
                      className="text-white flex-1"
                    />
                    <Pressable
                      onPress={() => setShowPassword(!showPassword)}
                      className="p-2"
                    >
                      <MaterialIcons
                        name={showPassword ? 'visibility-off' : 'visibility'}
                        size={20}
                        color="rgba(255,255,255,0.8)"
                      />
                    </Pressable>
                  </Input>
                </VStack>

                {/* Forgot Password */}
                <Pressable onPress={() => Alert.alert('Forgot Password', 'Password reset will be implemented soon.')}>
                  <Text size="sm" className="text-white/80 text-right">
                    Forgot Password?
                  </Text>
                </Pressable>

                {/* Login Button */}
                <Button
                  action="candyPink"
                  size="lg"
                  onPress={handleLogin}
                  disabled={!isFormValid || isLoading}
                  className="mt-4"
                >
                  <ButtonText className="font-semibold">
                    {isLoading ? 'Signing In...' : 'Sign In'}
                  </ButtonText>
                </Button>

                {/* Divider */}
                <HStack className="items-center my-4">
                  <Box className="flex-1 h-px bg-white/30" />
                  <Text size="sm" className="text-white/60 mx-4">
                    or continue with
                  </Text>
                  <Box className="flex-1 h-px bg-white/30" />
                </HStack>

                {/* OAuth Buttons */}
                <VStack space="sm">
                  <Button
                    action="glass"
                    size="lg"
                    onPress={() => handleOAuthLogin('auth0')}
                    disabled={isLoading}
                  >
                    <HStack space="sm" className="items-center">
                      <MaterialIcons name="login" size={20} color="white" />
                      <ButtonText className="text-white">Continue with Auth0</ButtonText>
                    </HStack>
                  </Button>

                  <Button
                    action="glass"
                    size="lg"
                    onPress={() => handleOAuthLogin('apple')}
                    disabled={isLoading}
                  >
                    <HStack space="sm" className="items-center">
                      <MaterialIcons name="apple" size={20} color="white" />
                      <ButtonText className="text-white">Continue with Apple</ButtonText>
                    </HStack>
                  </Button>
                </VStack>

                {/* Sign Up Link */}
                <HStack className="items-center justify-center mt-6">
                  <Text size="sm" className="text-white/80">
                    Don't have an account?{' '}
                  </Text>
                  <Pressable onPress={() => router.push('/auth/signup')}>
                    <Text size="sm" className="text-white font-semibold">
                      Sign Up
                    </Text>
                  </Pressable>
                </HStack>
              </VStack>
            </Box>
          </Animated.View>

          {/* Guest Mode */}
          <Animated.View entering={FadeInUp.delay(600)}>
            <Pressable
              onPress={handleGuestLogin}
              className="items-center py-4"
            >
              <Text size="sm" className="text-white/80">
                {isLoading ? 'Signing in as Guest...' : 'Continue as Guest'}
              </Text>
            </Pressable>
          </Animated.View>
        </VStack>
      </Box>
    </Box>
  );
}
