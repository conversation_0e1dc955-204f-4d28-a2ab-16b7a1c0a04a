/**
 * Signup Screen Component
 *
 * Professional signup screen with user registration,
 * OAuth integration, and modern glass morphism design.
 */

import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import { CheckIcon } from "lucide-react-native";
import React, { useState } from "react";
import { Alert, StatusBar, StyleSheet } from "react-native";
import Animated, { FadeInDown, FadeInUp } from "react-native-reanimated";
import { useSafeAreaInsets } from "react-native-safe-area-context";
// Import UI components following Rule #6
import { Box } from "@/components/ui/box";
import { Button, ButtonText } from "@/components/ui/button";
import {
	Checkbox,
	CheckboxIcon,
	CheckboxIndicator,
	CheckboxLabel,
} from "@/components/ui/checkbox";
import { HStack } from "@/components/ui/hstack";
import { Input, InputField } from "@/components/ui/input";
import { Pressable } from "@/components/ui/pressable";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";

// Import authentication context
import { useAuth } from "@/lib/contexts/AuthContext";

export default function SignupScreen() {
  const insets = useSafeAreaInsets();
  const { signUp } = useAuth();

  // Form state
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);

  // Form validation
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const isValidPassword = (password: string) => {
    return password.length >= 8;
  };

  const passwordsMatch = password === confirmPassword;

  const isFormValid =
    name.trim() !== '' &&
    email.trim() !== '' &&
    password.trim() !== '' &&
    confirmPassword.trim() !== '' &&
    isValidEmail(email) &&
    isValidPassword(password) &&
    passwordsMatch &&
    acceptTerms;

  // Handle signup
  const handleSignup = async () => {
    if (!isFormValid) {
      if (!isValidEmail(email)) {
        Alert.alert('Invalid Email', 'Please enter a valid email address.');
        return;
      }
      if (!isValidPassword(password)) {
        Alert.alert('Invalid Password', 'Password must be at least 8 characters long.');
        return;
      }
      if (!passwordsMatch) {
        Alert.alert('Password Mismatch', 'Passwords do not match.');
        return;
      }
      if (!acceptTerms) {
        Alert.alert('Terms Required', 'Please accept the Terms of Service and Privacy Policy.');
        return;
      }
      Alert.alert('Invalid Input', 'Please fill in all required fields.');
      return;
    }

    try {
      setIsLoading(true);
      console.log('🔐 Attempting signup with email:', email);

      await signUp(email.trim(), password, name.trim());

      console.log('✅ Signup successful, redirecting to home...');
      Alert.alert(
        'Account Created!',
        'Your account has been created successfully. Welcome to LearniScan!',
        [
          {
            text: 'Get Started',
            onPress: () => router.replace('/(tabs)/home')
          }
        ]
      );

    } catch (error: any) {
      console.error('❌ Signup failed:', error);
      Alert.alert(
        'Signup Failed',
        error.message || 'Failed to create account. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OAuth signup
  const handleOAuthSignup = async (provider: 'auth0' | 'apple') => {
    try {
      setIsLoading(true);
      console.log(`🔐 Attempting ${provider} OAuth signup...`);

      // TODO: Implement OAuth signup
      Alert.alert(
        'OAuth Signup',
        `${provider} signup will be implemented in the next phase.`,
        [{ text: 'OK' }]
      );

    } catch (error: any) {
      console.error(`❌ ${provider} OAuth signup failed:`, error);
      Alert.alert('OAuth Signup Failed', error.message || 'Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box className="flex-1">
      <StatusBar barStyle="light-content" />

      {/* Candy Gradient Background */}
      <LinearGradient
        colors={['#FF6B9D', '#A855F7', '#3B82F6']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={StyleSheet.absoluteFill}
      />

      {/* Content */}
      <Box className="flex-1" style={{ paddingTop: insets.top }}>
        <VStack className="flex-1 px-6">
          {/* Header */}
          <Animated.View entering={FadeInUp.delay(200)}>
            <HStack className="items-center justify-between mt-4 mb-8">
              <Pressable onPress={() => router.back()}>
                <Box className="w-10 h-10 bg-white/20 rounded-full items-center justify-center">
                  <MaterialIcons name="arrow-back" size={24} color="white" />
                </Box>
              </Pressable>

              <Text size="lg" className="font-semibold text-white">
                Create Account
              </Text>

              <Box className="w-10" />
            </HStack>
          </Animated.View>

          {/* Signup Form */}
          <Animated.View entering={FadeInDown.delay(400)} className="flex-1 justify-center">
            <Box className="bg-white/10 backdrop-blur-md rounded-3xl p-6 border border-white/20">
              <VStack space="lg">
                {/* Title */}
                <VStack space="sm" className="items-center mb-4">
                  <Text size="2xl" className="font-bold text-white">
                    Join LearniScan
                  </Text>
                  <Text size="sm" className="text-white/80 text-center">
                    Create your account to unlock all features
                  </Text>
                </VStack>

                {/* Name Input */}
                <VStack space="xs">
                  <Text size="sm" className="font-medium text-white/90">
                    Full Name
                  </Text>
                  <Input
                    variant="outline"
                    size="lg"
                    className="bg-white/20 border-white/30"
                  >
                    <InputField
                      placeholder="Enter your full name"
                      placeholderTextColor="rgba(255,255,255,0.6)"
                      value={name}
                      onChangeText={setName}
                      autoCapitalize="words"
                      autoCorrect={false}
                      className="text-white"
                    />
                  </Input>
                </VStack>

                {/* Email Input */}
                <VStack space="xs">
                  <Text size="sm" className="font-medium text-white/90">
                    Email Address
                  </Text>
                  <Input
                    variant="outline"
                    size="lg"
                    className="bg-white/20 border-white/30"
                  >
                    <InputField
                      placeholder="Enter your email"
                      placeholderTextColor="rgba(255,255,255,0.6)"
                      value={email}
                      onChangeText={setEmail}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      autoCorrect={false}
                      className="text-white"
                    />
                  </Input>
                </VStack>

                {/* Password Input */}
                <VStack space="xs">
                  <Text size="sm" className="font-medium text-white/90">
                    Password
                  </Text>
                  <Input
                    variant="outline"
                    size="lg"
                    className="bg-white/20 border-white/30"
                  >
                    <InputField
                      placeholder="Create a password (8+ characters)"
                      placeholderTextColor="rgba(255,255,255,0.6)"
                      value={password}
                      onChangeText={setPassword}
                      secureTextEntry={!showPassword}
                      className="text-white flex-1"
                    />
                    <Pressable
                      onPress={() => setShowPassword(!showPassword)}
                      className="p-2"
                    >
                      <MaterialIcons
                        name={showPassword ? 'visibility-off' : 'visibility'}
                        size={20}
                        color="rgba(255,255,255,0.8)"
                      />
                    </Pressable>
                  </Input>
                </VStack>

                {/* Confirm Password Input */}
                <VStack space="xs">
                  <Text size="sm" className="font-medium text-white/90">
                    Confirm Password
                  </Text>
                  <Input
                    variant="outline"
                    size="lg"
                    className="bg-white/20 border-white/30"
                  >
                    <InputField
                      placeholder="Confirm your password"
                      placeholderTextColor="rgba(255,255,255,0.6)"
                      value={confirmPassword}
                      onChangeText={setConfirmPassword}
                      secureTextEntry={!showConfirmPassword}
                      className="text-white flex-1"
                    />
                    <Pressable
                      onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="p-2"
                    >
                      <MaterialIcons
                        name={showConfirmPassword ? 'visibility-off' : 'visibility'}
                        size={20}
                        color="rgba(255,255,255,0.8)"
                      />
                    </Pressable>
                  </Input>
                </VStack>

                {/* Terms and Conditions */}
                <HStack space="sm" className="items-start">
                  <Checkbox
                    value={acceptTerms}
                    onValueChange={setAcceptTerms}
                    className="mt-1"
                  >
                    <CheckboxIndicator className="border-white/50">
                      <CheckboxIcon as={CheckIcon} className="text-white" />
                    </CheckboxIndicator>
                  </Checkbox>
                  <VStack className="flex-1">
                    <Text size="sm" className="text-white/90 leading-5">
                      I agree to the{' '}
                      <Text className="text-white font-semibold">Terms of Service</Text>
                      {' '}and{' '}
                      <Text className="text-white font-semibold">Privacy Policy</Text>
                    </Text>
                  </VStack>
                </HStack>

                {/* Signup Button */}
                <Button
                  action="candyPink"
                  size="lg"
                  onPress={handleSignup}
                  disabled={!isFormValid || isLoading}
                  className="mt-4"
                >
                  <ButtonText className="font-semibold">
                    {isLoading ? 'Creating Account...' : 'Create Account'}
                  </ButtonText>
                </Button>

                {/* Divider */}
                <HStack className="items-center my-4">
                  <Box className="flex-1 h-px bg-white/30" />
                  <Text size="sm" className="text-white/60 mx-4">
                    or sign up with
                  </Text>
                  <Box className="flex-1 h-px bg-white/30" />
                </HStack>

                {/* OAuth Buttons */}
                <VStack space="sm">
                  <Button
                    action="glass"
                    size="lg"
                    onPress={() => handleOAuthSignup('auth0')}
                    disabled={isLoading}
                  >
                    <HStack space="sm" className="items-center">
                      <MaterialIcons name="login" size={20} color="white" />
                      <ButtonText className="text-white">Continue with Auth0</ButtonText>
                    </HStack>
                  </Button>

                  <Button
                    action="glass"
                    size="lg"
                    onPress={() => handleOAuthSignup('apple')}
                    disabled={isLoading}
                  >
                    <HStack space="sm" className="items-center">
                      <MaterialIcons name="apple" size={20} color="white" />
                      <ButtonText className="text-white">Continue with Apple</ButtonText>
                    </HStack>
                  </Button>
                </VStack>

                {/* Sign In Link */}
                <HStack className="items-center justify-center mt-6">
                  <Text size="sm" className="text-white/80">
                    Already have an account?{' '}
                  </Text>
                  <Pressable onPress={() => router.push('/auth/login')}>
                    <Text size="sm" className="text-white font-semibold">
                      Sign In
                    </Text>
                  </Pressable>
                </HStack>
              </VStack>
            </Box>
          </Animated.View>

          {/* Guest Mode */}
          <Animated.View entering={FadeInUp.delay(600)}>
            <Pressable
              onPress={() => router.replace('/(tabs)/home')}
              className="items-center py-4"
            >
              <Text size="sm" className="text-white/80">
                Continue as Guest
              </Text>
            </Pressable>
          </Animated.View>
        </VStack>
      </Box>
    </Box>
  );
}
