/**
 * AI Study Session Screen
 * 
 * Provides AI-powered study sessions with:
 * - Personalized card recommendations
 * - Spaced repetition optimization
 * - Real-time progress tracking
 * - Adaptive difficulty adjustment
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 6: UI Design Consistency with Gluestack UI
 * - Rule 8: Smooth animations with react-native-reanimated
 * - Rule 9: Safe Area Management
 * - Rule 11: Zod validation for all state
 * - Rule 1: Interactive feedback protocol
 */

import React, { useState, useEffect } from 'react';
import { Alert, Dimensions } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { z } from 'zod';

// Icons
import {
  ArrowLeft,
  Brain,
  Clock,
  Target,
  TrendingUp,
  CheckCircle,
  XCircle,
  RotateCcw,
  <PERSON>rk<PERSON>,
  Award,
} from 'lucide-react-native';

// UI Components following Rule #6
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';
import { Progress, ProgressFilledTrack } from '@/components/ui/progress';
import { Card, CardHeader, CardBody } from '@/components/ui/card';
import { Heading } from '@/components/ui/heading';
import { Badge, BadgeText } from '@/components/ui/badge';

// Services
import { useAuth } from '@/lib/contexts/AuthContext';
import { 
  aiStudySessionService,
  type StudySessionInput,
  type StudyRecommendation,
  type StudyMetrics,
  type StudyProgress,
} from '@/lib/services/ai-study-session.service';

// State validation with Zod (Rule 11)
const StudySessionStateSchema = z.object({
  phase: z.enum(['setup', 'loading', 'studying', 'complete', 'error']),
  currentCardIndex: z.number().min(0),
  totalCards: z.number().min(0),
  sessionStartTime: z.string().optional(),
  sessionId: z.string().optional(),
  accuracy: z.number().min(0).max(1),
  responseTime: z.number().min(0),
  confidence: z.number().min(1).max(5),
});

type StudySessionState = z.infer<typeof StudySessionStateSchema>;

const { width: screenWidth } = Dimensions.get('window');

export default function StudySessionScreen() {
  const { user } = useAuth();
  const params = useLocalSearchParams();
  
  // State management with Zod validation (Rule 11)
  const [state, setState] = useState<StudySessionState>({
    phase: 'setup',
    currentCardIndex: 0,
    totalCards: 0,
    accuracy: 0,
    responseTime: 0,
    confidence: 3,
  });

  const [recommendation, setRecommendation] = useState<StudyRecommendation | null>(null);
  const [studyProgress, setStudyProgress] = useState<StudyProgress | null>(null);
  const [sessionMetrics, setSessionMetrics] = useState<StudyMetrics[]>([]);
  const [currentCard, setCurrentCard] = useState<any>(null);

  // Animation values (Rule 8)
  const progressValue = useSharedValue(0);
  const cardScale = useSharedValue(1);
  const confidenceScale = useSharedValue(1);

  // Early return if no user (Rule 12: Error handling)
  if (!user) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <Box flex={1} justifyContent="center" alignItems="center" p="$4">
          <Text>Please sign in to start a study session.</Text>
        </Box>
      </SafeAreaView>
    );
  }

  useEffect(() => {
    initializeStudySession();
  }, []);

  const initializeStudySession = async () => {
    try {
      setState(prev => ({ ...prev, phase: 'loading' }));

      // Parse session parameters
      const sessionInput: StudySessionInput = {
        userId: user.$id,
        sessionType: (params.type as any) || 'review',
        targetDuration: parseInt(params.duration as string) || 20,
        difficulty: params.difficulty as any,
        categories: params.categories ? JSON.parse(params.categories as string) : undefined,
        tags: params.tags ? JSON.parse(params.tags as string) : undefined,
        maxCards: parseInt(params.maxCards as string) || 10,
      };

      // Generate AI-powered study session
      const sessionRecommendation = await aiStudySessionService.generateStudySession(
        sessionInput,
        (progress) => {
          setStudyProgress(progress);
        }
      );

      setRecommendation(sessionRecommendation);
      setState(prev => ({
        ...prev,
        phase: 'studying',
        totalCards: sessionRecommendation.cards.length,
        sessionStartTime: new Date().toISOString(),
        sessionId: `session_${Date.now()}`,
      }));

      // Load first card
      if (sessionRecommendation.cards.length > 0) {
        loadCard(0);
      }

    } catch (error) {
      console.error('Study session initialization error:', error);
      setState(prev => ({ ...prev, phase: 'error' }));
      Alert.alert(
        'Session Error',
        'Failed to initialize study session. Please try again.',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    }
  };

  const loadCard = async (index: number) => {
    if (!recommendation || index >= recommendation.cards.length) return;

    const cardInfo = recommendation.cards[index];
    // In a full implementation, fetch the actual card data
    setCurrentCard({
      id: cardInfo.cardId,
      title: `Study Card ${index + 1}`,
      content: 'Card content would be loaded here...',
      difficulty: cardInfo.estimatedDifficulty,
    });

    // Animate card entrance
    cardScale.value = withSpring(1, { damping: 15, stiffness: 150 });
    progressValue.value = withTiming((index + 1) / recommendation.cards.length);
  };

  const handleCardResponse = async (correct: boolean, confidence: number) => {
    if (!currentCard || !state.sessionId) return;

    const responseTime = Date.now() - (state.sessionStartTime ? new Date(state.sessionStartTime).getTime() : Date.now());
    
    // Record metrics
    const metrics: StudyMetrics = {
      cardId: currentCard.id,
      userId: user.$id,
      difficulty: currentCard.difficulty,
      responseTime,
      accuracy: correct ? 1 : 0,
      confidence,
      timestamp: new Date().toISOString(),
      sessionId: state.sessionId,
    };

    setSessionMetrics(prev => [...prev, metrics]);

    // Update spaced repetition data
    try {
      await aiStudySessionService.updateSpacedRepetition(metrics);
    } catch (error) {
      console.error('Failed to update spaced repetition:', error);
    }

    // Animate confidence selection
    confidenceScale.value = withSpring(1.2, { damping: 10 }, () => {
      confidenceScale.value = withSpring(1);
    });

    // Move to next card or complete session
    const nextIndex = state.currentCardIndex + 1;
    if (nextIndex < state.totalCards) {
      setState(prev => ({ ...prev, currentCardIndex: nextIndex }));
      loadCard(nextIndex);
    } else {
      completeSession();
    }
  };

  const completeSession = async () => {
    try {
      setState(prev => ({ ...prev, phase: 'complete' }));

      // Analyze session performance
      if (sessionMetrics.length > 0 && state.sessionStartTime && state.sessionId) {
        const sessionResult = await aiStudySessionService.analyzeStudySession(
          sessionMetrics,
          {
            sessionId: state.sessionId,
            userId: user.$id,
            sessionType: (params.type as string) || 'review',
            startTime: state.sessionStartTime,
            endTime: new Date().toISOString(),
            duration: Date.now() - new Date(state.sessionStartTime).getTime(),
            cardsStudied: sessionMetrics.length,
          }
        );

        // Show completion with recommendations
        Alert.alert(
          'Session Complete! 🎉',
          `Great job! You studied ${sessionMetrics.length} cards.\n\nNext: ${sessionResult.recommendations.nextSessionType}\nSuggested break: ${sessionResult.recommendations.suggestedBreakTime} minutes`,
          [
            { text: 'View Results', onPress: () => router.push('/study/results') },
            { text: 'Done', onPress: () => router.back() },
          ]
        );
      }
    } catch (error) {
      console.error('Session completion error:', error);
      Alert.alert('Session Complete', 'Study session finished successfully!');
    }
  };

  // Animated styles (Rule 8)
  const progressAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scaleX: progressValue.value }],
  }));

  const cardAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: cardScale.value }],
  }));

  const confidenceAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: confidenceScale.value }],
  }));

  // Render different phases
  if (state.phase === 'loading') {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <LinearGradient
          colors={['#f0f9ff', '#e0f2fe', '#bae6fd']}
          style={{ flex: 1 }}
        >
          <VStack flex={1} justifyContent="center" alignItems="center" p="$4" space="lg">
            <Brain size={64} color="#0ea5e9" />
            <VStack alignItems="center" space="md">
              <Heading size="xl" textAlign="center">
                Preparing Your Study Session
              </Heading>
              <Text size="md" color="$neutral600" textAlign="center">
                {studyProgress?.message || 'Analyzing your learning progress...'}
              </Text>
            </VStack>
            
            {studyProgress && (
              <VStack w="$full" maxWidth={300} space="sm">
                <Progress value={studyProgress.progress} size="md">
                  <ProgressFilledTrack />
                </Progress>
                <Text size="sm" color="$neutral500" textAlign="center">
                  {studyProgress.progress}% complete
                </Text>
              </VStack>
            )}
          </VStack>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  if (state.phase === 'studying' && currentCard) {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <LinearGradient
          colors={['#fdf4ff', '#fae8ff', '#f3e8ff']}
          style={{ flex: 1 }}
        >
          {/* Header */}
          <HStack alignItems="center" justifyContent="space-between" p="$4">
            <Button variant="ghost" size="sm" onPress={() => router.back()}>
              <ButtonIcon as={ArrowLeft} />
            </Button>
            
            <VStack alignItems="center" space="xs">
              <Text size="sm" color="$neutral600">
                Card {state.currentCardIndex + 1} of {state.totalCards}
              </Text>
              <Progress value={(state.currentCardIndex + 1) / state.totalCards * 100} size="sm" w={120}>
                <ProgressFilledTrack />
              </Progress>
            </VStack>

            <HStack space="xs">
              <Badge variant="outline" action="candyPurple">
                <BadgeText>{Math.round((state.currentCardIndex + 1) / state.totalCards * 100)}%</BadgeText>
              </Badge>
            </HStack>
          </HStack>

          {/* Study Card */}
          <VStack flex={1} p="$4" space="lg">
            <Animated.View style={[{ flex: 1 }, cardAnimatedStyle]}>
              <Card variant="glass" style={{ flex: 1 }}>
                <CardHeader>
                  <HStack alignItems="center" space="md">
                    <Sparkles size={24} color="#a855f7" />
                    <VStack flex={1}>
                      <Heading size="lg">{currentCard.title}</Heading>
                      <Text size="sm" color="$neutral600">
                        Estimated difficulty: {Math.round(currentCard.difficulty * 100)}%
                      </Text>
                    </VStack>
                  </HStack>
                </CardHeader>
                
                <CardBody>
                  <VStack space="lg" flex={1}>
                    <Text size="md" lineHeight="$lg">
                      {currentCard.content}
                    </Text>
                    
                    {/* Study actions would go here */}
                    <VStack space="md" mt="auto">
                      <Text size="sm" fontWeight="$semibold" color="$neutral700">
                        How well did you understand this?
                      </Text>
                      
                      <HStack space="md" justifyContent="center">
                        <Button
                          action="candyPink"
                          variant="outline"
                          size="lg"
                          onPress={() => handleCardResponse(false, 2)}
                        >
                          <ButtonIcon as={XCircle} />
                          <ButtonText>Need Practice</ButtonText>
                        </Button>
                        
                        <Button
                          action="candyBlue"
                          size="lg"
                          onPress={() => handleCardResponse(true, 4)}
                        >
                          <ButtonIcon as={CheckCircle} />
                          <ButtonText>Got It!</ButtonText>
                        </Button>
                      </HStack>
                    </VStack>
                  </VStack>
                </CardBody>
              </Card>
            </Animated.View>
          </VStack>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  if (state.phase === 'complete') {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <LinearGradient
          colors={['#f0fdf4', '#dcfce7', '#bbf7d0']}
          style={{ flex: 1 }}
        >
          <VStack flex={1} justifyContent="center" alignItems="center" p="$4" space="xl">
            <Award size={80} color="#22c55e" />
            
            <VStack alignItems="center" space="md">
              <Heading size="2xl" textAlign="center">
                Session Complete! 🎉
              </Heading>
              <Text size="lg" color="$neutral600" textAlign="center">
                Great job studying {sessionMetrics.length} cards!
              </Text>
            </VStack>

            <VStack w="$full" maxWidth={300} space="md">
              <HStack justifyContent="space-between" alignItems="center">
                <Text>Accuracy:</Text>
                <Badge action="success">
                  <BadgeText>
                    {Math.round((sessionMetrics.reduce((sum, m) => sum + m.accuracy, 0) / sessionMetrics.length) * 100)}%
                  </BadgeText>
                </Badge>
              </HStack>
              
              <HStack justifyContent="space-between" alignItems="center">
                <Text>Cards Studied:</Text>
                <Badge action="candyBlue">
                  <BadgeText>{sessionMetrics.length}</BadgeText>
                </Badge>
              </HStack>
            </VStack>

            <VStack w="$full" maxWidth={300} space="md">
              <Button
                action="candyPurple"
                size="lg"
                onPress={() => router.push('/study/results')}
              >
                <ButtonIcon as={TrendingUp} />
                <ButtonText>View Detailed Results</ButtonText>
              </Button>
              
              <Button
                variant="outline"
                size="md"
                onPress={() => router.back()}
              >
                <ButtonText>Back to Study</ButtonText>
              </Button>
            </VStack>
          </VStack>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  // Error state
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#fef2f2' }}>
      <VStack flex={1} justifyContent="center" alignItems="center" p="$4" space="lg">
        <XCircle size={64} color="#ef4444" />
        <VStack alignItems="center" space="md">
          <Heading size="xl">Session Error</Heading>
          <Text size="md" color="$neutral600" textAlign="center">
            Something went wrong with your study session.
          </Text>
        </VStack>
        <Button action="candyPink" onPress={() => router.back()}>
          <ButtonText>Go Back</ButtonText>
        </Button>
      </VStack>
    </SafeAreaView>
  );
}
