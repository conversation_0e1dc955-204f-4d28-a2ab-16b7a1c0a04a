/**
 * Study Session Setup Screen
 * 
 * Allows users to configure AI-powered study sessions with:
 * - Session type selection (review, learn, practice, adaptive)
 * - Duration and difficulty settings
 * - Category and tag filtering
 * - Personalized recommendations
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 6: UI Design Consistency with Gluestack UI
 * - Rule 8: Smooth animations with react-native-reanimated
 * - Rule 9: Safe Area Management
 * - Rule 11: Zod validation for all state
 */

import React, { useState, useEffect } from 'react';
import { ScrollView, Alert } from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { z } from 'zod';

// Permission system integration
import { FeatureGate, PermissionCheck } from '@/components/auth/FeatureGate';
import { usePermissions, PERMISSIONS } from '@/hooks/usePermissions';

// Icons
import {
  Brain,
  Clock,
  Target,
  BookOpen,
  Zap,
  Settings,
  Play,
  TrendingUp,
  Award,
  Sparkles,
  Crown,
} from 'lucide-react-native';

// UI Components following Rule #6
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';
import { Card, CardHeader, CardBody } from '@/components/ui/card';
import { Heading } from '@/components/ui/heading';
import { Badge, BadgeText } from '@/components/ui/badge';
import { 
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
} from '@/components/ui/select';

// Services
import { useAuth } from '@/lib/contexts/AuthContext';

// State validation with Zod (Rule 11)
const StudySetupStateSchema = z.object({
  sessionType: z.enum(['review', 'learn', 'practice', 'adaptive']),
  duration: z.number().min(5).max(180),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  maxCards: z.number().min(5).max(50),
  categories: z.array(z.string()),
  tags: z.array(z.string()),
  isLoading: z.boolean(),
});

type StudySetupState = z.infer<typeof StudySetupStateSchema>;

const SESSION_TYPES = [
  {
    id: 'review',
    title: 'Review Session',
    description: 'Review cards due for spaced repetition',
    icon: BookOpen,
    color: '#3b82f6',
    gradient: ['#dbeafe', '#bfdbfe'],
  },
  {
    id: 'learn',
    title: 'Learn New',
    description: 'Study new knowledge cards',
    icon: Sparkles,
    color: '#8b5cf6',
    gradient: ['#ede9fe', '#ddd6fe'],
  },
  {
    id: 'practice',
    title: 'Practice Weak',
    description: 'Focus on challenging concepts',
    icon: Target,
    color: '#f59e0b',
    gradient: ['#fef3c7', '#fde68a'],
  },
  {
    id: 'adaptive',
    title: 'AI Adaptive',
    description: 'AI-optimized mixed session',
    icon: Brain,
    color: '#10b981',
    gradient: ['#d1fae5', '#a7f3d0'],
  },
];

const DURATION_OPTIONS = [
  { value: 10, label: '10 minutes', description: 'Quick review' },
  { value: 20, label: '20 minutes', description: 'Standard session' },
  { value: 30, label: '30 minutes', description: 'Deep study' },
  { value: 45, label: '45 minutes', description: 'Extended learning' },
  { value: 60, label: '1 hour', description: 'Intensive session' },
];

export default function StudySetupScreen() {
  const { user } = useAuth();

  // Permission system integration (Rule 11: State management)
  const { isPremium, hasPermission, userRole } = usePermissions();

  // State management with Zod validation (Rule 11)
  const [state, setState] = useState<StudySetupState>({
    sessionType: 'review',
    duration: 20,
    maxCards: 15,
    categories: [],
    tags: [],
    isLoading: false,
  });

  // Animation values (Rule 8)
  const fadeInValue = useSharedValue(0);
  const slideUpValue = useSharedValue(50);

  // Early return if no user (Rule 12: Error handling)
  if (!user) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <Box flex={1} justifyContent="center" alignItems="center" p="$4">
          <Text>Please sign in to start studying.</Text>
        </Box>
      </SafeAreaView>
    );
  }

  useEffect(() => {
    // Entrance animations (Rule 8)
    fadeInValue.value = withTiming(1, { duration: 800 });
    slideUpValue.value = withSpring(0, { damping: 15, stiffness: 150 });
  }, []);

  const handleStartSession = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));

      // Validate state before proceeding
      const validatedState = StudySetupStateSchema.parse(state);

      // Navigate to study session with parameters
      const params = new URLSearchParams({
        type: validatedState.sessionType,
        duration: validatedState.duration.toString(),
        maxCards: validatedState.maxCards.toString(),
        ...(validatedState.difficulty && { difficulty: validatedState.difficulty }),
        ...(validatedState.categories.length > 0 && { 
          categories: JSON.stringify(validatedState.categories) 
        }),
        ...(validatedState.tags.length > 0 && { 
          tags: JSON.stringify(validatedState.tags) 
        }),
      });

      router.push(`/study/session?${params.toString()}`);

    } catch (error) {
      console.error('Study session start error:', error);
      Alert.alert(
        'Setup Error',
        'Failed to start study session. Please check your settings and try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const updateState = (updates: Partial<StudySetupState>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  // Animated styles (Rule 8)
  const containerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: fadeInValue.value,
    transform: [{ translateY: slideUpValue.value }],
  }));

  const selectedSessionType = SESSION_TYPES.find(type => type.id === state.sessionType);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <LinearGradient
        colors={selectedSessionType?.gradient || ['#f8fafc', '#f1f5f9']}
        style={{ flex: 1 }}
      >
        <ScrollView flex={1} showsVerticalScrollIndicator={false}>
          <Animated.View style={containerAnimatedStyle}>
            <VStack p="$4" space="lg">
              {/* Header */}
              <VStack space="md" alignItems="center">
                <Brain size={48} color={selectedSessionType?.color || '#6b7280'} />
                <VStack alignItems="center" space="xs">
                  <Heading size="2xl" textAlign="center">
                    AI Study Session
                  </Heading>
                  <Text size="md" color="$neutral600" textAlign="center">
                    Personalized learning powered by AI
                  </Text>
                </VStack>
              </VStack>

              {/* Session Type Selection */}
              <VStack space="md">
                <Heading size="lg">Choose Session Type</Heading>
                <VStack space="sm">
                  {SESSION_TYPES.map((type) => {
                    const isSelected = state.sessionType === type.id;
                    const IconComponent = type.icon;
                    const isAIFeature = type.id === 'adaptive';
                    const hasAccess = !isAIFeature || hasPermission(PERMISSIONS.AI_STUDY);

                    return (
                      <Card
                        key={type.id}
                        variant={isSelected ? 'glass' : 'default'}
                        style={{
                          borderWidth: isSelected ? 2 : 1,
                          borderColor: isSelected ? type.color : '#e5e7eb',
                          opacity: hasAccess ? 1 : 0.6,
                        }}
                      >
                        <Box className="p-4">
                          <Button
                            variant="outline"
                            size="lg"
                            onPress={() => {
                              if (!hasAccess) {
                                Alert.alert(
                                  'Premium Feature',
                                  'AI Adaptive study sessions require a Premium subscription.',
                                  [
                                    { text: 'Maybe Later', style: 'cancel' },
                                    { text: 'Upgrade Now', onPress: () => router.push('/(tabs)/profile') }
                                  ]
                                );
                                return;
                              }
                              updateState({ sessionType: type.id as any });
                            }}
                            style={{ justifyContent: 'flex-start' }}
                            isDisabled={!hasAccess}
                          >
                            <HStack alignItems="center" space="md" flex={1}>
                              <IconComponent size={24} color={hasAccess ? type.color : '#9ca3af'} />
                              <VStack flex={1} alignItems="flex-start">
                                <HStack alignItems="center" space="sm">
                                  <Text
                                    className={`font-semibold text-base ${hasAccess ? 'text-gray-900' : 'text-gray-500'}`}
                                  >
                                    {type.title}
                                  </Text>
                                  {isAIFeature && !isPremium && (
                                    <Badge action="candyPink" size="sm">
                                      <Crown size={10} />
                                      <BadgeText>Premium</BadgeText>
                                    </Badge>
                                  )}
                                </HStack>
                                <Text
                                  size="sm"
                                  color={hasAccess ? '$neutral600' : '$neutral500'}
                                >
                                  {type.description}
                                </Text>
                              </VStack>
                              {isSelected && hasAccess && (
                                <Badge action="success">
                                  <BadgeText>Selected</BadgeText>
                                </Badge>
                              )}
                            </HStack>
                          </Button>
                        </Box>
                      </Card>
                    );
                  })}
                </VStack>
              </VStack>

              {/* Duration Selection */}
              <VStack space="md">
                <Heading size="lg">Session Duration</Heading>
                <HStack space="sm" flexWrap="wrap">
                  {DURATION_OPTIONS.map((option) => {
                    const isSelected = state.duration === option.value;
                    
                    return (
                      <Button
                        key={option.value}
                        variant={isSelected ? 'solid' : 'outline'}
                        action={isSelected ? 'candyPurple' : 'secondary'}
                        size="md"
                        onPress={() => updateState({ duration: option.value })}
                        style={{ marginBottom: 8 }}
                      >
                        <VStack alignItems="center" space="xs">
                          <ButtonText>{option.label}</ButtonText>
                          <Text size="xs" color={isSelected ? '$white' : '$neutral600'}>
                            {option.description}
                          </Text>
                        </VStack>
                      </Button>
                    );
                  })}
                </HStack>
              </VStack>

              {/* Advanced Settings */}
              <Card>
                <CardHeader>
                  <HStack alignItems="center" space="md">
                    <Settings size={20} color="#6b7280" />
                    <Heading size="md">Advanced Settings</Heading>
                  </HStack>
                </CardHeader>
                <CardBody>
                  <VStack space="md">
                    {/* Max Cards */}
                    <VStack space="sm">
                      <Text fontWeight="$semibold">Maximum Cards</Text>
                      <HStack space="sm" alignItems="center">
                        <Button
                          variant="outline"
                          size="sm"
                          onPress={() => updateState({ maxCards: Math.max(5, state.maxCards - 5) })}
                        >
                          <ButtonText>-</ButtonText>
                        </Button>
                        <Badge action="candyBlue" style={{ minWidth: 60 }}>
                          <BadgeText>{state.maxCards}</BadgeText>
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onPress={() => updateState({ maxCards: Math.min(50, state.maxCards + 5) })}
                        >
                          <ButtonText>+</ButtonText>
                        </Button>
                      </HStack>
                    </VStack>

                    {/* Difficulty Filter */}
                    <VStack space="sm">
                      <Text fontWeight="$semibold">Difficulty Level</Text>
                      <Select
                        selectedValue={state.difficulty || ''}
                        onValueChange={(value) => updateState({ 
                          difficulty: value ? value as any : undefined 
                        })}
                      >
                        <SelectTrigger variant="outline" size="md">
                          <SelectInput placeholder="Any difficulty" />
                          <SelectIcon />
                        </SelectTrigger>
                        <SelectPortal>
                          <SelectBackdrop />
                          <SelectContent>
                            <SelectDragIndicatorWrapper>
                              <SelectDragIndicator />
                            </SelectDragIndicatorWrapper>
                            <SelectItem label="Any difficulty" value="" />
                            <SelectItem label="Beginner" value="beginner" />
                            <SelectItem label="Intermediate" value="intermediate" />
                            <SelectItem label="Advanced" value="advanced" />
                          </SelectContent>
                        </SelectPortal>
                      </Select>
                    </VStack>
                  </VStack>
                </CardBody>
              </Card>

              {/* Session Preview */}
              <Card variant="glass">
                <CardHeader>
                  <HStack alignItems="center" space="md">
                    <TrendingUp size={20} color="#10b981" />
                    <Heading size="md">Session Preview</Heading>
                  </HStack>
                </CardHeader>
                <CardBody>
                  <VStack space="sm">
                    <HStack justifyContent="space-between" alignItems="center">
                      <Text>Type:</Text>
                      <Badge action="candyPurple">
                        <BadgeText>{selectedSessionType?.title}</BadgeText>
                      </Badge>
                    </HStack>
                    <HStack justifyContent="space-between" alignItems="center">
                      <Text>Duration:</Text>
                      <Badge action="candyBlue">
                        <BadgeText>{state.duration} minutes</BadgeText>
                      </Badge>
                    </HStack>
                    <HStack justifyContent="space-between" alignItems="center">
                      <Text>Max Cards:</Text>
                      <Badge action="candyPink">
                        <BadgeText>{state.maxCards} cards</BadgeText>
                      </Badge>
                    </HStack>
                    {state.difficulty && (
                      <HStack justifyContent="space-between" alignItems="center">
                        <Text>Difficulty:</Text>
                        <Badge action="warning">
                          <BadgeText>{state.difficulty}</BadgeText>
                        </Badge>
                      </HStack>
                    )}
                  </VStack>
                </CardBody>
              </Card>

              {/* Start Button */}
              <VStack space="md">
                <Button
                  action="candyPurple"
                  size="xl"
                  onPress={handleStartSession}
                  isDisabled={state.isLoading}
                >
                  <ButtonIcon as={Play} />
                  <ButtonText>
                    {state.isLoading ? 'Starting Session...' : 'Start AI Study Session'}
                  </ButtonText>
                </Button>
                
                <Text size="sm" color="$neutral500" textAlign="center">
                  AI will personalize your session based on your learning history
                </Text>
              </VStack>
            </VStack>
          </Animated.View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}
