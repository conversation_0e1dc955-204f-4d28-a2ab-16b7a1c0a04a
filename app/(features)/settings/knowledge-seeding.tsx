/**
 * Knowledge Seeding Screen
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 6: UI Design Consistency with Gluestack UI
 * - Rule 9: Safe Area Management
 * - Rule 1: Interactive feedback protocol
 * 
 * This screen allows seeding programming knowledge cards directly from the app.
 */

import React, { useState } from 'react';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft, Database, CheckCircle, XCircle, AlertCircle } from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Import UI components following Rule #6
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';
import { Progress, ProgressFilledTrack } from '@/components/ui/progress';
import { Card, CardHeader, CardBody } from '@/components/ui/card';
import { Heading } from '@/components/ui/heading';
import { Badge, BadgeText } from '@/components/ui/badge';
import { ScrollView } from 'react-native';

import { useAuth } from '@/lib/contexts/AuthContext';

import { 
  seedProgrammingKnowledge, 
  checkProgrammingKnowledgeExists,
  getProgrammingKnowledgePreview,
  type SeedingResult,
  type SeedingProgress 
} from '@/lib/services/knowledge-seeding.service';

export default function KnowledgeSeedingScreen() {
  const { user } = useAuth();
  const [isSeeding, setIsSeeding] = useState(false);
  const [seedingProgress, setSeedingProgress] = useState<SeedingProgress | null>(null);
  const [seedingResult, setSeedingResult] = useState<SeedingResult | null>(null);
  const [existingCheck, setExistingCheck] = useState<{
    exists: boolean;
    existingCount: number;
    missingCards: string[];
  } | null>(null);

  const programmingCards = getProgrammingKnowledgePreview();

  // Early return if no user (Rule 12: Error handling)
  if (!user) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <Box flex={1} justifyContent="center" alignItems="center" p="$4">
          <Text>Please sign in to seed knowledge cards.</Text>
        </Box>
      </SafeAreaView>
    );
  }

  React.useEffect(() => {
    checkExistingKnowledge();
  }, []);

  const checkExistingKnowledge = async () => {
    if (!user) return;
    
    try {
      const result = await checkProgrammingKnowledgeExists(user.$id);
      setExistingCheck(result);
    } catch (error) {
      console.error('Failed to check existing knowledge:', error);
    }
  };

  const handleSeedKnowledge = async () => {
    if (existingCheck?.exists) {
      Alert.alert(
        'Knowledge Already Exists',
        'Programming knowledge cards already exist in the database. Do you want to proceed anyway?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Proceed', onPress: performSeeding },
        ]
      );
    } else {
      performSeeding();
    }
  };

  const performSeeding = async () => {
    setIsSeeding(true);
    setSeedingProgress(null);
    setSeedingResult(null);

    try {
      const result = await seedProgrammingKnowledge((progress) => {
        setSeedingProgress(progress);
      });

      setSeedingResult(result);
      
      if (result.success) {
        Alert.alert(
          'Seeding Successful! 🎉',
          `Successfully created ${result.successCount} programming knowledge cards. You can now test the AI search with programming queries.`,
          [{ text: 'OK' }]
        );
        // Refresh the existing check
        await checkExistingKnowledge();
      } else {
        Alert.alert(
          'Seeding Failed',
          `Failed to create knowledge cards. ${result.errors.length} errors occurred.`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      Alert.alert(
        'Seeding Error',
        `An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`,
        [{ text: 'OK' }]
      );
    } finally {
      setIsSeeding(false);
      setSeedingProgress(null);
    }
  };

  const getProgressPercentage = () => {
    if (!seedingProgress) return 0;
    return (seedingProgress.current / seedingProgress.total) * 100;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return '$success500';
      case 'error': return '$error500';
      case 'creating': return '$primary500';
      default: return '$neutral500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return CheckCircle;
      case 'error': return XCircle;
      case 'creating': return AlertCircle;
      default: return Database;
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <ScrollView flex={1} p="$4">
        <VStack space="lg">
          {/* Header */}
          <HStack alignItems="center" space="md">
            <Button
              variant="ghost"
              size="sm"
              onPress={() => router.back()}
            >
              <ButtonIcon as={ArrowLeft} />
            </Button>
            <VStack flex={1}>
              <Heading size="xl">Knowledge Seeding</Heading>
              <Text size="sm" color="$neutral600">
                Add programming knowledge cards to the database
              </Text>
            </VStack>
          </HStack>

          {/* Current Status Card */}
          <Card>
            <CardHeader>
              <HStack alignItems="center" space="md">
                <Database size={24} color="#3b82f6" />
                <VStack flex={1}>
                  <Heading size="md">Database Status</Heading>
                  <Text size="sm" color="$neutral600">
                    Current programming knowledge cards
                  </Text>
                </VStack>
              </HStack>
            </CardHeader>
            <CardBody>
              {existingCheck ? (
                <VStack space="md">
                  <HStack justifyContent="space-between" alignItems="center">
                    <Text>Existing Cards:</Text>
                    <Badge
                      variant={existingCheck.existingCount > 0 ? 'solid' : 'outline'}
                      action={existingCheck.existingCount > 0 ? 'success' : 'muted'}
                    >
                      <BadgeText>{existingCheck.existingCount}</BadgeText>
                    </Badge>
                  </HStack>
                  <HStack justifyContent="space-between" alignItems="center">
                    <Text>Missing Cards:</Text>
                    <Badge
                      variant={existingCheck.missingCards.length > 0 ? 'solid' : 'outline'}
                      action={existingCheck.missingCards.length > 0 ? 'warning' : 'success'}
                    >
                      <BadgeText>{existingCheck.missingCards.length}</BadgeText>
                    </Badge>
                  </HStack>
                  {existingCheck.exists && (
                    <Text size="sm" color="$success600">
                      ✅ All programming knowledge cards are present
                    </Text>
                  )}
                </VStack>
              ) : (
                <Text color="$neutral500">Checking database status...</Text>
              )}
            </CardBody>
          </Card>

          {/* Seeding Progress */}
          {isSeeding && seedingProgress && (
            <Card>
              <CardHeader>
                <Heading size="md">Seeding Progress</Heading>
              </CardHeader>
              <CardBody>
                <VStack space="md">
                  <Progress value={getProgressPercentage()} size="md">
                    <ProgressFilledTrack />
                  </Progress>
                  <HStack justifyContent="space-between" alignItems="center">
                    <Text size="sm">
                      {seedingProgress.current} of {seedingProgress.total}
                    </Text>
                    <Text size="sm" color="$neutral600">
                      {Math.round(getProgressPercentage())}%
                    </Text>
                  </HStack>
                  <HStack alignItems="center" space="sm">
                    {React.createElement(getStatusIcon(seedingProgress.status), {
                      size: 16,
                      color: getStatusColor(seedingProgress.status) === '$success500' ? '#10b981' :
                             getStatusColor(seedingProgress.status) === '$error500' ? '#ef4444' :
                             getStatusColor(seedingProgress.status) === '$primary500' ? '#3b82f6' : '#6b7280'
                    })}
                    <Text size="sm" flex={1}>
                      {seedingProgress.status === 'creating' && 'Creating: '}
                      {seedingProgress.status === 'success' && 'Created: '}
                      {seedingProgress.status === 'error' && 'Failed: '}
                      {seedingProgress.currentCard}
                    </Text>
                  </HStack>
                </VStack>
              </CardBody>
            </Card>
          )}

          {/* Seeding Result */}
          {seedingResult && (
            <Card>
              <CardHeader>
                <HStack alignItems="center" space="md">
                  {React.createElement(seedingResult.success ? CheckCircle : XCircle, {
                    size: 24,
                    color: seedingResult.success ? '#10b981' : '#ef4444'
                  })}
                  <VStack flex={1}>
                    <Heading size="md">
                      {seedingResult.success ? 'Seeding Successful' : 'Seeding Failed'}
                    </Heading>
                    <Text size="sm" color="$neutral600">
                      {seedingResult.successCount} created, {seedingResult.errorCount} failed
                    </Text>
                  </VStack>
                </HStack>
              </CardHeader>
              {seedingResult.errors.length > 0 && (
                <CardBody>
                  <VStack space="sm">
                    <Text size="sm" fontWeight="$semibold" color="$error600">
                      Errors:
                    </Text>
                    {seedingResult.errors.slice(0, 3).map((error, index) => (
                      <Text key={index} size="xs" color="$error500">
                        • {error}
                      </Text>
                    ))}
                    {seedingResult.errors.length > 3 && (
                      <Text size="xs" color="$neutral500">
                        ... and {seedingResult.errors.length - 3} more errors
                      </Text>
                    )}
                  </VStack>
                </CardBody>
              )}
            </Card>
          )}

          {/* Programming Cards Preview */}
          <Card>
            <CardHeader>
              <Heading size="md">Programming Knowledge Cards</Heading>
              <Text size="sm" color="$neutral600">
                {programmingCards.length} cards ready to be seeded
              </Text>
            </CardHeader>
            <CardBody>
              <VStack space="sm">
                {programmingCards.map((card, index) => (
                  <HStack key={index} alignItems="center" space="md">
                    <Badge
                      variant="outline"
                      action={card.difficulty === 'beginner' ? 'success' : 
                             card.difficulty === 'intermediate' ? 'warning' : 'error'}
                    >
                      <BadgeText>{card.difficulty}</BadgeText>
                    </Badge>
                    <VStack flex={1}>
                      <Text size="sm" fontWeight="$medium">
                        {card.title}
                      </Text>
                      <Text size="xs" color="$neutral500">
                        {card.tags.join(', ')}
                      </Text>
                    </VStack>
                  </HStack>
                ))}
              </VStack>
            </CardBody>
          </Card>

          {/* Action Buttons */}
          <VStack space="md">
            <Button
              size="lg"
              onPress={handleSeedKnowledge}
              isDisabled={isSeeding}
              action={existingCheck?.exists ? 'secondary' : 'primary'}
            >
              <ButtonText>
                {isSeeding ? 'Seeding...' : 
                 existingCheck?.exists ? 'Re-seed Knowledge Cards' : 'Seed Programming Knowledge'}
              </ButtonText>
            </Button>
            
            <Button
              variant="outline"
              size="md"
              onPress={checkExistingKnowledge}
              isDisabled={isSeeding}
            >
              <ButtonText>Refresh Status</ButtonText>
            </Button>
          </VStack>

          {/* Benefits Info */}
          <Card>
            <CardHeader>
              <Heading size="md">Benefits After Seeding</Heading>
            </CardHeader>
            <CardBody>
              <VStack space="sm">
                <Text size="sm">🔍 <Text fontWeight="$semibold">AI Search Testing:</Text> Review Intent Test will find programming content</Text>
                <Text size="sm">🧠 <Text fontWeight="$semibold">Semantic Search:</Text> Better matching of programming concepts</Text>
                <Text size="sm">🎯 <Text fontWeight="$semibold">Intent Recognition:</Text> Improved AI training data</Text>
                <Text size="sm">📊 <Text fontWeight="$semibold">Knowledge Graph:</Text> Programming relationships and connections</Text>
                <Text size="sm">🧪 <Text fontWeight="$semibold">Test Scenarios:</Text> All 5 test scenarios will have relevant content</Text>
              </VStack>
            </CardBody>
          </Card>
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
}