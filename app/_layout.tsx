import "@/global.css";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import {
	DarkTheme,
	DefaultTheme,
	ThemeProvider,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { ErrorBoundary, Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect } from "react";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { AuthGuard } from "@/components/auth/AuthGuard";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
import { AuthProvider } from "@/lib/contexts/AuthContext";
import { KnowledgeCardsProvider } from "@/lib/contexts/KnowledgeCardsContext";
import { PermissionErrorProvider } from "@/lib/contexts/PermissionErrorContext";
import { useThemeStore } from "@/store/theme";
import "@/lib/helpers/polyfill";

// Import your global CSS file
import "../global.css";

export {
	// Catch any errors thrown by the Layout component.
	ErrorBoundary,
} from "expo-router";

export const unstable_settings = {
	// Ensure that reloading on `/modal` keeps a back button present.
	initialRouteName: "(tabs)",
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
	const { colorScheme } = useThemeStore();
	const [loaded, error] = useFonts({
		SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
		...FontAwesome.font,
	});

	// Expo Router uses Error Boundaries to catch errors in the navigation tree.
	useEffect(() => {
		if (error) throw error;
	}, [error]);

	useEffect(() => {
		if (loaded) {
			SplashScreen.hideAsync();
		}
	}, [loaded]);

	if (!loaded) {
		return null;
	}

	return (
		<GluestackUIProvider mode="light">
			<AuthProvider>
				<KnowledgeCardsProvider>
					<PermissionErrorProvider>
					<AuthGuard>
					<GestureHandlerRootView style={{ flex: 1 }}>
						<ThemeProvider
							value={colorScheme === "dark" ? DarkTheme : DefaultTheme}
						>
							<Stack>
								<Stack.Screen name="index" options={{ headerShown: false }} />
								<Stack.Screen
									name="welcome"
									options={{ headerShown: false, gestureEnabled: false }}
								/>
								<Stack.Screen name="(tabs)" options={{ headerShown: false }} />
								<Stack.Screen
									name="(knowledge)"
									options={{ headerShown: false }}
								/>
								<Stack.Screen
									name="modal"
									options={{ presentation: "modal" }}
								/>
								<Stack.Screen
									name="auth/login"
									options={{ headerShown: false, gestureEnabled: false }}
								/>
								<Stack.Screen
									name="auth/signup"
									options={{ headerShown: false, gestureEnabled: false }}
								/>
								<Stack.Screen
									name="login"
									options={{
										headerShown: false,
										// This prevents the user from going back to the previous screen
										// which is useful for login screens
										gestureEnabled: false,
									}}
								/>
							</Stack>
						</ThemeProvider>
					</GestureHandlerRootView>
				</AuthGuard>
				</PermissionErrorProvider>
			</KnowledgeCardsProvider>
		</AuthProvider>
		</GluestackUIProvider>
	);
}
