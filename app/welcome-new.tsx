import { useRouter } from "expo-router";
import React from "react";
import { <PERSON><PERSON>, ScrollView, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { HeroSection } from "@/components/home/<USER>";
import { HomeBackground } from "@/components/home/<USER>";
import { HomeHeader } from "@/components/home/<USER>";
import { Box } from "@/components/ui/box";

export default function Home() {
	const router = useRouter();

	const handleGetStarted = () => {
		console.log("🚀 Get Started button pressed - navigating to login...");
		router.push("/auth/login");
	};

	const handleWatchDemo = () => {
		console.log("🎬 Watch Demo button pressed");
		Alert.alert(
			"Demo Coming Soon",
			"The demo video will be available in the next update!",
			[{ text: "OK" }],
		);
	};

	return (
		<Box className="flex-1 relative">
			<HomeBackground />

			<SafeAreaView style={styles.safeAreaContainer}>
				<ScrollView
					contentContainerStyle={styles.scrollContent}
					showsVerticalScrollIndicator={false}
				>
					<HomeHeader />
					<HeroSection
						onGetStarted={handleGetStarted}
						onWatchDemo={handleWatchDemo}
					/>
				</ScrollView>
			</SafeAreaView>
		</Box>
	);
}

const styles = StyleSheet.create({
	safeAreaContainer: {
		flex: 1,
		position: "relative",
		zIndex: 9,
	},
	scrollContent: {
		flexGrow: 1,
		alignItems: "center",
		justifyContent: "center",
		minHeight: "100%",
	},
});
