import React, { useState, useEffect } from 'react';
import { ScrollView, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { 
  ArrowLeftIcon, 
  AccessibilityIcon, 
  EyeIcon, 
  EarIcon,
  TypeIcon,
  ZapIcon,
  PaletteIcon,
  VolumeXIcon
} from 'lucide-react-native';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { Button, ButtonText } from '@/components/ui/button';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Switch } from '@/components/ui/switch';
import { accessibilityService } from '@/lib/services/accessibility.service';

/**
 * Accessibility Settings Screen
 * Following Rule 11: State Management Architecture
 * Following Rule 12: Validation and Error Handling
 */
export default function AccessibilitySettingsScreen() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  
  // Accessibility settings state
  const [settings, setSettings] = useState({
    screenReader: false,
    highContrast: false,
    largeText: false,
    reduceMotion: false,
    voiceOver: false,
    hapticFeedback: true,
    audioDescriptions: false,
    colorBlindSupport: false,
    focusIndicators: true,
    keyboardNavigation: true,
    gestureAlternatives: false,
    textToSpeech: false,
  });

  // Animation values
  const fadeIn = useSharedValue(0);
  const slideUp = useSharedValue(50);

  useEffect(() => {
    initializeAccessibilitySettings();
    
    // Animate in
    fadeIn.value = withTiming(1, { duration: 800 });
    slideUp.value = withSpring(0, { damping: 15 });
  }, []);

  const initializeAccessibilitySettings = async () => {
    try {
      setIsLoading(true);
      
      // Load accessibility settings
      const accessibilitySettings = await accessibilityService.getSettings();
      setSettings(accessibilitySettings);
      
      setIsLoading(false);
    } catch (error) {
      console.warn('Failed to load accessibility settings:', error);
      setIsLoading(false);
    }
  };

  const handleSettingChange = async (key: string, value: any) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      
      // Save settings
      await accessibilityService.updateSettings(newSettings);
      
      // Apply settings immediately
      await accessibilityService.applySettings(newSettings);
      
      // Show confirmation for important changes
      if (['screenReader', 'highContrast', 'reduceMotion'].includes(key)) {
        Alert.alert(
          'Setting Applied',
          `${key.replace(/([A-Z])/g, ' $1').toLowerCase()} has been ${value ? 'enabled' : 'disabled'}.`
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update accessibility settings. Please try again.');
    }
  };

  const handleRunAccessibilityAudit = async () => {
    try {
      Alert.alert(
        'Accessibility Audit',
        'This will analyze the app for accessibility issues and provide recommendations. Continue?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Run Audit', 
            onPress: async () => {
              const result = await accessibilityService.runAccessibilityAudit();
              
              if (result.issues.length === 0) {
                Alert.alert(
                  'Audit Complete',
                  'No accessibility issues found! The app meets accessibility standards.'
                );
              } else {
                Alert.alert(
                  'Audit Complete',
                  `Found ${result.issues.length} accessibility issues. Check the accessibility report for details.`
                );
              }
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Accessibility audit failed. Please try again.');
    }
  };

  const handleResetToDefaults = async () => {
    try {
      Alert.alert(
        'Reset Settings',
        'This will reset all accessibility settings to their default values. Continue?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Reset', 
            onPress: async () => {
              await accessibilityService.resetToDefaults();
              await initializeAccessibilitySettings();
              Alert.alert('Settings Reset', 'All accessibility settings have been reset to defaults.');
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to reset settings. Please try again.');
    }
  };

  // Animated styles
  const containerStyle = useAnimatedStyle(() => ({
    opacity: fadeIn.value,
    transform: [{ translateY: slideUp.value }],
  }));

  return (
    <Box className="flex-1">
      {/* Background Gradient */}
      <LinearGradient
        colors={['#1e1b4b', '#312e81', '#4338ca', '#6366f1']}
        style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0 }}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <Animated.View style={[containerStyle, { flex: 1 }]}>
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          {/* Header */}
          <VStack space="md" className="pt-16 px-6 pb-4">
            <HStack className="items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                onPress={() => router.back()}
                className="bg-white/10 border-white/20"
              >
                <HStack className="items-center space-x-2">
                  <ArrowLeftIcon size={16} color="white" />
                  <ButtonText className="text-white text-sm">Back</ButtonText>
                </HStack>
              </Button>
            </HStack>

            <VStack space="sm">
              <HStack className="items-center space-x-3">
                <Box className="w-12 h-12 justify-center items-center rounded-full bg-green-500/20">
                  <AccessibilityIcon size={24} color="#10B981" />
                </Box>
                <VStack space="xs">
                  <Text className="text-white text-3xl font-bold">
                    Accessibility
                  </Text>
                  <Text className="text-white/70 text-base">
                    Configure accessibility features and preferences
                  </Text>
                </VStack>
              </HStack>
            </VStack>
          </VStack>

          {/* Visual Accessibility */}
          <VStack space="md" className="px-6 mb-6">
            <HStack className="items-center space-x-3">
              <EyeIcon size={20} color="#3B82F6" />
              <Text className="text-white text-xl font-semibold">
                Visual Accessibility
              </Text>
            </HStack>

            <VStack space="sm">
              {/* High Contrast */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      High Contrast Mode
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Increase contrast for better visibility
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.highContrast}
                    onValueChange={(value) => handleSettingChange('highContrast', value)}
                  />
                </HStack>
              </Box>

              {/* Large Text */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Large Text
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Increase text size for better readability
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.largeText}
                    onValueChange={(value) => handleSettingChange('largeText', value)}
                  />
                </HStack>
              </Box>

              {/* Color Blind Support */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Color Blind Support
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Adjust colors for color vision deficiency
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.colorBlindSupport}
                    onValueChange={(value) => handleSettingChange('colorBlindSupport', value)}
                  />
                </HStack>
              </Box>

              {/* Focus Indicators */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Focus Indicators
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Show clear focus indicators for navigation
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.focusIndicators}
                    onValueChange={(value) => handleSettingChange('focusIndicators', value)}
                  />
                </HStack>
              </Box>
            </VStack>
          </VStack>

          {/* Audio Accessibility */}
          <VStack space="md" className="px-6 mb-6">
            <HStack className="items-center space-x-3">
              <EarIcon size={20} color="#F59E0B" />
              <Text className="text-white text-xl font-semibold">
                Audio Accessibility
              </Text>
            </HStack>

            <VStack space="sm">
              {/* Screen Reader */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Screen Reader Support
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Enable screen reader compatibility
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.screenReader}
                    onValueChange={(value) => handleSettingChange('screenReader', value)}
                  />
                </HStack>
              </Box>

              {/* Text to Speech */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Text to Speech
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Read text content aloud
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.textToSpeech}
                    onValueChange={(value) => handleSettingChange('textToSpeech', value)}
                  />
                </HStack>
              </Box>

              {/* Audio Descriptions */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Audio Descriptions
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Provide audio descriptions for visual content
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.audioDescriptions}
                    onValueChange={(value) => handleSettingChange('audioDescriptions', value)}
                  />
                </HStack>
              </Box>
            </VStack>
          </VStack>

          {/* Motor Accessibility */}
          <VStack space="md" className="px-6 mb-6">
            <HStack className="items-center space-x-3">
              <ZapIcon size={20} color="#A855F7" />
              <Text className="text-white text-xl font-semibold">
                Motor Accessibility
              </Text>
            </HStack>

            <VStack space="sm">
              {/* Reduce Motion */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Reduce Motion
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Minimize animations and transitions
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.reduceMotion}
                    onValueChange={(value) => handleSettingChange('reduceMotion', value)}
                  />
                </HStack>
              </Box>

              {/* Haptic Feedback */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Haptic Feedback
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Provide tactile feedback for interactions
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.hapticFeedback}
                    onValueChange={(value) => handleSettingChange('hapticFeedback', value)}
                  />
                </HStack>
              </Box>

              {/* Keyboard Navigation */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Keyboard Navigation
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Enable full keyboard navigation support
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.keyboardNavigation}
                    onValueChange={(value) => handleSettingChange('keyboardNavigation', value)}
                  />
                </HStack>
              </Box>

              {/* Gesture Alternatives */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Gesture Alternatives
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Provide button alternatives to gestures
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.gestureAlternatives}
                    onValueChange={(value) => handleSettingChange('gestureAlternatives', value)}
                  />
                </HStack>
              </Box>
            </VStack>
          </VStack>

          {/* Tools & Testing */}
          <VStack space="md" className="px-6 mb-8">
            <Text className="text-white text-xl font-semibold">
              Tools & Testing
            </Text>

            <VStack space="sm">
              <Button
                onPress={handleRunAccessibilityAudit}
                className="bg-emerald-600/20 border border-emerald-400/30 rounded-xl p-4"
              >
                <HStack className="items-center space-x-3">
                  <AccessibilityIcon size={20} color="#10B981" />
                  <ButtonText className="text-emerald-300 text-base font-medium">
                    Run Accessibility Audit
                  </ButtonText>
                </HStack>
              </Button>

              <Button
                onPress={handleResetToDefaults}
                className="bg-red-600/20 border border-red-400/30 rounded-xl p-4"
              >
                <HStack className="items-center space-x-3">
                  <VolumeXIcon size={20} color="#EF4444" />
                  <ButtonText className="text-red-300 text-base font-medium">
                    Reset to Defaults
                  </ButtonText>
                </HStack>
              </Button>
            </VStack>
          </VStack>

          {/* Loading State */}
          {isLoading && (
            <VStack space="md" className="px-6 py-8">
              <Box className="bg-white/10 rounded-xl p-6 items-center">
                <AccessibilityIcon size={32} color="white" />
                <Text className="text-white text-lg font-medium mt-4 mb-2">
                  Loading Accessibility Settings...
                </Text>
                <Text className="text-white/70 text-sm text-center">
                  Configuring accessibility features and preferences
                </Text>
              </Box>
            </VStack>
          )}

          {/* Footer Spacing */}
          <Box className="h-8" />
        </ScrollView>
      </Animated.View>
    </Box>
  );
}
