import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>View, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { 
  ArrowLeftIcon, 
  MicIcon, 
  VolumeXIcon, 
  Volume2Icon,
  SettingsIcon,
  PlayIcon,
  StopCircleIcon
} from 'lucide-react-native';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { Button, ButtonText } from '@/components/ui/button';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Switch } from '@/components/ui/switch';
import { voiceInputService } from '@/lib/services/voice-input.service';

/**
 * Voice Settings Screen
 * Following Rule 8: Animation Implementation Standards
 * Following Rule 12: Validation and Error Handling
 */
export default function VoiceSettingsScreen() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isRecording, setIsRecording] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  
  // Voice settings state
  const [settings, setSettings] = useState({
    voiceInputEnabled: true,
    continuousListening: false,
    voiceCommands: true,
    speechFeedback: true,
    language: 'en-US',
    sensitivity: 'medium',
    noiseReduction: true,
  });

  // Animation values
  const fadeIn = useSharedValue(0);
  const slideUp = useSharedValue(50);
  const micScale = useSharedValue(1);

  useEffect(() => {
    initializeVoiceSettings();
    
    // Animate in
    fadeIn.value = withTiming(1, { duration: 800 });
    slideUp.value = withSpring(0, { damping: 15 });
  }, []);

  const initializeVoiceSettings = async () => {
    try {
      setIsLoading(true);
      
      // Load voice settings
      const voiceSettings = await voiceInputService.getSettings();
      setSettings(voiceSettings);
      
      setIsLoading(false);
    } catch (error) {
      console.warn('Failed to load voice settings:', error);
      setIsLoading(false);
    }
  };

  const handleSettingChange = async (key: string, value: any) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      
      // Save settings
      await voiceInputService.updateSettings(newSettings);
      
      // Animate mic when voice input is toggled
      if (key === 'voiceInputEnabled') {
        micScale.value = withSpring(value ? 1.2 : 0.8, { damping: 10 });
        setTimeout(() => {
          micScale.value = withSpring(1, { damping: 10 });
        }, 200);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update voice settings. Please try again.');
    }
  };

  const handleTestVoiceInput = async () => {
    try {
      setIsTesting(true);
      
      if (!settings.voiceInputEnabled) {
        Alert.alert('Voice Input Disabled', 'Please enable voice input first.');
        setIsTesting(false);
        return;
      }

      // Test voice input
      const result = await voiceInputService.testVoiceInput();
      
      if (result.success) {
        Alert.alert(
          'Voice Test Successful',
          `Recognized: "${result.text}"\nConfidence: ${((result.confidence || 0.8) * 100).toFixed(1)}%`
        );
      } else {
        Alert.alert('Voice Test Failed', result.error || 'Unable to recognize speech.');
      }
      
      setIsTesting(false);
    } catch (error) {
      Alert.alert('Error', 'Voice test failed. Please check your microphone permissions.');
      setIsTesting(false);
    }
  };

  const handleCalibrateMicrophone = async () => {
    try {
      Alert.alert(
        'Calibrate Microphone',
        'This will analyze your environment and optimize microphone settings. Continue?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Calibrate', 
            onPress: async () => {
              const result = await voiceInputService.calibrateMicrophone();
              if (result.success) {
                Alert.alert('Calibration Complete', 'Microphone settings have been optimized.');
                await initializeVoiceSettings();
              } else {
                Alert.alert('Calibration Failed', result.error || 'Unable to calibrate microphone.');
              }
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Microphone calibration failed.');
    }
  };

  // Animated styles
  const containerStyle = useAnimatedStyle(() => ({
    opacity: fadeIn.value,
    transform: [{ translateY: slideUp.value }],
  }));

  const micStyle = useAnimatedStyle(() => ({
    transform: [{ scale: micScale.value }],
  }));

  const languages = [
    { code: 'en-US', name: 'English (US)' },
    { code: 'en-GB', name: 'English (UK)' },
    { code: 'es-ES', name: 'Spanish' },
    { code: 'fr-FR', name: 'French' },
    { code: 'de-DE', name: 'German' },
    { code: 'it-IT', name: 'Italian' },
    { code: 'pt-BR', name: 'Portuguese' },
    { code: 'zh-CN', name: 'Chinese (Simplified)' },
    { code: 'ja-JP', name: 'Japanese' },
    { code: 'ko-KR', name: 'Korean' },
  ];

  const sensitivityLevels = [
    { value: 'low', label: 'Low', description: 'Less sensitive, reduces false triggers' },
    { value: 'medium', label: 'Medium', description: 'Balanced sensitivity (recommended)' },
    { value: 'high', label: 'High', description: 'More sensitive, better for quiet environments' },
  ];

  return (
    <Box className="flex-1">
      {/* Background Gradient */}
      <LinearGradient
        colors={['#1e1b4b', '#312e81', '#4338ca', '#6366f1']}
        style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0 }}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <Animated.View style={[containerStyle, { flex: 1 }]}>
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          {/* Header */}
          <VStack space="md" className="pt-16 px-6 pb-4">
            <HStack className="items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                onPress={() => router.back()}
                className="bg-white/10 border-white/20"
              >
                <HStack className="items-center space-x-2">
                  <ArrowLeftIcon size={16} color="white" />
                  <ButtonText className="text-white text-sm">Back</ButtonText>
                </HStack>
              </Button>
            </HStack>

            <VStack space="sm">
              <HStack className="items-center space-x-3">
                <Animated.View style={micStyle}>
                  <Box className="w-12 h-12 justify-center items-center rounded-full bg-candyBlue/20">
                    <MicIcon size={24} color="#3B82F6" />
                  </Box>
                </Animated.View>
                <VStack space="xs">
                  <Text className="text-white text-3xl font-bold">
                    Voice Settings
                  </Text>
                  <Text className="text-white/70 text-base">
                    Configure voice input and speech recognition
                  </Text>
                </VStack>
              </HStack>
            </VStack>
          </VStack>

          {/* Voice Input Settings */}
          <VStack space="md" className="px-6 mb-6">
            <Text className="text-white text-xl font-semibold">
              Voice Input
            </Text>

            <VStack space="sm">
              {/* Enable Voice Input */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Enable Voice Input
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Allow voice commands and speech recognition
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.voiceInputEnabled}
                    onValueChange={(value) => handleSettingChange('voiceInputEnabled', value)}
                  />
                </HStack>
              </Box>

              {/* Continuous Listening */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Continuous Listening
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Keep microphone active for hands-free operation
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.continuousListening}
                    onValueChange={(value) => handleSettingChange('continuousListening', value)}
                    disabled={!settings.voiceInputEnabled}
                  />
                </HStack>
              </Box>

              {/* Voice Commands */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Voice Commands
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Enable navigation and control via voice
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.voiceCommands}
                    onValueChange={(value) => handleSettingChange('voiceCommands', value)}
                    disabled={!settings.voiceInputEnabled}
                  />
                </HStack>
              </Box>

              {/* Speech Feedback */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Speech Feedback
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Provide audio responses and confirmations
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.speechFeedback}
                    onValueChange={(value) => handleSettingChange('speechFeedback', value)}
                  />
                </HStack>
              </Box>
            </VStack>
          </VStack>

          {/* Language Settings */}
          <VStack space="md" className="px-6 mb-6">
            <Text className="text-white text-xl font-semibold">
              Language & Recognition
            </Text>

            <VStack space="sm">
              {/* Language Selection */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <VStack space="sm">
                  <Text className="text-white text-base font-medium">
                    Recognition Language
                  </Text>
                  <Text className="text-white/70 text-sm mb-2">
                    Current: {languages.find(l => l.code === settings.language)?.name}
                  </Text>
                  <Button
                    onPress={() => {
                      // TODO: Implement language picker
                      Alert.alert('Language Selection', 'Language picker coming soon!');
                    }}
                    className="bg-candyBlue/20 border border-candyBlue/30"
                  >
                    <ButtonText className="text-candyBlue">Change Language</ButtonText>
                  </Button>
                </VStack>
              </Box>

              {/* Sensitivity */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <VStack space="sm">
                  <Text className="text-white text-base font-medium">
                    Microphone Sensitivity
                  </Text>
                  <Text className="text-white/70 text-sm mb-2">
                    Current: {sensitivityLevels.find(s => s.value === settings.sensitivity)?.label}
                  </Text>
                  <Button
                    onPress={() => {
                      // TODO: Implement sensitivity picker
                      Alert.alert('Sensitivity Settings', 'Sensitivity picker coming soon!');
                    }}
                    className="bg-candyPurple/20 border border-candyPurple/30"
                  >
                    <ButtonText className="text-candyPurple">Adjust Sensitivity</ButtonText>
                  </Button>
                </VStack>
              </Box>

              {/* Noise Reduction */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center justify-between">
                  <VStack space="xs" className="flex-1">
                    <Text className="text-white text-base font-medium">
                      Noise Reduction
                    </Text>
                    <Text className="text-white/70 text-sm">
                      Filter background noise for better recognition
                    </Text>
                  </VStack>
                  <Switch
                    value={settings.noiseReduction}
                    onValueChange={(value) => handleSettingChange('noiseReduction', value)}
                  />
                </HStack>
              </Box>
            </VStack>
          </VStack>

          {/* Testing & Calibration */}
          <VStack space="md" className="px-6 mb-8">
            <Text className="text-white text-xl font-semibold">
              Testing & Calibration
            </Text>

            <VStack space="sm">
              <Button
                onPress={handleTestVoiceInput}
                disabled={isTesting || !settings.voiceInputEnabled}
                className="bg-emerald-600/20 border border-emerald-400/30 rounded-xl p-4"
              >
                <HStack className="items-center space-x-3">
                  {isTesting ? (
                    <StopCircleIcon size={20} color="#10B981" />
                  ) : (
                    <PlayIcon size={20} color="#10B981" />
                  )}
                  <ButtonText className="text-emerald-300 text-base font-medium">
                    {isTesting ? 'Testing Voice Input...' : 'Test Voice Input'}
                  </ButtonText>
                </HStack>
              </Button>

              <Button
                onPress={handleCalibrateMicrophone}
                disabled={!settings.voiceInputEnabled}
                className="bg-candyOrange/20 border border-candyOrange/30 rounded-xl p-4"
              >
                <HStack className="items-center space-x-3">
                  <SettingsIcon size={20} color="#F97316" />
                  <ButtonText className="text-candyOrange text-base font-medium">
                    Calibrate Microphone
                  </ButtonText>
                </HStack>
              </Button>
            </VStack>
          </VStack>

          {/* Loading State */}
          {isLoading && (
            <VStack space="md" className="px-6 py-8">
              <Box className="bg-white/10 rounded-xl p-6 items-center">
                <MicIcon size={32} color="white" />
                <Text className="text-white text-lg font-medium mt-4 mb-2">
                  Loading Voice Settings...
                </Text>
                <Text className="text-white/70 text-sm text-center">
                  Configuring voice input and speech recognition
                </Text>
              </Box>
            </VStack>
          )}

          {/* Footer Spacing */}
          <Box className="h-8" />
        </ScrollView>
      </Animated.View>
    </Box>
  );
}
