import { MaterialIcons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useState } from "react";
import { Alert } from "react-native";
import { Box } from "@/components/ui/box";
import { Button, ButtonText } from "@/components/ui/button";
import { HStack } from "@/components/ui/hstack";
import { Spinner } from "@/components/ui/spinner";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { useAuth } from "@/lib/contexts/AuthContext";

export default function Welcome() {
	const router = useRouter();
	const { createAnonymousSession } = useAuth();
	const [isLoading, setIsLoading] = useState(false);

	const handleGetStarted = () => {
		router.push("/auth/login");
	};

	const handleGuestMode = async () => {
		try {
			setIsLoading(true);
			console.log("🔐 Creating anonymous session from welcome screen...");
			await createAnonymousSession();
			router.replace("/(tabs)/home");
		} catch (error) {
			console.error("Failed to create anonymous session:", error);
			Alert.alert("Error", "Failed to start guest mode. Please try again.", [
				{ text: "OK" },
			]);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Box className="flex-1 bg-background">
			{/* Main Content */}
			<VStack className="flex-1 justify-center items-center px-6">
				{/* App Icon/Logo */}
				<Box className="mb-8">
					<MaterialIcons name="document-scanner" size={80} color="#FF6B9D" />
				</Box>

				{/* Welcome Text */}
				<VStack space="md" className="items-center mb-12">
					<Text
						size="3xl"
						className="font-bold text-center text-typography-900"
						color="primary"
					>
						Welcome to LearniScan
					</Text>
					<Text size="lg" className="text-center text-typography-600 max-w-sm">
						Transform any document into an interactive learning experience with
						AI-powered knowledge cards
					</Text>
				</VStack>

				{/* Feature Highlights */}
				<VStack space="sm" className="items-center mb-12">
					<HStack space="sm" className="items-center">
						<MaterialIcons name="camera-alt" size={20} color="#A855F7" />
						<Text size="sm" color="secondary">
							Smart Document Scanning
						</Text>
					</HStack>
					<HStack space="sm" className="items-center">
						<MaterialIcons name="psychology" size={20} color="#3B82F6" />
						<Text size="sm" color="tertiary">
							AI-Powered Learning Cards
						</Text>
					</HStack>
					<HStack space="sm" className="items-center">
						<MaterialIcons name="share" size={20} color="#10B981" />
						<Text size="sm" className="text-green-600">
							Social Learning & Sharing
						</Text>
					</HStack>
				</VStack>
			</VStack>

			{/* Action Buttons */}
			<VStack space="md" className="px-6 pb-12">
				<Button
					action="candyPink"
					size="lg"
					onPress={handleGetStarted}
					className="w-full"
				>
					<ButtonText>Get Started</ButtonText>
				</Button>

				<Button
					action="glass"
					size="lg"
					onPress={handleGuestMode}
					isDisabled={isLoading}
					className="w-full"
				>
					{isLoading ? (
						<HStack space="sm" className="items-center">
							<Spinner size="small" color="#FF6B9D" />
							<ButtonText>Starting Guest Mode...</ButtonText>
						</HStack>
					) : (
						<ButtonText>Continue as Guest</ButtonText>
					)}
				</Button>
			</VStack>
		</Box>
	);
}
