/**
 * Enhanced Chat API for LearniScan
 *
 * Updated to use the new AI service configuration with proper error handling
 * and support for multiple AI operations (chat, OCR, translation, etc.)
 *
 * Following LearniScan development workflow rules:
 * - Rule 11: Zod validation for all inputs
 * - Rule 12: Comprehensive error handling with early returns
 * - Rule 5: TypeScript quality and type safety
 */

import { z } from 'zod';
import { streamText } from 'ai';
import { aiModels, AI_CONFIG } from '@/lib/config/ai.config';
import { aiService, AIServiceError } from '@/lib/services/ai.service';

// CORS headers for cross-origin requests
const CORS_HEADERS = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
  "Content-Type": "application/octet-stream",
};

// Request validation schema (Rule 11)
const ChatRequestSchema = z.object({
  messages: z.array(z.object({
    role: z.enum(['user', 'assistant', 'system']),
    content: z.string(),
  })),
  type: z.enum(['chat', 'ocr', 'translation']).default('chat'),
  imageData: z.string().optional(),
  translationTarget: z.string().optional(),
});

// Health check endpoint
export const GET = async () => {
  try {
    const health = await aiService.checkHealth();
    return Response.json({
      status: 'ok',
      ai_service: health,
      config: {
        baseURL: AI_CONFIG.baseURL,
        models: AI_CONFIG.models,
      },
    });
  } catch (error) {
    return Response.json(
      { status: 'error', message: 'AI service unavailable' },
      { status: 503 }
    );
  }
};

// Main chat endpoint with multi-modal support
export const POST = async (req: Request) => {
  try {
    // Parse and validate request (Rule 11)
    const body = await req.json();
    const validatedRequest = ChatRequestSchema.parse(body);

    // Handle different request types (Rule 12: Early return pattern)
    switch (validatedRequest.type) {
      case 'ocr':
        return await handleOCRRequest(validatedRequest);
      case 'translation':
        return await handleTranslationRequest(validatedRequest);
      case 'chat':
      default:
        return await handleChatRequest(validatedRequest);
    }
  } catch (error) {
    console.error('Chat API Error:', error);

    // Handle validation errors (Rule 12)
    if (error instanceof z.ZodError) {
      return Response.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    // Handle AI service errors (Rule 12)
    if (error instanceof AIServiceError) {
      const status = error.retryable ? 429 : 400;
      return Response.json(
        { error: error.message, code: error.code, retryable: error.retryable },
        { status }
      );
    }

    // Generic error fallback
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
};

// Handle regular chat requests
async function handleChatRequest(request: z.infer<typeof ChatRequestSchema>) {
  const result = streamText({
    model: aiModels.chat,
    messages: request.messages,
    temperature: 0.7,
  });

  return result.toDataStreamResponse({
    headers: {
      ...CORS_HEADERS,
      'Content-Type': 'application/octet-stream', // Override CORS_HEADERS Content-Type
    }
  });
}

// Handle OCR requests
async function handleOCRRequest(request: z.infer<typeof ChatRequestSchema>) {
  if (!request.imageData) {
    return Response.json(
      { error: 'Image data is required for OCR requests' },
      { status: 400 }
    );
  }

  const ocrResult = await aiService.extractTextFromImage({
    imageUri: request.imageData,
    enhanceAccuracy: true,
  });

  return Response.json(ocrResult);
}

// Handle translation requests
async function handleTranslationRequest(request: z.infer<typeof ChatRequestSchema>) {
  const lastMessage = request.messages[request.messages.length - 1];
  if (!lastMessage || !request.translationTarget) {
    return Response.json(
      { error: 'Text and target language are required for translation' },
      { status: 400 }
    );
  }

  const translationResult = await aiService.translateText({
    text: lastMessage.content,
    targetLanguage: request.translationTarget,
  });

  return Response.json(translationResult);
}

// CORS preflight handler
export const OPTIONS = async () => {
  return Response.json(null, { headers: CORS_HEADERS });
};

