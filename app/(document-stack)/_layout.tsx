import { Stack } from 'expo-router';
import { useColorScheme } from 'react-native';

export default function DocumentStackLayout() {
  const colorScheme = useColorScheme();

  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: colorScheme === 'dark' ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.95)',
        },
        headerTintColor: colorScheme === 'dark' ? '#F3F4F6' : '#111827',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        headerShadowVisible: false,
        headerTransparent: true,
        headerBlurEffect: 'regular',
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen 
        name="document-viewer" 
        options={{ 
          title: 'Document Viewer',
          headerBackTitle: 'Back'
        }} 
      />
      <Stack.Screen 
        name="document-editor" 
        options={{ 
          title: 'Edit Document',
          headerBackTitle: 'Cancel'
        }} 
      />
      <Stack.Screen 
        name="text-recognition" 
        options={{ 
          title: 'Text Recognition',
          headerBackTitle: 'Back'
        }} 
      />
      <Stack.Screen 
        name="enhancement-controls" 
        options={{ 
          title: 'Image Enhancement',
          headerBackTitle: 'Back'
        }} 
      />
      <Stack.Screen 
        name="ai-processing" 
        options={{ 
          title: 'AI Processing',
          headerBackTitle: 'Cancel',
          gestureEnabled: false // Prevent going back during processing
        }} 
      />
    </Stack>
  );
}