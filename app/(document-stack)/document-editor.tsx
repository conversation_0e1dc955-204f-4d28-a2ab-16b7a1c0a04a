import React, { useState } from 'react';
import { ScrollView, View } from 'react-native';
import { Text } from '../../components/ui/text';
import { VStack } from '../../components/ui/vstack';
import { HStack } from '../../components/ui/hstack';
import { Box } from '../../components/ui/box';
import { Button, ButtonText } from '../../components/ui/button';
import { Input, InputField } from '../../components/ui/input';
import { Textarea, TextareaInput } from '../../components/ui/textarea';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { 
  EnhancementControls
} from '../../components/learni-scan';
import { 
  SaveIcon,
  UndoIcon,
  RedoIcon,
  SettingsIcon,
  FileTextIcon
} from 'lucide-react-native';

export default function DocumentEditorScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  
  const [documentTitle, setDocumentTitle] = useState('Meeting Notes - Q4 Planning');
  const [documentDescription, setDocumentDescription] = useState('Strategic planning session notes from December 15th');
  const [documentTags, setDocumentTags] = useState('meeting, planning, q4, strategy');

  const handleSave = () => {
    console.log('Save document changes');
    router.back();
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <View className="flex-1 bg-background-0">
      {/* Editor Header */}
      <Box className="bg-glass-bg-primary border-b border-glass-border-primary backdrop-blur-md pt-16 pb-4 px-4">
        <HStack className="justify-between items-center">
          <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
            Edit Document
          </Text>
          
          <HStack className="space-x-2">
            <Button action="glass" variant="outline" size="sm" onPress={handleCancel}>
              <ButtonText>Cancel</ButtonText>
            </Button>
            <Button action="candyPink" size="sm" onPress={handleSave}>
              <HStack className="items-center space-x-1">
                <SaveIcon size={14} color="#FFFFFF" />
                <ButtonText>Save</ButtonText>
              </HStack>
            </Button>
          </HStack>
        </HStack>
      </Box>

      <ScrollView className="flex-1 p-4">
        <VStack space="lg">
          {/* Document Metadata */}
          <VStack space="md">
            <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
              Document Information
            </Text>
            
            <VStack space="sm">
              <VStack space="xs">
                <Text color="primary" size="sm" style={{ fontWeight: '600' }}>
                  Title
                </Text>
                <Input variant="outline" size="md">
                  <InputField
                    value={documentTitle}
                    onChangeText={setDocumentTitle}
                    placeholder="Enter document title"
                  />
                </Input>
              </VStack>
              
              <VStack space="xs">
                <Text color="primary" size="sm" style={{ fontWeight: '600' }}>
                  Description
                </Text>
                <Textarea variant="outline" size="md">
                  <TextareaInput
                    value={documentDescription}
                    onChangeText={setDocumentDescription}
                    placeholder="Enter document description"
                    numberOfLines={3}
                  />
                </Textarea>
              </VStack>
              
              <VStack space="xs">
                <Text color="primary" size="sm" style={{ fontWeight: '600' }}>
                  Tags (comma separated)
                </Text>
                <Input variant="outline" size="md">
                  <InputField
                    value={documentTags}
                    onChangeText={setDocumentTags}
                    placeholder="Enter tags separated by commas"
                  />
                </Input>
              </VStack>
            </VStack>
          </VStack>

          {/* Document Preview */}
          <VStack space="md">
            <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
              Preview
            </Text>
            
            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-8 min-h-64">
              <VStack space="md" className="items-center justify-center flex-1">
                <Box className="w-20 h-20 justify-center items-center rounded-full bg-glass-bg-secondary">
                  <FileTextIcon size={40} color="#9CA3AF" />
                </Box>
                <VStack space="xs" className="items-center">
                  <Text color="primary" size="md" style={{ fontWeight: 'bold' }}>
                    Document Editor
                  </Text>
                  <Text color="secondary" size="sm" className="text-center">
                    Document editing interface would appear here
                  </Text>
                </VStack>
              </VStack>
            </Box>
          </VStack>
        </VStack>
      </ScrollView>
    </View>
  );
}