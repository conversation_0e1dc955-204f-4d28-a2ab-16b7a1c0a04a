import React, { useState } from 'react';
import { ScrollView, View, Pressable } from 'react-native';
import { Text } from '../../components/ui/text';
import { VStack } from '../../components/ui/vstack';
import { HStack } from '../../components/ui/hstack';
import { Box } from '../../components/ui/box';
import { Button, ButtonText } from '../../components/ui/button';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { 
  TextRecognitionDisplay,
  ConfidenceScore,
  DocumentCard
} from '../../components/learni-scan';
import { 
  EyeIcon,
  EditIcon,
  ShareIcon,
  DownloadIcon,
  ZoomInIcon,
  ZoomOutIcon,
  FileTextIcon
} from 'lucide-react-native';

export default function DocumentViewerScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();

  // Mock document data
  const [document] = useState({
    id: (typeof params.id === 'string' ? params.id : 'doc-001'),
    title: 'Meeting Notes - Q4 Planning',
    description: 'Strategic planning session notes from December 15th',
    thumbnailUri: undefined,
    type: 'document' as const,
    size: 2048576,
    createdAt: new Date('2024-12-15'),
    modifiedAt: new Date('2024-12-16'),
    tags: ['meeting', 'planning', 'q4', 'strategy'],
    folder: 'Work Documents',
    isFavorite: true,
    confidence: 92,
    pageCount: 5,
    ocrStatus: 'completed' as const,
    metadata: {
      language: 'en',
      wordCount: 1247,
      resolution: '300dpi',
      colorSpace: 'sRGB'
    }
  });

  const handleEdit = () => {
    router.push({
      pathname: '/(document-stack)/document-editor',
      params: { id: document.id }
    });
  };

  return (
    <View className="flex-1 bg-background-0">
      {/* Document Controls */}
      <Box className="bg-glass-bg-primary border-b border-glass-border-primary backdrop-blur-md pt-16 pb-4 px-4">
        <HStack className="justify-between items-center">
          <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
            {document.title}
          </Text>
          
          <HStack className="space-x-2">
            <Button action="candyPink" variant="outline" size="sm">
              <HStack className="items-center space-x-1">
                <EyeIcon size={14} color="#FF6B9D" />
                <ButtonText>OCR</ButtonText>
              </HStack>
            </Button>
            <Button action="candyPurple" variant="outline" size="sm" onPress={handleEdit}>
              <HStack className="items-center space-x-1">
                <EditIcon size={14} color="#A855F7" />
                <ButtonText>Edit</ButtonText>
              </HStack>
            </Button>
          </HStack>
        </HStack>
      </Box>

      <ScrollView className="flex-1 p-4">
        <VStack space="lg">
          {/* Document Preview Placeholder */}
          <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-8 min-h-96">
            <VStack space="md" className="items-center justify-center flex-1">
              <Box className="w-24 h-24 justify-center items-center rounded-full bg-glass-bg-secondary">
                <FileTextIcon size={48} color="#9CA3AF" />
              </Box>
              <VStack space="xs" className="items-center">
                <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                  Document Viewer
                </Text>
                <Text color="secondary" size="sm" className="text-center">
                  Document image and content would be displayed here
                </Text>
              </VStack>
            </VStack>
          </Box>

          {/* Document Info */}
          <DocumentCard
            document={document}
            onPress={() => {}}
            onShare={() => console.log('Share document')}
            onDownload={() => console.log('Download document')}
            onEdit={handleEdit}
            onDelete={() => console.log('Delete document')}
            onFavorite={() => console.log('Toggle favorite')}
            showActions={true}
            variant="glass"
            size="lg"
            layout="card"
          />
        </VStack>
      </ScrollView>
    </View>
  );
}