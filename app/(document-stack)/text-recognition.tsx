import React, { useState } from 'react';
import { ScrollView, View } from 'react-native';
import { Text } from '../../components/ui/text';
import { VStack } from '../../components/ui/vstack';
import { HStack } from '../../components/ui/hstack';
import { Box } from '../../components/ui/box';
import { Button, ButtonText } from '../../components/ui/button';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { 
  TextRecognitionDisplay,
  ConfidenceScore
} from '../../components/learni-scan';
import { 
  EyeIcon,
  CopyIcon,
  ShareIcon,
  SearchIcon,
  FileTextIcon
} from 'lucide-react-native';

export default function TextRecognitionScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();

  // Mock recognized text data
  const [recognizedText] = useState([
    {
      id: 'text-001',
      text: 'Q4 Planning Meeting Notes',
      confidence: 96,
      bounds: { x: 120, y: 80, width: 280, height: 32 },
      isSelected: false,
      type: 'line' as const
    },
    {
      id: 'text-002',
      text: 'Strategic initiatives for the upcoming quarter including budget allocation and resource planning.',
      confidence: 92,
      bounds: { x: 80, y: 140, width: 520, height: 48 },
      isSelected: false,
      type: 'paragraph' as const
    }
  ]);

  const [selectedTextIds, setSelectedTextIds] = useState<string[]>([]);

  const handleTextSelection = (textIds: string[]) => {
    setSelectedTextIds(textIds);
  };

  const overallConfidence = Math.round(
    recognizedText.reduce((sum, text) => sum + text.confidence, 0) / recognizedText.length
  );

  return (
    <View className="flex-1 bg-background-0">
      {/* Text Recognition Header */}
      <Box className="bg-glass-bg-primary border-b border-glass-border-primary backdrop-blur-md pt-16 pb-4 px-4">
        <HStack className="justify-between items-center">
          <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
            Text Recognition
          </Text>
          
          <Text color="secondary" size="sm">
            {selectedTextIds.length} selected
          </Text>
        </HStack>
      </Box>

      <ScrollView className="flex-1 p-4">
        <VStack space="lg">
          {/* Overall Confidence */}
          <ConfidenceScore
            score={overallConfidence}
            breakdown={[
              { category: 'Text Detection', score: 94, weight: 0.4 },
              { category: 'Character Recognition', score: 91, weight: 0.35 },
              { category: 'Language Processing', score: 89, weight: 0.25 }
            ]}
            showBreakdown={true}
            animated={true}
            variant="glass"
            size="md"
          />

          {/* Text Recognition Display */}
          <VStack space="md">
            <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
              Recognized Text
            </Text>
            
            <TextRecognitionDisplay
              recognizedText={recognizedText}
              confidence={overallConfidence}
              onTextSelect={handleTextSelection}
              onCopy={(text) => console.log('Copy:', text)}
              onShare={(text) => console.log('Share:', text)}
              onSearch={(text) => console.log('Search:', text)}
              showConfidence={true}
              editable={true}
              variant="glass"
            />
          </VStack>

          {/* Text Statistics */}
          <VStack space="md">
            <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
              Statistics
            </Text>
            
            <HStack className="space-x-3">
              <Box className="flex-1 bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
                <VStack space="xs">
                  <Text color="candyPink" size="xl" style={{ fontWeight: 'bold' }}>
                    {recognizedText.length}
                  </Text>
                  <Text color="secondary" size="sm">
                    Text Blocks
                  </Text>
                </VStack>
              </Box>
              
              <Box className="flex-1 bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
                <VStack space="xs">
                  <Text color="candyPink" size="xl" style={{ fontWeight: 'bold' }}>
                    {overallConfidence}%
                  </Text>
                  <Text color="secondary" size="sm">
                    Confidence
                  </Text>
                </VStack>
              </Box>
            </HStack>
          </VStack>
        </VStack>
      </ScrollView>
    </View>
  );
}