import AsyncStorage from "@react-native-async-storage/async-storage";
import { Redirect } from "expo-router";
import { useEffect, useState } from "react";
import { ActivityIndicator, View } from "react-native";
import { Text } from "@/components/ui/text";
import { useAuth } from "@/lib/contexts/AuthContext";

export default function Index() {
	// In development mode, bypass all authentication and go directly to home
	if (__DEV__) {
		console.log('🚧 Development Mode: Bypassing authentication, going to home');
		return <Redirect href="/(tabs)/home" />;
	}

	const { isAuthenticated, isLoading } = useAuth();
	const [isFirstTime, setIsFirstTime] = useState(true);
	const [isCheckingFirstTime, setIsCheckingFirstTime] = useState(true);

	useEffect(() => {
		checkFirstTimeStatus();
	}, []);

	const checkFirstTimeStatus = async () => {
		try {
			// Check if this is the first time visiting the app
			const hasVisited = await AsyncStorage.getItem("hasVisited");
			if (!hasVisited) {
				setIsFirstTime(true);
				await AsyncStorage.setItem("hasVisited", "true");
			} else {
				setIsFirstTime(false);
			}
		} catch (error) {
			console.error("Error checking first time status:", error);
			setIsFirstTime(false); // Default to not first time on error
		} finally {
			setIsCheckingFirstTime(false);
		}
	};

	// Show loading while checking authentication and first time status
	if (isLoading || isCheckingFirstTime) {
		return (
			<View className="flex-1 justify-center items-center bg-background">
				<ActivityIndicator size="large" color="#FF6B9D" />
				<Text className="mt-4 text-lg text-typography-600">Loading...</Text>
			</View>
		);
	}

	// Original routing logic for production
	if (isFirstTime) {
		return <Redirect href="/welcome" />;
	}
	if (isAuthenticated) {
		return <Redirect href="/(tabs)/home" />;
	}
	return <Redirect href="/auth/login" />;
}
