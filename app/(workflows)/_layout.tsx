import { Stack } from "expo-router";

export default function WorkflowsLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="text-review" 
        options={{ 
          title: "Text Review",
          headerBackTitle: "Back"
        }} 
      />
      <Stack.Screen 
        name="social-share" 
        options={{ 
          title: "Share Knowledge",
          presentation: 'modal'
        }} 
      />
    </Stack>
  );
}
