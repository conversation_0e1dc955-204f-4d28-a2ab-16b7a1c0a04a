import { Tabs } from "expo-router";
import {
  HomeIcon,
  CameraIcon,
  BrainIcon,
  SettingsIcon
} from 'lucide-react-native';
import AuthGuard from '@/components/auth/AuthGuard';

// Import custom morphing tab bar
import { MorphingTabBar } from '@/components/navigation/MorphingTabBar';

// Define candy colors using design tokens
const designTokens = {
  colors: {
    primary: {
      500: '#FF6B9D', // candy-pink
    },
    secondary: {
      500: '#8B5CF6', // purple
    },
    tertiary: {
      500: '#3B82F6', // blue
    },
    success: {
      500: '#10B981', // green
    },
    background: {
      light: 'rgba(255, 255, 255, 0.85)',
      dark: 'rgba(31, 41, 55, 0.85)',
    },
    border: {
      light: 'rgba(255, 107, 157, 0.2)',
      dark: 'rgba(255, 107, 157, 0.15)',
    },
    text: {
      light: '#111827',
      dark: '#F9FAFB',
      muted: {
        light: '#6B7280',
        dark: '#9CA3AF',
      }
    }
  },
  shadows: {
    sm: {
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 6,
      elevation: 4,
    },
    md: {
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 12,
      elevation: 8,
    }
  }
};

// Tab configuration with associated colors - Core 4 tabs only
const tabConfig = [
  {
    name: "home",
    title: "Home",
    color: designTokens.colors.success[500],
    icon: HomeIcon,
  },
  {
    name: "scan",
    title: "Scan",
    color: designTokens.colors.primary[500],
    icon: CameraIcon,
  },
  {
    name: "knowledge",
    title: "Knowledge",
    color: designTokens.colors.secondary[500],
    icon: BrainIcon,
  },
  {
    name: "profile",
    title: "Profile",
    color: designTokens.colors.tertiary[500],
    icon: SettingsIcon,
  }
];

export const unstable_settings = {
  initialRouteName: 'home',
};

export const TabLayout = () => {

  return (
    <AuthGuard>
      <Tabs
        tabBar={(props) => <MorphingTabBar {...props} />}
        screenOptions={{
          headerShown: false,
        }}
      >
        {tabConfig.map((tab) => (
          <Tabs.Screen
            key={tab.name}
            name={tab.name}
            options={{
              title: tab.title,
            }}
          />
        ))}
      </Tabs>
    </AuthGuard>
  );
}

export default TabLayout;
