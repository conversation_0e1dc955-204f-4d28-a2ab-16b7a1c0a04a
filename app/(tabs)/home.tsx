import React from 'react';
import { ScrollView, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Box } from '@/components/ui/box';
import { HomeBackground } from '@/components/home/<USER>';
import { HomeHeader } from '@/components/home/<USER>';
import { HeroSection } from '@/components/home/<USER>';
import { StatsSection } from '@/components/home/<USER>';

export default function Home() {
  return (
    <Box className="flex-1 relative">
      <HomeBackground />

      <SafeAreaView style={styles.safeAreaContainer}>
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <HomeHeader />
          <HeroSection />
          <StatsSection />
        </ScrollView>
      </SafeAreaView>
    </Box>
  );
};

const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    position: 'relative',
    zIndex: 10,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingBottom: 40,
    paddingTop: 20,
  },
});
