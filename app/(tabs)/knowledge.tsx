import React, { useState, useEffect, useRef } from 'react';
import { ScrollView, Pressable, Dimensions, NativeScrollEvent, NativeSyntheticEvent } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

// Import tab bar visibility context
import { useTabBarVisibility } from '@/contexts/TabBarVisibilityContext';

// UI Components
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { Card, CardHeader, CardBody } from '@/components/ui/card';
import { Badge, BadgeText } from '@/components/ui/badge';
import { Button, ButtonText } from '@/components/ui/button';

// Icons
import {
  BrainIcon,
  BookOpenIcon,
  TrendingUpIcon,
  MessageSquareIcon,
  SproutIcon,
  BarChart3Icon,
  PlayIcon,
  ChevronRightIcon,
  SparklesIcon,
  GitBranchIcon,
  BookIcon,
  ClockIcon,
  CheckCircleIcon,
  AlertCircleIcon
} from 'lucide-react-native';

// Contexts and Services
// import { useKnowledgeCards } from '@/lib/contexts/KnowledgeCardsContext';
// import { useAuth } from '@/lib/contexts/AuthContext';

const { width: screenWidth } = Dimensions.get('window');

interface FeatureCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  iconColor: string;
  iconBgColor: string;
  onPress: () => void;
  badge?: string;
  badgeColor?: string;
  status?: 'available' | 'progress' | 'completed' | 'locked';
  stats?: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon: Icon,
  iconColor,
  iconBgColor,
  onPress,
  badge,
  badgeColor = 'bg-blue-500',
  status = 'available',
  stats
}) => {
  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon size={16} color="#10B981" />;
      case 'progress':
        return <ClockIcon size={16} color="#F59E0B" />;
      case 'locked':
        return <AlertCircleIcon size={16} color="#6B7280" />;
      default:
        return null;
    }
  };

  return (
    <Pressable onPress={onPress} className="mb-3">
      <Box className="bg-gradient-to-br from-white/25 to-white/15 backdrop-blur-2xl rounded-2xl p-5 border border-white/40 shadow-xl relative overflow-hidden">
        {/* Background decorative elements */}
        <Box className="absolute top-0 right-0 w-16 h-16 bg-white/5 rounded-full blur-xl" />
        <Box className="absolute bottom-0 left-0 w-12 h-12 bg-white/3 rounded-full blur-lg" />

        <VStack space="md" className="relative z-10">
          <HStack className="items-start justify-between">
            <HStack className="items-center space-x-4 flex-1">
              <Box
                className="w-14 h-14 justify-center items-center rounded-2xl shadow-xl border border-white/30"
                style={{ backgroundColor: iconBgColor }}
              >
                <Icon size={28} color={iconColor} />
              </Box>
              <VStack space="xs" className="flex-1">
                <HStack className="items-center space-x-2">
                  <Text className="text-white text-lg font-bold">
                    {title}
                  </Text>
                  {getStatusIcon()}
                </HStack>
                {stats && (
                  <Text className="text-white/80 text-sm font-semibold">
                    {stats}
                  </Text>
                )}
              </VStack>
            </HStack>
            {badge && (
              <Box className={`${badgeColor} rounded-xl px-3 py-1.5 shadow-lg border border-white/20`}>
                <Text className="text-white text-xs font-bold">
                  {badge}
                </Text>
              </Box>
            )}
          </HStack>

          <Text className="text-white/90 text-sm leading-relaxed font-medium">
            {description}
          </Text>

          <HStack className="items-center justify-between">
            <Box className="bg-gradient-to-r from-white/30 to-white/20 backdrop-blur-xl rounded-xl px-5 py-2.5 border border-white/50 shadow-lg">
              <HStack className="items-center space-x-2">
                <Text className="text-white font-bold text-sm">Open</Text>
                <ChevronRightIcon size={16} color="white" />
              </HStack>
            </Box>
          </HStack>
        </VStack>
      </Box>
    </Pressable>
  );
};

export default function KnowledgeHub() {
  // const { user } = useAuth();
  // const { state: cardsState, loadCards } = useKnowledgeCards();
  const [isLoading, setIsLoading] = useState(false);

  // Get route parameters for post-scan navigation
  const params = useLocalSearchParams();
  const isFromScan = params.source === 'scan';
  const highlightNew = params.highlightNew === 'true';
  const newCardIds = params.newCardIds ? JSON.parse(params.newCardIds as string) : [];

  // Tab bar visibility for scroll-based auto-hide
  const { hideTabBar, showTabBar } = useTabBarVisibility();
  const scrollY = useRef(0);
  const lastScrollY = useRef(0);

  // Handle scroll events for tab bar auto-hide
  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentScrollY = event.nativeEvent.contentOffset.y;
    const scrollDelta = currentScrollY - lastScrollY.current;

    // Only trigger hide/show if scroll delta is significant (> 5px)
    if (Math.abs(scrollDelta) > 5) {
      if (scrollDelta > 0 && currentScrollY > 50) {
        // Scrolling down and past threshold - hide tab bar
        hideTabBar();
      } else if (scrollDelta < 0) {
        // Scrolling up - show tab bar
        showTabBar();
      }
    }

    lastScrollY.current = currentScrollY;
    scrollY.current = currentScrollY;
  };

  // Mock data for display
  const cardsState = { cards: [] };

  // useEffect(() => {
  //   const initializeHub = async () => {
  //     try {
  //       await loadCards(true);
  //     } catch (error) {
  //       console.error('Failed to load knowledge data:', error);
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };

  //   initializeHub();
  // }, []);

  const knowledgeFeatures = [
    {
      title: "Knowledge Cards",
      description: "View, create, and manage your personalized knowledge cards for effective learning",
      icon: BookOpenIcon,
      iconColor: "#FF6B9D",
      iconBgColor: "rgba(255, 107, 157, 0.2)",
      onPress: () => {
        router.push({
          pathname: '/(knowledge)/cards',
          params: highlightNew ? {
            highlightNew: 'true',
            newCardIds: JSON.stringify(newCardIds),
            source: 'hub'
          } : {}
        });
      },
      badge: `${cardsState.cards.length} Cards`,
      badgeColor: "bg-pink-500",
      status: cardsState.cards.length > 0 ? 'progress' : 'available',
      stats: `${cardsState.cards.length} cards • Last updated today`
    },
    {
      title: "Knowledge Graph",
      description: "Explore interactive visualizations of your knowledge connections and relationships",
      icon: GitBranchIcon,
      iconColor: "#8B5CF6",
      iconBgColor: "rgba(139, 92, 246, 0.2)",
      onPress: () => {
        router.push({
          pathname: '/(knowledge)/graph',
          params: highlightNew ? {
            highlightNew: 'true',
            newCardIds: JSON.stringify(newCardIds),
            source: 'hub'
          } : {}
        });
      },
      badge: "Interactive",
      badgeColor: "bg-purple-500",
      status: 'available',
      stats: "Visual learning • AI-powered insights"
    },
    {
      title: "Study Sessions",
      description: "Start AI-powered study sessions with personalized recommendations and adaptive learning",
      icon: BookIcon,
      iconColor: "#10B981",
      iconBgColor: "rgba(16, 185, 129, 0.2)",
      onPress: () => console.log('Navigate to Study Sessions'),
      badge: "AI Powered",
      badgeColor: "bg-green-500",
      status: 'available',
      stats: "Adaptive learning • Progress tracking"
    },
    {
      title: "AI Learning Assistant",
      description: "Chat with your AI tutor for personalized explanations and learning guidance",
      icon: MessageSquareIcon,
      iconColor: "#3B82F6",
      iconBgColor: "rgba(59, 130, 246, 0.2)",
      onPress: () => console.log('Navigate to AI Learning Assistant'),
      badge: "Chat",
      badgeColor: "bg-blue-500",
      status: 'available',
      stats: "24/7 available • Context-aware responses"
    },
    {
      title: "Knowledge Seeding",
      description: "Seed your knowledge base with curated programming concepts and fundamentals",
      icon: SproutIcon,
      iconColor: "#F59E0B",
      iconBgColor: "rgba(245, 158, 11, 0.2)",
      onPress: () => console.log('Navigate to Knowledge Seeding'),
      badge: "5 Ready",
      badgeColor: "bg-yellow-500",
      status: 'available',
      stats: "Programming basics • Ready to seed"
    },
    {
      title: "Learning Analytics",
      description: "Track your learning progress, study patterns, and knowledge mastery over time",
      icon: BarChart3Icon,
      iconColor: "#EF4444",
      iconBgColor: "rgba(239, 68, 68, 0.2)",
      onPress: () => {
        // TODO: Implement analytics screen
        console.log('Navigate to Learning Analytics');
      },
      badge: "Coming Soon",
      badgeColor: "bg-red-500",
      status: 'locked',
      stats: "Progress insights • Study metrics"
    }
  ];

  if (isLoading) {
    return (
      <LinearGradient
        colors={['rgb(255, 107, 157)', 'rgb(168, 85, 247)', 'rgb(59, 130, 246)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={{ flex: 1 }}>
          <VStack className="flex-1 justify-center items-center">
            <Box className="w-16 h-16 bg-white/20 rounded-full items-center justify-center mb-4">
              <BrainIcon size={32} color="white" />
            </Box>
            <Text className="text-white text-lg font-semibold mb-2">Loading Knowledge Hub</Text>
            <Text className="text-white/70 text-sm text-center px-8">
              Preparing your personalized learning experience...
            </Text>
          </VStack>
        </SafeAreaView>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['rgb(255, 107, 157)', 'rgb(168, 85, 247)', 'rgb(59, 130, 246)']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView style={{ flex: 1 }}>
        {/* Compact Header with Animated Background */}
        <Box className="px-6 py-4 relative">
          {/* Animated Background Elements */}
          <Box className="absolute top-0 right-0 w-24 h-24 bg-emerald-500/20 rounded-full blur-3xl opacity-60" />
          <Box className="absolute top-8 left-8 w-16 h-16 bg-cyan-500/20 rounded-full blur-2xl opacity-40" />
          <Box className="absolute top-4 right-12 w-12 h-12 bg-purple-500/20 rounded-full blur-xl opacity-50" />

          <VStack space="md" className="relative z-10">
            {/* Compact Main Header */}
            <HStack className="items-center space-x-4">
              <Box className="w-14 h-14 bg-gradient-to-br from-emerald-500/30 to-cyan-500/30 backdrop-blur-xl rounded-2xl items-center justify-center shadow-xl border border-white/40">
                <BrainIcon size={28} color="white" />
              </Box>
              <VStack space="xs" className="flex-1">
                <Text className="text-white text-3xl font-bold tracking-tight">
                  Knowledge Hub
                </Text>
                <Text className="text-white/90 text-base font-medium">
                  Your centralized learning command center
                </Text>
              </VStack>
            </HStack>

            {/* Compact Welcome Card */}
            <Box className={`bg-gradient-to-r ${isFromScan ? 'from-green-500/30 to-emerald-500/20 border-green-300/40' : 'from-white/20 to-white/10 border-white/30'} backdrop-blur-2xl rounded-2xl p-4 border shadow-xl`}>
              <VStack space="xs">
                <Text className="text-white text-lg font-bold text-center">
                  {isFromScan ? '🎉 New Knowledge Cards Created!' : 'Welcome to your Knowledge Center ✨'}
                </Text>
                <Text className="text-white/85 text-center text-sm leading-relaxed">
                  {isFromScan
                    ? `Successfully generated ${newCardIds.length} knowledge card${newCardIds.length > 1 ? 's' : ''} from your scan. Explore them below!`
                    : 'Track progress, access resources, and discover new learning opportunities.'
                  }
                </Text>
              </VStack>
            </Box>
          </VStack>
        </Box>

        {/* Compact Stats Dashboard */}
        <Box className="px-6 mb-4">
          <HStack className="items-center justify-between mb-4">
            <Text className="text-white text-xl font-bold">Quick Overview</Text>
            <Pressable className="bg-gradient-to-r from-white/25 to-white/15 backdrop-blur-xl rounded-xl px-4 py-2 border border-white/40 shadow-lg">
              <Text className="text-white text-xs font-bold">View All</Text>
            </Pressable>
          </HStack>

          <HStack className="space-x-3">
            {/* Knowledge Cards Stat - Pink Theme */}
            <Box className="flex-1 bg-gradient-to-br from-pink-500/30 to-rose-500/20 backdrop-blur-2xl rounded-2xl p-4 border border-pink-300/30 shadow-xl relative overflow-hidden">
              {/* Background decoration */}
              <Box className="absolute top-0 right-0 w-12 h-12 bg-white/10 rounded-full blur-lg" />

              <VStack space="sm" className="relative z-10">
                <HStack className="items-center justify-between">
                  <Box className="w-12 h-12 bg-pink-500/40 rounded-xl items-center justify-center shadow-lg border border-pink-300/40">
                    <BookOpenIcon size={24} color="#FF6B9D" />
                  </Box>
                  <Text className="text-green-300 text-xs font-bold bg-green-500/20 px-2 py-1 rounded-full">
                    +{cardsState.cards.length}
                  </Text>
                </HStack>
                <VStack space="xs">
                  <Text className="text-white text-2xl font-bold tracking-tight">{cardsState.cards.length}</Text>
                  <Text className="text-white/90 text-xs font-semibold">Knowledge Cards</Text>
                </VStack>
              </VStack>
            </Box>

            {/* Study Sessions Stat - Green Theme */}
            <Box className="flex-1 bg-gradient-to-br from-emerald-500/30 to-green-500/20 backdrop-blur-2xl rounded-2xl p-4 border border-emerald-300/30 shadow-xl relative overflow-hidden">
              {/* Background decoration */}
              <Box className="absolute top-0 right-0 w-12 h-12 bg-white/10 rounded-full blur-lg" />

              <VStack space="sm" className="relative z-10">
                <HStack className="items-center justify-between">
                  <Box className="w-12 h-12 bg-emerald-500/40 rounded-xl items-center justify-center shadow-lg border border-emerald-300/40">
                    <BookIcon size={24} color="#10B981" />
                  </Box>
                  <Text className="text-blue-300 text-xs font-bold bg-blue-500/20 px-2 py-1 rounded-full">
                    Ready
                  </Text>
                </HStack>
                <VStack space="xs">
                  <Text className="text-white text-2xl font-bold tracking-tight">0</Text>
                  <Text className="text-white/90 text-xs font-semibold">Study Sessions</Text>
                </VStack>
              </VStack>
            </Box>

            {/* Mastery Level Stat - Purple Theme */}
            <Box className="flex-1 bg-gradient-to-br from-purple-500/30 to-violet-500/20 backdrop-blur-2xl rounded-2xl p-4 border border-purple-300/30 shadow-xl relative overflow-hidden">
              {/* Background decoration */}
              <Box className="absolute top-0 right-0 w-12 h-12 bg-white/10 rounded-full blur-lg" />

              <VStack space="sm" className="relative z-10">
                <HStack className="items-center justify-between">
                  <Box className="w-12 h-12 bg-purple-500/40 rounded-xl items-center justify-center shadow-lg border border-purple-300/40">
                    <TrendingUpIcon size={24} color="#8B5CF6" />
                  </Box>
                  <Text className="text-yellow-300 text-xs font-bold bg-yellow-500/20 px-2 py-1 rounded-full">
                    Growing
                  </Text>
                </HStack>
                <VStack space="xs">
                  <Text className="text-white text-2xl font-bold tracking-tight">0%</Text>
                  <Text className="text-white/90 text-xs font-semibold">Mastery Level</Text>
                </VStack>
              </VStack>
            </Box>
          </HStack>
        </Box>

        {/* Expanded Learning Tools Section */}
        <Box className="flex-1 px-6">
          <HStack className="items-center justify-between mb-4">
            <Text className="text-white text-xl font-bold">Learning Tools</Text>
            <Pressable className="bg-gradient-to-r from-white/25 to-white/15 backdrop-blur-xl rounded-xl px-4 py-2 border border-white/40 shadow-lg">
              <Text className="text-white text-xs font-bold">Customize</Text>
            </Pressable>
          </HStack>

          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 100 }}
            className="flex-1"
            onScroll={handleScroll}
            scrollEventThrottle={16}
          >
            <VStack space="md">
              {knowledgeFeatures.map((feature, index) => (
                <FeatureCard
                  key={index}
                  {...feature}
                />
              ))}
            </VStack>
          </ScrollView>
        </Box>
      </SafeAreaView>
    </LinearGradient>
  );
}
