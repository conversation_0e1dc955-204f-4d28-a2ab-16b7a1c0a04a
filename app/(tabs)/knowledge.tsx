import React, { useState, useEffect } from 'react';
import { ScrollView, Pressable, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

// UI Components
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { Card, CardHeader, CardBody } from '@/components/ui/card';
import { Badge, BadgeText } from '@/components/ui/badge';
import { Button, ButtonText } from '@/components/ui/button';

// Icons
import {
  BrainIcon,
  BookOpenIcon,
  TrendingUpIcon,
  MessageSquareIcon,
  SproutIcon,
  BarChart3Icon,
  PlayIcon,
  ChevronRightIcon,
  SparklesIcon,
  GitBranchIcon,
  BookIcon,
  ClockIcon,
  CheckCircleIcon,
  AlertCircleIcon
} from 'lucide-react-native';

// Contexts and Services
import { useKnowledgeCards } from '@/lib/contexts/KnowledgeCardsContext';
import { useAuth } from '@/lib/contexts/AuthContext';

const { width: screenWidth } = Dimensions.get('window');

interface FeatureCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  iconColor: string;
  iconBgColor: string;
  onPress: () => void;
  badge?: string;
  badgeColor?: string;
  status?: 'available' | 'progress' | 'completed' | 'locked';
  stats?: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon: Icon,
  iconColor,
  iconBgColor,
  onPress,
  badge,
  badgeColor = 'bg-blue-500',
  status = 'available',
  stats
}) => {
  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon size={16} color="#10B981" />;
      case 'progress':
        return <ClockIcon size={16} color="#F59E0B" />;
      case 'locked':
        return <AlertCircleIcon size={16} color="#6B7280" />;
      default:
        return null;
    }
  };

  return (
    <Pressable onPress={onPress} className="mb-4">
      <Card className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md">
        <CardHeader>
          <HStack className="items-center justify-between">
            <HStack className="items-center space-x-3 flex-1">
              <Box 
                className="w-12 h-12 justify-center items-center rounded-xl"
                style={{ backgroundColor: iconBgColor }}
              >
                <Icon size={24} color={iconColor} />
              </Box>
              <VStack space="xs" className="flex-1">
                <HStack className="items-center space-x-2">
                  <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                    {title}
                  </Text>
                  {getStatusIcon()}
                </HStack>
                {stats && (
                  <Text color="secondary" size="sm">
                    {stats}
                  </Text>
                )}
              </VStack>
            </HStack>
            {badge && (
              <Badge className={`${badgeColor} rounded-full`}>
                <BadgeText className="text-white text-xs font-medium">
                  {badge}
                </BadgeText>
              </Badge>
            )}
          </HStack>
        </CardHeader>
        <CardBody>
          <Text color="secondary" size="md" className="mb-3">
            {description}
          </Text>
          <HStack className="items-center justify-between">
            <Button action="glass" variant="outline" size="sm">
              <ButtonText>Open</ButtonText>
              <ChevronRightIcon size={16} color="#6B7280" className="ml-1" />
            </Button>
          </HStack>
        </CardBody>
      </Card>
    </Pressable>
  );
};

export default function KnowledgeHub() {
  const router = useRouter();
  const { user } = useAuth();
  const { state: cardsState, loadCards } = useKnowledgeCards();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initializeHub = async () => {
      try {
        await loadCards(true);
      } catch (error) {
        console.error('Failed to load knowledge data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeHub();
  }, []);

  const knowledgeFeatures = [
    {
      title: "Knowledge Cards",
      description: "View, create, and manage your personalized knowledge cards for effective learning",
      icon: BookOpenIcon,
      iconColor: "#FF6B9D",
      iconBgColor: "rgba(255, 107, 157, 0.2)",
      onPress: () => router.push('/(knowledge)/cards'),
      badge: `${cardsState.cards.length} Cards`,
      badgeColor: "bg-pink-500",
      status: cardsState.cards.length > 0 ? 'progress' : 'available',
      stats: `${cardsState.cards.length} cards • Last updated today`
    },
    {
      title: "Knowledge Graph",
      description: "Explore interactive visualizations of your knowledge connections and relationships",
      icon: GitBranchIcon,
      iconColor: "#8B5CF6",
      iconBgColor: "rgba(139, 92, 246, 0.2)",
      onPress: () => router.push('/(knowledge)/graph'),
      badge: "Interactive",
      badgeColor: "bg-purple-500",
      status: 'available',
      stats: "Visual learning • AI-powered insights"
    },
    {
      title: "Study Sessions",
      description: "Start AI-powered study sessions with personalized recommendations and adaptive learning",
      icon: BookIcon,
      iconColor: "#10B981",
      iconBgColor: "rgba(16, 185, 129, 0.2)",
      onPress: () => router.push('/(features)/study'),
      badge: "AI Powered",
      badgeColor: "bg-green-500",
      status: 'available',
      stats: "Adaptive learning • Progress tracking"
    },
    {
      title: "AI Learning Assistant",
      description: "Chat with your AI tutor for personalized explanations and learning guidance",
      icon: MessageSquareIcon,
      iconColor: "#3B82F6",
      iconBgColor: "rgba(59, 130, 246, 0.2)",
      onPress: () => router.push('/(features)/ai-chat'),
      badge: "Chat",
      badgeColor: "bg-blue-500",
      status: 'available',
      stats: "24/7 available • Context-aware responses"
    },
    {
      title: "Knowledge Seeding",
      description: "Seed your knowledge base with curated programming concepts and fundamentals",
      icon: SproutIcon,
      iconColor: "#F59E0B",
      iconBgColor: "rgba(245, 158, 11, 0.2)",
      onPress: () => router.push('/(features)/settings/knowledge-seeding'),
      badge: "5 Ready",
      badgeColor: "bg-yellow-500",
      status: 'available',
      stats: "Programming basics • Ready to seed"
    },
    {
      title: "Learning Analytics",
      description: "Track your learning progress, study patterns, and knowledge mastery over time",
      icon: BarChart3Icon,
      iconColor: "#EF4444",
      iconBgColor: "rgba(239, 68, 68, 0.2)",
      onPress: () => {
        // TODO: Implement analytics screen
        router.push('/(tabs)/profile');
      },
      badge: "Coming Soon",
      badgeColor: "bg-red-500",
      status: 'locked',
      stats: "Progress insights • Study metrics"
    }
  ];

  if (isLoading) {
    return (
      <LinearGradient
        colors={['rgb(255, 107, 157)', 'rgb(168, 85, 247)', 'rgb(59, 130, 246)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={{ flex: 1 }}>
          <VStack className="flex-1 justify-center items-center">
            <Box className="w-16 h-16 bg-white/20 rounded-full items-center justify-center mb-4">
              <BrainIcon size={32} color="white" />
            </Box>
            <Text className="text-white text-lg font-semibold mb-2">Loading Knowledge Hub</Text>
            <Text className="text-white/70 text-sm text-center px-8">
              Preparing your personalized learning experience...
            </Text>
          </VStack>
        </SafeAreaView>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['rgb(255, 107, 157)', 'rgb(168, 85, 247)', 'rgb(59, 130, 246)']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView style={{ flex: 1 }}>
        {/* Header */}
        <Box className="px-6 py-4">
          <VStack space="xs">
            <HStack className="items-center space-x-3">
              <Box className="w-10 h-10 bg-white/20 rounded-full items-center justify-center">
                <BrainIcon size={24} color="white" />
              </Box>
              <VStack space="xs">
                <Text className="text-white text-2xl font-bold">Knowledge Hub</Text>
                <Text className="text-white/70 text-sm">
                  Your centralized learning command center
                </Text>
              </VStack>
            </HStack>
          </VStack>
        </Box>

        {/* Quick Stats */}
        <Box className="px-6 mb-4">
          <HStack className="space-x-4">
            <Box className="flex-1 bg-white/10 backdrop-blur-md rounded-2xl p-4">
              <VStack space="xs" className="items-center">
                <Text className="text-white text-2xl font-bold">{cardsState.cards.length}</Text>
                <Text className="text-white/70 text-xs text-center">Knowledge Cards</Text>
              </VStack>
            </Box>
            <Box className="flex-1 bg-white/10 backdrop-blur-md rounded-2xl p-4">
              <VStack space="xs" className="items-center">
                <Text className="text-white text-2xl font-bold">0</Text>
                <Text className="text-white/70 text-xs text-center">Study Sessions</Text>
              </VStack>
            </Box>
            <Box className="flex-1 bg-white/10 backdrop-blur-md rounded-2xl p-4">
              <VStack space="xs" className="items-center">
                <Text className="text-white text-2xl font-bold">0%</Text>
                <Text className="text-white/70 text-xs text-center">Mastery Level</Text>
              </VStack>
            </Box>
          </HStack>
        </Box>

        {/* Features Grid */}
        <ScrollView 
          className="flex-1 px-6"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}
        >
          <VStack space="md">
            {knowledgeFeatures.map((feature, index) => (
              <FeatureCard
                key={index}
                {...feature}
              />
            ))}
          </VStack>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}
