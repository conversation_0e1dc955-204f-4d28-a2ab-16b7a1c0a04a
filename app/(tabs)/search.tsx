import React, { useState, useRef } from 'react';
import { ScrollView, View, Pressable, TextInput, KeyboardAvoidingView, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Text } from '../../components/ui/text';
import { VStack } from '../../components/ui/vstack';
import { HStack } from '../../components/ui/hstack';
import { Box } from '../../components/ui/box';
import { Button, ButtonText } from '../../components/ui/button';
import { Badge, BadgeText } from '../../components/ui/badge';
import { useRouter } from 'expo-router';
import {
  MessageSquareIcon,
  SendIcon,
  BotIcon,
  UserIcon,
  SparklesIcon,
  FileTextIcon,
  ImageIcon,
  MicIcon,
  PlusIcon
} from 'lucide-react-native';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  type?: 'text' | 'suggestion';
}

export default function AIChatScreen() {
  const router = useRouter();
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hi! I'm your AI tutor. I've analyzed your Machine Learning document and I'm ready to help you understand the concepts better. What would you like to explore first?",
      sender: 'ai',
      timestamp: new Date(),
      type: 'text'
    }
  ]);
  const [isTyping, setIsTyping] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const [quickSuggestions] = useState([
    "Give me examples",
    "Create a quiz",
    "Explain neural networks",
    "Show learning path"
  ]);

  const sendMessage = async () => {
    if (!message.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: message,
      sender: 'user',
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage]);
    setMessage('');
    setIsTyping(true);

    // Simulate AI response delay
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: generateAIResponse(userMessage.text),
        sender: 'ai',
        timestamp: new Date(),
        type: 'text'
      };

      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1500);
  };

  const generateAIResponse = (userText: string): string => {
    // Simple mock AI responses
    if (userText.toLowerCase().includes('neural network')) {
      return "Neural networks are computing systems inspired by biological neural networks. They consist of interconnected nodes (neurons) that process information through weighted connections. Would you like me to explain the different types of neural networks?";
    }
    if (userText.toLowerCase().includes('example')) {
      return "Here are some practical examples:\n\n• **Email Spam Detection**: Uses supervised learning to classify emails\n• **Image Recognition**: Identifies objects in photos\n• **Recommendation Systems**: Suggests products or content\n• **Voice Assistants**: Process and understand speech\n\nWhich example would you like me to explain in detail?";
    }
    return "That's a great question! Based on your Machine Learning document, I can help explain that concept in more detail. What specific aspect would you like me to focus on?";
  };

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <KeyboardAvoidingView
        className="flex-1"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Chat Header */}
        <Box className="bg-glass-bg-primary border-b border-glass-border-primary backdrop-blur-md px-4 py-3">
          <HStack className="items-center space-x-3">
            <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyPink/20">
              <BotIcon size={20} color="#FF6B9D" />
            </Box>
            <VStack space="xs">
              <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                AI Tutor
              </Text>
              <HStack className="items-center space-x-1">
                <Box className="w-2 h-2 rounded-full bg-green-500" />
                <Text color="secondary" size="sm">Online</Text>
              </HStack>
            </VStack>
          </HStack>
        </Box>

        {/* Topic Context */}
        <Box className="bg-glass-bg-card border-b border-glass-border-secondary px-4 py-3">
          <HStack className="items-center justify-between">
            <HStack className="items-center space-x-3 flex-1">
              <Box className="w-8 h-8 justify-center items-center rounded-lg bg-candyPurple/20">
                <FileTextIcon size={16} color="#A855F7" />
              </Box>
              <VStack space="xs" className="flex-1">
                <Text color="primary" size="sm" style={{ fontWeight: '600' }}>
                  Discussing: Machine Learning Basics
                </Text>
                <Text color="secondary" size="xs">
                  12 knowledge cards • 8 concepts
                </Text>
              </VStack>
            </HStack>
            <Button action="glass" variant="outline" size="sm">
              <ButtonText>Change Topic</ButtonText>
            </Button>
          </HStack>
        </Box>

        {/* Chat Messages */}
        <ScrollView
          ref={scrollViewRef}
          className="flex-1 px-4 py-2"
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => scrollViewRef.current?.scrollToEnd({ animated: true })}
        >
          <VStack space="md">
            {messages.map((msg) => (
              <HStack
                key={msg.id}
                className={`${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                {msg.sender === 'ai' && (
                  <Box className="w-8 h-8 justify-center items-center rounded-full bg-candyPink/20 mr-2">
                    <BotIcon size={16} color="#FF6B9D" />
                  </Box>
                )}

                <Box
                  className={`max-w-[80%] p-3 rounded-2xl ${
                    msg.sender === 'user'
                      ? 'bg-candyPink rounded-br-md'
                      : 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-bl-md'
                  }`}
                >
                  <Text
                    color={msg.sender === 'user' ? 'primary' : 'primary'}
                    size="md"
                    style={{ color: msg.sender === 'user' ? '#FFFFFF' : undefined }}
                  >
                    {msg.text}
                  </Text>
                  <Text
                    color={msg.sender === 'user' ? 'secondary' : 'secondary'}
                    size="xs"
                    className="mt-1 opacity-70"
                    style={{ color: msg.sender === 'user' ? '#FFFFFF' : undefined, opacity: 0.7 }}
                  >
                    {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </Text>
                </Box>

                {msg.sender === 'user' && (
                  <Box className="w-8 h-8 justify-center items-center rounded-full bg-candyBlue/20 ml-2">
                    <UserIcon size={16} color="#3B82F6" />
                  </Box>
                )}
              </HStack>
            ))}

            {isTyping && (
              <HStack className="justify-start">
                <Box className="w-8 h-8 justify-center items-center rounded-full bg-candyPink/20 mr-2">
                  <BotIcon size={16} color="#FF6B9D" />
                </Box>
                <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-2xl rounded-bl-md p-3">
                  <Text color="secondary" size="md">AI is typing...</Text>
                </Box>
              </HStack>
            )}
          </VStack>
        </ScrollView>

        {/* Quick Suggestions */}
        <Box className="px-4 py-2">
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <HStack className="space-x-2">
              {quickSuggestions.map((suggestion, index) => (
                <Pressable
                  key={index}
                  onPress={() => {
                    setMessage(suggestion);
                    sendMessage();
                  }}
                >
                  <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-full px-4 py-2">
                    <Text color="primary" size="sm">
                      {suggestion}
                    </Text>
                  </Box>
                </Pressable>
              ))}
            </HStack>
          </ScrollView>
        </Box>

        {/* Message Input */}
        <Box className="bg-glass-bg-primary border-t border-glass-border-primary backdrop-blur-md px-4 py-3">
          <HStack className="items-end space-x-3">
            <Box className="flex-1 bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-2xl px-4 py-3">
              <TextInput
                value={message}
                onChangeText={setMessage}
                placeholder="Ask me anything about machine learning..."
                placeholderTextColor="#9CA3AF"
                multiline
                maxLength={500}
                style={{
                  color: '#111827',
                  fontSize: 16,
                  maxHeight: 100,
                  minHeight: 20,
                }}
                onSubmitEditing={sendMessage}
                blurOnSubmit={false}
              />
            </Box>

            <HStack className="space-x-2">
              <Button action="glass" variant="outline" size="sm">
                <MicIcon size={16} color="#9CA3AF" />
              </Button>

              <Button
                action="candyPink"
                variant="solid"
                size="sm"
                onPress={sendMessage}
                disabled={!message.trim()}
              >
                <SendIcon size={16} color="#FFFFFF" />
              </Button>
            </HStack>
          </HStack>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}