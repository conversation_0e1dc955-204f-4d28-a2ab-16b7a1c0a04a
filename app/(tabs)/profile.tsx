/**
 * Refined Profile Screen Component
 *
 * Clean, modern profile screen with candy colors, glass effects, and card-based layout.
 * Follows LearniScan design system with improved information architecture.
 */

import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import {
	BellIcon,
	BookOpenIcon,
	CloudIcon,
	LogOutIcon,
	MoonIcon,
	ScanIcon,
	SettingsIcon,
	StarIcon,
	TrendingUpIcon,
	LibraryIcon,
	BrainIcon,
	ChevronRightIcon,
} from "lucide-react-native";
import React, { useEffect, useState } from "react";
import { Alert, Image, ScrollView, StatusBar } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAuth } from "@/lib/contexts/AuthContext";
import { Badge, BadgeText } from "../../components/ui/badge";
import { Box } from "../../components/ui/box";
import { Button, ButtonText } from "../../components/ui/button";
import { HStack } from "../../components/ui/hstack";

// Permission system integration
import { usePermissions } from '@/hooks/usePermissions';
import { UsageIndicator } from '@/components/ui/usage-indicator';
import { UsageDashboard } from '@/components/ui/usage-dashboard';
import {
	Select,
	SelectBackdrop,
	SelectContent,
	SelectDragIndicator,
	SelectDragIndicatorWrapper,
	SelectIcon,
	SelectInput,
	SelectItem,
	SelectPortal,
	SelectTrigger,
} from "../../components/ui/select";
import { Switch } from "../../components/ui/switch";
import { Text } from "../../components/ui/text";
import { VStack } from "../../components/ui/vstack";

export default function Profile() {
	const router = useRouter();
	const {
		user: authUser,
		userProfile,
		isAuthenticated,
		isAnonymous,
		signOut,
		updateUserProfile,
	} = useAuth();

	// Permission system integration
	const { isPremium, userRole, planInfo } = usePermissions();

	const [localSettings, setLocalSettings] = useState({
		autoEnhance: true,
		cloudSync: true,
		notifications: true,
		darkMode: false,
		language: "en",
		quality: "high" as const,
		autoBackup: true,
		shareAnalytics: false,
	});

	// Sync settings with user profile when available
	useEffect(() => {
		if (userProfile?.preferences) {
			setLocalSettings((prev) => ({
				...prev,
				notifications: userProfile.preferences.notifications,
				darkMode: userProfile.preferences.theme === "dark",
				language: userProfile.preferences.language,
			}));
		}
	}, [userProfile]);

	const handleSettingChange = async (key: string, value: boolean | string) => {
		setLocalSettings((prev) => ({ ...prev, [key]: value }));

		// Update user profile for certain settings
		if (userProfile && !isAnonymous) {
			try {
				const updatedProfile = await updateUserProfile({
					preferences: {
						...userProfile.preferences,
						[key]: value as boolean,
					},
				});
			} catch (error) {
				Alert.alert("Error", "Failed to update settings. Please try again.");
			}
		}
	};;

	const handleUpgrade = () => {
		Alert.alert(
			"Upgrade to Premium",
			"Premium features will be available in the next update!",
			[{ text: "OK" }]
		);
	};

	const handleSignOut = async () => {
		Alert.alert("Sign Out", "Are you sure you want to sign out?", [
			{ text: "Cancel", style: "cancel" },
			{
				text: "Sign Out",
				style: "destructive",
				onPress: async () => {
					try {
						await signOut();
						router.replace("/auth/login");
					} catch (error) {
						Alert.alert("Error", "Failed to sign out. Please try again.");
					}
				},
			},
		]);
	};

	// Get user data with fallbacks
	const displayUser = {
		name: authUser?.name || userProfile?.name || "Guest User",
		email: authUser?.email || userProfile?.email || "<EMAIL>",
		avatar: userProfile?.avatar || "https://picsum.photos/200/300",
		plan: userProfile?.subscription?.plan || "Free",
		documentsScanned: userProfile?.learningStats?.totalScans || 0,
		totalCards: userProfile?.learningStats?.totalCards || 0,
		streakDays: userProfile?.learningStats?.streakDays || 0,
	};

	return (
		<SafeAreaView className="flex-1">
			<StatusBar
				barStyle="light-content"
				backgroundColor="transparent"
				translucent
			/>
			{/* Unified Candy Gradient Background */}
			<LinearGradient
				colors={['#FF6B9D', '#A855F7', '#3B82F6', '#10B981']}
				start={{ x: 0, y: 0 }}
				end={{ x: 1, y: 1 }}
				style={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					opacity: 0.15,
				}}
			/>
			<ScrollView
				className="flex-1"
				showsVerticalScrollIndicator={false}
			>

			<VStack space="lg" className="p-6 pb-24">
				{/* Enhanced Profile Header */}
				<Box className="bg-white/20 backdrop-blur-lg rounded-3xl p-6 border border-white/30 shadow-lg">
					<VStack space="lg">
						<HStack className="items-start space-x-4">
							<Box className="w-20 h-20 rounded-full overflow-hidden bg-white/20 border-2 border-white/30">
								<Image
									source={{ uri: displayUser.avatar }}
									className="w-full h-full"
									resizeMode="cover"
								/>
							</Box>

							<VStack space="xs" className="flex-1 mt-1">
								<HStack className="items-center space-x-3">
									<Text size="2xl" className="font-bold text-gray-800">
										{displayUser.name}
									</Text>
									<Badge
										action={displayUser.plan === 'Premium' ? 'candyPurple' : 'candyPink'}
										variant="solid"
										size="sm"
									>
										<BadgeText>{displayUser.plan}</BadgeText>
									</Badge>
								</HStack>
								<Text size="md" className="text-gray-700">
									{displayUser.email}
								</Text>
								{displayUser.plan !== "Premium" && !isAnonymous && (
									<Button
										action="candyPink"
										variant="outline"
										size="xs"
										onPress={handleUpgrade}
										className="self-start mt-2"
									>
										<ButtonText>Upgrade</ButtonText>
									</Button>
								)}
							</VStack>
						</HStack>

						{/* Quick Actions for Anonymous Users */}
						{isAnonymous && (
							<HStack space="sm" className="mt-4">
								<Button
									action="candyBlue"
									size="md"
									className="flex-1"
									onPress={() => router.push('/auth/signup')}
								>
									<ButtonText>Create Account</ButtonText>
								</Button>
								<Button
									action="glass"
									variant="outline"
									size="md"
									className="flex-1"
									onPress={() => router.push('/auth/login')}
								>
									<ButtonText>Sign In</ButtonText>
								</Button>
							</HStack>
						)}
					</VStack>
				</Box>

				{/* Enhanced Usage Statistics */}
				<VStack space="md">
					<Text size="xl" className="font-bold text-gray-800">
						Your Progress
					</Text>

					<HStack className="space-x-4">
						<Box className="flex-1 bg-white/20 backdrop-blur-lg rounded-2xl p-5 border border-white/30 shadow-md">
							<VStack space="sm" className="items-center">
								<Box className="w-12 h-12 bg-candyPink/30 rounded-full items-center justify-center">
									<ScanIcon size={24} color="#FF6B9D" />
								</Box>
								<Text
									size="3xl"
									className="font-bold text-candyPink"
								>
									{displayUser.documentsScanned}
								</Text>
								<Text size="sm" className="text-gray-700 text-center font-medium">
									Documents{'\n'}Scanned
								</Text>
							</VStack>
						</Box>

						<Box className="flex-1 bg-white/20 backdrop-blur-lg rounded-2xl p-5 border border-white/30 shadow-md">
							<VStack space="sm" className="items-center">
								<Box className="w-12 h-12 bg-candyPurple/30 rounded-full items-center justify-center">
									<BookOpenIcon size={24} color="#A855F7" />
								</Box>
								<Text
									size="3xl"
									className="font-bold text-candyPurple"
								>
									{displayUser.totalCards}
								</Text>
								<Text size="sm" className="text-gray-700 text-center font-medium">
									Knowledge{'\n'}Cards
								</Text>
							</VStack>
						</Box>

						<Box className="flex-1 bg-white/20 backdrop-blur-lg rounded-2xl p-5 border border-white/30 shadow-md">
							<VStack space="sm" className="items-center">
								<Box className="w-12 h-12 bg-candyBlue/30 rounded-full items-center justify-center">
									<TrendingUpIcon size={24} color="#3B82F6" />
								</Box>
								<Text
									size="3xl"
									className="font-bold text-candyBlue"
								>
									{displayUser.streakDays}
								</Text>
								<Text size="sm" className="text-gray-700 text-center font-medium">
									Day{'\n'}Streak
								</Text>
							</VStack>
						</Box>
					</HStack>
				</VStack>

				{/* Usage Dashboard - Permission System Integration */}
				{isAuthenticated && !isAnonymous && (
					<VStack space="md">
						<Text size="xl" className="font-bold text-gray-800">
							Usage & Limits
						</Text>
						<UsageDashboard
							variant="detailed"
							showUpgradePrompt={!isPremium}
							onUpgrade={handleUpgrade}
						/>
					</VStack>
				)}

				{/* Feature Navigation Menu */}
				<VStack space="md">
					<Text size="xl" className="font-bold text-gray-800">
						Features
					</Text>

					<Box className="bg-white/20 backdrop-blur-lg rounded-2xl p-2 border border-white/30 shadow-lg">
						<VStack space="xs">
							{/* Library */}
							<Box className="p-4">
								<Button
									action="glass"
									size="lg"
									className="w-full justify-start rounded-2xl"
									onPress={() => router.push('/(features)/library')}
								>
									<HStack className="items-center justify-between w-full">
										<HStack className="items-center space-x-3">
											<LibraryIcon size={20} color="#8B5CF6" />
											<VStack space="xs" className="items-start">
												<Text size="md" className="font-semibold text-gray-900">
													Document Library
												</Text>
												<Text size="sm" className="text-gray-600">
													Browse and manage your scanned documents
												</Text>
											</VStack>
										</HStack>
										<ChevronRightIcon size={16} color="#6B7280" />
									</HStack>
								</Button>
							</Box>

							{/* Divider */}
							<Box className="h-px bg-white/10 mx-4" />

							{/* Study */}
							<Box className="p-4">
								<Button
									action="glass"
									size="lg"
									className="w-full justify-start rounded-2xl"
									onPress={() => router.push('/(features)/study')}
								>
									<HStack className="items-center justify-between w-full">
										<HStack className="items-center space-x-3">
											<BrainIcon size={20} color="#3B82F6" />
											<VStack space="xs" className="items-start">
												<Text size="md" className="font-semibold text-gray-900">
													Study Sessions
												</Text>
												<Text size="sm" className="text-gray-600">
													AI-powered learning and practice sessions
												</Text>
											</VStack>
										</HStack>
										<ChevronRightIcon size={16} color="#6B7280" />
									</HStack>
								</Button>
							</Box>

							{/* Divider */}
							<Box className="h-px bg-white/10 mx-4" />

							{/* Settings */}
							<Box className="p-4">
								<Button
									action="glass"
									size="lg"
									className="w-full justify-start rounded-2xl"
									onPress={() => router.push('/(features)/settings/knowledge-seeding')}
								>
									<HStack className="items-center justify-between w-full">
										<HStack className="items-center space-x-3">
											<SettingsIcon size={20} color="#10B981" />
											<VStack space="xs" className="items-start">
												<Text size="md" className="font-semibold text-gray-900">
													Advanced Settings
												</Text>
												<Text size="sm" className="text-gray-600">
													Knowledge seeding and advanced configuration
												</Text>
											</VStack>
										</HStack>
										<ChevronRightIcon size={16} color="#6B7280" />
									</HStack>
								</Button>
							</Box>
						</VStack>
					</Box>
				</VStack>

				{/* Test Knowledge Graph Button */}
				<VStack space="md">
					<Button
						action="candyPurple"
						size="lg"
						className="w-full"
						onPress={() => {
							Alert.alert('Knowledge Graph', 'Navigating to knowledge graph...');
							router.push('/(knowledge)/graph');
						}}
					>
						<ButtonText>🧠 Test Knowledge Graph</ButtonText>
					</Button>
					
					{/* Knowledge Seeding Button */}
					<Button
						action="candyBlue"
						variant="outline"
						size="lg"
						className="w-full"
						onPress={() => {
							router.push('/(features)/settings/knowledge-seeding');
						}}
					>
						<ButtonText>🌱 Seed Programming Knowledge</ButtonText>
					</Button>
				</VStack>

				{/* Consolidated Settings */}
				<VStack space="md">
					<Text size="xl" className="font-bold text-gray-800">
						Settings
					</Text>

					<Box className="bg-white/20 backdrop-blur-lg rounded-2xl p-2 border border-white/30 shadow-lg">
						<VStack space="xs">
							{/* Auto Enhancement */}
							<Box className="p-4">
								<HStack className="justify-between items-center">
									<VStack space="xs" className="flex-1">
										<HStack className="items-center space-x-2">
											<SettingsIcon size={16} color="#6B7280" />
											<Text size="md" className="font-semibold text-gray-900">
												Auto Enhancement
											</Text>
										</HStack>
										<Text size="sm" className="text-gray-600">
											Automatically enhance scanned images
										</Text>
									</VStack>
									<Switch
										value={localSettings.autoEnhance}
										onValueChange={(value) =>
											handleSettingChange("autoEnhance", value)
										}
									/>
								</HStack>
							</Box>

							{/* Divider */}
							<Box className="h-px bg-white/10 mx-4" />

							{/* Scan Quality */}
							<Box className="p-4">
								<HStack className="justify-between items-center">
									<VStack space="xs" className="flex-1">
										<HStack className="items-center space-x-2">
											<StarIcon size={16} color="#6B7280" />
											<Text size="md" className="font-semibold text-gray-900">
												Scan Quality
											</Text>
										</HStack>
										<Text size="sm" className="text-gray-600">
											Default quality for new scans
										</Text>
									</VStack>
									<Select
										selectedValue={localSettings.quality}
										onValueChange={(value) =>
											handleSettingChange("quality", value)
										}
									>
										<SelectTrigger variant="outline" size="sm">
											<SelectInput />
											<SelectIcon />
										</SelectTrigger>
										<SelectPortal>
											<SelectBackdrop />
											<SelectContent>
												<SelectDragIndicatorWrapper>
													<SelectDragIndicator />
												</SelectDragIndicatorWrapper>
												<SelectItem label="High" value="high" />
												<SelectItem label="Medium" value="medium" />
												<SelectItem label="Low" value="low" />
											</SelectContent>
										</SelectPortal>
									</Select>
								</HStack>
							</Box>

							{/* Divider */}
							<Box className="h-px bg-white/10 mx-4" />

							{/* Cloud Sync */}
							<Box className="p-4">
								<HStack className="justify-between items-center">
									<VStack space="xs" className="flex-1">
										<HStack className="items-center space-x-2">
											<CloudIcon size={16} color="#6B7280" />
											<Text size="md" className="font-semibold text-gray-900">
												Cloud Sync
											</Text>
										</HStack>
										<Text size="sm" className="text-gray-600">
											Sync documents across devices
										</Text>
									</VStack>
									<Switch
										value={localSettings.cloudSync}
										onValueChange={(value) =>
											handleSettingChange("cloudSync", value)
										}
										isDisabled={isAnonymous}
									/>
								</HStack>
							</Box>

							{/* Divider */}
							<Box className="h-px bg-white/10 mx-4" />

							{/* Notifications */}
							<Box className="p-4">
								<HStack className="justify-between items-center">
									<VStack space="xs" className="flex-1">
										<HStack className="items-center space-x-2">
											<BellIcon size={16} color="#6B7280" />
											<Text size="md" className="font-semibold text-gray-900">
												Notifications
											</Text>
										</HStack>
										<Text size="sm" className="text-gray-600">
											Receive scan completion alerts
										</Text>
									</VStack>
									<Switch
										value={localSettings.notifications}
										onValueChange={(value) =>
											handleSettingChange("notifications", value)
										}
									/>
								</HStack>
							</Box>

							{/* Divider */}
							<Box className="h-px bg-white/10 mx-4" />

							{/* Dark Mode */}
							<Box className="p-4">
								<HStack className="justify-between items-center">
									<VStack space="xs" className="flex-1">
										<HStack className="items-center space-x-2">
											<MoonIcon size={16} color="#6B7280" />
											<Text size="md" className="font-semibold text-gray-900">
												Dark Mode
											</Text>
										</HStack>
										<Text size="sm" className="text-gray-600">
											Use dark theme
										</Text>
									</VStack>
									<Switch
										value={localSettings.darkMode}
										onValueChange={(value) =>
											handleSettingChange("darkMode", value)
										}
									/>
								</HStack>
							</Box>
						</VStack>
					</Box>
				</VStack>

				{/* Premium Upgrade Section */}
				{displayUser.plan !== "Premium" && !isAnonymous && (
					<Box className="bg-gradient-to-r from-candyPink/25 to-candyPurple/25 backdrop-blur-lg rounded-3xl p-6 border border-candyPink/40 shadow-lg">
						<VStack space="md">
							<HStack className="items-center space-x-4">
								<Box className="w-14 h-14 bg-candyPink/25 rounded-full items-center justify-center">
									<StarIcon size={28} color="#FF6B9D" />
								</Box>
								<VStack className="flex-1">
									<Text size="xl" className="font-bold text-gray-900">
										Upgrade to Premium
									</Text>
									<Text size="md" className="text-gray-600">
										Unlock advanced features and unlimited scanning
									</Text>
								</VStack>
							</HStack>

							<Button action="candyPink" size="lg" onPress={handleUpgrade} className="rounded-2xl">
								<ButtonText className="font-semibold">Upgrade Now</ButtonText>
							</Button>
						</VStack>
					</Box>
				)}

				{/* Clean Account Section */}
				<VStack space="md">
					<Text size="xl" className="font-bold text-gray-800">
						Account
					</Text>

					<Box className="bg-white/20 backdrop-blur-lg rounded-2xl p-2 border border-white/30 shadow-lg">
						<VStack space="xs">
							{isAuthenticated && (
								<Box className="p-4">
									<Button
										action="negative"
										variant="outline"
										size="lg"
										onPress={handleSignOut}
										className="w-full rounded-2xl"
									>
										<HStack className="items-center space-x-3">
											<LogOutIcon size={18} color="#EF4444" />
											<ButtonText className="font-semibold">
												{isAnonymous ? "End Guest Session" : "Sign Out"}
											</ButtonText>
										</HStack>
									</Button>
								</Box>
							)}

							{!isAuthenticated && (
								<Box className="p-4">
									<Button
										action="candyBlue"
										size="lg"
										onPress={() => router.push("/auth/login")}
										className="w-full rounded-2xl"
									>
										<ButtonText className="font-semibold">Sign In to Your Account</ButtonText>
									</Button>
								</Box>
							)}
						</VStack>
					</Box>
				</VStack>
			</VStack>
		</ScrollView>
	</SafeAreaView>
);
}
