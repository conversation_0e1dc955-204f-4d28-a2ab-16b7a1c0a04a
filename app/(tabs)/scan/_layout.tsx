import { Stack, useRouter } from 'expo-router';
import { useEffect } from 'react';
import { useTabBarVisibility } from '@/contexts/TabBarVisibilityContext';
import { Pressable, Text } from 'react-native';
import { ArrowLeft } from 'lucide-react-native';

export const unstable_settings = {
  initialRouteName: 'camera',
};

export default function ScanLayout() {
  const { hideTabBar, showTabBar } = useTabBarVisibility();
  const router = useRouter();

  // Hide tab bar when entering scan screens
  useEffect(() => {
    hideTabBar();

    // Show tab bar when leaving scan screens
    return () => {
      showTabBar();
    };
  }, [hideTabBar, showTabBar]);

  const BackButton = () => (
    <Pressable
      onPress={() => router.push('/(tabs)/home')}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        paddingHorizontal: 16,
        paddingVertical: 8,
      }}
    >
      <ArrowLeft size={20} color="#FFFFFF" />
      <Text style={{ color: '#FFFFFF', fontWeight: '500' }}>Home</Text>
    </Pressable>
  );

  return (
    <Stack
      screenOptions={{
        headerShown: true,
        presentation: 'card',
        animation: 'fade',
        headerStyle: {
          backgroundColor: 'transparent',
        },
        headerTransparent: true,
        headerTintColor: '#FFFFFF',
        headerLeft: () => <BackButton />,
        headerTitle: '',
      }}
    >
      <Stack.Screen
        name="camera"
        options={{ title: 'Camera' }}
      />
      <Stack.Screen
        name="upload"
        options={{ title: 'Upload' }}
      />
      <Stack.Screen
        name="gallery"
        options={{ title: 'Gallery' }}
      />
      <Stack.Screen
        name="review"
        options={{ title: 'Review' }}
      />
    </Stack>
  );
}
