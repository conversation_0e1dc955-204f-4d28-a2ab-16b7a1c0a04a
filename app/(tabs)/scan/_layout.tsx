import { Stack } from 'expo-router';

export const unstable_settings = {
  initialRouteName: 'camera',
};

export default function ScanLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        presentation: 'card',
        animation: 'fade',
        headerStyle: {
          backgroundColor: 'transparent',
        },
      }}
    >
      <Stack.Screen 
        name="camera" 
        options={{ title: 'Camera' }}
      />
      <Stack.Screen 
        name="upload" 
        options={{ title: 'Upload' }}
      />
      <Stack.Screen 
        name="gallery" 
        options={{ title: 'Gallery' }}
      />
      <Stack.Screen 
        name="review" 
        options={{ title: 'Review' }}
      />
    </Stack>
  );
}
