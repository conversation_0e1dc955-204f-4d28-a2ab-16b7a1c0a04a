import { Stack, useRouter, useFocusEffect } from 'expo-router';
import { useEffect, useCallback, useMemo } from 'react';
import { useTabBarVisibility } from '@/contexts/TabBarVisibilityContext';
import { Pressable, Text } from 'react-native';
import { ArrowLeft } from 'lucide-react-native';

export const unstable_settings = {
  initialRouteName: 'camera',
};

export default function ScanLayout() {
  const {
    registerHideRequest,
    unregisterHideRequest,
    guaranteeTabBarVisible
  } = useTabBarVisibility();
  const router = useRouter();

  // Hide tab bar when entering scan screens with guaranteed restoration
  useEffect(() => {
    const screenName = 'scan-layout';

    // Register this screen as requesting tab bar to be hidden
    registerHideRequest(screenName);

    // Cleanup function with guaranteed restoration
    return () => {
      unregisterHideRequest(screenName);

      // Double guarantee - force show tab bar after a delay
      setTimeout(() => {
        guaranteeTabBarVisible();
      }, 200);
    };
  }, [registerHideRequest, unregisterHideRequest, guaranteeTabBarVisible]);

  // Additional focus effect for navigation handling
  useFocusEffect(
    useCallback(() => {
      // When screen loses focus (user navigates away), guarantee tab bar restoration
      return () => {
        setTimeout(() => {
          guaranteeTabBarVisible();
        }, 100);
      };
    }, [guaranteeTabBarVisible])
  );

  // Memoize back button handler to prevent re-renders
  const handleBackPress = useCallback(() => {
    // Guarantee tab bar restoration before navigation
    guaranteeTabBarVisible();
    router.push('/(tabs)/home');
  }, [router, guaranteeTabBarVisible]);

  // Memoize back button component
  const BackButton = useMemo(() => (
    <Pressable
      onPress={handleBackPress}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        paddingHorizontal: 16,
        paddingVertical: 8,
      }}
    >
      <ArrowLeft size={20} color="#FFFFFF" />
      <Text style={{ color: '#FFFFFF', fontWeight: '500' }}>Home</Text>
    </Pressable>
  ), [handleBackPress]);

  // Memoize screen options to prevent re-creation
  const screenOptions = useMemo(() => ({
    headerShown: false, // Disable header completely for better performance
    presentation: 'card' as const,
    animation: 'simple_push' as const, // Use simpler animation
  }), []);

  return (
    <Stack screenOptions={screenOptions}>
      <Stack.Screen
        name="camera"
        options={{ title: 'Camera' }}
      />
      <Stack.Screen
        name="upload"
        options={{ title: 'Upload' }}
      />
      <Stack.Screen
        name="gallery"
        options={{ title: 'Gallery' }}
      />
      <Stack.Screen
        name="review"
        options={{ title: 'Review' }}
      />
    </Stack>
  );
}
