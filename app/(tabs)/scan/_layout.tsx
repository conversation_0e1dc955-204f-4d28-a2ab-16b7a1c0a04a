import { Stack, useRouter } from 'expo-router';
import { useEffect, useCallback, useMemo } from 'react';
import { useTabBarVisibility } from '@/contexts/TabBarVisibilityContext';
import { Pressable, Text } from 'react-native';
import { ArrowLeft } from 'lucide-react-native';

export const unstable_settings = {
  initialRouteName: 'camera',
};

export default function ScanLayout() {
  const { hideTabBar, showTabBar } = useTabBarVisibility();
  const router = useRouter();

  // Hide tab bar when entering scan screens - optimized
  useEffect(() => {
    hideTabBar();

    // Show tab bar when leaving scan screens
    return () => {
      showTabBar();
    };
  }, []); // Remove dependencies to prevent re-runs

  // Memoize back button handler to prevent re-renders
  const handleBackPress = useCallback(() => {
    router.push('/(tabs)/home');
  }, [router]);

  // Memoize back button component
  const BackButton = useMemo(() => (
    <Pressable
      onPress={handleBackPress}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        paddingHorizontal: 16,
        paddingVertical: 8,
      }}
    >
      <ArrowLeft size={20} color="#FFFFFF" />
      <Text style={{ color: '#FFFFFF', fontWeight: '500' }}>Home</Text>
    </Pressable>
  ), [handleBackPress]);

  // Memoize screen options to prevent re-creation
  const screenOptions = useMemo(() => ({
    headerShown: false, // Disable header completely for better performance
    presentation: 'card' as const,
    animation: 'simple_push' as const, // Use simpler animation
  }), []);

  return (
    <Stack screenOptions={screenOptions}>
      <Stack.Screen
        name="camera"
        options={{ title: 'Camera' }}
      />
      <Stack.Screen
        name="upload"
        options={{ title: 'Upload' }}
      />
      <Stack.Screen
        name="gallery"
        options={{ title: 'Gallery' }}
      />
      <Stack.Screen
        name="review"
        options={{ title: 'Review' }}
      />
    </Stack>
  );
}
