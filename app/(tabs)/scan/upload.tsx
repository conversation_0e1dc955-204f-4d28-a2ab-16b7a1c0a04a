import React, { useState, useCallback } from 'react';
import { StyleSheet, Alert, StatusBar, Image, ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import * as ImagePicker from 'expo-image-picker';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  withTiming
} from 'react-native-reanimated';
import { 
  ArrowLeftIcon,
  SettingsIcon,
  CloudUploadIcon,
  XIcon
} from 'lucide-react-native';
import { z } from 'zod';

// Import UI components following Rule #6
import { Pressable } from '@/components/ui/pressable';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/button';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Guideline } from '@/components/home/<USER>';
import { TabSelectorButtons, TabType } from '@/components/home/<USER>';

// Constants for file validation
const SUPPORTED_FORMATS = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/heic',
  'image/heif', // Additional HEIF format
  'application/pdf'
];
const SUPPORTED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'heic', 'heif', 'pdf'];

// Zod validation schema following Rule #11
const UploadStateSchema = z.object({
  selectedFiles: z.array(z.object({
    uri: z.string(),
    fileName: z.string().optional(),
    fileSize: z.number().optional(),
    type: z.string().optional(),
    width: z.number().optional(),
    height: z.number().optional(),
  })),
  isUploading: z.boolean(),
  uploadProgress: z.number().min(0).max(100),
  dragActive: z.boolean(),
  guidelineState: z.enum(['scanning', 'captured', 'processing']),
});

type IUploadState = z.infer<typeof UploadStateSchema>;

export default function UploadScreen() {
  const insets = useSafeAreaInsets();
  
  // Direct state definitions (simple state) following Camera pattern
  const [activeTab, setActiveTab] = useState<TabType>('upload');
  const [selectedFiles, setSelectedFiles] = useState<IUploadState['selectedFiles']>([]);
  const [isUploading, setIsUploading] = useState<IUploadState['isUploading']>(false);
  const [uploadProgress, setUploadProgress] = useState<IUploadState['uploadProgress']>(0);
  const [dragActive, setDragActive] = useState<IUploadState['dragActive']>(false);
  const [guidelineState, setGuidelineState] = useState<IUploadState['guidelineState']>('scanning');

  // Animation values following Rule #8
  const uploadAreaScale = useSharedValue(1);
  const uploadAreaOpacity = useSharedValue(1);
  const progressWidth = useSharedValue(0);

  const uploadAreaAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: uploadAreaScale.value }],
      opacity: uploadAreaOpacity.value,
    };
  });

  const progressAnimatedStyle = useAnimatedStyle(() => {
    return {
      width: `${progressWidth.value}%`,
    };
  });

  // File validation following Rule #12
  const validateFile = (file: any): boolean => {
    // Early return pattern for validation
    if (!file) {
      console.log('validateFile: No file provided');
      return false;
    }

    // Log file details for debugging
    console.log('validateFile: File details:', {
      uri: file.uri,
      type: file.type,
      fileName: file.fileName,
      fileSize: file.fileSize,
      width: file.width,
      height: file.height
    });

    // For images from expo-image-picker, we primarily rely on the URI extension
    // since the type might not always be set correctly
    if (file.uri) {
      const extension = file.uri.split('.').pop()?.toLowerCase();
      console.log('validateFile: Extracted extension:', extension);

      if (extension && SUPPORTED_EXTENSIONS.includes(extension)) {
        // Valid extension found
        console.log('validateFile: Valid extension detected');
      } else if (file.type && SUPPORTED_FORMATS.includes(file.type)) {
        // Valid MIME type found
        console.log('validateFile: Valid MIME type detected');
      } else {
        // Check if it's an image from the picker (expo-image-picker usually returns valid images)
        if (file.width && file.height && file.uri.includes('ImagePicker')) {
          console.log('validateFile: Image picker file detected, allowing');
          // Allow images from expo-image-picker even if format detection fails
        } else {
          Alert.alert('Unsupported Format', `Please select a valid file (PDF, JPG, PNG, HEIC). Detected: ${extension || file.type || 'unknown'}`);
          return false;
        }
      }
    }

    // Check file size
    if (file.fileSize && file.fileSize > 50 * 1024 * 1024) {
      Alert.alert('File Too Large', 'Please select a file smaller than 50MB');
      return false;
    }

    console.log('validateFile: File validation passed');
    return true;
  };

  const handleImagePicker = async () => {
    try {
      setIsUploading(true);
      setGuidelineState('processing');
      uploadAreaScale.value = withSpring(0.95);
      
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: false, // Disable editing to preserve original format
        quality: 0.9,
        allowsMultipleSelection: false,
        exif: false, // Don't need EXIF data
      });

      if (!result.canceled && result.assets[0]) {
        const file = result.assets[0];
        
        if (validateFile(file)) {
          setSelectedFiles(prev => [...prev, {
            uri: file.uri,
            fileName: file.fileName || 'image.jpg',
            fileSize: file.fileSize,
            type: file.type || 'image',
            width: file.width,
            height: file.height,
          }]);
          setGuidelineState('captured');
          
          // Simulate upload progress
          progressWidth.value = withTiming(100, { duration: 2000 });
        } else {
          setGuidelineState('scanning');
        }
      } else {
        setGuidelineState('scanning');
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to select image');
      setGuidelineState('scanning');
    } finally {
      setIsUploading(false);
      uploadAreaScale.value = withSpring(1);
    }
  };

  const handleCameraPicker = async () => {
    try {
      setIsUploading(true);
      setGuidelineState('processing');
      
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false, // Disable editing to preserve original format
        quality: 0.9,
        exif: false, // Don't need EXIF data
      });

      if (!result.canceled && result.assets[0]) {
        const file = result.assets[0];
        
        if (validateFile(file)) {
          setSelectedFiles(prev => [...prev, {
            uri: file.uri,
            fileName: file.fileName || 'camera_image.jpg',
            fileSize: file.fileSize,
            type: file.type || 'image',
            width: file.width,
            height: file.height,
          }]);
          setGuidelineState('captured');
        } else {
          setGuidelineState('scanning');
        }
      } else {
        setGuidelineState('scanning');
      }
    } catch (error) {
      console.error('Error capturing image:', error);
      Alert.alert('Error', 'Failed to capture image');
      setGuidelineState('scanning');
    } finally {
      setIsUploading(false);
    }
  };

  const handleTabChange = useCallback((tab: TabType) => {
    if (tab === activeTab) return; // Prevent unnecessary navigation

    setActiveTab(tab);

    switch (tab) {
      case 'camera':
        router.replace('./camera');
        break;
      case 'gallery':
        router.replace('./gallery');
        break;
      case 'upload':
        router.replace('./upload');
        break;
    }
  }, [activeTab, router]);

  const handleUploadAreaPress = () => {
    Alert.alert(
      'Select File',
      'Choose how you want to add your document',
      [
        {
          text: 'Photo Library',
          onPress: handleImagePicker,
        },
        {
          text: 'Camera',
          onPress: handleCameraPicker,
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    setGuidelineState(selectedFiles.length > 1 ? 'captured' : 'scanning');
  };

  const handleReviewNavigation = () => {
    const latestFile = selectedFiles[selectedFiles.length - 1];
    if (!latestFile) return;

    router.replace({
      pathname: './review',
      params: {
        imageUri: latestFile.uri,
        source: 'upload',
        timestamp: Date.now().toString()
      }
    });
  };

  const handleRetake = () => {
    setSelectedFiles([]);
    setGuidelineState('scanning');
    setUploadProgress(0);
    progressWidth.value = withTiming(0, { duration: 300 });
  };

  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return 'Unknown size';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <LinearGradient
      colors={['#7c2d92', '#be185d', '#ec4899', '#f472b6']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Floating decorative elements */}
      <Box className="absolute top-20 right-10 w-16 h-16 rounded-full bg-white/5 backdrop-blur-md" style={styles.floatingElement1} />
      <Box className="absolute top-40 left-8 w-12 h-12 rounded-full bg-pink-400/10 backdrop-blur-md" style={styles.floatingElement2} />
      <Box className="absolute bottom-32 right-6 w-20 h-20 rounded-full bg-white/5 backdrop-blur-md" style={styles.floatingElement3} />

      {/* Header with glass morphism */}
      <Box
        className="flex-row justify-between items-center px-5 py-4 bg-white/10 backdrop-blur-md border-b border-white/20"
        style={[styles.glassHeader, { paddingTop: insets.top + 16 }]}
      >
        <HStack className="items-center gap-3">
          <Pressable onPress={() => router.push('/(tabs)/home')}>
            <HStack className="items-center gap-2">
              <ArrowLeftIcon size={20} color="#FFFFFF" />
              <Text className="text-white font-medium">Home</Text>
            </HStack>
          </Pressable>
        </HStack>

        <Text className="text-white text-lg font-bold">Upload Document</Text>

        <HStack className="gap-3">
          <Pressable>
            <Box className="p-2 rounded-full bg-white/10 backdrop-blur-md" style={styles.glassButton}>
              <SettingsIcon size={20} color="#FFFFFF" />
            </Box>
          </Pressable>
        </HStack>
      </Box>

      {/* Tab Navigation with glass morphism */}
      <TabSelectorButtons
        activeTab={activeTab}
        onTabChange={handleTabChange}
        activeTabColor="#f472b6"
      />
      
      {/* Main Content Area - ScrollView to handle overflow */}
      <ScrollView
        className="flex-1 px-5"
        contentContainerStyle={{
          paddingVertical: 24,
          paddingBottom: insets.bottom + 120,
          flexGrow: 1
        }}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        bounces={true}
      >
        {/* Upload area with enhanced design */}
        <Animated.View style={uploadAreaAnimatedStyle}>
          <Pressable
            onPress={handleUploadAreaPress}
            disabled={isUploading}
            className={`
              border-2 border-dashed border-white/40 rounded-2xl p-8 h-64
              bg-white/5 backdrop-blur-md
              ${isUploading ? 'opacity-50' : 'active:opacity-80'}
            `}
            style={styles.glassUploadArea}
          >
            <VStack className="items-center justify-center space-y-6 flex-1">
              {/* Upload icon with animation */}
              <Box className="w-20 h-20 rounded-full bg-white/10 justify-center items-center">
                <CloudUploadIcon size={40} color="#f472b6" />
              </Box>
              
              {/* Upload text */}
              <VStack className="items-center space-y-2">
                <Text className="text-white text-xl font-semibold">
                  {selectedFiles.length > 0 ? 'Add More Files' : 'Upload Document'}
                </Text>
                <Text className="text-white/80 text-center">
                  Tap to browse or take a photo
                </Text>
              </VStack>
              
              {/* Supported formats */}
              <Text className="text-white/60 text-sm text-center">
                Supports: PDF, JPG, PNG, HEIC (up to 50MB)
              </Text>
              
              {/* Upload progress */}
              {isUploading && (
                <VStack className="w-full space-y-2">
                  <Text className="text-white text-sm text-center">Uploading...</Text>
                  <Box className="w-full h-2 bg-white/20 rounded-full overflow-hidden">
                    <Animated.View
                      style={[progressAnimatedStyle, { height: '100%', backgroundColor: '#f472b6' }]}
                    />
                  </Box>
                </VStack>
              )}
            </VStack>
          </Pressable>
        </Animated.View>
        
        {/* Selected files preview */}
        {selectedFiles.length > 0 && (
          <VStack className="space-y-4 mt-8">
            <Text className="text-white text-lg font-semibold">
              Selected Files ({selectedFiles.length})
            </Text>
            
            <VStack className="space-y-3">
              {selectedFiles.map((file, index) => (
                <Box 
                  key={index}
                  className="flex-row items-center p-4 bg-white/10 rounded-xl backdrop-blur-md"
                  style={styles.glassCard}
                >
                  {/* File preview */}
                  <Box className="w-12 h-12 rounded-lg overflow-hidden mr-3">
                    <Image 
                      source={{ uri: file.uri }}
                      style={{ width: '100%', height: '100%' }}
                      resizeMode="cover"
                    />
                  </Box>
                  
                  {/* File info */}
                  <VStack className="flex-1 space-y-1">
                    <Text className="text-white font-medium" numberOfLines={1}>
                      {file.fileName}
                    </Text>
                    <Text className="text-white/70 text-sm">
                      {formatFileSize(file.fileSize)} • {file.width}×{file.height}
                    </Text>
                  </VStack>
                  
                  {/* Remove button */}
                  <Pressable onPress={() => removeFile(index)}>
                    <Box className="p-2 rounded-full bg-red-500/20">
                      <XIcon size={16} color="#EF4444" />
                    </Box>
                  </Pressable>
                </Box>
              ))}
            </VStack>
          </VStack>
        )}
        
        {/* Action buttons */}
        {selectedFiles.length > 0 && (
          <HStack className="space-x-3 mt-8">
            <Box className="flex-1">
              <Button
                variant="outline"
                className="border-white/30 bg-white/10"
                onPress={() => setSelectedFiles([])}
              >
                <Text className="text-white">Clear All</Text>
              </Button>
            </Box>
            <Box className="flex-1">
              <Button
                className="bg-gradient-to-r from-pink-400 to-pink-600"
                onPress={() => {
                  if (selectedFiles.length > 0) {
                    // Navigate to review screen with the first selected file
                    router.replace({
                      pathname: './review',
                      params: { imageUri: selectedFiles[0].uri }
                    });
                  } else {
                    Alert.alert('No Files', 'Please select files to process');
                  }
                }}
              >
                <Text className="text-white font-bold">Process Files</Text>
              </Button>
            </Box>
          </HStack>
        )}
      </ScrollView>
      {/* Guidelines integrated at bottom */}
      <Guideline
        state={guidelineState}
        bottomInset={insets.bottom}
        marginBottom={20}
        onReviewPress={handleReviewNavigation}
        onRetakePress={handleRetake}
        customTips={[
          'Use good lighting for clear document photos',
          'Ensure documents are flat and fully visible',
          'Multiple files can be selected and processed together',
          'Tap "Photo Library" or "Camera" to add files'
        ]}
      />
    </LinearGradient>
  );
}

// StyleSheet following Rule #6 (prefer StyleSheet.create for static styles)
const styles = StyleSheet.create({
  glassHeader: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  glassButton: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  glassUploadArea: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  glassCard: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  floatingElement1: {
    shadowColor: '#f472b6',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  floatingElement2: {
    shadowColor: '#ec4899',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 6,
  },
  floatingElement3: {
    shadowColor: '#f472b6',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
});