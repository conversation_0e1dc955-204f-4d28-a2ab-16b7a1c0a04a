import React, { useState, useEffect, useCallback } from 'react';
import { Dimensions, Alert, Image, StyleSheet, ScrollView, StatusBar } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as MediaLibrary from 'expo-media-library';
import { router } from 'expo-router';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming
} from 'react-native-reanimated';
import {
  ImageIcon,
  RefreshCwIcon,
  AlertCircleIcon,
  GridIcon,
  SettingsIcon
} from 'lucide-react-native';
import { z } from 'zod';

// Import UI components following Rule #6
import { Pressable } from '@/components/ui/pressable';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/button';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Guideline } from '@/components/home/<USER>';
import { TabType } from '@/components/home/<USER>';
import { ScanScreenLayout } from '@/components/scan/ScanScreenLayout';

const { width: screenWidth } = Dimensions.get('window');
const imageSize = (screenWidth - 60) / 3; // 3 columns with padding

// Zod validation schema following Rule #11
const GalleryStateSchema = z.object({
  activeTab: z.enum(['camera', 'upload', 'gallery']),
  photos: z.array(z.object({
    id: z.string(),
    uri: z.string(),
    width: z.number(),
    height: z.number(),
    creationTime: z.number(),
  })),
  loading: z.boolean(),
  hasPermission: z.boolean().nullable(),
  refreshing: z.boolean(),
  selectedPhotos: z.array(z.string()),
  guidelineState: z.enum(['scanning', 'captured', 'processing']),
  selectedPhoto: z.object({
    id: z.string(),
    uri: z.string(),
    width: z.number(),
    height: z.number(),
    creationTime: z.number(),
  }).nullable(),
});

type IGalleryState = z.infer<typeof GalleryStateSchema>;

export default function GalleryScreen() {
  // Direct state definitions (simple state) following Camera pattern
  const [activeTab, setActiveTab] = useState<TabType>('gallery');
  const [photos, setPhotos] = useState<IGalleryState['photos']>([]);
  const [loading, setLoading] = useState<IGalleryState['loading']>(true);
  const [hasPermission, setHasPermission] = useState<IGalleryState['hasPermission']>(null);
  const [refreshing, setRefreshing] = useState<IGalleryState['refreshing']>(false);
  const [selectedPhotos, setSelectedPhotos] = useState<IGalleryState['selectedPhotos']>([]);
  const [guidelineState, setGuidelineState] = useState<IGalleryState['guidelineState']>('scanning');
  const [selectedPhoto, setSelectedPhoto] = useState<IGalleryState['selectedPhoto']>(null);

  // Animation values following Rule #8
  const gridOpacity = useSharedValue(0);
  const refreshRotation = useSharedValue(0);

  const gridAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: gridOpacity.value,
    };
  });

  const refreshAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${refreshRotation.value}deg` }],
    };
  });

  const requestPermission = async () => {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      const granted = status === 'granted';
      setHasPermission(granted);
      return granted;
    } catch (error) {
      console.error('Error requesting media library permission:', error);
      setHasPermission(false);
      return false;
    }
  };

  const loadPhotos = async (forceRefresh = false) => {
    try {
      if (forceRefresh) {
        setRefreshing(true);
        refreshRotation.value = withTiming(360, { duration: 1000 });
      }

      const permission = hasPermission ?? await requestPermission();
      if (!permission) {
        setLoading(false);
        setRefreshing(false);
        return;
      }

      const { assets } = await MediaLibrary.getAssetsAsync({
        mediaType: 'photo',
        sortBy: ['creationTime'],
        first: 100,
      });

      // Transform assets to match our schema
      const transformedPhotos = assets.map(asset => ({
        id: asset.id,
        uri: asset.uri,
        width: asset.width,
        height: asset.height,
        creationTime: asset.creationTime,
      }));

      setPhotos(transformedPhotos);

      // Animate grid appearance
      gridOpacity.value = withTiming(1, { duration: 500 });
    } catch (error) {
      console.error('Error loading photos:', error);
      Alert.alert('Error', 'Failed to load photos from gallery');
    } finally {
      setLoading(false);
      setRefreshing(false);
      refreshRotation.value = 0;
    }
  };

  useEffect(() => {
    loadPhotos();
  }, []);

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    if (tab === 'camera') {
      router.replace('./camera');
    } else if (tab === 'upload') {
      router.replace('./upload');
    } else if (tab === 'gallery') {
      router.replace('./gallery');
    }
  };

  const handlePhotoSelect = (photo: any) => {
    setSelectedPhoto(photo);
    setGuidelineState('captured');
    console.log('Selected photo:', photo.uri);
  };

  const handleReviewNavigation = () => {
    if (!selectedPhoto) return;

    router.replace({
      pathname: './review',
      params: {
        imageUri: selectedPhoto.uri,
        source: 'gallery',
        timestamp: Date.now().toString()
      }
    });
  };

  const handleRetake = () => {
    setSelectedPhoto(null);
    setGuidelineState('scanning');
  };

  const handleRefresh = () => {
    loadPhotos(true);
  };

  const renderPermissionRequest = () => (
    <VStack className="flex-1 justify-center items-center px-6">
      <VStack className="items-center space-y-6">
        <Box className="w-20 h-20 rounded-full bg-white/10 justify-center items-center">
          <AlertCircleIcon size={40} color="#f472b6" />
        </Box>
        <VStack className="items-center space-y-2">
          <Text className="text-white text-xl font-semibold text-center">
            Gallery Access Required
          </Text>
          <Text className="text-white/80 text-center">
            We need access to your photo library to show your recent photos for scanning.
          </Text>
        </VStack>
        <Button
          onPress={requestPermission}
          className="bg-gradient-to-r from-purple-400 to-purple-600 px-8 py-3"
        >
          <Text className="text-white font-bold">Grant Access</Text>
        </Button>
      </VStack>
    </VStack>
  );

  const renderEmptyState = () => (
    <VStack className="flex-1 justify-center items-center px-6">
      <VStack className="items-center space-y-6">
        <Box className="w-20 h-20 rounded-full bg-white/10 justify-center items-center">
          <ImageIcon size={40} color="#a855f7" />
        </Box>
        <VStack className="items-center space-y-2">
          <Text className="text-white text-xl font-semibold text-center">
            No Photos Found
          </Text>
          <Text className="text-white/80 text-center">
            Your photo gallery appears to be empty. Take some photos or try refreshing.
          </Text>
        </VStack>
        <Pressable
          onPress={handleRefresh}
          disabled={refreshing}
          className={`px-6 py-3 rounded-2xl ${refreshing ? 'opacity-50' : 'active:opacity-80'}`}
          style={styles.refreshButton}
        >
          <HStack className="items-center space-x-2">
            <Animated.View style={refreshAnimatedStyle}>
              <RefreshCwIcon size={16} color="#FFFFFF" />
            </Animated.View>
            <Text className="text-white font-medium">
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </Text>
          </HStack>
        </Pressable>
      </VStack>
    </VStack>
  );

  const renderPhotoGrid = () => (
    <Animated.View style={[{ flex: 1 }, gridAnimatedStyle]}>
      <ScrollView
        className="flex-1 px-5"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingBottom: 120,
          flexGrow: 1
        }}
        keyboardShouldPersistTaps="handled"
        bounces={true}
      >
        <VStack className="space-y-4">
          <HStack className="justify-between items-center">
            <Text className="text-white text-lg font-semibold">
              Recent Photos ({photos.length})
            </Text>
            <Pressable onPress={handleRefresh} disabled={refreshing}>
              <Animated.View style={refreshAnimatedStyle}>
                <RefreshCwIcon size={20} color="#a855f7" />
              </Animated.View>
            </Pressable>
          </HStack>

          <Box className="flex-row flex-wrap justify-between">
            {photos.map((photo) => (
              <Pressable
                key={photo.id}
                onPress={() => handlePhotoSelect(photo)}
                className="mb-4 rounded-2xl overflow-hidden bg-white/10 backdrop-blur-md active:scale-95"
                style={[
                  styles.photoItem,
                  {
                    width: imageSize,
                    height: imageSize,
                  }
                ]}
              >
                <Image
                  source={{ uri: photo.uri }}
                  style={{ width: imageSize, height: imageSize }}
                  resizeMode="cover"
                />
                <Box
                  className="absolute inset-0 rounded-2xl border border-white/20"
                  style={styles.photoOverlay}
                />
              </Pressable>
            ))}
          </Box>
        </VStack>
      </ScrollView>
    </Animated.View>
  );

  if (loading) {
    return (
      <ScanScreenLayout
        title="Gallery"
        activeTab={activeTab}
        onTabChange={handleTabChange}
        gradientColors={['#581c87', '#7c3aed', '#a855f7', '#c084fc']}
        scrollable={false}
      >
        <VStack className="flex-1 justify-center items-center">
          <VStack className="items-center space-y-4">
            <Box className="w-16 h-16 rounded-full bg-white/10 justify-center items-center">
              <GridIcon size={32} color="#a855f7" />
            </Box>
            <Text className="text-white/80">Loading gallery...</Text>
          </VStack>
        </VStack>
      </ScanScreenLayout>
    );
  }

  return (
    <ScanScreenLayout
      title="Photo Gallery"
      activeTab={activeTab}
      onTabChange={handleTabChange}
      gradientColors={['#581c87', '#7c3aed', '#a855f7', '#c084fc']}
      contentContainerStyle={{
        paddingVertical: 24,
        paddingBottom: 120,
        flexGrow: 1
      }}
    >
      {/* Floating decorative elements */}
      <Box className="absolute top-20 right-10 w-16 h-16 rounded-full bg-white/5 backdrop-blur-md" style={styles.floatingElement1} />
      <Box className="absolute top-40 left-8 w-12 h-12 rounded-full bg-purple-400/10 backdrop-blur-md" style={styles.floatingElement2} />
      <Box className="absolute bottom-32 right-6 w-20 h-20 rounded-full bg-white/5 backdrop-blur-md" style={styles.floatingElement3} />

      {/* Content */}
      <VStack className="flex-1 py-6">
        {hasPermission === false ? (
          renderPermissionRequest()
        ) : photos.length === 0 ? (
          renderEmptyState()
        ) : (
          renderPhotoGrid()
        )}
      </VStack>

      {/* Tips Section with glass morphism */}
      <Guideline
        state={guidelineState}
        bottomInset={0}
        marginBottom={20}
        onReviewPress={handleReviewNavigation}
        onRetakePress={handleRetake}
        customTips={[
          'Tap any photo to select and process it',
          'Recent photos appear first for easy access',
          'Pull to refresh to load new photos',
          'Select high-quality images for best results'
        ]}
      />
    </ScanScreenLayout>
  );
}

// StyleSheet following Rule #6 (prefer StyleSheet.create for static styles)
const styles = StyleSheet.create({
  glassHeader: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  glassButton: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  glassCard: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  refreshButton: {
    backgroundColor: '#7c3aed',
    shadowColor: '#7c3aed',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  photoItem: {
    shadowColor: '#a855f7',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  photoOverlay: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  floatingElement1: {
    shadowColor: '#a855f7',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  floatingElement2: {
    shadowColor: '#c084fc',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 6,
  },
  floatingElement3: {
    shadowColor: '#a855f7',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
});
