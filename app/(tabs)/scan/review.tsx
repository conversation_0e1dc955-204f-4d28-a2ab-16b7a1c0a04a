import { LinearGradient } from "expo-linear-gradient";
import { router, useLocalSearchParams } from "expo-router";
import {
	ArrowLeftIcon,
	CopyIcon,
	EditIcon,
	FileTextIcon,
	MapIcon,
	MessageCircleIcon,
	ShareIcon,
	StarIcon,
	VolumeXIcon,
} from "lucide-react-native";

// Import Post-Scan Navigation Components
import { PostScanNavigationModal } from '@/components/scan/PostScanNavigationModal';
import React, { useState, useEffect, useTransition, useDeferredValue, startTransition } from "react";
import {
	Alert,
	Pressable,
	ScrollView,
	StatusBar,
	StyleSheet,
} from "react-native";
import Animated, {
	useAnimatedStyle,
	useSharedValue,
	withSpring,
	withTiming,
} from "react-native-reanimated";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { z } from "zod";
import { Badge, BadgeIcon, BadgeText } from "@/components/ui/badge";
// Import UI components following Rule #6
import { Box } from "@/components/ui/box";
// Import Gluestack UI Button components
import { Button, ButtonGroup, ButtonText } from "@/components/ui/button";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { useAuth } from "@/lib/contexts/AuthContext";
import { useKnowledgeCards } from "@/lib/contexts/KnowledgeCardsContext";
import { usePermissionErrorHandler } from "@/lib/hooks/usePermissionErrorHandler";
import { aiService, AIServiceError } from "@/lib/services/ai.service";
import { aiCardGenerator, type CardGenerationProgress } from "@/lib/services/ai-card-generator.service";
// AppWrite services imported through contexts
import type { CompleteKnowledgeCard } from '@/types/appwrite-v2';

// Custom Toggle Component using Gluestack UI Button
const ToggleButton = ({
	leftLabel,
	rightLabel,
	isRightSelected,
	onToggle,
}: {
	leftLabel: string;
	rightLabel: string;
	isRightSelected: boolean;
	onToggle: () => void;
}) => {
	return (
		<ButtonGroup isAttached flexDirection="row">
			<Button
				variant={!isRightSelected ? "solid" : "outline"}
				size="sm"
				onPress={() => isRightSelected && onToggle()}
				className="rounded-none"
				style={[
					styles.toggleButtonLeft,
					!isRightSelected && styles.toggleButtonActive,
				]}
			>
				<ButtonText
					style={[
						styles.toggleButtonText,
						!isRightSelected && styles.toggleButtonTextActive,
					]}
				>
					{leftLabel}
				</ButtonText>
			</Button>
			<Button
				variant={isRightSelected ? "solid" : "outline"}
				size="sm"
				onPress={() => !isRightSelected && onToggle()}
				className="rounded-none"
				style={[
					styles.toggleButtonRight,
					isRightSelected && styles.toggleButtonActive,
				]}
			>
				<ButtonText
					style={[
						styles.toggleButtonText,
						isRightSelected && styles.toggleButtonTextActive,
					]}
				>
					{rightLabel}
				</ButtonText>
			</Button>
		</ButtonGroup>
	);
};

export default function ReviewScreen() {
	const insets = useSafeAreaInsets();
	const { user, isAuthenticated, isAnonymous } = useAuth(); // Get current user for AppWrite operations
	const { createCard } = useKnowledgeCards(); // Get knowledge cards operations
	const { withPermissionErrorHandling } = usePermissionErrorHandler();
	const params = useLocalSearchParams(); // Get route parameters for image URI

	// Zod validation schemas following Rule #11
	const ReviewStateSchema = z.object({
		extractedText: z.string(),
		translatedText: z.string(),
		selectedLanguage: z.string(),
		showTranslation: z.boolean(),
		ocrConfidence: z.number().min(0).max(100),
		isTranslating: z.boolean(),
		highlightedWords: z.array(z.string()),
		// AI processing states
		isProcessingOCR: z.boolean(),
		ocrProgress: z.number().min(0).max(100),
		ocrError: z.string().optional(),
		detectedLanguage: z.string().optional(),
		textBlocks: z.array(z.object({
			text: z.string(),
			bounds: z.object({
				x: z.number(),
				y: z.number(),
				width: z.number(),
				height: z.number(),
			}),
			confidence: z.number(),
		})).optional(),
		// AI card generation states
		isGeneratingCards: z.boolean(),
		cardGenerationProgress: z.number().min(0).max(100),
		cardGenerationStage: z.enum(['analyzing', 'structuring', 'enhancing', 'finalizing']).optional(),
		cardGenerationError: z.string().optional(),
		generatedCardsCount: z.number().min(0),
		// Post-scan navigation states
		showNavigationModal: z.boolean(),
		createdCards: z.array(z.any()).optional(), // CompleteKnowledgeCard array
	});

	type ReviewState = z.infer<typeof ReviewStateSchema>;

	// React 18 concurrent features for performance optimization
	const [isPending, startTransition] = useTransition();

	const [state, setState] = useState<ReviewState>({
		extractedText: "",
		translatedText: "",
		selectedLanguage: "",
		showTranslation: false,
		ocrConfidence: 0,
		isTranslating: false,
		highlightedWords: [],
		// AI processing states
		isProcessingOCR: true, // Start with OCR processing
		ocrProgress: 0,
		ocrError: undefined,
		detectedLanguage: undefined,
		textBlocks: undefined,
		// AI card generation states
		isGeneratingCards: false,
		cardGenerationProgress: 0,
		cardGenerationStage: undefined,
		cardGenerationError: undefined,
		generatedCardsCount: 0,
		// Post-scan navigation states
		showNavigationModal: false,
		createdCards: undefined,
	});

	// Deferred values for performance optimization during heavy operations
	const deferredExtractedText = useDeferredValue(state.extractedText);
	const deferredTranslatedText = useDeferredValue(state.translatedText);

	// AI OCR Processing Function with React 18 concurrent features (Rule #12: Comprehensive error handling)
	const processImageWithAI = async (imageUri: string) => {
		try {
			// Use startTransition for non-urgent state updates
			startTransition(() => {
				setState(prev => ({
					...prev,
					isProcessingOCR: true,
					ocrProgress: 0,
					ocrError: undefined
				}));
			});

			// Process OCR with AI service
			const ocrResult = await aiService.extractTextFromImage(
				{ imageUri, enhanceAccuracy: true },
				(progress) => {
					// Progress updates are urgent for user feedback
					setState(prev => ({ ...prev, ocrProgress: progress }));
				}
			);

			// Use startTransition for final result updates (non-urgent)
			startTransition(() => {
				setState(prev => ({
					...prev,
					extractedText: ocrResult.extractedText,
					ocrConfidence: ocrResult.confidence,
					detectedLanguage: ocrResult.language,
					textBlocks: ocrResult.textBlocks,
					isProcessingOCR: false,
					ocrProgress: 100,
					highlightedWords: ocrResult.extractedText
						.split(/\s+/)
						.filter(word => word.length > 4)
						.slice(0, 5), // Highlight first 5 significant words
				}));
			});

		} catch (error) {
			console.error('AI OCR Error:', error);

			// Handle different error types (Rule #12: Early return pattern)
			let errorMessage = 'Failed to process image with AI service';
			let isRetryable = false;

			if (error instanceof AIServiceError) {
				errorMessage = error.message;
				isRetryable = error.retryable;
			} else if (error instanceof Error) {
				errorMessage = error.message;
			}

			// Update state with error (no mock data fallback)
			startTransition(() => {
				setState(prev => ({
					...prev,
					isProcessingOCR: false,
					ocrError: errorMessage,
					ocrProgress: 0,
				}));
			});

			// Show user-friendly error with retry option
			Alert.alert(
				'OCR Processing Failed',
				errorMessage,
				[
					{ text: 'Cancel', style: 'cancel' },
					...(isRetryable ? [{
						text: 'Retry',
						onPress: () => processImageWithAI(imageUri)
					}] : []),
					{ text: 'OK' }
				]
			);
		}
	};

	// Trigger AI OCR processing on component mount (Rule #10: Performance optimization)
	useEffect(() => {
		// Get image URI from route parameters
		const imageUri = params.imageUri as string;

		// Fallback to mock image URI for demo purposes if no real image provided
		const finalImageUri = imageUri || 'https://via.placeholder.com/800x600/FFFFFF/000000?text=Machine+Learning+Document';

		// Only process if we haven't already processed
		if (state.isProcessingOCR && !state.extractedText) {
			processImageWithAI(finalImageUri);
		}
	}, [params.imageUri]); // Depend on imageUri parameter

	// Animation values following Rule #8
	const confidenceProgress = useSharedValue(0);
	const translateButtonScale = useSharedValue(1);

	const confidenceAnimatedStyle = useAnimatedStyle(() => {
		return {
			width: `${confidenceProgress.value}%`,
		};
	});

	const translateButtonAnimatedStyle = useAnimatedStyle(() => {
		return {
			transform: [{ scale: translateButtonScale.value }],
		};
	});

	// Initialize confidence animation
	React.useEffect(() => {
		confidenceProgress.value = withTiming(state.ocrConfidence, {
			duration: 1000,
		});
	}, [state.ocrConfidence]);

	// Translation functionality following Rule #12
	const handleTranslate = async () => {
		if (!state.selectedLanguage) {
			Alert.alert(
				"Language Required",
				"Please select a language to translate to.",
			);
			return;
		}

		if (!state.extractedText) {
			Alert.alert(
				"No Text Available",
				"Please wait for text extraction to complete.",
			);
			return;
		}

		try {
			// Use startTransition for non-urgent UI updates
			startTransition(() => {
				setState((prev) => ({ ...prev, isTranslating: true }));
			});
			translateButtonScale.value = withSpring(0.95);

			// Use AI service for translation
			const translationResult = await aiService.translateText(
				{
					text: state.extractedText,
					targetLanguage: state.selectedLanguage,
					sourceLanguage: state.detectedLanguage,
				},
				(chunk) => {
					// Streaming updates are urgent for user feedback
					setState((prev) => ({
						...prev,
						translatedText: chunk,
						showTranslation: true,
					}));
				}
			);

			// Final result update with startTransition
			startTransition(() => {
				setState((prev) => ({
					...prev,
					translatedText: translationResult.translatedText,
					isTranslating: false,
					showTranslation: true,
				}));
			});
			translateButtonScale.value = withSpring(1);

		} catch (error) {
			console.error("AI Translation error:", error);

			let errorMessage = 'Failed to translate text with AI service';
			let isRetryable = false;

			if (error instanceof AIServiceError) {
				errorMessage = error.message;
				isRetryable = error.retryable;
			} else if (error instanceof Error) {
				errorMessage = error.message;
			}

			// Update error state with startTransition
			startTransition(() => {
				setState((prev) => ({ ...prev, isTranslating: false }));
			});
			translateButtonScale.value = withSpring(1);

			// Show error with retry option (no mock fallback)
			Alert.alert(
				"Translation Failed",
				errorMessage,
				[
					{ text: 'Cancel', style: 'cancel' },
					...(isRetryable ? [{
						text: 'Retry',
						onPress: handleTranslate
					}] : []),
					{ text: 'OK' }
				]
			);
		}
	};

	const handleCopyText = async () => {
		// const textToCopy = state.showTranslation ? state.translatedText : state.extractedText;
		// In a real app, you would use Clipboard from @react-native-clipboard/clipboard
		Alert.alert("Copied", "Text copied to clipboard!");
	};

	const handleReadAloud = () => {
		// In a real app, you would use expo-speech
		Alert.alert("Read Aloud", "Text-to-speech functionality would start here.");
	};

	// AI-Powered Knowledge Card Generation (Rule #12: Comprehensive error handling)
	const handleGenerateKnowledgeCards = async () => {
		console.log("🚀 AI Generate Knowledge Cards button pressed!");

		// Early return if no user (Rule #12)
		if (!user || !isAuthenticated) {
			Alert.alert(
				"Authentication Required",
				"Please log in to create knowledge cards.",
			);
			return;
		}

		// Early return if no extracted text (Rule #12)
		if (!state.extractedText || state.extractedText.trim().length === 0) {
			Alert.alert(
				"No Content Available",
				"Please wait for text extraction to complete before generating cards.",
			);
			return;
		}

		// Early return if already generating (Rule #12)
		if (state.isGeneratingCards) {
			Alert.alert(
				"Generation in Progress",
				"Please wait for the current card generation to complete.",
			);
			return;
		}

		const result = await withPermissionErrorHandling(async () => {
			// Initialize card generation state
			startTransition(() => {
				setState(prev => ({
					...prev,
					isGeneratingCards: true,
					cardGenerationProgress: 0,
					cardGenerationStage: 'analyzing',
					cardGenerationError: undefined,
					generatedCardsCount: 0,
				}));
			});

			console.log("🤖 Starting AI card generation for user:", user.$id);

			// Prepare AI generation input
			const generationInput = {
				extractedText: state.extractedText,
				context: {
					subject: state.detectedLanguage ? `Content in ${state.detectedLanguage}` : undefined,
					level: 'intermediate' as const,
					focus: 'Educational knowledge card creation',
					language: state.detectedLanguage,
				},
				options: {
					generateMultiple: false, // Start with single card
					maxCards: 1,
					includeQuestions: true,
					enhanceWithAI: true,
				},
			};

			// Generate cards with AI
			const aiGeneratedCards = await aiCardGenerator.generateKnowledgeCards(
				generationInput,
				(progress: CardGenerationProgress) => {
					// Update progress (urgent updates)
					setState(prev => ({
						...prev,
						cardGenerationProgress: progress.progress,
						cardGenerationStage: progress.stage,
					}));
				}
			);

			console.log("🎯 AI generated cards:", aiGeneratedCards);

			// Convert AI cards to database format and create them
			const createdCards: CompleteKnowledgeCard[] = [];
			for (const aiCard of aiGeneratedCards) {
				const cardInput = aiCardGenerator.convertToCardInput(
					aiCard,
					user.$id,
					{
						originalText: state.extractedText,
						confidence: state.ocrConfidence,
						scanDate: new Date().toISOString(),
						ocrLanguage: state.detectedLanguage,
						textBlocks: state.textBlocks,
					}
				);

				console.log("📊 Creating card with data:", cardInput);

				// Create the card using the knowledge cards context
				const knowledgeCard = await createCard(cardInput);
				createdCards.push(knowledgeCard);
			}

			// Update final state and show navigation modal
			startTransition(() => {
				setState(prev => ({
					...prev,
					isGeneratingCards: false,
					cardGenerationProgress: 100,
					generatedCardsCount: createdCards.length,
					showNavigationModal: true,
					createdCards: createdCards,
				}));
			});

			console.log("✅ AI knowledge cards created successfully:", createdCards);
			return createdCards;

		}, "AI Knowledge card creation");

		if (!result) {
			// Handle error case
			startTransition(() => {
				setState(prev => ({
					...prev,
					isGeneratingCards: false,
					cardGenerationError: "Failed to generate knowledge cards",
					cardGenerationProgress: 0,
				}));
			});
		}
	};

	const handleCreateLearningPath = () => {
		Alert.alert("Learning Path", "Creating personalized learning path...");
	};

	const handleStartAIChat = () => {
		Alert.alert("AI Chat", "Starting AI chat about this content...");
	};

	const renderHighlightedText = (text: string) => {
		const parts = text.split(
			/(Machine learning|supervised learning|unsupervised learning|reinforcement learning)/gi,
		);

		return parts.map((part, index) => {
			const isHighlighted = state.highlightedWords.some(
				(word) => part.toLowerCase() === word.toLowerCase(),
			);

			if (isHighlighted) {
				return (
					<Text
						key={index}
						className="bg-pink-400/30 px-1 py-0.5 rounded text-white"
					>
						{part}
					</Text>
				);
			}
			return part;
		});
	};

	// Navigation modal handlers
	const handleCloseNavigationModal = () => {
		startTransition(() => {
			setState(prev => ({
				...prev,
				showNavigationModal: false,
			}));
		});
	};

	const handleNavigationSelected = (destination: string) => {
		console.log(`🧭 User selected navigation to: ${destination}`);
		// Modal will close automatically in the PostScanNavigationModal component
	};

	return (
		<LinearGradient
			colors={["#FF6B9D", "#A855F7", "#3B82F6", "#06B6D4"]}
			start={{ x: 0, y: 0 }}
			end={{ x: 1, y: 1 }}
			style={{ flex: 1 }}
		>
			<StatusBar
				barStyle="light-content"
				backgroundColor="transparent"
				translucent
			/>

			{/* Header */}
			<Box
				className="flex-row justify-between items-center px-5 py-4"
				style={{ paddingTop: insets.top + 16 }}
			>
				<HStack className="items-center space-x-4">
					<Pressable onPress={() => router.back()}>
						<Box
							className="p-2 rounded-lg bg-white/10 backdrop-blur-md"
							style={styles.glassButton}
						>
							<ArrowLeftIcon size={24} color="#FFFFFF" />
						</Box>
					</Pressable>
					<VStack>
						<Text className="text-white text-xl font-bold">Extracted Text</Text>
						{isPending && (
							<Text className="text-blue-300 text-xs">
								⚡ Optimizing performance...
							</Text>
						)}
					</VStack>
				</HStack>

				<HStack className="space-x-2">
					<Pressable onPress={() => Alert.alert("Edit", "Edit functionality")}>
						<Box
							className="p-2 rounded-lg bg-white/10 backdrop-blur-md"
							style={styles.glassButton}
						>
							<EditIcon size={20} color="#FFFFFF" />
						</Box>
					</Pressable>
					<Pressable
						onPress={() => Alert.alert("Share", "Share functionality")}
					>
						<Box
							className="p-2 rounded-lg bg-white/10 backdrop-blur-md"
							style={styles.glassButton}
						>
							<ShareIcon size={20} color="#FFFFFF" />
						</Box>
					</Pressable>
				</HStack>
			</Box>

			<ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
				{/* AI OCR Processing Indicator */}
				<Box className="px-5 mb-6">
					<Box
						className="p-5 rounded-xl bg-white/10 backdrop-blur-md"
						style={styles.glassCard}
					>
						{state.isProcessingOCR ? (
							// Processing State
							<VStack space="md">
								<HStack className="justify-between items-center">
									<Text className="text-white text-base font-medium">
										🤖 AI Processing Text...
									</Text>
									<Text className="text-blue-400 text-base font-semibold">
										{Math.round(state.ocrProgress)}%
									</Text>
								</HStack>
								<Box className="h-2 bg-white/20 rounded-full overflow-hidden">
									<Animated.View
										style={[
											{
												height: "100%",
												backgroundColor: "#3B82F6",
												borderRadius: 4,
												width: `${state.ocrProgress}%`,
											},
										]}
									/>
								</Box>
								<Text className="text-white/70 text-sm">
									Using AI vision model to extract text with high accuracy...
								</Text>
							</VStack>
						) : state.ocrError ? (
							// Error State with Retry Option
							<VStack space="md">
								<HStack className="justify-between items-center">
									<Text className="text-white text-base font-medium">
										❌ OCR Processing Failed
									</Text>
									<Text className="text-red-400 text-base font-semibold">
										Error
									</Text>
								</HStack>
								<Text className="text-red-300 text-sm">
									{state.ocrError}
								</Text>
								<HStack className="justify-between items-center mt-2">
									<Text className="text-white/60 text-xs">
										No text extracted. Please try again.
									</Text>
									<Button
										onPress={() => {
											const mockImageUri = 'https://via.placeholder.com/800x600/FFFFFF/000000?text=Machine+Learning+Document';
											processImageWithAI(mockImageUri);
										}}
										disabled={isPending}
										className="bg-blue-600 hover:bg-blue-700 px-4 py-2"
										size="sm"
									>
										<ButtonText className="text-white text-sm">
											{isPending ? 'Retrying...' : 'Retry OCR'}
										</ButtonText>
									</Button>
								</HStack>
							</VStack>
						) : (
							// Success State
							<VStack space="sm">
								<HStack className="justify-between items-center">
									<HStack className="items-center gap-2">
										<Text className="text-white text-base font-medium">
											✅ AI OCR Complete
										</Text>
										{state.detectedLanguage && (
											<Text className="text-blue-300 text-xs px-2 py-1 bg-blue-500/20 rounded-full">
												{state.detectedLanguage.toUpperCase()}
											</Text>
										)}
									</HStack>
									<Text className="text-green-400 text-base font-semibold">
										{state.ocrConfidence}%
									</Text>
								</HStack>
								<Box className="h-2 bg-white/20 rounded-full overflow-hidden">
									<Animated.View
										style={[
											{
												height: "100%",
												backgroundColor:
													state.ocrConfidence >= 80
														? "#10B981"
														: state.ocrConfidence >= 60
															? "#F59E0B"
															: "#EF4444",
												borderRadius: 4,
											},
											confidenceAnimatedStyle,
										]}
									/>
								</Box>
								{state.textBlocks && (
									<Text className="text-white/60 text-xs">
										Detected {state.textBlocks.length} text blocks
									</Text>
								)}
							</VStack>
						)}
					</Box>
				</Box>

				{/* AI Card Generation Progress Indicator */}
				{(state.isGeneratingCards || state.cardGenerationError || state.generatedCardsCount > 0) && (
					<Box className="px-5 mb-6">
						<Box
							className="p-5 rounded-xl bg-white/10 backdrop-blur-md"
							style={styles.glassCard}
						>
							{state.isGeneratingCards ? (
								// Card Generation Processing State
								<VStack space="md">
									<HStack className="justify-between items-center">
										<Text className="text-white text-base font-medium">
											🧠 AI Generating Cards...
										</Text>
										<Text className="text-purple-400 text-base font-semibold">
											{Math.round(state.cardGenerationProgress)}%
										</Text>
									</HStack>
									<Box className="h-2 bg-white/20 rounded-full overflow-hidden">
										<Animated.View
											style={[
												{
													height: "100%",
													backgroundColor: "#8B5CF6",
													borderRadius: 4,
													width: `${state.cardGenerationProgress}%`,
												},
											]}
										/>
									</Box>
									<Text className="text-white/70 text-sm">
										{state.cardGenerationStage === 'analyzing' && 'Analyzing content structure...'}
										{state.cardGenerationStage === 'structuring' && 'Creating knowledge framework...'}
										{state.cardGenerationStage === 'enhancing' && 'Enhancing with study questions...'}
										{state.cardGenerationStage === 'finalizing' && 'Finalizing knowledge cards...'}
									</Text>
								</VStack>
							) : state.cardGenerationError ? (
								// Card Generation Error State
								<VStack space="sm">
									<HStack className="justify-between items-center">
										<Text className="text-white text-base font-medium">
											❌ Card Generation Failed
										</Text>
										<Text className="text-red-400 text-base font-semibold">
											Error
										</Text>
									</HStack>
									<Text className="text-red-300 text-sm">
										{state.cardGenerationError}
									</Text>
									<HStack className="justify-between items-center mt-2">
										<Text className="text-white/60 text-xs">
											No cards generated. Please try again.
										</Text>
										<Button
											onPress={handleGenerateKnowledgeCards}
											disabled={isPending || state.isGeneratingCards}
											className="bg-purple-600 hover:bg-purple-700 px-4 py-2"
											size="sm"
										>
											<ButtonText className="text-white text-sm">
												{isPending ? 'Retrying...' : 'Retry Generation'}
											</ButtonText>
										</Button>
									</HStack>
								</VStack>
							) : (
								// Card Generation Success State
								<VStack space="sm">
									<HStack className="justify-between items-center">
										<Text className="text-white text-base font-medium">
											🎉 AI Cards Generated
										</Text>
										<Text className="text-green-400 text-base font-semibold">
											{state.generatedCardsCount} card{state.generatedCardsCount !== 1 ? 's' : ''}
										</Text>
									</HStack>
									<Text className="text-white/70 text-sm">
										Successfully created {state.generatedCardsCount} AI-enhanced knowledge card{state.generatedCardsCount !== 1 ? 's' : ''} from extracted text.
									</Text>
								</VStack>
							)}
						</Box>
					</Box>
				)}

				{/* Translation Controls */}
				<Box className="px-5 mb-6">
					<Box
						className="p-5 rounded-xl bg-white/10 backdrop-blur-md"
						style={styles.glassCard}
					>
						<VStack className="space-y-5">
							<VStack className="space-y-3">
								<Text className="text-white text-base font-medium">
									Translate to:
								</Text>
								<HStack className="items-stretch">
									<Box className="flex-1">
										<Pressable
											onPress={() => {
												Alert.alert("Select Language", "Choose a language", [
													{
														text: "Spanish",
														onPress: () =>
															setState((prev) => ({
																...prev,
																selectedLanguage: "Spanish",
															})),
													},
													{
														text: "French",
														onPress: () =>
															setState((prev) => ({
																...prev,
																selectedLanguage: "French",
															})),
													},
													{
														text: "German",
														onPress: () =>
															setState((prev) => ({
																...prev,
																selectedLanguage: "German",
															})),
													},
													{ text: "Cancel", style: "cancel" },
												]);
											}}
											style={[styles.languageSelector]}
										>
											<Text
												className="text-white text-base"
												style={{ lineHeight: 20 }}
											>
												{state.selectedLanguage || "Select Language"}
											</Text>
										</Pressable>
									</Box>
									<Animated.View style={translateButtonAnimatedStyle}>
										<Pressable
											onPress={handleTranslate}
											disabled={state.isTranslating || isPending}
											style={[
												styles.translateButton,
												styles.translateButtonConnected,
												(state.isTranslating || isPending) && { opacity: 0.6 }
											]}
										>
											<HStack className="items-center gap-2">
												{(state.isTranslating || isPending) && (
													<Box className="w-4 h-4">
														<Text className="text-white text-xs">⏳</Text>
													</Box>
												)}
												<Text
													className="text-white text-base font-semibold"
													style={{ lineHeight: 20 }}
												>
													{state.isTranslating || isPending ? "Translating..." : "Translate"}
												</Text>
											</HStack>
										</Pressable>
									</Animated.View>
								</HStack>
							</VStack>

							<HStack className="items-center justify-center py-4 px-6">
								<ToggleButton
									leftLabel="Original"
									rightLabel="Translation"
									isRightSelected={state.showTranslation}
									onToggle={() =>
										setState((prev) => ({
											...prev,
											showTranslation: !prev.showTranslation,
										}))
									}
								/>
							</HStack>
						</VStack>
					</Box>
				</Box>

				{/* Main Content */}
				<Box className="px-5 mb-8">
					<Box
						className="p-6 rounded-xl bg-white/10 backdrop-blur-md"
						style={styles.glassCard}
					>
						<HStack className="justify-between items-center mb-5">
							<Text className="text-white text-lg font-semibold">
								{state.showTranslation
									? `${state.selectedLanguage} Translation`
									: "Original Text"}
							</Text>
							<HStack className="space-x-3">
								{!state.showTranslation || (
									<Badge size="lg" variant="solid" action="muted">
										<BadgeText>Translated</BadgeText>
										<BadgeIcon as={StarIcon} className="ml-2" />
									</Badge>
								)}
								<Pressable onPress={handleReadAloud}>
									<Box
										className="p-3 rounded-lg bg-white/10"
										style={styles.glassButton}
									>
										<VolumeXIcon size={18} color="#FFFFFF" />
									</Box>
								</Pressable>
								<Pressable onPress={handleCopyText}>
									<Box
										className="p-3 rounded-lg bg-white/10"
										style={styles.glassButton}
									>
										<CopyIcon size={18} color="#FFFFFF" />
									</Box>
								</Pressable>
							</HStack>
						</HStack>

						<Box className="leading-relaxed">
							<Text className="text-white/90 text-base leading-8">
								{state.showTranslation
									? deferredTranslatedText
									: renderHighlightedText(deferredExtractedText)}
							</Text>
						</Box>
					</Box>
				</Box>

				{/* Action Buttons */}
				<Box
					className="px-5 pb-8"
					style={{ paddingBottom: insets.bottom + 32 }}
				>
					<VStack className="space-y-4">
						<Pressable
							onPress={handleGenerateKnowledgeCards}
							disabled={state.isGeneratingCards || isPending || !state.extractedText}
							className="py-4 px-6 rounded-xl bg-pink-500 mx-1"
							style={[
								styles.actionButton,
								{ marginBottom: 8 },
								(state.isGeneratingCards || isPending || !state.extractedText) && { opacity: 0.6 }
							]}
						>
							<HStack className="items-center justify-center space-x-2">
								{state.isGeneratingCards && (
									<Text className="text-white text-base">🧠</Text>
								)}
								<Text className="text-white text-base font-semibold">
									{state.isGeneratingCards
										? `Generating... ${Math.round(state.cardGenerationProgress)}%`
										: state.generatedCardsCount > 0
											? `Generate More Cards (${state.generatedCardsCount} created)`
											: "🤖 Generate AI Knowledge Cards"
									}
								</Text>
								{!state.isGeneratingCards && <FileTextIcon size={20} color="#FFFFFF" />}
							</HStack>
						</Pressable>

						<Pressable
							onPress={handleCreateLearningPath}
							className="py-4 px-6 rounded-xl bg-blue-500 mx-1"
							style={[styles.actionButton, { marginBottom: 8 }]}
						>
							<HStack className="items-center justify-center space-x-2">
								<Text className="text-white text-base font-semibold">
									Create Learning Path
								</Text>
								<MapIcon size={20} color="#FFFFFF" />
							</HStack>
						</Pressable>

						<Pressable
							onPress={handleStartAIChat}
							className="py-4 px-6 rounded-xl bg-white/10 backdrop-blur-md mx-1"
							style={[styles.glassButton, { marginBottom: 8 }]}
						>
							<HStack className="items-center justify-center space-x-2">
								<Text className="text-white text-base font-semibold">
									Start AI Chat
								</Text>
								<MessageCircleIcon size={20} color="#FFFFFF" />
							</HStack>
						</Pressable>
					</VStack>
				</Box>
			</ScrollView>

			{/* Post-Scan Navigation Modal */}
			{state.showNavigationModal && state.createdCards && (
				<PostScanNavigationModal
					visible={state.showNavigationModal}
					onClose={handleCloseNavigationModal}
					createdCards={state.createdCards}
					scanData={{
						extractedText: state.extractedText,
						confidence: state.ocrConfidence,
						language: state.detectedLanguage,
					}}
					onNavigate={handleNavigationSelected}
				/>
			)}
		</LinearGradient>
	);
}

// StyleSheet following Rule #6 (prefer StyleSheet.create for static styles)
const styles = StyleSheet.create({
	glassButton: {
		shadowColor: "#000",
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.2,
		shadowRadius: 4,
		elevation: 4,
	},
	glassCard: {
		shadowColor: "#000",
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.1,
		shadowRadius: 4,
		elevation: 4,
	},
	languageSelector: {
		height: 48,
		paddingVertical: 12,
		paddingHorizontal: 16,
		backgroundColor: "rgba(255, 255, 255, 0.1)",
		borderTopLeftRadius: 8,
		borderBottomLeftRadius: 8,
		borderTopRightRadius: 0,
		borderBottomRightRadius: 0,
		justifyContent: "center",
		shadowColor: "#000",
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.2,
		shadowRadius: 4,
		elevation: 4,
	},
	translateButton: {
		shadowColor: "#A855F7",
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.3,
		shadowRadius: 4,
		elevation: 4,
	},
	translateButtonConnected: {
		height: 48,
		paddingVertical: 12,
		paddingHorizontal: 20,
		backgroundColor: "#A855F7",
		borderTopLeftRadius: 0,
		borderBottomLeftRadius: 0,
		borderTopRightRadius: 8,
		borderBottomRightRadius: 8,
		justifyContent: "center",
		alignItems: "center",
	},
	toggleButtonLeft: {
		backgroundColor: "rgba(255, 255, 255, 0.1)",
		borderTopLeftRadius: 6,
		borderBottomLeftRadius: 6,
		borderTopRightRadius: 0,
		borderBottomRightRadius: 0,
		borderRightWidth: 0,
		paddingHorizontal: 14,
		paddingVertical: 6,
		minWidth: 75,
	},
	toggleButtonRight: {
		backgroundColor: "rgba(255, 255, 255, 0.1)",
		borderTopLeftRadius: 0,
		borderBottomLeftRadius: 0,
		borderTopRightRadius: 6,
		borderBottomRightRadius: 6,
		borderLeftWidth: 0,
		paddingHorizontal: 14,
		paddingVertical: 6,
		minWidth: 75,
	},
	toggleButtonActive: {
		backgroundColor: "#FF6B9D",
	},
	toggleButtonText: {
		color: "rgba(255, 255, 255, 0.7)",
		fontSize: 14,
		fontWeight: "500",
	},
	toggleButtonTextActive: {
		color: "#FFFFFF",
		fontWeight: "600",
	},
	actionButton: {
		shadowColor: "#000",
		shadowOffset: { width: 0, height: 4 },
		shadowOpacity: 0.3,
		shadowRadius: 8,
		elevation: 8,
	},
});
