import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Pressable, StyleSheet, StatusBar, Alert, Dimensions, ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withRepeat, 
  withTiming, 
  withSpring,
  interpolate,
  Easing
} from 'react-native-reanimated';
import {
  ZapIcon,
  SettingsIcon,
  CameraIcon,
  RotateCcwIcon,
  ImageIcon,
  Crown,
  ArrowLeft
} from 'lucide-react-native';
import { z } from 'zod';

// Import Smart Camera Interface for AI enhancement
import { SmartCameraInterface } from '@/components/camera/SmartCameraInterface';

// Import Permission System
import { FeatureGate } from '@/components/auth/FeatureGate';
import { usePermissions, PERMISSIONS } from '@/hooks/usePermissions';

// Import UI components following Rule #6
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';
import { Guideline } from '@/components/home/<USER>';
import { TabSelectorButtons, TabType } from '@/components/home/<USER>';

const CameraStateSchema = z.object({
  facing: z.enum(['back', 'front']),
  flash: z.enum(['off', 'on', 'auto']),
  isCapturing: z.boolean(),
  guidelineState: z.enum(['scanning', 'captured', 'processing']),
  capturedPhoto: z.string().nullable(),
});

type ICameraState = z.infer<typeof CameraStateSchema>;

// Enhanced document frame component with real-time edge detection
const DocumentFrame: React.FC<{ isScanning: boolean }> = ({ isScanning }) => {
  const scanLinePosition = useSharedValue(0);
  const cornerScale = useSharedValue(1);
  const cornerOpacity = useSharedValue(0.8);
  const frameScale = useSharedValue(1);
  const edgeDetectionOpacity = useSharedValue(0);
  const boundaryScale = useSharedValue(1);

  // Enhanced scanning animation with edge detection following Rule #8
  useEffect(() => {
    if (isScanning) {
      // Scanning line animation
      scanLinePosition.value = withRepeat(
        withTiming(1, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
        -1,
        true
      );
      // Corner pulsing animation
      cornerScale.value = withRepeat(
        withSpring(1.2, { damping: 15, stiffness: 200 }),
        -1,
        true
      );
      // Corner opacity pulsing
      cornerOpacity.value = withRepeat(
        withTiming(1, { duration: 1000 }),
        -1,
        true
      );
      // Frame breathing effect
      frameScale.value = withRepeat(
        withSpring(1.02, { damping: 20, stiffness: 100 }),
        -1,
        true
      );
      // Edge detection highlighting
      edgeDetectionOpacity.value = withRepeat(
        withTiming(0.9, { duration: 800 }),
        -1,
        true
      );
      boundaryScale.value = withRepeat(
        withSpring(1.05, { damping: 12, stiffness: 150 }),
        -1,
        true
      );
    } else {
      scanLinePosition.value = withTiming(0, { duration: 300 });
      cornerScale.value = withSpring(1);
      cornerOpacity.value = withTiming(0.8, { duration: 300 });
      frameScale.value = withSpring(1);
      edgeDetectionOpacity.value = withTiming(0, { duration: 300 });
      boundaryScale.value = withSpring(1);
    }
  }, [isScanning]);

  // Simplified edge detection simulation
  useEffect(() => {
    if (!isScanning) {
      const interval = setInterval(() => {
        // Simple detection simulation
        const detected = Math.random() > 0.5;
        edgeDetectionOpacity.value = withSpring(detected ? 0.8 : 0.3);
        boundaryScale.value = withSpring(detected ? 1.02 : 1);
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [isScanning]);

  const scanLineStyle = useAnimatedStyle(() => {
    const translateY = interpolate(
      scanLinePosition.value,
      [0, 1],
      [0, 200] // Frame height - scan line height
    );
    
    return {
      transform: [{ translateY }],
      opacity: isScanning ? 0.8 : 0,
    };
  });

  const cornerStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: cornerScale.value }],
      opacity: cornerOpacity.value,
    };
  });

  const frameStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: frameScale.value }],
    };
  });

  const edgeDetectionStyle = useAnimatedStyle(() => {
    return {
      opacity: edgeDetectionOpacity.value,
      transform: [{ scale: boundaryScale.value }],
    };
  });

  return (
    <Box className="relative">
      {/* Main viewfinder frame with glass morphism and breathing animation */}
      <Animated.View style={frameStyle}>
        <Box 
          className="w-80 h-52 border-2 border-dashed border-white/60 rounded-2xl bg-black/20 backdrop-blur-md"
          style={styles.glassFrame}
        >
        {/* Enhanced corner indicators with glow effect */}
        <Animated.View style={[styles.cornerTopLeft, cornerStyle]}>
          <Box className="w-8 h-8 border-l-4 border-t-4 border-candy-pink rounded-tl-xl shadow-lg" 
               style={{ shadowColor: '#FF6B9D', shadowOpacity: 0.6, shadowRadius: 8 }} />
        </Animated.View>
        <Animated.View style={[styles.cornerTopRight, cornerStyle]}>
          <Box className="w-8 h-8 border-r-4 border-t-4 border-candy-pink rounded-tr-xl shadow-lg" 
               style={{ shadowColor: '#FF6B9D', shadowOpacity: 0.6, shadowRadius: 8 }} />
        </Animated.View>
        <Animated.View style={[styles.cornerBottomLeft, cornerStyle]}>
          <Box className="w-8 h-8 border-l-4 border-b-4 border-candy-pink rounded-bl-xl shadow-lg" 
               style={{ shadowColor: '#FF6B9D', shadowOpacity: 0.6, shadowRadius: 8 }} />
        </Animated.View>
        <Animated.View style={[styles.cornerBottomRight, cornerStyle]}>
          <Box className="w-8 h-8 border-r-4 border-b-4 border-candy-pink rounded-br-xl shadow-lg" 
               style={{ shadowColor: '#FF6B9D', shadowOpacity: 0.6, shadowRadius: 8 }} />
        </Animated.View>

        {/* Real-time document boundary detection overlay */}
        <Animated.View style={[styles.edgeDetectionOverlay, edgeDetectionStyle]}>
          <Box 
            className="w-full h-full border-2 border-green-400 rounded-xl"
            style={{
              borderColor: '#10B981',
              shadowColor: '#10B981',
              shadowOpacity: 0.6,
              shadowRadius: 12,
              shadowOffset: { width: 0, height: 0 },
            }}
          />
          {/* Edge detection indicators */}
          <Box className="absolute top-2 left-2 w-4 h-4 bg-green-400 rounded-full" 
               style={{ shadowColor: '#10B981', shadowOpacity: 0.8, shadowRadius: 6 }} />
          <Box className="absolute top-2 right-2 w-4 h-4 bg-green-400 rounded-full" 
               style={{ shadowColor: '#10B981', shadowOpacity: 0.8, shadowRadius: 6 }} />
          <Box className="absolute bottom-2 left-2 w-4 h-4 bg-green-400 rounded-full" 
               style={{ shadowColor: '#10B981', shadowOpacity: 0.8, shadowRadius: 6 }} />
          <Box className="absolute bottom-2 right-2 w-4 h-4 bg-green-400 rounded-full" 
               style={{ shadowColor: '#10B981', shadowOpacity: 0.8, shadowRadius: 6 }} />
        </Animated.View>

        {/* Scanning line animation */}
        <Animated.View style={[styles.scanLine, scanLineStyle]}>
          <LinearGradient
            colors={['transparent', '#FF6B9D', 'transparent']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.scanLineGradient}
          />
        </Animated.View>

        {/* Center content with detection status */}
        <VStack className="flex-1 justify-center items-center p-6">
          <CameraIcon size={32} color="#FFFFFF" />
          <Text className="text-white text-center mt-2">
            Position document within frame
          </Text>
          <Text className="text-white/70 text-sm text-center mt-1">
            Align corners for best results
          </Text>
          
          {/* Real-time detection status */}
          <Animated.View style={edgeDetectionStyle}>
            <HStack className="items-center mt-3 px-3 py-1 bg-green-500/20 rounded-full border border-green-400/30">
              <Box className="w-2 h-2 bg-green-400 rounded-full mr-2" />
              <Text className="text-green-300 text-xs font-medium">
                Document Detected
              </Text>
            </HStack>
          </Animated.View>
        </VStack>
        </Box>
      </Animated.View>
    </Box>
  );
};

// Using official expo-camera permissions hook

export default function CameraScreen() {
  const insets = useSafeAreaInsets();
  
  // Direct state definitions (simple state)
  const [activeTab, setActiveTab] = useState<TabType>('camera');
  const [facing, setFacing] = useState<CameraType>('back');
  const [flash, setFlash] = useState<ICameraState['flash']>('off');
  const [isCapturing, setIsCapturing] = useState<ICameraState['isCapturing']>(false);
   const [capturedPhoto, setCapturedPhoto] = useState<ICameraState['capturedPhoto']>(null);
  const [guidelineState, setGuidelineState] = useState<ICameraState['guidelineState']>('scanning' as const);
  const [aiMode, setAiMode] = useState<boolean>(false); // AI enhancement mode toggle

  // Permission system integration (Rule 11: Zod validation)
  const { trackUsage, hasPermission, userRole, isPremium } = usePermissions();
  
  // Official expo-camera permissions hook
  const [permission, requestPermission] = useCameraPermissions();

  const cameraRef = useRef<any>(null);

  // Tab navigation handler - optimized with useCallback
  const handleTabChange = useCallback((tab: TabType) => {
    if (tab === activeTab) return; // Prevent unnecessary navigation

    setActiveTab(tab);

    // Use a switch for better performance
    switch (tab) {
      case 'upload':
        router.replace('./upload');
        break;
      case 'gallery':
        router.replace('./gallery');
        break;
      case 'camera':
        router.replace('./camera');
        break;
    }
  }, [activeTab, router]);

  // Camera control handlers
  const toggleFlash = () => {
    setFlash(prev => prev === 'off' ? 'on' : 'off');
  };

  const toggleCameraFacing = () => {
    setFacing(prev => prev === 'back' ? 'front' : 'back');
  };

  // Early return for permission handling (Rule #12)
  if (!permission) {
    return (
      <Box className="flex-1 justify-center items-center bg-black">
        <Text className="text-white">Requesting camera permission...</Text>
      </Box>
    );
  }

  if (!permission.granted) {
    return (
      <Box className="flex-1 justify-center items-center bg-black">
        <Text className="text-white text-center">
          Camera access is required to scan documents
        </Text>
        <Pressable onPress={requestPermission} style={{ marginTop: 16, padding: 12, backgroundColor: '#FF6B9D', borderRadius: 8 }}>
          <Text className="text-white text-center font-bold">Grant Permission</Text>
        </Pressable>
      </Box>
    );
  }

  const takePicture = async () => {
    if (!cameraRef.current || isCapturing) return;

    // Track usage before processing (Rule 10: Performance optimization)
    const canUse = await trackUsage('daily_scans');
    if (!canUse) {
      Alert.alert(
        'Daily Limit Reached',
        'You have reached your daily scan limit. Upgrade to Premium for unlimited scans!',
        [
          { text: 'Maybe Later', style: 'cancel' },
          { text: 'Upgrade Now', onPress: () => {
            // Navigate to upgrade screen
            router.push('/upgrade');
          }}
        ]
      );
      return;
    }

    setIsCapturing(true);
    setGuidelineState('processing');
    
    try {
      // Use the new camera service for enhanced functionality
      const { cameraService } = await import('@/lib/services/camera.service');
      
      // For now, we'll just capture and process the photo
      // Full workflow integration will be added when user authentication is ready
      const processedPhoto = await cameraService.capturePhoto(cameraRef, {
        quality: 0.8,
        skipProcessing: false,
        base64: false,
      });
      
      // Update state with captured photo
      setCapturedPhoto(processedPhoto.uri);
      setGuidelineState('captured');
      
      console.log('Photo captured and processed:', {
        uri: processedPhoto.uri,
        dimensions: `${processedPhoto.width}x${processedPhoto.height}`,
        fileSize: `${(processedPhoto.fileSize / 1024 / 1024).toFixed(2)}MB`,
      });
      
      // TODO: Navigate to review screen with photo data
      // router.push({
      //   pathname: './review',
      //   params: { photoUri: processedPhoto.uri }
      // });
      
    } catch (error) {
      setGuidelineState('scanning');
      console.error('Error taking picture:', error);
      
      // Show user-friendly error message
      Alert.alert(
        'Camera Error',
        'Failed to capture photo. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsCapturing(false);
    }
  };

   const handleReviewNavigation = () => {
    if (!capturedPhoto) return;

    router.replace({
      pathname: './review',
      params: {
        imageUri: capturedPhoto,
        source: 'camera',
        timestamp: Date.now().toString()
      }
    });
  };

  const handleRetake = () => {
    setCapturedPhoto(null);
    setGuidelineState('scanning');
  };

  return (
    <LinearGradient
      colors={['#1e1b4b', '#312e81', '#4338ca', '#6366f1']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: insets.bottom + 20
        }}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        bounces={true}
      >
      {/* Header with glass morphism */}
      <Box 
        className="flex-row justify-between items-center px-5 py-4 bg-white/10 backdrop-blur-md border-b border-white/20"
        style={[
          styles.glassHeader,
          { paddingTop: insets.top + 16 }
        ]}
      >
          <HStack className="items-center gap-3">
            <Pressable onPress={() => router.push('/(tabs)/home')}>
              <HStack className="items-center gap-2">
                <ArrowLeft size={20} color="#FFFFFF" />
                <Text className="text-white font-medium">Home</Text>
              </HStack>
            </Pressable>
          </HStack>

          <Text className="text-white text-lg font-bold">Scan Document</Text>

          <HStack className="gap-3">
            <Pressable onPress={toggleFlash}>
              <Box
                className="p-2 rounded-full bg-white/10 backdrop-blur-md"
                style={[styles.glassButton, flash === 'on' && styles.activeFlash]}
              >
                <ZapIcon size={20} color="#FFFFFF" />
              </Box>
            </Pressable>
            <Pressable onPress={() => router.push('/(tabs)/profile')}>
              <Box className="p-2 rounded-full bg-white/10 backdrop-blur-md" style={styles.glassButton}>
                <SettingsIcon size={20} color="#FFFFFF" />
              </Box>
            </Pressable>
          </HStack>
        </Box>



        {/* Tab Navigation with glass morphism */}
        <TabSelectorButtons
          activeTab={activeTab}
          onTabChange={handleTabChange}
          activeTabColor="#FF6B9D"
        />

        {/* Camera Interface - AI Enhanced or Standard */}
        <Box style={{ height: Dimensions.get('window').height * 0.6, minHeight: 400 }}>
          {aiMode ? (
            <FeatureGate
              permission={PERMISSIONS.AI_CAMERA}
              showUpgradePrompt={true}
              fallback={
                <Box className="justify-center items-center p-4" style={{ height: '100%' }}>
                  <VStack space="md" className="items-center">
                    <Crown size={48} color="#FF6B9D" />
                    <Text size="lg" className="font-semibold text-center">
                      AI Camera Enhancement
                    </Text>
                    <Text size="sm" className="text-neutral-600 text-center">
                      Upgrade to Premium for AI-powered camera features
                    </Text>
                    <Button
                      action="candyPink"
                      onPress={() => router.push('/(tabs)/profile')}
                    >
                      <ButtonIcon as={Crown} />
                      <ButtonText>Upgrade to Premium</ButtonText>
                    </Button>
                  </VStack>
                </Box>
              }
            >
              <SmartCameraInterface
                onCapture={(imageUri, enhancedUri) => {
                  setCapturedPhoto(enhancedUri || imageUri);
                  setGuidelineState('captured');
                  console.log('AI Enhanced photo captured:', {
                    original: imageUri,
                    enhanced: enhancedUri,
                  });
                }}
                onClose={() => setAiMode(false)}
                enableAIGuidance={true}
                enableAutoCapture={false}
                enableDocumentDetection={true}
                enableImageEnhancement={true}
                realTimeAnalysis={true}
              />
            </FeatureGate>
          ) : (
            <CameraView
              ref={cameraRef}
              style={styles.camera}
              facing={facing}
              flash={flash}
            >
              <VStack className="flex-1 justify-center items-center px-5">
                <DocumentFrame isScanning={isCapturing} />
              </VStack>
            </CameraView>
          )}
        </Box>

        {/* Camera Controls */}
        {!aiMode && (
          <HStack className="justify-around items-center px-10 pb-10">
            <Pressable onPress={() => router.replace('./gallery')}>
              <Box className="p-4 rounded-full bg-white/10 backdrop-blur-md" style={styles.glassButton}>
                <ImageIcon size={24} color="#FFFFFF" />
              </Box>
            </Pressable>

            <Pressable
              onPress={takePicture}
              disabled={isCapturing}
              style={[styles.captureButton, isCapturing && styles.capturingButton]}
            >
              <Box className="w-18 h-18 rounded-full bg-white justify-center items-center">
                <CameraIcon size={28} color="#1e1b4b" />
              </Box>
            </Pressable>

            <Pressable onPress={toggleCameraFacing}>
              <Box className="p-4 rounded-full bg-white/10 backdrop-blur-md" style={styles.glassButton}>
                <RotateCcwIcon size={24} color="#FFFFFF" />
              </Box>
            </Pressable>
          </HStack>
        )}

        {/* AI Mode Toggle with Permission Check */}
        <Box className="absolute bottom-20 right-5">
          {hasPermission(PERMISSIONS.AI_CAMERA) ? (
            <Pressable
              onPress={() => setAiMode(!aiMode)}
              style={[
                styles.glassButton,
                {
                  padding: 12,
                  borderRadius: 25,
                  backgroundColor: aiMode ? 'rgba(139, 92, 246, 0.3)' : 'rgba(255, 255, 255, 0.1)',
                  borderWidth: aiMode ? 2 : 1,
                  borderColor: aiMode ? '#8b5cf6' : 'rgba(255, 255, 255, 0.3)',
                }
              ]}
            >
              <HStack className="items-center gap-2">
                <ZapIcon size={20} color={aiMode ? '#8b5cf6' : '#FFFFFF'} />
                <Text className={`text-xs font-semibold ${aiMode ? 'text-purple-300' : 'text-white'}`}>
                  {aiMode ? 'AI ON' : 'AI'}
                </Text>
              </HStack>
            </Pressable>
          ) : (
            <Pressable
              onPress={() => router.push('/(tabs)/profile')}
              style={[
                styles.glassButton,
                {
                  padding: 12,
                  borderRadius: 25,
                  backgroundColor: 'rgba(255, 107, 157, 0.2)',
                  borderWidth: 2,
                  borderColor: '#FF6B9D',
                }
              ]}
            >
              <HStack className="items-center gap-2">
                <Crown size={16} color="#FF6B9D" />
                <Text className="text-xs font-semibold text-pink-300">
                  {isPremium ? 'AI' : 'PRO'}
                </Text>
              </HStack>
            </Pressable>
          )}
        </Box>

        {/* Tips Section with glass morphism */}
        <Guideline
          state={guidelineState}
          bottomInset={insets.bottom}
          marginBottom={20}
          onReviewPress={handleReviewNavigation}
          onRetakePress={handleRetake}
          customTips={[
            'Ensure good lighting for clear scans',
            'Place document flat on surface', 
            'Align document with corner guides',
            'Auto-crop will detect edges'
          ]}
        />
      </ScrollView>
    </LinearGradient>
  );
}

// StyleSheet following Rule #6 (prefer StyleSheet.create for static styles)
const styles = StyleSheet.create({
  glassFrame: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  glassHeader: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  glassButton: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  glassCard: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  activeFlash: {
    backgroundColor: '#FBBF24', // Yellow for active flash
  },
  captureButton: {
    width: 72,
    height: 72,
    borderRadius: 36,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  capturingButton: {
    opacity: 0.7,
    transform: [{ scale: 0.95 }],
  },
  // Corner indicator positions
  cornerTopLeft: {
    position: 'absolute',
    top: -3,
    left: -3,
  },
  cornerTopRight: {
    position: 'absolute',
    top: -3,
    right: -3,
  },
  cornerBottomLeft: {
    position: 'absolute',
    bottom: -3,
    left: -3,
  },
  cornerBottomRight: {
    position: 'absolute',
    bottom: -3,
    right: -3,
  },
  // Edge detection overlay
  edgeDetectionOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 5,
  },
  // Scanning line animation
  scanLine: {
    position: 'absolute',
    top: 20,
    left: 20,
    right: 20,
    height: 3,
    zIndex: 10,
  },
  scanLineGradient: {
    flex: 1,
    borderRadius: 2,
  },
  camera: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
});