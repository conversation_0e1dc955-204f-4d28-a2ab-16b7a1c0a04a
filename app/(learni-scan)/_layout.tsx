import { Tabs } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { useColorScheme } from 'react-native';
import { CameraIcon, FolderIcon, SearchIcon, UserIcon } from 'lucide-react-native';

export default function LearniScanTabLayout() {
  const colorScheme = useColorScheme();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#FF6B9D', // Candy Pink
        tabBarInactiveTintColor: colorScheme === 'dark' ? '#6B7280' : '#9CA3AF',
        tabBarStyle: {
          backgroundColor: colorScheme === 'dark' ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          borderTopColor: 'rgba(255, 107, 157, 0.2)',
          backdropFilter: 'blur(12px)',
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          elevation: 0,
          borderTopWidth: 1,
        },
        headerStyle: {
          backgroundColor: colorScheme === 'dark' ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(12px)',
        },
        headerTintColor: colorScheme === 'dark' ? '#F3F4F6' : '#111827',
        headerShadowVisible: false,
        headerTransparent: true,
      }}>
      
      <Tabs.Screen
        name="scan"
        options={{
          title: "Scan",
          tabBarIcon: ({ color, size }) => (
            <CameraIcon size={size} color={color} />
          ),
          headerTitle: "Document Scanner",
        }}
      />
      
      <Tabs.Screen
        name="library"
        options={{
          title: "Library",
          tabBarIcon: ({ color, size }) => (
            <FolderIcon size={size} color={color} />
          ),
          headerTitle: "Document Library",
        }}
      />
      
      <Tabs.Screen
        name="search"
        options={{
          title: "Search",
          tabBarIcon: ({ color, size }) => (
            <SearchIcon size={size} color={color} />
          ),
          headerTitle: "Search Documents",
        }}
      />
      
      <Tabs.Screen
        name="profile"
        options={{
          title: "Profile",
          tabBarIcon: ({ color, size }) => (
            <UserIcon size={size} color={color} />
          ),
          headerTitle: "Profile & Settings",
        }}
      />
    </Tabs>
  );
}