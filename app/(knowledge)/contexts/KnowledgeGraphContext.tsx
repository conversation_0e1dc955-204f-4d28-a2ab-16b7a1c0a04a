import React, { createContext, useContext, useReducer, useCallback, useEffect, useTransition, startTransition } from 'react';
import { Dimensions } from 'react-native';
import {
  KnowledgeGraph,
  KnowledgeNode,
  KnowledgeEdge,
  GraphViewport,
  GraphInteraction,
  GraphPerformance,
  LAYOUT_CONFIGS,
  CANDY_COLORS,
  getNodeColor,
  getNodeSize,
  isNodeVisible
} from '../types/graph';
import { useKnowledgeCards } from '@/lib/contexts/KnowledgeCardsContext';
import { applyFlowchartLayout, applyMindMapLayout, applyNetworkLayout } from '../utils/layoutAlgorithms';
import { aiGraphIntelligence, type GraphIntelligence, type GraphAnalysisProgress } from '@/lib/services/ai-graph-intelligence.service';

// Graph state interface
interface KnowledgeGraphState {
  graph: KnowledgeGraph | null;
  viewport: GraphViewport;
  interaction: GraphInteraction;
  performance: GraphPerformance;
  isLoading: boolean;
  error: string | null;
  // AI Intelligence state
  intelligence: GraphIntelligence | null;
  isAnalyzing: boolean;
  analysisProgress: number;
  analysisStage: 'analyzing' | 'clustering' | 'relationships' | 'optimizing' | null;
  analysisError: string | null;
}

// Action types
type GraphAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_GRAPH'; payload: KnowledgeGraph }
  | { type: 'UPDATE_VIEWPORT'; payload: Partial<GraphViewport> }
  | { type: 'UPDATE_INTERACTION'; payload: Partial<GraphInteraction> }
  | { type: 'UPDATE_PERFORMANCE'; payload: Partial<GraphPerformance> }
  | { type: 'SET_VIEW_MODE'; payload: KnowledgeGraph['viewMode'] }
  | { type: 'SELECT_NODE'; payload: string | null }
  | { type: 'TOGGLE_NODE_SELECTION'; payload: string }
  | { type: 'CLEAR_SELECTION' }
  | { type: 'SET_HOVERED_NODE'; payload: string | null }
  | { type: 'SET_CONTEXT_MENU'; payload: string | null }
  // AI Intelligence actions
  | { type: 'SET_ANALYZING'; payload: boolean }
  | { type: 'SET_ANALYSIS_PROGRESS'; payload: { progress: number; stage: GraphAnalysisProgress['stage'] } }
  | { type: 'SET_ANALYSIS_ERROR'; payload: string | null }
  | { type: 'SET_INTELLIGENCE'; payload: GraphIntelligence | null };

// Initial state
const initialState: KnowledgeGraphState = {
  graph: null,
  viewport: {
    scale: 1,
    offset: { x: 0, y: 0 },
    bounds: {
      minScale: 0.3,
      maxScale: 3,
      width: Dimensions.get('window').width,
      height: Dimensions.get('window').height,
    },
  },
  interaction: {
    selectedNodes: [],
    hoveredNode: null,
    draggedNode: null,
    isGestureActive: false,
    lastTapTime: 0,
    contextMenuNode: null,
  },
  performance: {
    renderMode: 'full',
    visibleNodes: new Set(),
    visibleEdges: new Set(),
    lastRenderTime: 0,
    frameRate: 60,
  },
  isLoading: false,
  error: null,
  // AI Intelligence state
  intelligence: null,
  isAnalyzing: false,
  analysisProgress: 0,
  analysisStage: null,
  analysisError: null,
};

// Reducer function
const graphReducer = (state: KnowledgeGraphState, action: GraphAction): KnowledgeGraphState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'SET_GRAPH':
      return { ...state, graph: action.payload, isLoading: false, error: null };
    
    case 'UPDATE_VIEWPORT':
      return { 
        ...state, 
        viewport: { ...state.viewport, ...action.payload } 
      };
    
    case 'UPDATE_INTERACTION':
      return { 
        ...state, 
        interaction: { ...state.interaction, ...action.payload } 
      };
    
    case 'UPDATE_PERFORMANCE':
      return { 
        ...state, 
        performance: { ...state.performance, ...action.payload } 
      };
    
    case 'SET_VIEW_MODE':
      if (!state.graph) return state;
      return {
        ...state,
        graph: { ...state.graph, viewMode: action.payload }
      };
    
    case 'SELECT_NODE':
      return {
        ...state,
        interaction: {
          ...state.interaction,
          selectedNodes: action.payload ? [action.payload] : [],
        }
      };
    
    case 'TOGGLE_NODE_SELECTION':
      const isSelected = state.interaction.selectedNodes.includes(action.payload);
      return {
        ...state,
        interaction: {
          ...state.interaction,
          selectedNodes: isSelected
            ? state.interaction.selectedNodes.filter(id => id !== action.payload)
            : [...state.interaction.selectedNodes, action.payload],
        }
      };
    
    case 'CLEAR_SELECTION':
      return {
        ...state,
        interaction: {
          ...state.interaction,
          selectedNodes: [],
          contextMenuNode: null,
        }
      };
    
    case 'SET_HOVERED_NODE':
      return {
        ...state,
        interaction: {
          ...state.interaction,
          hoveredNode: action.payload,
        }
      };
    
    case 'SET_CONTEXT_MENU':
      return {
        ...state,
        interaction: {
          ...state.interaction,
          contextMenuNode: action.payload,
        }
      };

    // AI Intelligence cases
    case 'SET_ANALYZING':
      return {
        ...state,
        isAnalyzing: action.payload,
        analysisError: action.payload ? null : state.analysisError,
      };

    case 'SET_ANALYSIS_PROGRESS':
      return {
        ...state,
        analysisProgress: action.payload.progress,
        analysisStage: action.payload.stage,
      };

    case 'SET_ANALYSIS_ERROR':
      return {
        ...state,
        analysisError: action.payload,
        isAnalyzing: false,
        analysisProgress: 0,
        analysisStage: null,
      };

    case 'SET_INTELLIGENCE':
      return {
        ...state,
        intelligence: action.payload,
        isAnalyzing: false,
        analysisProgress: 100,
        analysisStage: 'optimizing',
        analysisError: null,
      };

    default:
      return state;
  }
};

// Context interface
interface KnowledgeGraphContextType {
  state: KnowledgeGraphState;
  actions: {
    generateGraphFromCards: () => Promise<void>;
    updateViewport: (viewport: Partial<GraphViewport>) => void;
    updateInteraction: (interaction: Partial<GraphInteraction>) => void;
    setViewMode: (mode: KnowledgeGraph['viewMode']) => void;
    selectNode: (nodeId: string | null) => void;
    toggleNodeSelection: (nodeId: string) => void;
    clearSelection: () => void;
    setHoveredNode: (nodeId: string | null) => void;
    setContextMenu: (nodeId: string | null) => void;
    optimizePerformance: () => void;
    // AI Intelligence actions
    analyzeGraphWithAI: () => Promise<void>;
    enhanceGraphWithAI: () => void;
    clearAnalysis: () => void;
  };
}

// Create context
const KnowledgeGraphContext = createContext<KnowledgeGraphContextType | undefined>(undefined);

// Provider component
export const KnowledgeGraphProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(graphReducer, initialState);
  const { state: cardsState } = useKnowledgeCards();
  const cards = cardsState.cards;

  // React 18 concurrent features for performance optimization
  const [isPending, startTransition] = useTransition();

  // Generate graph from knowledge cards
  const generateGraphFromCards = useCallback(async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      if (!cards || cards.length === 0) {
        throw new Error('No knowledge cards available');
      }

      // Transform cards into nodes
      const nodes: KnowledgeNode[] = cards.map((cardData, index) => {
        const angle = (index / cards.length) * 2 * Math.PI;
        const radius = 150;
        const centerX = 200;
        const centerY = 200;

        return {
          id: cardData.card.$id,
          label: cardData.card.title || `Card ${index + 1}`,
          type: 'concept',
          position: {
            x: centerX + Math.cos(angle) * radius,
            y: centerY + Math.sin(angle) * radius,
          },
          connections: [], // Will be populated based on relationships
          metadata: {
            level: 1,
            cluster: cardData.card.category || 'general',
            importance: (cardData.card.confidence || 0.5),
            color: getNodeColor('concept', cardData.card.category),
            size: getNodeSize(cardData.card.confidence || 0.5),
            confidence: cardData.card.confidence || 0.5,
            category: cardData.card.category,
            description: cardData.content?.content || cardData.content?.summary,
            tags: cardData.tagNames || [],
          },
        };
      });

      // Generate edges based on shared categories and tags
      const edges: KnowledgeEdge[] = [];
      for (let i = 0; i < nodes.length; i++) {
        for (let j = i + 1; j < nodes.length; j++) {
          const nodeA = nodes[i];
          const nodeB = nodes[j];
          
          // Calculate relationship strength
          let strength = 0;
          
          // Same category
          if (nodeA.metadata.category === nodeB.metadata.category) {
            strength += 0.5;
          }
          
          // Shared tags
          const sharedTags = (nodeA.metadata.tags || []).filter(tag => 
            (nodeB.metadata.tags || []).includes(tag)
          );
          strength += sharedTags.length * 0.2;
          
          // Create edge if strength is significant
          if (strength > 0.3) {
            const edgeId = `${nodeA.id}-${nodeB.id}`;
            edges.push({
              id: edgeId,
              source: nodeA.id,
              target: nodeB.id,
              type: 'related',
              strength: Math.min(strength, 1),
              metadata: {
                color: CANDY_COLORS.purple,
                width: Math.max(1, strength * 3),
                style: 'solid',
                animated: strength > 0.7,
              },
            });
            
            // Update node connections
            nodeA.connections.push(nodeB.id);
            nodeB.connections.push(nodeA.id);
          }
        }
      }

      const graph: KnowledgeGraph = {
        nodes,
        edges,
        viewMode: 'network',
        bounds: { width: 400, height: 400 },
        metadata: {
          title: 'Knowledge Graph',
          description: 'Generated from knowledge cards',
          created: new Date(),
          updated: new Date(),
          version: '1.0.0',
        },
      };

      dispatch({ type: 'SET_GRAPH', payload: graph });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Unknown error' });
    }
  }, [cards]);

  // Action creators
  const actions = {
    generateGraphFromCards,
    updateViewport: (viewport: Partial<GraphViewport>) => 
      dispatch({ type: 'UPDATE_VIEWPORT', payload: viewport }),
    updateInteraction: (interaction: Partial<GraphInteraction>) => 
      dispatch({ type: 'UPDATE_INTERACTION', payload: interaction }),
    setViewMode: (mode: KnowledgeGraph['viewMode']) => {
      if (!state.graph || state.isLoading) return;

      // Set loading state to prevent rapid switching
      dispatch({ type: 'SET_LOADING', payload: true });

      // Use setTimeout to allow UI to update with loading state
      setTimeout(() => {
        const screenDimensions = Dimensions.get('window');
        const layoutOptions = {
          width: screenDimensions.width - 32, // Account for padding
          height: screenDimensions.height - 200, // Account for header space
          padding: 50,
        };

        let updatedGraph = { ...state.graph! };

        switch (mode) {
          case 'flowchart':
            updatedGraph = applyFlowchartLayout(updatedGraph, layoutOptions);
            break;
          case 'mindmap':
            updatedGraph = applyMindMapLayout(updatedGraph, layoutOptions);
            break;
          case 'network':
            updatedGraph = applyNetworkLayout(updatedGraph, layoutOptions);
            break;
        }

        // Use SET_GRAPH to update the entire graph with new positions and viewMode
        dispatch({ type: 'SET_GRAPH', payload: updatedGraph });
        dispatch({ type: 'SET_LOADING', payload: false });
      }, 10); // Small delay to allow UI update
    },
    selectNode: (nodeId: string | null) => 
      dispatch({ type: 'SELECT_NODE', payload: nodeId }),
    toggleNodeSelection: (nodeId: string) => 
      dispatch({ type: 'TOGGLE_NODE_SELECTION', payload: nodeId }),
    clearSelection: () => 
      dispatch({ type: 'CLEAR_SELECTION' }),
    setHoveredNode: (nodeId: string | null) => 
      dispatch({ type: 'SET_HOVERED_NODE', payload: nodeId }),
    setContextMenu: (nodeId: string | null) => 
      dispatch({ type: 'SET_CONTEXT_MENU', payload: nodeId }),
    optimizePerformance: () => {
      if (!state.graph) return;
      
      const visibleNodes = new Set<string>();
      const visibleEdges = new Set<string>();
      
      // Calculate visible nodes based on viewport
      state.graph.nodes.forEach(node => {
        if (isNodeVisible(node, state.viewport, state.viewport.bounds)) {
          visibleNodes.add(node.id);
        }
      });
      
      // Calculate visible edges
      state.graph.edges.forEach(edge => {
        if (visibleNodes.has(edge.source) && visibleNodes.has(edge.target)) {
          visibleEdges.add(edge.id);
        }
      });
      
      // Determine render mode based on scale and node count
      let renderMode: GraphPerformance['renderMode'] = 'full';
      if (state.viewport.scale < 0.5 || visibleNodes.size > 50) {
        renderMode = 'simplified';
      }
      if (state.viewport.scale < 0.3 || visibleNodes.size > 100) {
        renderMode = 'minimal';
      }
      
      dispatch({ 
        type: 'UPDATE_PERFORMANCE', 
        payload: { 
          visibleNodes, 
          visibleEdges, 
          renderMode,
          lastRenderTime: Date.now(),
        } 
      });
    },

    // AI Intelligence actions
    analyzeGraphWithAI: async () => {
      if (!cards || cards.length === 0) {
        dispatch({ type: 'SET_ANALYSIS_ERROR', payload: 'No knowledge cards available for analysis' });
        return;
      }

      if (state.isAnalyzing) {
        return; // Already analyzing
      }

      try {
        dispatch({ type: 'SET_ANALYZING', payload: true });

        // Prepare cards data for AI analysis
        const analysisInput = {
          cards: cards.map(card => ({
            card: {
              $id: card.card.$id,
              title: card.card.title,
              category: card.card.category,
              difficulty: card.card.difficulty,
            },
            content: card.content ? {
              content: card.content.content,
              summary: card.content.summary,
            } : undefined,
            tagNames: card.tagNames,
          })),
          options: {
            enableSemanticAnalysis: true,
            enableClustering: true,
            enableHierarchy: true,
            maxRelationships: 25,
            minSimilarityThreshold: 0.3,
          },
        };

        // Analyze with AI
        const intelligence = await aiGraphIntelligence.analyzeKnowledgeGraph(
          analysisInput,
          (progress: GraphAnalysisProgress) => {
            // Update progress (urgent updates)
            dispatch({
              type: 'SET_ANALYSIS_PROGRESS',
              payload: {
                progress: progress.progress,
                stage: progress.stage
              }
            });
          }
        );

        // Update intelligence state with startTransition (non-urgent)
        startTransition(() => {
          dispatch({ type: 'SET_INTELLIGENCE', payload: intelligence });
        });

      } catch (error) {
        console.error('AI Graph Analysis Error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to analyze graph with AI';
        dispatch({ type: 'SET_ANALYSIS_ERROR', payload: errorMessage });
      }
    },

    enhanceGraphWithAI: () => {
      if (!state.graph || !state.intelligence) {
        return;
      }

      try {
        // Apply AI intelligence to enhance the graph
        const enhancedGraph = aiGraphIntelligence.enhanceKnowledgeGraph(
          state.graph,
          state.intelligence
        );

        // Update graph with AI enhancements using startTransition
        startTransition(() => {
          dispatch({ type: 'SET_GRAPH', payload: enhancedGraph });
        });

      } catch (error) {
        console.error('Graph Enhancement Error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to enhance graph with AI';
        dispatch({ type: 'SET_ANALYSIS_ERROR', payload: errorMessage });
      }
    },

    clearAnalysis: () => {
      startTransition(() => {
        dispatch({ type: 'SET_INTELLIGENCE', payload: null });
        dispatch({ type: 'SET_ANALYSIS_ERROR', payload: null });
        dispatch({ type: 'SET_ANALYZING', payload: false });
      });
    },
  };

  // Auto-generate graph when cards change
  useEffect(() => {
    if (cards && cards.length > 0 && !state.graph) {
      generateGraphFromCards();
    }
  }, [cards, generateGraphFromCards, state.graph]);

  return (
    <KnowledgeGraphContext.Provider value={{ state, actions }}>
      {children}
    </KnowledgeGraphContext.Provider>
  );
};

// Hook to use the context
export const useKnowledgeGraph = () => {
  const context = useContext(KnowledgeGraphContext);
  if (context === undefined) {
    throw new Error('useKnowledgeGraph must be used within a KnowledgeGraphProvider');
  }
  return context;
};
