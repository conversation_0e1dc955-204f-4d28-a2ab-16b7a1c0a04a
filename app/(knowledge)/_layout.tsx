import { type FC } from 'react';
import { Stack } from 'expo-router';

export const unstable_settings = {
  initialRouteName: 'cards',
};

export const KnowledgeLayout: FC = () => {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        presentation: 'card',
        animation: 'fade',
        headerStyle: {
          backgroundColor: 'transparent',
        },
      }}
    >
      <Stack.Screen 
        name="cards" 
        options={{ title: 'Knowledge Cards' }}
      />
      <Stack.Screen 
        name="graph" 
        options={{ title: 'Knowledge Graph' }}
      />
      <Stack.Screen 
        name="share" 
        options={{ title: 'Knowledge Share' }}
      />
    </Stack>
  );
}

export default KnowledgeLayout;
