import React, { useCallback, useMemo, useRef, useEffect } from 'react';
import { Dimensions, Pressable } from 'react-native';
import { GestureDetector, Gesture } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
  interpolate,
  Extrapolate,
  withTiming,
  withSequence,
  Easing
} from 'react-native-reanimated';
import Svg, { Circle, Line, Text as SvgText, G, Defs, LinearGradient, Stop } from 'react-native-svg';
import * as Haptics from 'expo-haptics';
import { useKnowledgeGraph } from '../contexts/KnowledgeGraphContext';
import { 
  KnowledgeNode, 
  KnowledgeEdge, 
  isPointInNode, 
  GESTURE_THRESHOLDS,
  NODE_SIZES 
} from '../types/graph';

interface InteractiveGraphSVGProps {
  width: number;
  height: number;
  onNodePress?: (node: KnowledgeNode) => void;
  onNodeLongPress?: (node: KnowledgeNode) => void;
  onNodeDoublePress?: (node: KnowledgeNode) => void;
}

export const InteractiveGraphSVG: React.FC<InteractiveGraphSVGProps> = ({
  width,
  height,
  onNodePress,
  onNodeLongPress,
  onNodeDoublePress,
}) => {
  const { state, actions } = useKnowledgeGraph();
  const { graph, viewport, interaction, performance } = state;

  // Shared values for gestures
  const scale = useSharedValue(viewport.scale);
  const offset = useSharedValue(viewport.offset);
  const savedScale = useSharedValue(viewport.scale);
  const savedOffset = useSharedValue(viewport.offset);
  const lastTapTime = useSharedValue(0);

  // Visual feedback shared values
  const touchFeedbackScale = useSharedValue(1);
  const touchFeedbackOpacity = useSharedValue(0);
  const touchPosition = useSharedValue({ x: 0, y: 0 });
  const gestureHintOpacity = useSharedValue(0);
  const nodeHighlightScale = useSharedValue(1);

  // Update viewport in context
  const updateViewport = useCallback((newScale: number, newOffset: { x: number; y: number }) => {
    actions.updateViewport({
      scale: newScale,
      offset: newOffset,
    });
    actions.optimizePerformance();
  }, [actions]);

  // Update interaction state (wrapped for runOnJS)
  const updateInteractionState = useCallback((isActive: boolean) => {
    actions.updateInteraction({ isGestureActive: isActive });
  }, [actions]);

  // Visual feedback functions
  const showTouchFeedback = useCallback((x: number, y: number) => {
    touchPosition.value = { x, y };
    touchFeedbackOpacity.value = withSequence(
      withTiming(1, { duration: 100 }),
      withTiming(0, { duration: 300 })
    );
    touchFeedbackScale.value = withSequence(
      withTiming(1.2, { duration: 100 }),
      withTiming(1, { duration: 300 })
    );
  }, []);

  const showGestureHint = useCallback((type: 'pinch' | 'drag' | 'tap') => {
    gestureHintOpacity.value = withSequence(
      withTiming(1, { duration: 200 }),
      withTiming(1, { duration: 1000 }),
      withTiming(0, { duration: 300 })
    );
  }, []);

  const highlightNode = useCallback((nodeId: string) => {
    nodeHighlightScale.value = withSequence(
      withTiming(1.3, { duration: 150, easing: Easing.out(Easing.quad) }),
      withTiming(1, { duration: 200, easing: Easing.inOut(Easing.quad) })
    );
  }, []);

  // Haptic feedback functions
  const triggerHapticFeedback = useCallback(async (type: 'light' | 'medium' | 'heavy' | 'success') => {
    try {
      switch (type) {
        case 'light':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
        case 'medium':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
        case 'heavy':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;
        case 'success':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
      }
    } catch (error) {
      console.log('Haptic feedback not available:', error);
    }
  }, []);

  // Handle node interactions with enhanced feedback
  const handleNodeInteraction = useCallback((node: KnowledgeNode, interactionType: 'tap' | 'longPress' | 'doublePress') => {
    // Visual feedback
    runOnJS(highlightNode)(node.id);

    switch (interactionType) {
      case 'tap':
        // Light haptic feedback for tap
        runOnJS(triggerHapticFeedback)('light');
        actions.toggleNodeSelection(node.id);
        onNodePress?.(node);
        break;
      case 'longPress':
        // Medium haptic feedback for long press
        runOnJS(triggerHapticFeedback)('medium');
        actions.setContextMenu(node.id);
        onNodeLongPress?.(node);
        break;
      case 'doublePress':
        // Success haptic feedback for double press (focus action)
        runOnJS(triggerHapticFeedback)('success');

        // Focus on node - animate to center and optimal zoom
        const centerX = width / 2 - node.position.x;
        const centerY = height / 2 - node.position.y;
        const optimalScale = 1.5;

        scale.value = withSpring(optimalScale, { damping: 15, stiffness: 100 });
        offset.value = withSpring({ x: centerX, y: centerY }, { damping: 15, stiffness: 100 });
        savedScale.value = optimalScale;
        savedOffset.value = { x: centerX, y: centerY };

        runOnJS(updateViewport)(optimalScale, { x: centerX, y: centerY });
        onNodeDoublePress?.(node);
        break;
    }
  }, [actions, onNodePress, onNodeLongPress, onNodeDoublePress, width, height, scale, offset, savedScale, savedOffset, updateViewport, highlightNode, triggerHapticFeedback]);

  // Pan gesture with visual feedback
  const panGesture = Gesture.Pan()
    .averageTouches(true)
    .onStart((event) => {
      runOnJS(updateInteractionState)(true);
      runOnJS(showGestureHint)('drag');
      runOnJS(triggerHapticFeedback)('light');
    })
    .onUpdate((event) => {
      offset.value = {
        x: event.translationX + savedOffset.value.x,
        y: event.translationY + savedOffset.value.y,
      };
    })
    .onEnd(() => {
      savedOffset.value = offset.value;
      runOnJS(updateViewport)(scale.value, offset.value);
      runOnJS(updateInteractionState)(false);
    });

  // Pinch gesture with visual feedback
  const pinchGesture = Gesture.Pinch()
    .onStart(() => {
      runOnJS(updateInteractionState)(true);
      runOnJS(showGestureHint)('pinch');
      runOnJS(triggerHapticFeedback)('light');
    })
    .onUpdate((event) => {
      const newScale = Math.max(
        viewport.bounds.minScale,
        Math.min(viewport.bounds.maxScale, savedScale.value * event.scale)
      );
      scale.value = newScale;
    })
    .onEnd(() => {
      savedScale.value = scale.value;
      runOnJS(updateViewport)(scale.value, offset.value);
      runOnJS(updateInteractionState)(false);
      // Medium haptic feedback on pinch end
      runOnJS(triggerHapticFeedback)('medium');
    });

  // Tap gesture for node selection with visual feedback
  const tapGesture = Gesture.Tap()
    .maxDuration(GESTURE_THRESHOLDS.tapTimeout)
    .onStart((event) => {
      // Show touch feedback at tap location
      runOnJS(showTouchFeedback)(event.x, event.y);

      const currentTime = Date.now();
      const timeSinceLastTap = currentTime - lastTapTime.value;

      if (timeSinceLastTap < GESTURE_THRESHOLDS.doubleTapTimeout) {
        // Double tap detected
        const touchPoint = {
          x: (event.x - offset.value.x) / scale.value,
          y: (event.y - offset.value.y) / scale.value,
        };

        if (graph) {
          const tappedNode = graph.nodes.find(node =>
            isPointInNode(touchPoint, node, { scale: 1, offset: { x: 0, y: 0 }, bounds: viewport.bounds })
          );

          if (tappedNode) {
            runOnJS(handleNodeInteraction)(tappedNode, 'doublePress');
          }
        }
      } else {
        // Single tap
        const touchPoint = {
          x: (event.x - offset.value.x) / scale.value,
          y: (event.y - offset.value.y) / scale.value,
        };

        if (graph) {
          const tappedNode = graph.nodes.find(node =>
            isPointInNode(touchPoint, node, { scale: 1, offset: { x: 0, y: 0 }, bounds: viewport.bounds })
          );

          if (tappedNode) {
            runOnJS(handleNodeInteraction)(tappedNode, 'tap');
          } else {
            runOnJS(actions.clearSelection)();
            // Light haptic feedback for empty tap
            runOnJS(triggerHapticFeedback)('light');
          }
        }
      }

      lastTapTime.value = currentTime;
    });

  // Long press gesture
  const longPressGesture = Gesture.LongPress()
    .minDuration(GESTURE_THRESHOLDS.longPressTimeout)
    .onStart((event) => {
      const touchPoint = {
        x: (event.x - offset.value.x) / scale.value,
        y: (event.y - offset.value.y) / scale.value,
      };
      
      if (graph) {
        const tappedNode = graph.nodes.find(node => 
          isPointInNode(touchPoint, node, { scale: 1, offset: { x: 0, y: 0 }, bounds: viewport.bounds })
        );
        
        if (tappedNode) {
          runOnJS(handleNodeInteraction)(tappedNode, 'longPress');
        }
      }
    });

  // Compose gestures
  const composedGesture = Gesture.Simultaneous(
    panGesture,
    pinchGesture,
    Gesture.Race(tapGesture, longPressGesture)
  );

  // Animated style for the container
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: offset.value.x },
      { translateY: offset.value.y },
      { scale: scale.value },
    ],
  }));

  // Render nodes based on performance mode
  const renderNode = useCallback((node: KnowledgeNode) => {
    const isSelected = interaction.selectedNodes.includes(node.id);
    const isHovered = interaction.hoveredNode === node.id;
    const isVisible = performance.visibleNodes.has(node.id);
    
    if (!isVisible && performance.renderMode !== 'full') {
      return null;
    }

    const nodeRadius = performance.renderMode === 'minimal' 
      ? NODE_SIZES.small / 2 
      : node.metadata.size / 2;
    
    const strokeWidth = isSelected ? 4 : isHovered ? 3 : 2;
    const strokeColor = isSelected ? '#FFFFFF' : isHovered ? '#F3F4F6' : '#E5E7EB';

    return (
      <G key={node.id}>
        {/* Node shadow for depth */}
        <Circle
          cx={node.position.x + 2}
          cy={node.position.y + 2}
          r={nodeRadius}
          fill="rgba(0,0,0,0.1)"
        />
        
        {/* Main node */}
        <Circle
          cx={node.position.x}
          cy={node.position.y}
          r={nodeRadius}
          fill={node.metadata.color}
          stroke={strokeColor}
          strokeWidth={strokeWidth}
        />
        
        {/* Node label (only in detailed modes) */}
        {performance.renderMode === 'full' && viewport.scale > 0.8 && (
          <SvgText
            x={node.position.x}
            y={node.position.y + nodeRadius + 20}
            textAnchor="middle"
            fontSize={Math.max(10, 12 * viewport.scale)}
            fill="#374151"
            fontWeight="600"
          >
            {node.label.length > 15 ? `${node.label.substring(0, 15)}...` : node.label}
          </SvgText>
        )}
        
        {/* Selection indicator */}
        {isSelected && (
          <Circle
            cx={node.position.x}
            cy={node.position.y}
            r={nodeRadius + 8}
            fill="none"
            stroke="#EC4899"
            strokeWidth={3}
            strokeDasharray="5,5"
          />
        )}
      </G>
    );
  }, [interaction.selectedNodes, interaction.hoveredNode, performance, scale]);

  // Render edges
  const renderEdge = useCallback((edge: KnowledgeEdge) => {
    if (!graph) return null;
    
    const sourceNode = graph.nodes.find(n => n.id === edge.source);
    const targetNode = graph.nodes.find(n => n.id === edge.target);
    
    if (!sourceNode || !targetNode) return null;
    
    const isVisible = performance.visibleEdges.has(edge.id);
    if (!isVisible && performance.renderMode !== 'full') {
      return null;
    }

    const opacity = performance.renderMode === 'minimal' ? 0.3 : 0.6;
    
    return (
      <Line
        key={edge.id}
        x1={sourceNode.position.x}
        y1={sourceNode.position.y}
        x2={targetNode.position.x}
        y2={targetNode.position.y}
        stroke={edge.metadata.color}
        strokeWidth={edge.metadata.width}
        strokeDasharray={edge.metadata.style === 'dashed' ? '5,5' : undefined}
        opacity={opacity}
      />
    );
  }, [graph, performance]);

  if (!graph) {
    return null;
  }

  return (
    <GestureDetector gesture={composedGesture}>
      <Animated.View style={[{ width, height }, animatedStyle]}>
        <Svg width={width} height={height} viewBox={`0 0 ${width} ${height}`}>
          <Defs>
            {/* Gradient definitions for enhanced visuals - Emerald-Cyan theme */}
            <LinearGradient id="nodeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#10B981" stopOpacity="0.8" />
              <Stop offset="100%" stopColor="#06B6D4" stopOpacity="0.8" />
            </LinearGradient>
            <LinearGradient id="nodeGradientSelected" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#059669" stopOpacity="0.9" />
              <Stop offset="100%" stopColor="#0891B2" stopOpacity="0.9" />
            </LinearGradient>
          </Defs>
          
          {/* Render edges first (behind nodes) */}
          <G>
            {graph.edges.map(renderEdge)}
          </G>
          
          {/* Render nodes */}
          <G>
            {graph.nodes.map(renderNode)}
          </G>
        </Svg>
      </Animated.View>
    </GestureDetector>
  );
};

export default InteractiveGraphSVG;
