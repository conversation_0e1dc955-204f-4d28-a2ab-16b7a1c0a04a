import React, { useState, useRef, useEffect } from 'react';
import { View, Dimensions, StyleSheet } from 'react-native';
import Svg, { G, Circle, Line, Text as SvgText } from 'react-native-svg';
import { PanGestureHandler, PinchGestureHandler, State, GestureHandlerRootView } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedGestureHandler,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import * as d3 from 'd3';

// Types
export type Node = {
  id: string;
  label: string;
  group?: string;
  x?: number;
  y?: number;
  color?: string;
  size?: number;
};

export type Edge = {
  source: string;
  target: string;
  label?: string;
};

export type GraphData = {
  nodes: Node[];
  edges: Edge[];
};

export type ViewMode = 'flowchart' | 'mindmap' | 'network';type KnowledgeGraphSVGProps = {
  data: GraphData;
  viewMode: ViewMode;
  width?: number;
  height?: number;
  onNodeSelect?: (node: Node) => void;
};

const AnimatedSvg = Animated.createAnimatedComponent(Svg);
const AnimatedG = Animated.createAnimatedComponent(G);

export const KnowledgeGraphSVG: React.FC<KnowledgeGraphSVGProps> = ({
  data,
  viewMode,
  width = Dimensions.get('window').width,
  height = 400,
  onNodeSelect,
}) => {
  // References
  const svgRef = useRef<Svg>(null);
  const pinchRef = useRef<PinchGestureHandler>(null);
  const panRef = useRef<PanGestureHandler>(null);

  // State
  const [graphData, setGraphData] = useState<GraphData>({ nodes: [], edges: [] });
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);

  // Animated values
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const originX = useSharedValue(0);
  const originY = useSharedValue(0);  // Process data based on view mode
  useEffect(() => {
    // Clone data to avoid mutating props
    const processedData = JSON.parse(JSON.stringify(data));
    
    // Apply layout algorithm based on view mode
    switch (viewMode) {
      case 'flowchart':
        applyHierarchicalLayout(processedData);
        break;
      case 'mindmap':
        applyRadialLayout(processedData);
        break;
      case 'network':
        applyForceDirectedLayout(processedData);
        break;
      default:
        applyForceDirectedLayout(processedData);
    }
    
    setGraphData(processedData);
  }, [data, viewMode]);

  // Layout algorithms
  const applyHierarchicalLayout = (data: GraphData) => {
    // Create a map of nodes by id for quick lookup
    const nodesById = new Map(data.nodes.map(node => [node.id, node]));
    
    // Create a hierarchy structure
    const hierarchy: Record<string, string[]> = {};
    
    // Find root nodes (nodes that are not targets in any edge)
    const targetNodeIds = new Set(data.edges.map(edge => edge.target));
    const rootNodeIds = data.nodes
      .map(node => node.id)
      .filter(id => !targetNodeIds.has(id));    
    // Simple positioning for now
    data.nodes.forEach((node, index) => {
      node.x = 50 + (index % 3) * 100;
      node.y = 50 + Math.floor(index / 3) * 80;
    });
  };

  const applyRadialLayout = (data: GraphData) => {
    // Simple radial layout
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 4;
    
    data.nodes.forEach((node, index) => {
      const angle = (index / data.nodes.length) * 2 * Math.PI;
      node.x = centerX + radius * Math.cos(angle);
      node.y = centerY + radius * Math.sin(angle);
    });
  };

  const applyForceDirectedLayout = (data: GraphData) => {
    // Simple grid layout for now
    const cols = Math.ceil(Math.sqrt(data.nodes.length));
    const cellWidth = width / cols;
    const cellHeight = height / Math.ceil(data.nodes.length / cols);
    
    data.nodes.forEach((node, index) => {
      const col = index % cols;
      const row = Math.floor(index / cols);
      node.x = col * cellWidth + cellWidth / 2;
      node.y = row * cellHeight + cellHeight / 2;
    });
  };  // Gesture handlers
  const panGestureHandler = useAnimatedGestureHandler({
    onStart: (_, ctx: any) => {
      ctx.startX = translateX.value;
      ctx.startY = translateY.value;
    },
    onActive: (event, ctx) => {
      translateX.value = ctx.startX + event.translationX;
      translateY.value = ctx.startY + event.translationY;
    },
    onEnd: () => {
      translateX.value = withSpring(translateX.value);
      translateY.value = withSpring(translateY.value);
    },
  });

  const pinchGestureHandler = useAnimatedGestureHandler({
    onStart: (event, ctx: any) => {
      ctx.startScale = scale.value;
      originX.value = event.focalX;
      originY.value = event.focalY;
    },
    onActive: (event, ctx) => {
      const newScale = Math.max(0.5, Math.min(4, ctx.startScale * event.scale));
      scale.value = newScale;
    },
    onEnd: () => {
      scale.value = withSpring(scale.value);
    },
  });

  // Handle node selection
  const handleNodePress = (node: Node) => {
    setSelectedNode(node);
    if (onNodeSelect) {
      onNodeSelect(node);
    }
  };  // Animated styles
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
      ],
    };
  });

  // Node colors based on group - Updated for emerald-cyan theme
  const getNodeColor = (node: Node) => {
    if (node.color) return node.color;

    // Updated color scheme for emerald-cyan theme
    const colorMap: Record<string, string> = {
      root: '#10B981', // emerald (primary)
      primary: '#06B6D4', // cyan (secondary)
      secondary: '#14B8A6', // teal (tertiary)
      tertiary: '#8B5CF6', // violet (accent)
      default: '#10B981', // emerald
    };

    return colorMap[node.group || 'default'] || colorMap.default;
  };

  return (
    <GestureHandlerRootView style={styles.container}>
      <PinchGestureHandler
        ref={pinchRef}
        onGestureEvent={pinchGestureHandler}
        simultaneousHandlers={panRef}
      >
        <Animated.View style={styles.container}>
          <PanGestureHandler
            ref={panRef}
            onGestureEvent={panGestureHandler}
            simultaneousHandlers={pinchRef}
          >
            <Animated.View style={styles.container}>              <AnimatedSvg
                ref={svgRef}
                width={width}
                height={height}
                style={[styles.svg, { width, height }]}
              >
                <AnimatedG style={animatedStyle}>
                  {/* Render edges */}
                  {graphData.edges.map((edge, index) => {
                    const source = graphData.nodes.find(n => n.id === edge.source);
                    const target = graphData.nodes.find(n => n.id === edge.target);
                    
                    if (!source || !target || !source.x || !source.y || !target.x || !target.y) {
                      return null;
                    }
                    
                    return (
                      <Line
                        key={`edge-${index}`}
                        x1={source.x}
                        y1={source.y}
                        x2={target.x}
                        y2={target.y}
                        stroke="rgba(255, 255, 255, 0.6)"
                        strokeWidth={1.5}
                      />
                    );
                  })}
                  
                  {/* Render nodes */}
                  {graphData.nodes.map((node, index) => {
                    if (!node.x || !node.y) return null;
                    
                    const nodeSize = node.size || 20;
                    const isSelected = selectedNode?.id === node.id;
                    const nodeColor = getNodeColor(node);
                    
                    return (
                      <G
                        key={`node-${index}`}
                        onPress={() => runOnJS(handleNodePress)(node)}
                      >                        <Circle
                          cx={node.x}
                          cy={node.y}
                          r={nodeSize}
                          fill={nodeColor}
                          stroke={isSelected ? 'white' : 'rgba(255, 255, 255, 0.3)'}
                          strokeWidth={isSelected ? 3 : 1}
                        />
                        <SvgText
                          x={node.x}
                          y={node.y + nodeSize + 12}
                          fontSize="10"
                          fontWeight="bold"
                          fill="white"
                          textAnchor="middle"
                        >
                          {node.label}
                        </SvgText>
                      </G>
                    );
                  })}
                </AnimatedG>
              </AnimatedSvg>
            </Animated.View>
          </PanGestureHandler>
        </Animated.View>
      </PinchGestureHandler>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
  },
  svg: {
    backgroundColor: 'transparent',
  },
});

export default KnowledgeGraphSVG;