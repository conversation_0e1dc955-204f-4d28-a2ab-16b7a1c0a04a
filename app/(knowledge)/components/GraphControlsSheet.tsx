import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Button, ButtonText } from '@/components/ui/button';
import { Pressable } from '@/components/ui/pressable';
import { Box } from '@/components/ui/box';
import { RotateCcw, Download, Share2, Edit } from 'lucide-react-native';

export type ViewMode = 'flowchart' | 'mindmap' | 'network';

type GraphControlsSheetProps = {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  onReset: () => void;
  onExport: () => void;
  onShare: () => void;
  onEdit: () => void;
  graphStats: {
    concepts: number;
    connections: number;
    clusters: number;
    levels: number;
  };
};

const GraphControlsSheet: React.FC<GraphControlsSheetProps> = ({
  viewMode,
  onViewModeChange,
  onReset,
  onExport,
  onShare,
  onEdit,
  graphStats,
}) => {
  return (
    <View style={styles.container}>
      {/* View Mode Toggle */}
      <HStack className="items-center justify-between px-4 mb-4">
        <HStack className="bg-white/10 border border-white/20 backdrop-blur-md p-1 rounded-xl">
          <Button
            action={viewMode === 'flowchart' ? 'candyPink' : undefined}
            variant={viewMode === 'flowchart' ? 'solid' : 'outline'}
            className="px-3 py-2 rounded-lg"
            onPress={() => onViewModeChange('flowchart')}
          >
            <ButtonText className={`text-sm font-semibold ${viewMode === 'flowchart' ? 'text-white' : 'text-white/70'}`}>
              Flowchart
            </ButtonText>
          </Button>          <Button
            action={viewMode === 'mindmap' ? 'candyPink' : undefined}
            variant={viewMode === 'mindmap' ? 'solid' : 'outline'}
            className="px-3 py-2 rounded-lg"
            onPress={() => onViewModeChange('mindmap')}
          >
            <ButtonText className={`text-sm font-semibold ${viewMode === 'mindmap' ? 'text-white' : 'text-white/70'}`}>
              Mind Map
            </ButtonText>
          </Button>
          <Button
            action={viewMode === 'network' ? 'candyPink' : undefined}
            variant={viewMode === 'network' ? 'solid' : 'outline'}
            className="px-3 py-2 rounded-lg"
            onPress={() => onViewModeChange('network')}
          >
            <ButtonText className={`text-sm font-semibold ${viewMode === 'network' ? 'text-white' : 'text-white/70'}`}>
              Network
            </ButtonText>
          </Button>
        </HStack>
        
        <HStack className="space-x-2">
          <Pressable 
            className="w-10 h-10 bg-white/10 rounded-full items-center justify-center"
            onPress={onReset}
          >
            <RotateCcw size={18} color="white" />
          </Pressable>
        </HStack>
      </HStack>
      {/* Graph Stats */}
      <Box className="mx-4 bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-4 mb-6">
        <HStack className="justify-around">
          <VStack className="items-center">
            <Text className="text-lg font-bold text-candyPink mb-1">{graphStats.concepts}</Text>
            <Text className="text-white/70 text-xs">Concepts</Text>
          </VStack>
          <VStack className="items-center">
            <Text className="text-lg font-bold text-candyPurple mb-1">{graphStats.connections}</Text>
            <Text className="text-white/70 text-xs">Connections</Text>
          </VStack>
          <VStack className="items-center">
            <Text className="text-lg font-bold text-candyBlue mb-1">{graphStats.clusters}</Text>
            <Text className="text-white/70 text-xs">Clusters</Text>
          </VStack>
          <VStack className="items-center">
            <Text className="text-lg font-bold text-emerald-400 mb-1">{graphStats.levels}</Text>
            <Text className="text-white/70 text-xs">Levels</Text>
          </VStack>
        </HStack>
      </Box>

      {/* Actions */}
      <HStack className="mx-4 justify-around mb-6">
        <VStack className="items-center">
          <Pressable 
            className="w-12 h-12 bg-white/10 rounded-full items-center justify-center mb-2"
            onPress={onExport}
          >
            <Download size={20} color="white" />
          </Pressable>
          <Text className="text-white/70 text-xs">Export</Text>
        </VStack>        <VStack className="items-center">
          <Pressable 
            className="w-12 h-12 bg-white/10 rounded-full items-center justify-center mb-2"
            onPress={onShare}
          >
            <Share2 size={20} color="white" />
          </Pressable>
          <Text className="text-white/70 text-xs">Share</Text>
        </VStack>
        <VStack className="items-center">
          <Pressable 
            className="w-12 h-12 bg-white/10 rounded-full items-center justify-center mb-2"
            onPress={onEdit}
          >
            <Edit size={20} color="white" />
          </Pressable>
          <Text className="text-white/70 text-xs">Edit</Text>
        </VStack>
        <VStack className="items-center">
          <Pressable 
            className="w-12 h-12 bg-white/10 rounded-full items-center justify-center mb-2"
            onPress={onReset}
          >
            <RotateCcw size={20} color="white" />
          </Pressable>
          <Text className="text-white/70 text-xs">Reset</Text>
        </VStack>
      </HStack>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 12,
    paddingBottom: 24,
  },
});

export default GraphControlsSheet;