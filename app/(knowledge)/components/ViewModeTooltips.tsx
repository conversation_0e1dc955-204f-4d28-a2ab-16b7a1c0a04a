/**
 * View Mode Tooltips Component
 * 
 * Provides interactive tooltips and explanations for different graph view modes.
 * Includes detailed descriptions and visual examples for each mode.
 */

import React, { useState } from 'react';
import { Modal, Pressable, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  XIcon,
  TreePineIcon,
  RadarIcon,
  NetworkIcon,
  InfoIcon,
  ArrowDownIcon,
  CircleIcon,
  GitBranchIcon
} from 'lucide-react-native';

// UI Components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';

const { width: screenWidth } = Dimensions.get('window');

interface ViewModeTooltipProps {
  /** Whether the tooltip modal is visible */
  visible: boolean;
  /** Function to close the tooltip */
  onClose: () => void;
  /** The view mode to explain */
  mode: 'flowchart' | 'mindmap' | 'network';
}

interface ViewModeInfo {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  gradientColors: [string, string];
  features: string[];
  bestFor: string[];
  visualExample: React.ComponentType<any>;
}

// Visual example components for each mode
const FlowchartExample: React.FC = () => (
  <VStack space="sm" className="items-center py-4">
    <Box className="w-16 h-8 bg-emerald-500/30 rounded-lg items-center justify-center border border-emerald-300/40">
      <Text className="text-white text-xs font-bold">Root</Text>
    </Box>
    <ArrowDownIcon size={16} color="white" />
    <HStack space="md">
      <Box className="w-12 h-6 bg-cyan-500/30 rounded items-center justify-center border border-cyan-300/40">
        <Text className="text-white text-xs">A</Text>
      </Box>
      <Box className="w-12 h-6 bg-cyan-500/30 rounded items-center justify-center border border-cyan-300/40">
        <Text className="text-white text-xs">B</Text>
      </Box>
    </HStack>
  </VStack>
);

const MindMapExample: React.FC = () => (
  <VStack space="sm" className="items-center py-4">
    <HStack space="md" className="items-center">
      <Box className="w-10 h-6 bg-purple-500/30 rounded items-center justify-center border border-purple-300/40">
        <Text className="text-white text-xs">A</Text>
      </Box>
      <Box className="w-4 h-0.5 bg-white/50" />
      <Box className="w-16 h-8 bg-cyan-500/30 rounded-lg items-center justify-center border border-cyan-300/40">
        <Text className="text-white text-xs font-bold">Center</Text>
      </Box>
      <Box className="w-4 h-0.5 bg-white/50" />
      <Box className="w-10 h-6 bg-purple-500/30 rounded items-center justify-center border border-purple-300/40">
        <Text className="text-white text-xs">B</Text>
      </Box>
    </HStack>
  </VStack>
);

const NetworkExample: React.FC = () => (
  <VStack space="sm" className="items-center py-4">
    <HStack space="md" className="items-center">
      <Box className="w-10 h-6 bg-emerald-500/30 rounded items-center justify-center border border-emerald-300/40">
        <Text className="text-white text-xs">A</Text>
      </Box>
      <Box className="w-4 h-0.5 bg-white/50" />
      <Box className="w-10 h-6 bg-cyan-500/30 rounded items-center justify-center border border-cyan-300/40">
        <Text className="text-white text-xs">B</Text>
      </Box>
    </HStack>
    <Box className="w-0.5 h-4 bg-white/50" />
    <Box className="w-10 h-6 bg-purple-500/30 rounded items-center justify-center border border-purple-300/40">
      <Text className="text-white text-xs">C</Text>
    </Box>
  </VStack>
);

const VIEW_MODE_INFO: Record<string, ViewModeInfo> = {
  flowchart: {
    title: 'Flowchart View',
    description: 'Displays your knowledge in a hierarchical, top-down structure that shows clear parent-child relationships and logical flow between concepts.',
    icon: TreePineIcon,
    gradientColors: ['rgb(16, 185, 129)', 'rgb(6, 182, 212)'],
    features: [
      'Hierarchical organization',
      'Clear parent-child relationships',
      'Top-down logical flow',
      'Easy to follow progression'
    ],
    bestFor: [
      'Learning sequences',
      'Process documentation',
      'Skill development paths',
      'Structured knowledge'
    ],
    visualExample: FlowchartExample
  },
  mindmap: {
    title: 'Mind Map View',
    description: 'Organizes knowledge around central concepts with ideas branching outward radially, perfect for exploring related topics and creative connections.',
    icon: RadarIcon,
    gradientColors: ['rgb(6, 182, 212)', 'rgb(139, 92, 246)'],
    features: [
      'Central concept focus',
      'Radial branching layout',
      'Creative associations',
      'Non-linear exploration'
    ],
    bestFor: [
      'Brainstorming sessions',
      'Topic exploration',
      'Creative thinking',
      'Concept mapping'
    ],
    visualExample: MindMapExample
  },
  network: {
    title: 'Network View',
    description: 'Shows complex interconnections between all concepts as a web of relationships, revealing hidden patterns and unexpected connections.',
    icon: NetworkIcon,
    gradientColors: ['rgb(139, 92, 246)', 'rgb(16, 185, 129)'],
    features: [
      'Complex interconnections',
      'Web-like relationships',
      'Pattern discovery',
      'Unexpected connections'
    ],
    bestFor: [
      'Research analysis',
      'Finding connections',
      'System thinking',
      'Knowledge discovery'
    ],
    visualExample: NetworkExample
  }
};

export const ViewModeTooltip: React.FC<ViewModeTooltipProps> = ({
  visible,
  onClose,
  mode
}) => {
  const modeInfo = VIEW_MODE_INFO[mode];
  const VisualExample = modeInfo.visualExample;

  if (!modeInfo) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <Box className="flex-1 bg-black/50 justify-center items-center px-6">
        <LinearGradient
          colors={modeInfo.gradientColors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{ 
            width: screenWidth - 48,
            borderRadius: 24,
            padding: 2
          }}
        >
          <Box className="bg-black/20 backdrop-blur-2xl rounded-3xl p-6 relative">
            {/* Close Button */}
            <Pressable 
              onPress={onClose}
              className="absolute top-4 right-4 w-8 h-8 bg-white/20 rounded-full items-center justify-center z-10"
            >
              <XIcon size={16} color="white" />
            </Pressable>

            <VStack space="lg">
              {/* Header */}
              <VStack space="md" className="items-center">
                <LinearGradient
                  colors={modeInfo.gradientColors}
                  className="w-16 h-16 rounded-full items-center justify-center shadow-xl"
                >
                  <modeInfo.icon size={32} color="white" />
                </LinearGradient>
                
                <Text className="text-white text-2xl font-bold text-center">
                  {modeInfo.title}
                </Text>
                
                <Text className="text-white/90 text-center text-base leading-relaxed">
                  {modeInfo.description}
                </Text>
              </VStack>

              {/* Visual Example */}
              <Box className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 border border-white/30">
                <VStack space="sm">
                  <Text className="text-white font-bold text-center">Visual Example</Text>
                  <VisualExample />
                </VStack>
              </Box>

              {/* Features and Best For */}
              <HStack space="md">
                <VStack space="sm" className="flex-1">
                  <Text className="text-white font-bold text-lg">Features</Text>
                  {modeInfo.features.map((feature, index) => (
                    <HStack key={index} className="items-center space-x-2">
                      <Box className="w-2 h-2 bg-white/70 rounded-full" />
                      <Text className="text-white/90 text-sm flex-1">{feature}</Text>
                    </HStack>
                  ))}
                </VStack>
                
                <VStack space="sm" className="flex-1">
                  <Text className="text-white font-bold text-lg">Best For</Text>
                  {modeInfo.bestFor.map((use, index) => (
                    <HStack key={index} className="items-center space-x-2">
                      <Box className="w-2 h-2 bg-white/70 rounded-full" />
                      <Text className="text-white/90 text-sm flex-1">{use}</Text>
                    </HStack>
                  ))}
                </VStack>
              </HStack>

              {/* Close Button */}
              <Pressable
                onPress={onClose}
                className="bg-gradient-to-r from-white/30 to-white/20 backdrop-blur-xl rounded-xl px-6 py-3 border border-white/50 shadow-lg"
              >
                <Text className="text-white font-bold text-center">Got it!</Text>
              </Pressable>
            </VStack>
          </Box>
        </LinearGradient>
      </Box>
    </Modal>
  );
};

interface ViewModeTooltipsProps {
  /** Currently visible tooltip mode */
  activeTooltip: 'flowchart' | 'mindmap' | 'network' | null;
  /** Function to show a specific tooltip */
  onShowTooltip: (mode: 'flowchart' | 'mindmap' | 'network') => void;
  /** Function to hide the current tooltip */
  onHideTooltip: () => void;
}

export const ViewModeTooltips: React.FC<ViewModeTooltipsProps> = ({
  activeTooltip,
  onShowTooltip,
  onHideTooltip
}) => {
  return (
    <>
      {activeTooltip && (
        <ViewModeTooltip
          visible={!!activeTooltip}
          onClose={onHideTooltip}
          mode={activeTooltip}
        />
      )}
    </>
  );
};

export default ViewModeTooltips;
