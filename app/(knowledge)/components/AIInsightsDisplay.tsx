/**
 * AI Graph Insights Display Component
 * 
 * Shows detailed AI analysis results including relationships, clusters, and hierarchy
 * Following LearniScan development workflow rules:
 * - Rule 6: Gluestack UI components with candy theme
 * - Rule 8: Smooth animations with react-native-reanimated
 * - Rule 9: Safe area management
 * - Rule 1: Interactive feedback protocol integration
 */

import React, { useState } from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

// Gluestack UI components (Rule 6)
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Pressable } from '@/components/ui/pressable';
import { Badge, BadgeText } from '@/components/ui/badge';

// Icons
import { 
  ChevronDown, 
  ChevronRight, 
  Link, 
  Target, 
  TrendingUp, 
  Lightbulb,
  ArrowRight,
  Circle,
  GitBranch
} from 'lucide-react-native';

// Types
import type { GraphIntelligence, SemanticRelationship, ConceptCluster } from '@/lib/services/ai-graph-intelligence.service';

interface AIInsightsDisplayProps {
  intelligence: GraphIntelligence;
  onRelationshipPress?: (relationship: SemanticRelationship) => void;
  onClusterPress?: (cluster: ConceptCluster) => void;
  className?: string;
}

interface ExpandableSectionProps {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  accentColor?: string;
}

const ExpandableSection: React.FC<ExpandableSectionProps> = ({
  title,
  icon,
  children,
  defaultExpanded = false,
  accentColor = '#10B981',
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const animationValue = useSharedValue(defaultExpanded ? 1 : 0);
  const rotationValue = useSharedValue(defaultExpanded ? 1 : 0);

  const toggleExpanded = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    
    animationValue.value = withTiming(newExpanded ? 1 : 0, { duration: 300 });
    rotationValue.value = withSpring(newExpanded ? 1 : 0);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    height: interpolate(animationValue.value, [0, 1], [0, 1000]),
    opacity: animationValue.value,
  }));

  const rotationStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${interpolate(rotationValue.value, [0, 1], [0, 90])}deg` }],
  }));

  return (
    <Box className="mb-4">
      <Pressable onPress={toggleExpanded}>
        <Box
          className="p-4 rounded-2xl"
          style={[styles.glassCard, { borderColor: `${accentColor}40` }]}
        >
          <HStack className="items-center justify-between">
            <HStack className="items-center flex-1" space="md">
              <Box
                className="p-2 rounded-xl"
                style={{ backgroundColor: `${accentColor}20` }}
              >
                {icon}
              </Box>
              <Text className="text-white text-lg font-semibold flex-1">
                {title}
              </Text>
            </HStack>
            <Animated.View style={rotationStyle}>
              <ChevronRight size={20} color="#FFFFFF" />
            </Animated.View>
          </HStack>
        </Box>
      </Pressable>

      {isExpanded && (
        <Animated.View style={[animatedStyle, { overflow: 'hidden' }]}>
          <Box className="mt-2 px-2">
            {children}
          </Box>
        </Animated.View>
      )}
    </Box>
  );
};

export const AIInsightsDisplay: React.FC<AIInsightsDisplayProps> = ({
  intelligence,
  onRelationshipPress,
  onClusterPress,
  className,
}) => {
  // Get relationship type colors
  const getRelationshipColor = (type: SemanticRelationship['type']) => {
    const colors = {
      prerequisite: '#F59E0B',
      related: '#A855F7',
      contains: '#10B981',
      similar: '#3B82F6',
      builds_on: '#EC4899',
      applies_to: '#06B6D4',
    };
    return colors[type] || '#A855F7';
  };

  // Get relationship type icon
  const getRelationshipIcon = (type: SemanticRelationship['type']) => {
    const iconProps = { size: 16, color: '#FFFFFF' };
    switch (type) {
      case 'prerequisite': return <TrendingUp {...iconProps} />;
      case 'builds_on': return <GitBranch {...iconProps} />;
      case 'contains': return <Circle {...iconProps} />;
      case 'applies_to': return <ArrowRight {...iconProps} />;
      default: return <Link {...iconProps} />;
    }
  };

  return (
    <ScrollView 
      className={className}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      <VStack space="lg" className="px-4">
        {/* Key Insights Section */}
        <ExpandableSection
          title="🧠 Key Insights"
          icon={<Lightbulb size={24} color="#10B981" />}
          defaultExpanded={true}
          accentColor="#10B981"
        >
          <VStack space="md">
            <Box
              className="p-4 rounded-xl"
              style={[styles.glassCard, { backgroundColor: 'rgba(16, 185, 129, 0.1)' }]}
            >
              <VStack space="sm">
                <HStack className="justify-between items-center">
                  <Text className="text-white/90 text-sm">Learning Complexity</Text>
                  <Badge className="bg-green-600/20 border border-green-400/30">
                    <BadgeText className="text-green-300 capitalize">
                      {intelligence.insights.learningComplexity}
                    </BadgeText>
                  </Badge>
                </HStack>
                <HStack className="justify-between items-center">
                  <Text className="text-white/90 text-sm">Connection Density</Text>
                  <Text className="text-green-300 font-mono">
                    {intelligence.insights.connectionDensity.toFixed(2)}
                  </Text>
                </HStack>
                <HStack className="justify-between items-center">
                  <Text className="text-white/90 text-sm">Total Concepts</Text>
                  <Text className="text-green-300 font-mono">
                    {intelligence.insights.totalConcepts}
                  </Text>
                </HStack>
              </VStack>
            </Box>

            {/* Key Topics */}
            <VStack space="sm">
              <Text className="text-white text-sm font-medium">Key Topics</Text>
              <HStack className="flex-wrap gap-2">
                {intelligence.insights.keyTopics.map((topic, index) => (
                  <Badge
                    key={index}
                    className="bg-cyan-600/20 border border-cyan-400/30"
                  >
                    <BadgeText className="text-cyan-300 text-xs">
                      {topic}
                    </BadgeText>
                  </Badge>
                ))}
              </HStack>
            </VStack>
          </VStack>
        </ExpandableSection>

        {/* Semantic Relationships Section */}
        <ExpandableSection
          title={`🔗 Relationships (${intelligence.relationships.length})`}
          icon={<Link size={24} color="#A855F7" />}
          accentColor="#A855F7"
        >
          <VStack space="sm">
            {intelligence.relationships.slice(0, 10).map((relationship, index) => (
              <Pressable
                key={index}
                onPress={() => onRelationshipPress?.(relationship)}
              >
                <Box
                  className="p-3 rounded-xl"
                  style={[
                    styles.glassCard,
                    { borderColor: `${getRelationshipColor(relationship.type)}40` }
                  ]}
                >
                  <VStack space="sm">
                    <HStack className="items-center justify-between">
                      <HStack className="items-center flex-1" space="sm">
                        <Box
                          className="p-1 rounded-lg"
                          style={{ backgroundColor: `${getRelationshipColor(relationship.type)}20` }}
                        >
                          {getRelationshipIcon(relationship.type)}
                        </Box>
                        <VStack className="flex-1">
                          <Text className="text-white text-sm font-medium">
                            {relationship.type.replace('_', ' ')}
                          </Text>
                          <Text className="text-white/60 text-xs">
                            Strength: {(relationship.strength * 100).toFixed(0)}%
                          </Text>
                        </VStack>
                      </HStack>
                    </HStack>
                    
                    <Text className="text-white/80 text-xs">
                      {relationship.reasoning}
                    </Text>
                    
                    <HStack className="flex-wrap gap-1">
                      {relationship.keywords.map((keyword, kidx) => (
                        <Badge
                          key={kidx}
                          className="bg-white/10 border border-white/20"
                        >
                          <BadgeText className="text-white/70 text-xs">
                            {keyword}
                          </BadgeText>
                        </Badge>
                      ))}
                    </HStack>
                  </VStack>
                </Box>
              </Pressable>
            ))}
            
            {intelligence.relationships.length > 10 && (
              <Text className="text-white/60 text-center text-xs mt-2">
                +{intelligence.relationships.length - 10} more relationships
              </Text>
            )}
          </VStack>
        </ExpandableSection>

        {/* Concept Clusters Section */}
        <ExpandableSection
          title={`🎯 Clusters (${intelligence.clusters.length})`}
          icon={<Target size={24} color="#06B6D4" />}
          accentColor="#06B6D4"
        >
          <VStack space="md">
            {intelligence.clusters.map((cluster, index) => (
              <Pressable
                key={cluster.id}
                onPress={() => onClusterPress?.(cluster)}
              >
                <Box
                  className="p-4 rounded-xl"
                  style={[
                    styles.glassCard,
                    { borderColor: `${cluster.color}40` }
                  ]}
                >
                  <VStack space="sm">
                    <HStack className="items-center justify-between">
                      <HStack className="items-center flex-1" space="sm">
                        <Box
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: cluster.color }}
                        />
                        <VStack className="flex-1">
                          <Text className="text-white text-base font-semibold">
                            {cluster.name}
                          </Text>
                          <Text className="text-white/60 text-xs">
                            {cluster.cardIds.length} concepts • {(cluster.importance * 100).toFixed(0)}% importance
                          </Text>
                        </VStack>
                      </HStack>
                    </HStack>
                    
                    <Text className="text-white/80 text-sm">
                      {cluster.description}
                    </Text>
                    
                    <VStack space="xs">
                      <Text className="text-white/70 text-xs font-medium">
                        Center Concept: {cluster.centerConcept}
                      </Text>
                    </VStack>
                  </VStack>
                </Box>
              </Pressable>
            ))}
          </VStack>
        </ExpandableSection>

        {/* Learning Hierarchy Section */}
        <ExpandableSection
          title="📚 Learning Path"
          icon={<TrendingUp size={24} color="#EC4899" />}
          accentColor="#EC4899"
        >
          <VStack space="md">
            {/* Learning Path */}
            <VStack space="sm">
              <Text className="text-white text-sm font-medium">Recommended Study Order</Text>
              {intelligence.hierarchy.learningPath.map((cardId, index) => (
                <HStack key={cardId} className="items-center" space="md">
                  <Box
                    className="w-8 h-8 rounded-full items-center justify-center"
                    style={{ backgroundColor: 'rgba(236, 72, 153, 0.2)' }}
                  >
                    <Text className="text-pink-300 text-sm font-bold">
                      {index + 1}
                    </Text>
                  </Box>
                  <Text className="text-white/90 text-sm flex-1">
                    Card {cardId}
                  </Text>
                  <Text className="text-white/60 text-xs">
                    Level {intelligence.hierarchy.levels[cardId] || 1}
                  </Text>
                </HStack>
              ))}
            </VStack>

            {/* Prerequisites */}
            <VStack space="sm">
              <Text className="text-white text-sm font-medium">Prerequisites</Text>
              {Object.entries(intelligence.hierarchy.prerequisites).map(([cardId, prereqs]) => (
                prereqs.length > 0 && (
                  <Box
                    key={cardId}
                    className="p-3 rounded-xl"
                    style={[styles.glassCard, { backgroundColor: 'rgba(236, 72, 153, 0.1)' }]}
                  >
                    <VStack space="xs">
                      <Text className="text-white text-sm font-medium">
                        Card {cardId}
                      </Text>
                      <Text className="text-white/70 text-xs">
                        Requires: {prereqs.map(id => `Card ${id}`).join(', ')}
                      </Text>
                    </VStack>
                  </Box>
                )
              ))}
            </VStack>
          </VStack>
        </ExpandableSection>
      </VStack>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  glassCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
    elevation: 4,
  },
});

export default AIInsightsDisplay;
