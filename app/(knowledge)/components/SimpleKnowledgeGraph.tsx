import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { Button, ButtonText } from '@/components/ui/button';
import { Pressable } from '@/components/ui/pressable';

export type ViewMode = 'flowchart' | 'mindmap' | 'network';

type SimpleKnowledgeGraphProps = {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
};

const SimpleKnowledgeGraph: React.FC<SimpleKnowledgeGraphProps> = ({
  viewMode,
  onViewModeChange,
}) => {
  const [selectedNode, setSelectedNode] = useState<string | null>(null);

  // Sample data for testing
  const nodes = [
    { id: 'ML', label: 'Machine Learning', color: '#FF6B9D' },
    { id: 'SL', label: 'Supervised Learning', color: '#A855F7' },
    { id: 'UL', label: 'Unsupervised Learning', color: '#3B82F6' },
    { id: 'RL', label: 'Reinforcement Learning', color: '#06B6D4' },
    { id: 'C', label: 'Classification', color: '#10B981' },
    { id: 'R', label: 'Regression', color: '#F59E0B' },
  ];

  const stats = {
    concepts: nodes.length,
    connections: 8,
    clusters: 3,
    levels: 2,
  };

  return (
    <VStack className="flex-1">
      {/* View Mode Controls */}
      <HStack className="items-center justify-between px-4 mb-4">
        <HStack className="bg-white/10 border border-white/20 backdrop-blur-md p-1 rounded-xl">
          <Button
            action={viewMode === 'flowchart' ? 'candyPink' : undefined}
            variant={viewMode === 'flowchart' ? 'solid' : 'outline'}
            className="px-3 py-2 rounded-lg"
            onPress={() => onViewModeChange('flowchart')}
          >
            <ButtonText className={`text-sm font-semibold ${viewMode === 'flowchart' ? 'text-white' : 'text-white/70'}`}>
              Flowchart
            </ButtonText>
          </Button>
          <Button
            action={viewMode === 'mindmap' ? 'candyPink' : undefined}
            variant={viewMode === 'mindmap' ? 'solid' : 'outline'}
            className="px-3 py-2 rounded-lg"
            onPress={() => onViewModeChange('mindmap')}
          >
            <ButtonText className={`text-sm font-semibold ${viewMode === 'mindmap' ? 'text-white' : 'text-white/70'}`}>
              Mind Map
            </ButtonText>
          </Button>
          <Button
            action={viewMode === 'network' ? 'candyPink' : undefined}
            variant={viewMode === 'network' ? 'solid' : 'outline'}
            className="px-3 py-2 rounded-lg"
            onPress={() => onViewModeChange('network')}
          >
            <ButtonText className={`text-sm font-semibold ${viewMode === 'network' ? 'text-white' : 'text-white/70'}`}>
              Network
            </ButtonText>
          </Button>
        </HStack>
      </HStack>

      {/* Graph Stats */}
      <Box className="mx-4 bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-4 mb-6">
        <HStack className="justify-around">
          <VStack className="items-center">
            <Text className="text-lg font-bold text-candyPink mb-1">{stats.concepts}</Text>
            <Text className="text-white/70 text-xs">Concepts</Text>
          </VStack>
          <VStack className="items-center">
            <Text className="text-lg font-bold text-candyPurple mb-1">{stats.connections}</Text>
            <Text className="text-white/70 text-xs">Connections</Text>
          </VStack>
          <VStack className="items-center">
            <Text className="text-lg font-bold text-candyBlue mb-1">{stats.clusters}</Text>
            <Text className="text-white/70 text-xs">Clusters</Text>
          </VStack>
          <VStack className="items-center">
            <Text className="text-lg font-bold text-emerald-400 mb-1">{stats.levels}</Text>
            <Text className="text-white/70 text-xs">Levels</Text>
          </VStack>
        </HStack>
      </Box>

      {/* Simple Graph Visualization */}
      <Box className="flex-1 mx-4 mb-4 bg-white/5 border border-white/20 backdrop-blur-md rounded-2xl overflow-hidden">
        <VStack className="p-4">
          <Text className="text-lg font-bold text-white mb-4">
            {viewMode === 'flowchart' ? 'Hierarchical View' : 
             viewMode === 'mindmap' ? 'Radial Mind Map' : 'Network View'}
          </Text>
          
          <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
            <VStack className="space-y-4">
              {nodes.map((node, index) => (
                <Pressable
                  key={node.id}
                  className={`p-4 rounded-xl border ${
                    selectedNode === node.id 
                      ? 'bg-white/20 border-white/40' 
                      : 'bg-white/10 border-white/20'
                  }`}
                  onPress={() => setSelectedNode(selectedNode === node.id ? null : node.id)}
                >
                  <HStack className="items-center space-x-3">
                    <View 
                      style={[
                        styles.nodeIndicator, 
                        { backgroundColor: node.color }
                      ]} 
                    />
                    <VStack className="flex-1">
                      <Text className="text-white font-semibold">{node.label}</Text>
                      <Text className="text-white/60 text-sm">
                        {viewMode} representation
                      </Text>
                    </VStack>
                  </HStack>
                  
                  {selectedNode === node.id && (
                    <VStack className="mt-3 pt-3 border-t border-white/20">
                      <Text className="text-white/80 text-sm">
                        This is a {node.label.toLowerCase()} concept in the knowledge graph.
                        Tap to explore connections and related topics.
                      </Text>
                    </VStack>
                  )}
                </Pressable>
              ))}
            </VStack>
          </ScrollView>
        </VStack>
      </Box>
    </VStack>
  );
};

const styles = StyleSheet.create({
  nodeIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
});

export default SimpleKnowledgeGraph;