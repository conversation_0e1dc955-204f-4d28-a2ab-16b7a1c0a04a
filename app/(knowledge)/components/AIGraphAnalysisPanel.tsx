/**
 * AI Graph Analysis Control Panel Component
 * 
 * Provides UI for triggering AI analysis, showing progress, and displaying results
 * Following LearniScan development workflow rules:
 * - Rule 6: Gluestack UI components with candy theme
 * - Rule 8: Smooth animations with react-native-reanimated
 * - Rule 9: Safe area management
 * - Rule 1: Interactive feedback protocol integration
 */

import React, { useEffect } from 'react';
import { StyleSheet, Alert } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

// Gluestack UI components (Rule 6)
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Pressable } from '@/components/ui/pressable';
import { Progress, ProgressFilledTrack } from '@/components/ui/progress';

// Icons
import { Brain, Play, Square, AlertCircle, CheckCircle, BarChart3, Zap } from 'lucide-react-native';

// Context and types
import { useKnowledgeGraph } from '../contexts/KnowledgeGraphContext';
import type { GraphAnalysisProgress } from '@/lib/services/ai-graph-intelligence.service';

interface AIGraphAnalysisPanelProps {
  className?: string;
}

export const AIGraphAnalysisPanel: React.FC<AIGraphAnalysisPanelProps> = ({ className }) => {
  const { state, actions } = useKnowledgeGraph();
  const {
    isAnalyzing,
    analysisProgress,
    analysisStage,
    analysisError,
    intelligence,
  } = state;

  // Animation values (Rule 8)
  const progressAnimation = useSharedValue(0);
  const buttonScale = useSharedValue(1);
  const pulseAnimation = useSharedValue(0);

  // Update progress animation
  useEffect(() => {
    progressAnimation.value = withTiming(analysisProgress, { duration: 500 });
  }, [analysisProgress]);

  // Pulse animation for analyzing state
  useEffect(() => {
    if (isAnalyzing) {
      pulseAnimation.value = withTiming(1, { duration: 1000 }, () => {
        pulseAnimation.value = withTiming(0, { duration: 1000 });
      });
    } else {
      pulseAnimation.value = withTiming(0, { duration: 300 });
    }
  }, [isAnalyzing]);

  // Animated styles
  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${progressAnimation.value}%`,
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: buttonScale.value }],
  }));

  const pulseAnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(pulseAnimation.value, [0, 1], [0.3, 1]),
    transform: [{ scale: interpolate(pulseAnimation.value, [0, 1], [1, 1.05]) }],
  }));

  // Handlers
  const handleStartAnalysis = async () => {
    buttonScale.value = withSpring(0.95, {}, () => {
      buttonScale.value = withSpring(1);
    });

    try {
      await actions.analyzeGraphWithAI();
    } catch (error) {
      Alert.alert(
        'Analysis Failed',
        'Failed to start AI analysis. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleStopAnalysis = () => {
    actions.clearAnalysis();
  };

  const handleEnhanceGraph = () => {
    if (intelligence) {
      actions.enhanceGraphWithAI();
      Alert.alert(
        'Graph Enhanced!',
        'AI insights have been applied to your knowledge graph.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleRetryAnalysis = () => {
    actions.clearAnalysis();
    setTimeout(() => {
      handleStartAnalysis();
    }, 100);
  };

  // Get stage display info
  const getStageInfo = (stage: typeof analysisStage) => {
    const stageMap = {
      analyzing: { text: 'Analyzing content structure...', icon: '🔍', color: '#3B82F6' },
      clustering: { text: 'Creating concept clusters...', icon: '🎯', color: '#8B5CF6' },
      relationships: { text: 'Detecting relationships...', icon: '🔗', color: '#10B981' },
      optimizing: { text: 'Optimizing graph structure...', icon: '⚡', color: '#06B6D4' },
    };
    return stageMap[stage] || { text: 'Initializing...', icon: '🤖', color: '#6B7280' };
  };

  const currentStageInfo = getStageInfo(analysisStage);

  return (
    <Box className={`${className}`}>
      {/* Main Analysis Panel */}
      <Box
        className="mx-4 mb-4 rounded-3xl overflow-hidden"
        style={styles.glassCard}
      >
        <LinearGradient
          colors={['rgba(16, 185, 129, 0.1)', 'rgba(6, 182, 212, 0.1)']}
          style={styles.gradientBackground}
        >
          <VStack space="lg" className="p-6">
            {/* Header */}
            <HStack className="items-center justify-between">
              <HStack className="items-center" space="md">
                <Animated.View style={[pulseAnimatedStyle]}>
                  <Box
                    className="p-3 rounded-2xl"
                    style={[styles.glassCard, { backgroundColor: 'rgba(6, 182, 212, 0.2)' }]}
                  >
                    <Brain size={28} color="#06B6D4" />
                  </Box>
                </Animated.View>
                <VStack>
                  <Text className="text-white text-xl font-bold">
                    🧠 AI Graph Analysis
                  </Text>
                  <Text className="text-white/70 text-sm">
                    Neural network powered insights
                  </Text>
                </VStack>
              </HStack>

              {/* Status Indicator */}
              <Box className="items-center">
                {isAnalyzing && (
                  <HStack className="items-center" space="sm">
                    <Box className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                    <Text className="text-blue-300 text-xs font-medium">ANALYZING</Text>
                  </HStack>
                )}
                {intelligence && !isAnalyzing && (
                  <HStack className="items-center" space="sm">
                    <CheckCircle size={16} color="#10B981" />
                    <Text className="text-green-300 text-xs font-medium">COMPLETE</Text>
                  </HStack>
                )}
                {analysisError && (
                  <HStack className="items-center" space="sm">
                    <AlertCircle size={16} color="#EF4444" />
                    <Text className="text-red-300 text-xs font-medium">ERROR</Text>
                  </HStack>
                )}
              </Box>
            </HStack>

            {/* Progress Section */}
            {(isAnalyzing || analysisProgress > 0) && (
              <VStack space="md">
                <HStack className="items-center justify-between">
                  <HStack className="items-center" space="sm">
                    <Text className="text-lg">{currentStageInfo.icon}</Text>
                    <Text className="text-white/90 text-sm font-medium">
                      {currentStageInfo.text}
                    </Text>
                  </HStack>
                  <Text className="text-white font-mono text-sm">
                    {Math.round(analysisProgress)}%
                  </Text>
                </HStack>

                {/* Animated Progress Bar */}
                <Box className="h-3 bg-white/20 rounded-full overflow-hidden">
                  <Animated.View
                    style={[
                      progressAnimatedStyle,
                      {
                        height: '100%',
                        borderRadius: 6,
                      },
                    ]}
                  >
                    <LinearGradient
                      colors={['#10B981', '#06B6D4', '#8B5CF6']}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={{ flex: 1 }}
                    />
                  </Animated.View>
                </Box>
              </VStack>
            )}

            {/* Error Display */}
            {analysisError && (
              <Box
                className="p-4 rounded-xl border border-red-400/30"
                style={{ backgroundColor: 'rgba(239, 68, 68, 0.1)' }}
              >
                <HStack className="items-center justify-between">
                  <HStack className="items-center flex-1" space="sm">
                    <AlertCircle size={20} color="#EF4444" />
                    <Text className="text-red-200 text-sm flex-1">
                      {analysisError}
                    </Text>
                  </HStack>
                  <Button
                    onPress={handleRetryAnalysis}
                    className="bg-red-600/20 border border-red-400/30 px-3 py-2"
                    size="sm"
                  >
                    <ButtonText className="text-red-300 text-xs">Retry</ButtonText>
                  </Button>
                </HStack>
              </Box>
            )}

            {/* Results Summary */}
            {intelligence && !isAnalyzing && (
              <VStack space="md">
                <HStack className="items-center" space="sm">
                  <BarChart3 size={20} color="#10B981" />
                  <Text className="text-white text-base font-semibold">
                    Analysis Results
                  </Text>
                </HStack>

                <HStack className="justify-around">
                  <VStack className="items-center">
                    <Text className="text-2xl font-bold text-blue-400">
                      {intelligence.relationships.length}
                    </Text>
                    <Text className="text-white/70 text-xs">Relationships</Text>
                  </VStack>
                  <VStack className="items-center">
                    <Text className="text-2xl font-bold text-purple-400">
                      {intelligence.clusters.length}
                    </Text>
                    <Text className="text-white/70 text-xs">Clusters</Text>
                  </VStack>
                  <VStack className="items-center">
                    <Text className="text-2xl font-bold text-green-400">
                      {intelligence.insights.totalConcepts}
                    </Text>
                    <Text className="text-white/70 text-xs">Concepts</Text>
                  </VStack>
                  <VStack className="items-center">
                    <Text className="text-2xl font-bold text-cyan-400">
                      {intelligence.insights.learningComplexity}
                    </Text>
                    <Text className="text-white/70 text-xs">Complexity</Text>
                  </VStack>
                </HStack>
              </VStack>
            )}

            {/* Action Buttons */}
            <HStack space="md">
              {!isAnalyzing ? (
                <Animated.View style={[buttonAnimatedStyle, { flex: 1 }]}>
                  <Button
                    onPress={handleStartAnalysis}
                    className="flex-1 py-4"
                    style={styles.primaryButton}
                  >
                    <HStack className="items-center" space="sm">
                      <Play size={20} color="#FFFFFF" />
                      <ButtonText className="text-white font-semibold">
                        {intelligence ? 'Analyze Again' : 'Start AI Analysis'}
                      </ButtonText>
                    </HStack>
                  </Button>
                </Animated.View>
              ) : (
                <Button
                  onPress={handleStopAnalysis}
                  className="flex-1 py-4 bg-red-600/20 border border-red-400/30"
                >
                  <HStack className="items-center" space="sm">
                    <Square size={16} color="#EF4444" />
                    <ButtonText className="text-red-300 font-semibold">
                      Stop Analysis
                    </ButtonText>
                  </HStack>
                </Button>
              )}

              {intelligence && !isAnalyzing && (
                <Button
                  onPress={handleEnhanceGraph}
                  className="px-6 py-4"
                  style={styles.secondaryButton}
                >
                  <HStack className="items-center" space="sm">
                    <Zap size={18} color="#06B6D4" />
                    <ButtonText className="text-cyan-300 font-semibold">
                      Enhance
                    </ButtonText>
                  </HStack>
                </Button>
              )}
            </HStack>
          </VStack>
        </LinearGradient>
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create({
  glassCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 8,
  },
  gradientBackground: {
    flex: 1,
  },
  primaryButton: {
    backgroundColor: 'rgba(16, 185, 129, 0.8)',
    borderWidth: 1,
    borderColor: 'rgba(16, 185, 129, 0.3)',
  },
  secondaryButton: {
    backgroundColor: 'rgba(6, 182, 212, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(6, 182, 212, 0.3)',
  },
});

export default AIGraphAnalysisPanel;
