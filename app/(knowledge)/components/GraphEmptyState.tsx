/**
 * Graph Empty State Component
 * 
 * Provides comprehensive onboarding experience for new users when the Knowledge Graph is empty.
 * Includes visual guidance, step-by-step instructions, and call-to-action buttons.
 */

import React from 'react';
import { Pressable, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import {
  BrainIcon,
  ScanIcon,
  BookOpenIcon,
  ArrowRightIcon,
  SparklesIcon,
  NetworkIcon,
  TreePineIcon,
  RadarIcon
} from 'lucide-react-native';

// UI Components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';

const { width: screenWidth } = Dimensions.get('window');

interface GraphEmptyStateProps {
  /** Callback when user wants to start scanning */
  onStartScanning?: () => void;
  /** Callback when user wants to view knowledge cards */
  onViewCards?: () => void;
  /** Callback when user wants to learn about view modes */
  onLearnViewModes?: () => void;
}

interface OnboardingStepProps {
  step: number;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  iconColor: string;
  iconBgColor: string;
  onPress?: () => void;
  buttonText?: string;
}

const OnboardingStep: React.FC<OnboardingStepProps> = ({
  step,
  title,
  description,
  icon: Icon,
  iconColor,
  iconBgColor,
  onPress,
  buttonText
}) => (
  <Box className="bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-2xl rounded-2xl p-5 border border-white/30 shadow-xl relative overflow-hidden">
    {/* Background decoration */}
    <Box className="absolute top-0 right-0 w-16 h-16 bg-white/5 rounded-full blur-xl" />
    
    <VStack space="md">
      <HStack className="items-center space-x-4">
        {/* Step number */}
        <Box className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-cyan-500 rounded-full items-center justify-center shadow-lg">
          <Text className="text-white text-sm font-bold">{step}</Text>
        </Box>
        
        {/* Icon */}
        <Box 
          className="w-12 h-12 justify-center items-center rounded-xl shadow-lg border border-white/30"
          style={{ backgroundColor: iconBgColor }}
        >
          <Icon size={24} color={iconColor} />
        </Box>
        
        <VStack space="xs" className="flex-1">
          <Text className="text-white text-lg font-bold">{title}</Text>
        </VStack>
      </HStack>
      
      <Text className="text-white/90 text-sm leading-relaxed">
        {description}
      </Text>
      
      {onPress && buttonText && (
        <Pressable
          onPress={onPress}
          className="bg-gradient-to-r from-emerald-500 to-cyan-500 rounded-xl px-4 py-3 shadow-lg"
        >
          <HStack className="items-center justify-center space-x-2">
            <Text className="text-white font-bold text-sm">{buttonText}</Text>
            <ArrowRightIcon size={16} color="white" />
          </HStack>
        </Pressable>
      )}
    </VStack>
  </Box>
);

interface ViewModePreviewProps {
  mode: 'flowchart' | 'mindmap' | 'network';
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  gradientColors: [string, string];
}

const ViewModePreview: React.FC<ViewModePreviewProps> = ({
  mode,
  title,
  description,
  icon: Icon,
  gradientColors
}) => (
  <Box className="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-xl rounded-xl p-4 border border-white/20 shadow-lg">
    <VStack space="sm">
      <HStack className="items-center space-x-3">
        <LinearGradient
          colors={gradientColors}
          className="w-10 h-10 rounded-lg items-center justify-center"
        >
          <Icon size={20} color="white" />
        </LinearGradient>
        <Text className="text-white text-base font-bold flex-1">{title}</Text>
      </HStack>
      <Text className="text-white/80 text-xs leading-relaxed">{description}</Text>
    </VStack>
  </Box>
);

export const GraphEmptyState: React.FC<GraphEmptyStateProps> = ({
  onStartScanning,
  onViewCards,
  onLearnViewModes
}) => {
  const handleStartScanning = () => {
    onStartScanning?.();
    router.push('/(tabs)/scan');
  };

  const handleViewCards = () => {
    onViewCards?.();
    router.push('/(knowledge)/cards');
  };

  const handleLearnViewModes = () => {
    onLearnViewModes?.();
    // This will be handled by the parent component to show tooltips
  };

  return (
    <VStack className="flex-1 px-6 py-8" space="lg">
      {/* Hero Section */}
      <VStack space="md" className="items-center">
        <Box className="w-20 h-20 bg-gradient-to-br from-emerald-500/30 to-cyan-500/20 rounded-full items-center justify-center shadow-xl border border-emerald-300/40">
          <BrainIcon size={40} color="#10B981" />
        </Box>
        
        <VStack space="sm" className="items-center">
          <Text className="text-white text-2xl font-bold text-center">
            Welcome to Knowledge Graph! 🧠
          </Text>
          <Text className="text-white/90 text-center text-base leading-relaxed px-4">
            Visualize connections between your knowledge cards and discover new insights through AI-powered analysis.
          </Text>
        </VStack>
      </VStack>

      {/* Getting Started Steps */}
      <VStack space="md">
        <Text className="text-white text-xl font-bold">Getting Started</Text>
        
        <OnboardingStep
          step={1}
          title="Scan Content"
          description="Start by scanning text, documents, or images to create your first knowledge cards."
          icon={ScanIcon}
          iconColor="#FF6B9D"
          iconBgColor="rgba(255, 107, 157, 0.2)"
          onPress={handleStartScanning}
          buttonText="Start Scanning"
        />
        
        <OnboardingStep
          step={2}
          title="Build Your Collection"
          description="Create multiple knowledge cards to see meaningful connections and relationships."
          icon={BookOpenIcon}
          iconColor="#8B5CF6"
          iconBgColor="rgba(139, 92, 246, 0.2)"
          onPress={handleViewCards}
          buttonText="View Cards"
        />
        
        <OnboardingStep
          step={3}
          title="Explore Connections"
          description="Watch as your knowledge graph automatically forms connections between related concepts."
          icon={NetworkIcon}
          iconColor="#10B981"
          iconBgColor="rgba(16, 185, 129, 0.2)"
        />
      </VStack>

      {/* View Modes Preview */}
      <VStack space="md">
        <Text className="text-white text-xl font-bold">Visualization Modes</Text>
        <Text className="text-white/80 text-sm">
          Choose how you want to explore your knowledge:
        </Text>
        
        <VStack space="sm">
          <ViewModePreview
            mode="flowchart"
            title="Flowchart"
            description="Hierarchical top-down structure showing clear knowledge relationships"
            icon={TreePineIcon}
            gradientColors={['rgb(16, 185, 129)', 'rgb(6, 182, 212)']}
          />
          
          <ViewModePreview
            mode="mindmap"
            title="Mind Map"
            description="Radial layout with central concepts and branching ideas"
            icon={RadarIcon}
            gradientColors={['rgb(6, 182, 212)', 'rgb(139, 92, 246)']}
          />
          
          <ViewModePreview
            mode="network"
            title="Network"
            description="Interconnected web showing complex relationships between concepts"
            icon={NetworkIcon}
            gradientColors={['rgb(139, 92, 246)', 'rgb(16, 185, 129)']}
          />
        </VStack>
      </VStack>

      {/* Call to Action */}
      <Box className="bg-gradient-to-r from-emerald-500/20 to-cyan-500/20 backdrop-blur-xl rounded-2xl p-6 border border-emerald-300/30 shadow-xl">
        <VStack space="md" className="items-center">
          <SparklesIcon size={32} color="#10B981" />
          <Text className="text-white text-lg font-bold text-center">
            Ready to Build Your Knowledge Graph?
          </Text>
          <Text className="text-white/90 text-center text-sm leading-relaxed">
            Start by scanning your first piece of content and watch your knowledge come to life!
          </Text>
          
          <Pressable
            onPress={handleStartScanning}
            className="bg-gradient-to-r from-emerald-500 to-cyan-500 rounded-xl px-8 py-4 shadow-xl"
          >
            <HStack className="items-center space-x-3">
              <ScanIcon size={20} color="white" />
              <Text className="text-white font-bold text-base">Start Your First Scan</Text>
            </HStack>
          </Pressable>
        </VStack>
      </Box>
    </VStack>
  );
};

export default GraphEmptyState;
