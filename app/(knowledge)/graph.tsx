import React, { use<PERSON><PERSON>back, useState } from 'react';
import { <PERSON><PERSON>, Dimensions, ScrollView, Modal } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, Search, Settings, Plus, Minus, X, RotateCcw, Eye, Brain, BarChart3 } from 'lucide-react-native';
import { router } from 'expo-router';

// UI Components
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { Pressable } from '@/components/ui/pressable';
import { LinearGradient } from 'expo-linear-gradient';

// Knowledge Graph Components
import { KnowledgeGraphProvider, useKnowledgeGraph } from './contexts/KnowledgeGraphContext';
import { InteractiveGraphSVG } from './components/InteractiveGraphSVG';
import { KnowledgeNode } from './types/graph';

// AI Components
import { AIGraphAnalysisPanel } from './components/AIGraphAnalysisPanel';
import { AIInsightsDisplay } from './components/AIInsightsDisplay';
import type { SemanticRelationship, ConceptCluster } from '@/lib/services/ai-graph-intelligence.service';

// Enhanced View Mode Toggle Button Component
interface ViewModeToggleProps {
  isActive: boolean;
  onPress: () => void;
  label: string;
  gradientColors?: string[];
  disabled?: boolean;
}

const ViewModeToggle: React.FC<ViewModeToggleProps> = ({
  isActive,
  onPress,
  label,
  gradientColors = ['rgb(16, 185, 129)', 'rgb(6, 182, 212)'],
  disabled = false
}) => {
  return (
    <Pressable
      className={`px-4 py-2 rounded-lg overflow-hidden ${isActive ? 'scale-105' : 'opacity-80'} ${disabled ? 'opacity-50' : ''}`}
      onPress={disabled ? undefined : onPress}
      style={{
        transform: [{ scale: isActive ? 1.05 : 1 }],
      }}
      disabled={disabled}
    >
      {isActive ? (
        <LinearGradient
          colors={gradientColors as [string, string, ...string[]]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          className="absolute inset-0"
          style={{ borderRadius: 8 }}
        />
      ) : (
        <Box className="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20" />
      )}
      <Text
        className={`text-sm font-semibold ${isActive ? 'text-white' : 'text-white/70'}`}
        style={{
          textShadowColor: isActive ? 'rgba(0, 0, 0, 0.3)' : 'transparent',
          textShadowOffset: { width: 0, height: 1 },
          textShadowRadius: 2,
        }}
      >
        {label}
      </Text>
    </Pressable>
  );
};

// Enhanced Floating Action Button Component
const FloatingActionButton = ({
  children,
  mainIcon,
  onMainPress
}: {
  children: React.ReactNode;
  mainIcon: React.ReactNode;
  onMainPress?: () => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <VStack className="items-center relative">
      {isOpen && (
        <VStack className="absolute bottom-16 gap-2">
          {children}
        </VStack>
      )}
      <Pressable
        className="w-14 h-14 bg-emerald-500 rounded-full items-center justify-center shadow-lg"
        onPress={() => {
          setIsOpen(!isOpen);
          if (onMainPress && !isOpen) onMainPress();
        }}
      >
        {mainIcon}
      </Pressable>
    </VStack>
  );
};

const FloatingActionButtonItem = ({
  icon,
  onPress,
  label
}: {
  icon: React.ReactNode;
  onPress: () => void;
  label?: string;
}) => {
  return (
    <HStack className="items-center">
      {label && (
        <Box className="mr-2 bg-black/70 px-3 py-1 rounded-lg">
          <Text className="text-white text-xs">{label}</Text>
        </Box>
      )}
      <Pressable
        className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-full items-center justify-center shadow-md"
        onPress={onPress}
      >
        {icon}
      </Pressable>
    </HStack>
  );
};

// Enhanced Graph Component with Interactive Features
const GraphContent: React.FC = () => {
  const { state, actions } = useKnowledgeGraph();
  const { graph, viewport, isLoading, error, intelligence } = state;
  const screenDimensions = Dimensions.get('window');

  // AI Modal state
  const [showAIPanel, setShowAIPanel] = useState(false);
  const [showInsights, setShowInsights] = useState(false);

  // Handle node interactions
  const handleNodePress = useCallback((node: KnowledgeNode) => {
    console.log('Node pressed:', node.label);
  }, []);

  const handleNodeLongPress = useCallback((node: KnowledgeNode) => {
    Alert.alert(
      node.label,
      node.metadata.description || 'No description available',
      [
        { text: 'Edit', onPress: () => console.log('Edit node:', node.id) },
        { text: 'Delete', style: 'destructive', onPress: () => console.log('Delete node:', node.id) },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  }, []);

  const handleNodeDoublePress = useCallback((node: KnowledgeNode) => {
    console.log('Node double pressed - focusing on:', node.label);
  }, []);

  // Handle view mode changes
  const handleViewModeChange = useCallback((mode: 'flowchart' | 'mindmap' | 'network') => {
    actions.setViewMode(mode);
  }, [actions]);

  // Reset viewport
  const handleResetViewport = useCallback(() => {
    actions.updateViewport({
      scale: 1,
      offset: { x: 0, y: 0 },
    });
  }, [actions]);

  // Generate new graph
  const handleRegenerateGraph = useCallback(() => {
    actions.generateGraphFromCards();
  }, [actions]);

  // AI interaction handlers
  const handleOpenAIPanel = useCallback(() => {
    setShowAIPanel(true);
  }, []);

  const handleCloseAIPanel = useCallback(() => {
    setShowAIPanel(false);
  }, []);

  const handleShowInsights = useCallback(() => {
    if (intelligence) {
      setShowInsights(true);
    } else {
      Alert.alert(
        'No AI Analysis',
        'Please run AI analysis first to view insights.',
        [{ text: 'OK' }]
      );
    }
  }, [intelligence]);

  const handleCloseInsights = useCallback(() => {
    setShowInsights(false);
  }, []);

  const handleRelationshipPress = useCallback((relationship: SemanticRelationship) => {
    Alert.alert(
      'Relationship Details',
      `Type: ${relationship.type}\nStrength: ${(relationship.strength * 100).toFixed(0)}%\nReasoning: ${relationship.reasoning}`,
      [{ text: 'OK' }]
    );
  }, []);

  const handleClusterPress = useCallback((cluster: ConceptCluster) => {
    Alert.alert(
      'Cluster Details',
      `Name: ${cluster.name}\nDescription: ${cluster.description}\nImportance: ${(cluster.importance * 100).toFixed(0)}%\nConcepts: ${cluster.cardIds.length}`,
      [{ text: 'OK' }]
    );
  }, []);

  if (isLoading) {
    return (
      <LinearGradient
        colors={['rgb(255, 107, 157)', 'rgb(168, 85, 247)', 'rgb(59, 130, 246)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={{ flex: 1 }}>
          <VStack className="flex-1 justify-center items-center">
            <Box className="w-16 h-16 bg-white/20 rounded-full items-center justify-center mb-4">
              <Search size={32} color="white" />
            </Box>
            <Text className="text-white text-lg font-semibold mb-2">Generating Knowledge Graph</Text>
            <Text className="text-white/70 text-sm text-center px-8">
              Analyzing your knowledge cards and creating connections...
            </Text>
          </VStack>
        </SafeAreaView>
      </LinearGradient>
    );
  }

  if (error) {
    return (
      <LinearGradient
        colors={['rgb(255, 107, 157)', 'rgb(168, 85, 247)', 'rgb(59, 130, 246)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={{ flex: 1 }}>
          <VStack className="flex-1 justify-center items-center">
            <Box className="w-16 h-16 bg-red-500/20 rounded-full items-center justify-center mb-4">
              <X size={32} color="#EF4444" />
            </Box>
            <Text className="text-white text-lg font-semibold mb-2">Error Loading Graph</Text>
            <Text className="text-white/70 text-sm text-center px-8 mb-6">{error}</Text>
            <Pressable
              className="px-6 py-3 bg-pink-500 rounded-xl"
              onPress={handleRegenerateGraph}
            >
              <Text className="text-white font-semibold text-center">Try Again</Text>
            </Pressable>
          </VStack>
        </SafeAreaView>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['rgb(16, 185, 129)', 'rgb(6, 182, 212)', 'rgb(139, 92, 246)']} // Emerald-Cyan-Purple gradient
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView style={{ flex: 1 }}>
        <VStack className="flex-1">
          {/* Minimal Header with Navigation */}
          <HStack className="px-4 py-3 justify-between items-center">
            <Pressable
              className="w-10 h-10 rounded-full bg-white/10 items-center justify-center"
              onPress={() => router.back()}
            >
              <ArrowLeft size={20} color="white" />
            </Pressable>

            <Text className="text-white text-lg font-semibold">Knowledge Graph</Text>

            <Pressable
              className="w-10 h-10 rounded-full bg-white/10 items-center justify-center"
              onPress={() => console.log('Settings')}
            >
              <Settings size={20} color="white" />
            </Pressable>
          </HStack>

          {/* Enhanced View Mode Toggle */}
          <HStack className="items-center justify-center mb-3 px-4">
            <Box className="bg-black/20 border border-white/10 backdrop-blur-md p-1 rounded-xl shadow-lg">
              <HStack className="gap-1">
                <ViewModeToggle
                  isActive={graph?.viewMode === 'flowchart'}
                  onPress={() => handleViewModeChange('flowchart')}
                  label="Flowchart"
                  gradientColors={['rgb(16, 185, 129)', 'rgb(6, 182, 212)']} // emerald to cyan
                  disabled={isLoading}
                />
                <ViewModeToggle
                  isActive={graph?.viewMode === 'mindmap'}
                  onPress={() => handleViewModeChange('mindmap')}
                  label="Mind Map"
                  gradientColors={['rgb(6, 182, 212)', 'rgb(139, 92, 246)']} // cyan to purple
                  disabled={isLoading}
                />
                <ViewModeToggle
                  isActive={graph?.viewMode === 'network'}
                  onPress={() => handleViewModeChange('network')}
                  label="Network"
                  gradientColors={['rgb(139, 92, 246)', 'rgb(16, 185, 129)']} // purple to emerald
                  disabled={isLoading}
                />
              </HStack>
            </Box>
          </HStack>

          {/* Enhanced Graph Stats Bar */}
          <HStack className="mx-4 mb-2 px-4 py-2 bg-white/10 backdrop-blur-md rounded-xl justify-between border border-white/10 shadow-sm">
            <HStack className="items-center space-x-2">
              <LinearGradient
                colors={['rgb(16, 185, 129)', 'rgb(52, 211, 153)'] as [string, string, ...string[]]}
                className="w-3 h-3 rounded-full"
              />
              <Text className="text-white text-xs font-medium">{graph?.nodes.length || 0} Nodes</Text>
            </HStack>
            <HStack className="items-center space-x-2">
              <LinearGradient
                colors={['rgb(6, 182, 212)', 'rgb(45, 212, 191)'] as [string, string, ...string[]]}
                className="w-3 h-3 rounded-full"
              />
              <Text className="text-white text-xs font-medium">{graph?.edges.length || 0} Links</Text>
            </HStack>
            <HStack className="items-center space-x-2">
              <LinearGradient
                colors={['rgb(139, 92, 246)', 'rgb(168, 85, 247)'] as [string, string, ...string[]]}
                className="w-3 h-3 rounded-full"
              />
              <Text className="text-white text-xs font-medium capitalize">{graph?.viewMode}</Text>
            </HStack>
          </HStack>

          {/* Full Screen Graph Visualization */}
          <Box className="flex-1 bg-white/5 border border-white/10 backdrop-blur-md rounded-3xl overflow-hidden mx-4 mb-4">
            {graph && (
              <InteractiveGraphSVG
                width={screenDimensions.width - 32} // Account for padding
                height={screenDimensions.height - 180} // Full height minus header space
                onNodePress={handleNodePress}
                onNodeLongPress={handleNodeLongPress}
                onNodeDoublePress={handleNodeDoublePress}
              />
            )}
          </Box>

          {/* Enhanced Floating Action Button Group */}
          <Box className="absolute bottom-6 right-4">
            <FloatingActionButton
              mainIcon={<Search size={24} color="white" />}
              onMainPress={() => console.log('Search')}
            >
              <FloatingActionButtonItem
                icon={<Brain size={20} color="white" />}
                onPress={handleOpenAIPanel}
                label="AI Analysis"
              />
              <FloatingActionButtonItem
                icon={<BarChart3 size={20} color="white" />}
                onPress={handleShowInsights}
                label="AI Insights"
              />
              <FloatingActionButtonItem
                icon={<Plus size={20} color="white" />}
                onPress={() => actions.updateViewport({
                  scale: Math.min(viewport.bounds.maxScale, viewport.scale * 1.2)
                })}
                label="Zoom In"
              />
              <FloatingActionButtonItem
                icon={<Minus size={20} color="white" />}
                onPress={() => actions.updateViewport({
                  scale: Math.max(viewport.bounds.minScale, viewport.scale * 0.8)
                })}
                label="Zoom Out"
              />
              <FloatingActionButtonItem
                icon={<RotateCcw size={20} color="white" />}
                onPress={handleResetViewport}
                label="Reset"
              />
              <FloatingActionButtonItem
                icon={<Eye size={20} color="white" />}
                onPress={handleRegenerateGraph}
                label="Regenerate"
              />
            </FloatingActionButton>
          </Box>

          {/* AI Analysis Panel Modal */}
          <Modal
            visible={showAIPanel}
            animationType="slide"
            presentationStyle="pageSheet"
            onRequestClose={handleCloseAIPanel}
          >
            <LinearGradient
              colors={['rgb(16, 185, 129)', 'rgb(6, 182, 212)', 'rgb(139, 92, 246)']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={{ flex: 1 }}
            >
              <SafeAreaView style={{ flex: 1 }}>
                <VStack className="flex-1">
                  {/* Modal Header */}
                  <HStack className="px-4 py-3 justify-between items-center">
                    <Text className="text-white text-lg font-semibold">AI Graph Analysis</Text>
                    <Pressable
                      className="w-10 h-10 rounded-full bg-white/10 items-center justify-center"
                      onPress={handleCloseAIPanel}
                    >
                      <X size={20} color="white" />
                    </Pressable>
                  </HStack>

                  {/* AI Analysis Panel */}
                  <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
                    <AIGraphAnalysisPanel />
                  </ScrollView>
                </VStack>
              </SafeAreaView>
            </LinearGradient>
          </Modal>

          {/* AI Insights Modal */}
          <Modal
            visible={showInsights && !!intelligence}
            animationType="slide"
            presentationStyle="pageSheet"
            onRequestClose={handleCloseInsights}
          >
            <LinearGradient
              colors={['rgb(16, 185, 129)', 'rgb(6, 182, 212)', 'rgb(139, 92, 246)']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={{ flex: 1 }}
            >
              <SafeAreaView style={{ flex: 1 }}>
                <VStack className="flex-1">
                  {/* Modal Header */}
                  <HStack className="px-4 py-3 justify-between items-center">
                    <Text className="text-white text-lg font-semibold">AI Insights</Text>
                    <Pressable
                      className="w-10 h-10 rounded-full bg-white/10 items-center justify-center"
                      onPress={handleCloseInsights}
                    >
                      <X size={20} color="white" />
                    </Pressable>
                  </HStack>

                  {/* AI Insights Display */}
                  {intelligence && (
                    <AIInsightsDisplay
                      intelligence={intelligence}
                      onRelationshipPress={handleRelationshipPress}
                      onClusterPress={handleClusterPress}
                      className="flex-1"
                    />
                  )}
                </VStack>
              </SafeAreaView>
            </LinearGradient>
          </Modal>
        </VStack>
      </SafeAreaView>
    </LinearGradient>
  );
};

// Main Graph component with provider
export const Graph: React.FC = () => {
  return (
    <KnowledgeGraphProvider>
      <GraphContent />
    </KnowledgeGraphProvider>
  );
};

export default Graph;