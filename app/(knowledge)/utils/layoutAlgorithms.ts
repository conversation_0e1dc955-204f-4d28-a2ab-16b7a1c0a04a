import { KnowledgeGraph, KnowledgeNode, KnowledgeEdge } from '../types/graph';

export interface LayoutOptions {
  width: number;
  height: number;
  padding?: number;
}

/**
 * Apply hierarchical flowchart layout to the graph
 */
export const applyFlowchartLayout = (
  graph: KnowledgeGraph,
  options: LayoutOptions
): KnowledgeGraph => {
  const { width, height, padding = 50 } = options;
  const nodes = [...graph.nodes];
  const edges = [...graph.edges];

  // Build hierarchy based on connections
  const nodeConnections = new Map<string, string[]>();
  const incomingConnections = new Map<string, number>();
  
  // Initialize connection maps
  nodes.forEach(node => {
    nodeConnections.set(node.id, []);
    incomingConnections.set(node.id, 0);
  });

  // Build connection graph
  edges.forEach(edge => {
    const sourceConnections = nodeConnections.get(edge.source) || [];
    sourceConnections.push(edge.target);
    nodeConnections.set(edge.source, sourceConnections);
    
    const incomingCount = incomingConnections.get(edge.target) || 0;
    incomingConnections.set(edge.target, incomingCount + 1);
  });

  // Find root nodes (nodes with no incoming connections)
  const rootNodes = nodes.filter(node => 
    (incomingConnections.get(node.id) || 0) === 0
  );

  // If no clear root, use the node with most connections as root
  if (rootNodes.length === 0) {
    const mostConnectedNode = nodes.reduce((max, node) => {
      const connections = nodeConnections.get(node.id)?.length || 0;
      const maxConnections = nodeConnections.get(max.id)?.length || 0;
      return connections > maxConnections ? node : max;
    });
    rootNodes.push(mostConnectedNode);
  }

  // Assign levels using BFS
  const levels = new Map<string, number>();
  const queue: { nodeId: string; level: number }[] = [];
  
  rootNodes.forEach(node => {
    levels.set(node.id, 0);
    queue.push({ nodeId: node.id, level: 0 });
  });

  let maxLevel = 0;
  while (queue.length > 0) {
    const { nodeId, level } = queue.shift()!;
    const connections = nodeConnections.get(nodeId) || [];
    
    connections.forEach(targetId => {
      if (!levels.has(targetId)) {
        const newLevel = level + 1;
        levels.set(targetId, newLevel);
        maxLevel = Math.max(maxLevel, newLevel);
        queue.push({ nodeId: targetId, level: newLevel });
      }
    });
  }

  // Group nodes by level
  const nodesByLevel = new Map<number, KnowledgeNode[]>();
  for (let i = 0; i <= maxLevel; i++) {
    nodesByLevel.set(i, []);
  }

  nodes.forEach(node => {
    const level = levels.get(node.id) || 0;
    const levelNodes = nodesByLevel.get(level) || [];
    levelNodes.push(node);
    nodesByLevel.set(level, levelNodes);
  });

  // Position nodes
  const levelHeight = (height - 2 * padding) / Math.max(1, maxLevel);
  
  nodes.forEach(node => {
    const level = levels.get(node.id) || 0;
    const levelNodes = nodesByLevel.get(level) || [];
    const nodeIndex = levelNodes.findIndex(n => n.id === node.id);
    const levelWidth = width - 2 * padding;
    const nodeSpacing = levelWidth / Math.max(1, levelNodes.length - 1);
    
    node.position = {
      x: levelNodes.length === 1 
        ? width / 2 
        : padding + nodeIndex * nodeSpacing,
      y: padding + level * levelHeight,
    };
  });

  return {
    ...graph,
    nodes,
    viewMode: 'flowchart',
  };
};

/**
 * Apply radial mind map layout to the graph
 */
export const applyMindMapLayout = (
  graph: KnowledgeGraph,
  options: LayoutOptions
): KnowledgeGraph => {
  const { width, height } = options;
  const nodes = [...graph.nodes];
  const edges = [...graph.edges];

  const centerX = width / 2;
  const centerY = height / 2;

  // Find the most connected node as center
  const connectionCounts = new Map<string, number>();
  edges.forEach(edge => {
    connectionCounts.set(edge.source, (connectionCounts.get(edge.source) || 0) + 1);
    connectionCounts.set(edge.target, (connectionCounts.get(edge.target) || 0) + 1);
  });

  const centerNode = nodes.reduce((max, node) => {
    const connections = connectionCounts.get(node.id) || 0;
    const maxConnections = connectionCounts.get(max.id) || 0;
    return connections > maxConnections ? node : max;
  });

  // Position center node
  centerNode.position = { x: centerX, y: centerY };

  // Build adjacency list
  const adjacencyList = new Map<string, string[]>();
  nodes.forEach(node => adjacencyList.set(node.id, []));
  edges.forEach(edge => {
    adjacencyList.get(edge.source)?.push(edge.target);
    adjacencyList.get(edge.target)?.push(edge.source);
  });

  // BFS to assign levels and positions
  const visited = new Set<string>();
  const levels = new Map<string, number>();
  const queue: { nodeId: string; level: number }[] = [];

  visited.add(centerNode.id);
  levels.set(centerNode.id, 0);
  queue.push({ nodeId: centerNode.id, level: 0 });

  let maxLevel = 0;
  while (queue.length > 0) {
    const { nodeId, level } = queue.shift()!;
    const neighbors = adjacencyList.get(nodeId) || [];
    
    neighbors.forEach(neighborId => {
      if (!visited.has(neighborId)) {
        visited.add(neighborId);
        const newLevel = level + 1;
        levels.set(neighborId, newLevel);
        maxLevel = Math.max(maxLevel, newLevel);
        queue.push({ nodeId: neighborId, level: newLevel });
      }
    });
  }

  // Group nodes by level
  const nodesByLevel = new Map<number, KnowledgeNode[]>();
  for (let i = 0; i <= maxLevel; i++) {
    nodesByLevel.set(i, []);
  }

  nodes.forEach(node => {
    if (node.id !== centerNode.id) {
      const level = levels.get(node.id) || 1;
      const levelNodes = nodesByLevel.get(level) || [];
      levelNodes.push(node);
      nodesByLevel.set(level, levelNodes);
    }
  });

  // Position nodes in concentric circles
  const maxRadius = Math.min(width, height) / 2 - 50;
  
  for (let level = 1; level <= maxLevel; level++) {
    const levelNodes = nodesByLevel.get(level) || [];
    const radius = (level / maxLevel) * maxRadius;
    const angleStep = (2 * Math.PI) / levelNodes.length;
    
    levelNodes.forEach((node, index) => {
      const angle = index * angleStep;
      node.position = {
        x: centerX + radius * Math.cos(angle),
        y: centerY + radius * Math.sin(angle),
      };
    });
  }

  return {
    ...graph,
    nodes,
    viewMode: 'mindmap',
  };
};

/**
 * Apply force-directed network layout to the graph
 */
export const applyNetworkLayout = (
  graph: KnowledgeGraph,
  options: LayoutOptions
): KnowledgeGraph => {
  const { width, height, padding = 50 } = options;
  const nodes = [...graph.nodes];
  const edges = [...graph.edges];

  // Optimized force-directed layout simulation
  const iterations = Math.min(30, Math.max(10, nodes.length * 2)); // Adaptive iterations
  const repulsionStrength = 800;
  const attractionStrength = 0.15;
  const damping = 0.85;

  // Initialize random positions if not set
  nodes.forEach(node => {
    if (!node.position.x || !node.position.y) {
      node.position = {
        x: padding + Math.random() * (width - 2 * padding),
        y: padding + Math.random() * (height - 2 * padding),
      };
    }
  });

  // Add velocity to nodes
  const velocities = new Map<string, { x: number; y: number }>();
  nodes.forEach(node => {
    velocities.set(node.id, { x: 0, y: 0 });
  });

  // Build adjacency list for connected nodes
  const adjacencyList = new Map<string, string[]>();
  nodes.forEach(node => adjacencyList.set(node.id, []));
  edges.forEach(edge => {
    adjacencyList.get(edge.source)?.push(edge.target);
    adjacencyList.get(edge.target)?.push(edge.source);
  });

  // Simulation loop with early convergence detection
  let totalMovement = Infinity;
  const convergenceThreshold = 1.0; // Stop when movement is minimal

  for (let iteration = 0; iteration < iterations && totalMovement > convergenceThreshold; iteration++) {
    // Reset forces
    const forces = new Map<string, { x: number; y: number }>();
    nodes.forEach(node => {
      forces.set(node.id, { x: 0, y: 0 });
    });

    // Optimized repulsion forces with distance cutoff
    const maxRepulsionDistance = 200; // Skip distant nodes
    for (let i = 0; i < nodes.length; i++) {
      for (let j = i + 1; j < nodes.length; j++) {
        const nodeA = nodes[i];
        const nodeB = nodes[j];

        const dx = nodeA.position.x - nodeB.position.x;
        const dy = nodeA.position.y - nodeB.position.y;
        const distanceSquared = dx * dx + dy * dy;

        // Skip distant nodes to reduce computation
        if (distanceSquared > maxRepulsionDistance * maxRepulsionDistance) {
          continue;
        }

        const distance = Math.sqrt(distanceSquared) || 1;
        const force = repulsionStrength / distanceSquared;
        const fx = (dx / distance) * force;
        const fy = (dy / distance) * force;

        const forceA = forces.get(nodeA.id)!;
        const forceB = forces.get(nodeB.id)!;

        forceA.x += fx;
        forceA.y += fy;
        forceB.x -= fx;
        forceB.y -= fy;
      }
    }

    // Attraction forces (connected nodes attract each other)
    edges.forEach(edge => {
      const sourceNode = nodes.find(n => n.id === edge.source);
      const targetNode = nodes.find(n => n.id === edge.target);
      
      if (sourceNode && targetNode) {
        const dx = targetNode.position.x - sourceNode.position.x;
        const dy = targetNode.position.y - sourceNode.position.y;
        const distance = Math.sqrt(dx * dx + dy * dy) || 1;
        
        const force = attractionStrength * distance;
        const fx = (dx / distance) * force;
        const fy = (dy / distance) * force;
        
        const forceSource = forces.get(sourceNode.id)!;
        const forceTarget = forces.get(targetNode.id)!;
        
        forceSource.x += fx;
        forceSource.y += fy;
        forceTarget.x -= fx;
        forceTarget.y -= fy;
      }
    });

    // Update velocities and positions with movement tracking
    totalMovement = 0;
    nodes.forEach(node => {
      const force = forces.get(node.id)!;
      const velocity = velocities.get(node.id)!;

      velocity.x = (velocity.x + force.x) * damping;
      velocity.y = (velocity.y + force.y) * damping;

      const oldX = node.position.x;
      const oldY = node.position.y;

      node.position.x += velocity.x;
      node.position.y += velocity.y;

      // Keep nodes within bounds
      node.position.x = Math.max(padding, Math.min(width - padding, node.position.x));
      node.position.y = Math.max(padding, Math.min(height - padding, node.position.y));

      // Track total movement for convergence detection
      const movement = Math.sqrt((node.position.x - oldX) ** 2 + (node.position.y - oldY) ** 2);
      totalMovement += movement;
    });
  }

  return {
    ...graph,
    nodes,
    viewMode: 'network',
  };
};