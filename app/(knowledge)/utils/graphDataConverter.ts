import { GraphData, Node, Edge, ViewMode } from '../components/KnowledgeGraphSVG';

// Convert Mermaid-style data to SVG graph data
export const convertMermaidToGraphData = (viewMode: ViewMode): GraphData => {
  switch (viewMode) {
    case 'flowchart':
      return getFlowchartData();
    case 'mindmap':
      return getMindmapData();
    case 'network':
      return getNetworkData();
    default:
      return getFlowchartData();
  }
};

const getFlowchartData = (): GraphData => {
  const nodes: Node[] = [
    { id: 'ML', label: 'Machine Learning', group: 'root', size: 25 },
    { id: 'SL', label: 'Supervised Learning', group: 'primary', size: 20 },
    { id: 'UL', label: 'Unsupervised Learning', group: 'primary', size: 20 },
    { id: 'RL', label: 'Reinforcement Learning', group: 'primary', size: 20 },
    
    { id: 'C', label: 'Classification', group: 'secondary', size: 18 },
    { id: 'R', label: 'Regression', group: 'secondary', size: 18 },
    
    { id: 'CL', label: 'Clustering', group: 'secondary', size: 18 },
    { id: 'DR', label: 'Dimensionality Reduction', group: 'secondary', size: 18 },    
    { id: 'QL', label: 'Q-Learning', group: 'secondary', size: 18 },
    { id: 'PG', label: 'Policy Gradient', group: 'secondary', size: 18 },
    
    { id: 'LR', label: 'Logistic Regression', group: 'tertiary', size: 15 },
    { id: 'SVM', label: 'Support Vector Machine', group: 'tertiary', size: 15 },
    { id: 'RF', label: 'Random Forest', group: 'tertiary', size: 15 },
    
    { id: 'LIN', label: 'Linear Regression', group: 'tertiary', size: 15 },
    { id: 'POLY', label: 'Polynomial Regression', group: 'tertiary', size: 15 },
    
    { id: 'KM', label: 'K-Means', group: 'tertiary', size: 15 },
    { id: 'HC', label: 'Hierarchical Clustering', group: 'tertiary', size: 15 },
    
    { id: 'PCA', label: 'Principal Component Analysis', group: 'tertiary', size: 15 },
    { id: 'LDA', label: 'Linear Discriminant Analysis', group: 'tertiary', size: 15 },
  ];

  const edges: Edge[] = [
    { source: 'ML', target: 'SL' },
    { source: 'ML', target: 'UL' },
    { source: 'ML', target: 'RL' },
    
    { source: 'SL', target: 'C' },
    { source: 'SL', target: 'R' },
    
    { source: 'UL', target: 'CL' },
    { source: 'UL', target: 'DR' },    
    { source: 'RL', target: 'QL' },
    { source: 'RL', target: 'PG' },
    
    { source: 'C', target: 'LR' },
    { source: 'C', target: 'SVM' },
    { source: 'C', target: 'RF' },
    
    { source: 'R', target: 'LIN' },
    { source: 'R', target: 'POLY' },
    
    { source: 'CL', target: 'KM' },
    { source: 'CL', target: 'HC' },
    
    { source: 'DR', target: 'PCA' },
    { source: 'DR', target: 'LDA' },
  ];

  return { nodes, edges };
};

const getMindmapData = (): GraphData => {
  const nodes: Node[] = [
    { id: 'ML', label: 'Machine Learning', group: 'root', size: 30 },
    
    // Main branches
    { id: 'SL', label: 'Supervised Learning', group: 'primary', size: 22 },
    { id: 'UL', label: 'Unsupervised Learning', group: 'primary', size: 22 },
    { id: 'RL', label: 'Reinforcement Learning', group: 'primary', size: 22 },
    
    // Supervised Learning branch
    { id: 'C', label: 'Classification', group: 'secondary', size: 18 },
    { id: 'R', label: 'Regression', group: 'secondary', size: 18 },    
    // Classification algorithms
    { id: 'LR', label: 'Logistic Regression', group: 'tertiary', size: 15 },
    { id: 'SVM', label: 'SVM', group: 'tertiary', size: 15 },
    { id: 'RF', label: 'Random Forest', group: 'tertiary', size: 15 },
    { id: 'NN', label: 'Neural Networks', group: 'tertiary', size: 15 },
    
    // Regression algorithms
    { id: 'LIN', label: 'Linear Regression', group: 'tertiary', size: 15 },
    { id: 'POLY', label: 'Polynomial Regression', group: 'tertiary', size: 15 },
    { id: 'RIDGE', label: 'Ridge Regression', group: 'tertiary', size: 15 },
    
    // Unsupervised Learning branch
    { id: 'CL', label: 'Clustering', group: 'secondary', size: 18 },
    { id: 'DR', label: 'Dimensionality Reduction', group: 'secondary', size: 18 },
    
    // Clustering algorithms
    { id: 'KM', label: 'K-Means', group: 'tertiary', size: 15 },
    { id: 'HC', label: 'Hierarchical', group: 'tertiary', size: 15 },
    { id: 'DBSCAN', label: 'DBSCAN', group: 'tertiary', size: 15 },
    
    // Dimensionality Reduction algorithms
    { id: 'PCA', label: 'PCA', group: 'tertiary', size: 15 },
    { id: 'TSNE', label: 't-SNE', group: 'tertiary', size: 15 },
    { id: 'LDA', label: 'LDA', group: 'tertiary', size: 15 },    
    // Reinforcement Learning branch
    { id: 'QL', label: 'Q-Learning', group: 'secondary', size: 18 },
    { id: 'PG', label: 'Policy Gradient', group: 'secondary', size: 18 },
    { id: 'AC', label: 'Actor-Critic', group: 'secondary', size: 18 },
  ];

  const edges: Edge[] = [
    // Main branches
    { source: 'ML', target: 'SL' },
    { source: 'ML', target: 'UL' },
    { source: 'ML', target: 'RL' },
    
    // Supervised Learning
    { source: 'SL', target: 'C' },
    { source: 'SL', target: 'R' },
    
    // Classification
    { source: 'C', target: 'LR' },
    { source: 'C', target: 'SVM' },
    { source: 'C', target: 'RF' },
    { source: 'C', target: 'NN' },
    
    // Regression
    { source: 'R', target: 'LIN' },
    { source: 'R', target: 'POLY' },
    { source: 'R', target: 'RIDGE' },
    
    // Unsupervised Learning
    { source: 'UL', target: 'CL' },
    { source: 'UL', target: 'DR' },
    
    // Clustering
    { source: 'CL', target: 'KM' },
    { source: 'CL', target: 'HC' },
    { source: 'CL', target: 'DBSCAN' },
    
    // Dimensionality Reduction
    { source: 'DR', target: 'PCA' },
    { source: 'DR', target: 'TSNE' },
    { source: 'DR', target: 'LDA' },
    
    // Reinforcement Learning
    { source: 'RL', target: 'QL' },
    { source: 'RL', target: 'PG' },
    { source: 'RL', target: 'AC' },
  ];

  return { nodes, edges };
};const getNetworkData = (): GraphData => {
  const nodes: Node[] = [
    // Core ML Concepts
    { id: 'ML', label: 'Machine Learning', group: 'root', size: 25 },
    { id: 'DATA', label: 'Data Processing', group: 'root', size: 25 },
    { id: 'EVAL', label: 'Model Evaluation', group: 'root', size: 25 },
    
    // Learning Types
    { id: 'SL', label: 'Supervised Learning', group: 'primary', size: 20 },
    { id: 'UL', label: 'Unsupervised Learning', group: 'primary', size: 20 },
    { id: 'RL', label: 'Reinforcement Learning', group: 'primary', size: 20 },
    
    // Algorithms
    { id: 'LR', label: 'Logistic Regression', group: 'tertiary', size: 15 },
    { id: 'SVM', label: 'SVM', group: 'tertiary', size: 15 },
    { id: 'RF', label: 'Random Forest', group: 'tertiary', size: 15 },
    { id: 'KM', label: 'K-Means', group: 'tertiary', size: 15 },
    { id: 'PCA', label: 'PCA', group: 'tertiary', size: 15 },
    { id: 'QL', label: 'Q-Learning', group: 'tertiary', size: 15 },
    
    // Applications
    { id: 'NLP', label: 'Natural Language Processing', group: 'default', size: 18 },
    { id: 'CV_APP', label: 'Computer Vision', group: 'default', size: 18 },
    { id: 'RECSYS', label: 'Recommendation Systems', group: 'default', size: 18 },
  ];

  const edges: Edge[] = [
    // Core connections
    { source: 'ML', target: 'SL' },
    { source: 'ML', target: 'UL' },
    { source: 'ML', target: 'RL' },
    { source: 'ML', target: 'DATA' },
    { source: 'ML', target: 'EVAL' },
    
    // Algorithm connections
    { source: 'SL', target: 'LR' },
    { source: 'SL', target: 'SVM' },
    { source: 'SL', target: 'RF' },
    { source: 'UL', target: 'KM' },
    { source: 'UL', target: 'PCA' },
    { source: 'RL', target: 'QL' },
    
    // Application connections
    { source: 'SL', target: 'NLP' },
    { source: 'SL', target: 'CV_APP' },
    { source: 'UL', target: 'RECSYS' },
    { source: 'RF', target: 'NLP' },
    { source: 'KM', target: 'RECSYS' },
    { source: 'SVM', target: 'CV_APP' },
  ];

  return { nodes, edges };
};// Calculate graph statistics
export const calculateGraphStats = (data: GraphData) => {
  const concepts = data.nodes.length;
  const connections = data.edges.length;
  
  // Calculate clusters using a simple connected components algorithm
  const clusters = calculateConnectedComponents(data);
  
  // Calculate levels (maximum depth from root nodes)
  const levels = calculateMaxDepth(data);
  
  return {
    concepts,
    connections,
    clusters,
    levels,
  };
};

const calculateConnectedComponents = (data: GraphData): number => {
  const visited = new Set<string>();
  let componentCount = 0;
  
  const dfs = (nodeId: string) => {
    if (visited.has(nodeId)) return;
    visited.add(nodeId);
    
    // Find all connected nodes
    data.edges.forEach(edge => {
      if (edge.source === nodeId && !visited.has(edge.target)) {
        dfs(edge.target);
      }
      if (edge.target === nodeId && !visited.has(edge.source)) {
        dfs(edge.source);
      }
    });
  };
  
  data.nodes.forEach(node => {
    if (!visited.has(node.id)) {
      dfs(node.id);
      componentCount++;
    }
  });
  
  return componentCount;
};const calculateMaxDepth = (data: GraphData): number => {
  // Find root nodes (nodes that are not targets in any edge)
  const targetNodeIds = new Set(data.edges.map(edge => edge.target));
  const rootNodeIds = data.nodes
    .map(node => node.id)
    .filter(id => !targetNodeIds.has(id));
  
  if (rootNodeIds.length === 0) return 1;
  
  let maxDepth = 0;
  
  const calculateDepth = (nodeId: string, currentDepth: number, visited: Set<string>): number => {
    if (visited.has(nodeId)) return currentDepth;
    visited.add(nodeId);
    
    let depth = currentDepth;
    
    // Find children
    const childEdges = data.edges.filter(edge => edge.source === nodeId);
    
    childEdges.forEach(edge => {
      const childDepth = calculateDepth(edge.target, currentDepth + 1, new Set(visited));
      depth = Math.max(depth, childDepth);
    });
    
    return depth;
  };
  
  rootNodeIds.forEach(rootId => {
    const depth = calculateDepth(rootId, 1, new Set());
    maxDepth = Math.max(maxDepth, depth);
  });
  
  return maxDepth;
};

export default {
  convertMermaidToGraphData,
  calculateGraphStats,
};