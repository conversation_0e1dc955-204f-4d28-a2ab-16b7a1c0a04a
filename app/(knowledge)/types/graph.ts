// Knowledge Graph Types for LearniScan
export interface KnowledgeNode {
  id: string;
  label: string;
  type: 'concept' | 'topic' | 'subtopic' | 'detail';
  position: { x: number; y: number };
  connections: string[]; // Array of connected node IDs
  metadata: {
    level: number;
    cluster: string;
    importance: number; // 0-1 scale
    color: string;
    size: number; // Visual size multiplier
    confidence: number; // 0-1 scale
    category?: string;
    description?: string;
    tags?: string[];
  };
}

export interface KnowledgeEdge {
  id: string;
  source: string; // Node ID
  target: string; // Node ID
  type: 'related' | 'prerequisite' | 'contains' | 'similar';
  strength: number; // 0-1 scale
  metadata: {
    color: string;
    width: number;
    style: 'solid' | 'dashed' | 'dotted';
    animated?: boolean;
  };
}

export interface KnowledgeGraph {
  nodes: KnowledgeNode[];
  edges: KnowledgeEdge[];
  viewMode: 'flowchart' | 'mindmap' | 'network';
  bounds: { width: number; height: number };
  metadata: {
    title: string;
    description?: string;
    created: Date;
    updated: Date;
    version: string;
  };
}

export interface GraphViewport {
  scale: number;
  offset: { x: number; y: number };
  bounds: { 
    minScale: number; 
    maxScale: number; 
    width: number; 
    height: number; 
  };
}

export interface GraphInteraction {
  selectedNodes: string[];
  hoveredNode: string | null;
  draggedNode: string | null;
  isGestureActive: boolean;
  lastTapTime: number;
  contextMenuNode: string | null;
}

export interface GraphLayout {
  type: 'force' | 'hierarchical' | 'radial' | 'grid';
  parameters: {
    nodeSpacing: number;
    levelSpacing: number;
    centerForce: number;
    repulsionForce: number;
    linkDistance: number;
    iterations: number;
  };
}

export interface GraphPerformance {
  renderMode: 'full' | 'simplified' | 'minimal';
  visibleNodes: Set<string>;
  visibleEdges: Set<string>;
  lastRenderTime: number;
  frameRate: number;
}

// Candy color palette for LearniScan theme
export const CANDY_COLORS = {
  pink: '#FF6B9D',
  purple: '#A855F7',
  blue: '#3B82F6',
  cyan: '#06B6D4',
  emerald: '#10B981',
  amber: '#F59E0B',
  rose: '#EC4899',
  violet: '#8B5CF6',
  indigo: '#6366F1',
  teal: '#14B8A6',
} as const;

export type CandyColor = keyof typeof CANDY_COLORS;

// Node size constants for touch optimization
export const NODE_SIZES = {
  minimum: 44, // Minimum touch target (accessibility)
  small: 32,
  medium: 48,
  large: 64,
  extraLarge: 80,
} as const;

// Gesture thresholds for mobile optimization
export const GESTURE_THRESHOLDS = {
  tapTimeout: 300, // ms
  doubleTapTimeout: 500, // ms
  longPressTimeout: 800, // ms
  panThreshold: 10, // pixels
  pinchThreshold: 0.1, // scale difference
  velocityThreshold: 100, // pixels/second
} as const;

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  maxVisibleNodes: 100,
  maxVisibleEdges: 200,
  simplificationScale: 0.5,
  minimalScale: 0.3,
  targetFrameRate: 60,
  maxRenderTime: 16, // ms (60fps)
} as const;

// Layout algorithms configuration
export const LAYOUT_CONFIGS = {
  flowchart: {
    type: 'hierarchical' as const,
    parameters: {
      nodeSpacing: 120,
      levelSpacing: 100,
      centerForce: 0.1,
      repulsionForce: 300,
      linkDistance: 80,
      iterations: 50,
    },
  },
  mindmap: {
    type: 'radial' as const,
    parameters: {
      nodeSpacing: 100,
      levelSpacing: 80,
      centerForce: 0.2,
      repulsionForce: 200,
      linkDistance: 60,
      iterations: 100,
    },
  },
  network: {
    type: 'force' as const,
    parameters: {
      nodeSpacing: 80,
      levelSpacing: 60,
      centerForce: 0.05,
      repulsionForce: 400,
      linkDistance: 100,
      iterations: 150,
    },
  },
} as const;

// Utility functions
export const getNodeColor = (type: KnowledgeNode['type'], category?: string): string => {
  if (category && category in CANDY_COLORS) {
    return CANDY_COLORS[category as CandyColor];
  }

  // Updated color scheme for emerald-cyan theme
  switch (type) {
    case 'concept':
      return CANDY_COLORS.emerald;  // Primary: emerald
    case 'topic':
      return CANDY_COLORS.cyan;     // Secondary: cyan
    case 'subtopic':
      return CANDY_COLORS.teal;     // Tertiary: teal
    case 'detail':
      return CANDY_COLORS.violet;   // Accent: violet
    default:
      return CANDY_COLORS.emerald;
  }
};

export const getNodeSize = (importance: number, baseSize: number = NODE_SIZES.medium): number => {
  return Math.max(NODE_SIZES.minimum, baseSize * (0.7 + importance * 0.6));
};

export const getEdgeWidth = (strength: number, baseWidth: number = 2): number => {
  return Math.max(1, baseWidth * (0.5 + strength * 1.5));
};

export const isNodeVisible = (
  node: KnowledgeNode, 
  viewport: GraphViewport, 
  screenDimensions: { width: number; height: number }
): boolean => {
  const nodeScreenX = node.position.x * viewport.scale + viewport.offset.x;
  const nodeScreenY = node.position.y * viewport.scale + viewport.offset.y;
  const nodeSize = getNodeSize(node.metadata.importance) * viewport.scale;
  
  return (
    nodeScreenX + nodeSize >= -50 &&
    nodeScreenX - nodeSize <= screenDimensions.width + 50 &&
    nodeScreenY + nodeSize >= -50 &&
    nodeScreenY - nodeSize <= screenDimensions.height + 50
  );
};

export const calculateDistance = (
  point1: { x: number; y: number }, 
  point2: { x: number; y: number }
): number => {
  const dx = point1.x - point2.x;
  const dy = point1.y - point2.y;
  return Math.sqrt(dx * dx + dy * dy);
};

export const isPointInNode = (
  point: { x: number; y: number },
  node: KnowledgeNode,
  viewport: GraphViewport
): boolean => {
  const nodeScreenX = node.position.x * viewport.scale + viewport.offset.x;
  const nodeScreenY = node.position.y * viewport.scale + viewport.offset.y;
  const nodeRadius = getNodeSize(node.metadata.importance) * viewport.scale / 2;
  
  const distance = calculateDistance(point, { x: nodeScreenX, y: nodeScreenY });
  return distance <= nodeRadius;
};
