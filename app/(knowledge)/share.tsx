import React, { useState } from 'react';
import { ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Clipboard from 'expo-clipboard';
import { ArrowLeft, Download, Settings, Lightbulb, BookOpen, Calendar, Share2, MessageCircle, Send, Users, Globe } from 'lucide-react-native';

// UI Components
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { Button, ButtonText } from '@/components/ui/button';
import { Badge, BadgeText } from '@/components/ui/badge';
import { Pressable } from '@/components/ui/pressable';
import { Input, InputField } from '@/components/ui/input';
import { Textarea, TextareaInput } from '@/components/ui/textarea';
import { LinearGradient } from 'expo-linear-gradient';

export const Share: React.FC = () => {
  // State Management (Rule #11 - Zod Schema)
  const [cardContent, setCardContent] = useState({
    title: 'What is Supervised Learning?',
    description: 'Learning with labeled examples to make predictions on new, unseen data. Essential for classification and regression tasks.',
    category: 'Machine Learning'
  });
  
  const [selectedTheme, setSelectedTheme] = useState<'pink-purple' | 'blue-cyan' | 'green-yellow' | 'orange-red'>('pink-purple');
  const [selectedFormat, setSelectedFormat] = useState<'square' | 'story' | 'wide'>('square');
  const [selectedTemplate, setSelectedTemplate] = useState<'minimal' | 'detailed' | 'modern'>('minimal');
  const [selectedFont, setSelectedFont] = useState<'default' | 'bold' | 'elegant'>('default');
  const [showLogo, setShowLogo] = useState(true);
  const [showDate, setShowDate] = useState(true);
  const [exportQuality, setExportQuality] = useState<'standard' | 'high' | 'ultra'>('high');
  const [copySuccess, setCopySuccess] = useState(false);
  const [exportFormat, setExportFormat] = useState<'png' | 'jpg' | 'pdf' | 'svg'>('png');
  const [isExporting, setIsExporting] = useState(false);

  const shareUrl = 'https://learniscan.app/card/ml-supervised-learning';

  const handleCopyLink = async () => {
    try {
      await Clipboard.setStringAsync(shareUrl);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleSocialShare = async (platform: string) => {
    const title = cardContent.title;
    const description = cardContent.description;
    const url = shareUrl;

    switch (platform) {
      case 'instagram':
        // Instagram doesn't support direct URL sharing, copy content to clipboard
        await Clipboard.setStringAsync(`${title}\n\n${description}\n\n${url}`);
        // Show notification that content was copied
        break;
      case 'twitter':
        // In a real app, you would use Linking.openURL with Twitter intent
        await Clipboard.setStringAsync(`${title} - ${description} ${url}`);
        break;
      case 'linkedin':
        // LinkedIn sharing would use their API or intent
        await Clipboard.setStringAsync(`${title}\n\n${description}\n\n${url}`);
        break;
      case 'facebook':
        // Facebook sharing would use their SDK or intent
        await Clipboard.setStringAsync(`${title}\n\n${description}\n\n${url}`);
        break;
      case 'whatsapp':
        // WhatsApp sharing intent
        await Clipboard.setStringAsync(`${title}\n\n${description}\n\n${url}`);
        break;
      case 'telegram':
        // Telegram sharing intent
        await Clipboard.setStringAsync(`${title}\n\n${description}\n\n${url}`);
        break;
    }
  };

  const handleExport = async (format: string) => {
    setIsExporting(true);
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // In a real app, this would generate and download the file
      await Clipboard.setStringAsync(`Exported ${cardContent.title} as ${format.toUpperCase()}`);

      setIsExporting(false);
    } catch (error) {
      console.error('Export failed:', error);
      setIsExporting(false);
    }
  };

  const generateShareableLink = (type: 'public' | 'private' | 'embed') => {
    const baseUrl = 'https://learniscan.app/card/ml-supervised-learning';
    switch (type) {
      case 'public':
        return `${baseUrl}?share=public`;
      case 'private':
        return `${baseUrl}?share=private&token=abc123`;
      case 'embed':
        return `${baseUrl}/embed`;
      default:
        return baseUrl;
    }
  };

  // Theme helper functions
  const getThemeGradientColors = (theme: typeof selectedTheme): [string, string] => {
    switch (theme) {
      case 'pink-purple':
        return ['rgba(255, 107, 157, 0.2)', 'rgba(168, 85, 247, 0.2)'];
      case 'blue-cyan':
        return ['rgba(59, 130, 246, 0.2)', 'rgba(6, 182, 212, 0.2)'];
      case 'green-yellow':
        return ['rgba(16, 185, 129, 0.2)', 'rgba(245, 158, 11, 0.2)'];
      case 'orange-red':
        return ['rgba(249, 115, 22, 0.2)', 'rgba(239, 68, 68, 0.2)'];
      default:
        return ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)'];
    }
  };

  const getThemeShadowColor = (theme: typeof selectedTheme) => {
    switch (theme) {
      case 'pink-purple': return '#FF6B9D';
      case 'blue-cyan': return '#3B82F6';
      case 'green-yellow': return '#10B981';
      case 'orange-red': return '#F97316';
      default: return '#FF6B9D';
    }
  };

  const getThemeBadgeColor = (theme: typeof selectedTheme): 'candyPink' | 'candyPurple' | 'candyBlue' => {
    switch (theme) {
      case 'pink-purple': return 'candyPink';
      case 'blue-cyan': return 'candyBlue';
      case 'green-yellow': return 'candyPurple';
      case 'orange-red': return 'candyPink';
      default: return 'candyPink';
    }
  };

  const getFormatDimensions = (format: typeof selectedFormat) => {
    switch (format) {
      case 'square':
        return { aspectRatio: 1, maxHeight: 300 };
      case 'story':
        return { aspectRatio: 9/16, maxHeight: 400 };
      case 'wide':
        return { aspectRatio: 16/9, maxHeight: 200 };
      default:
        return { aspectRatio: 1, maxHeight: 300 };
    }
  };

  return (
    <LinearGradient
      colors={[
        'rgb(255, 107, 157)', // candy-pink
        'rgb(168, 85, 247)',  // candy-purple
        'rgb(59, 130, 246)'   // candy-blue
      ]}
      locations={[0, 0.6, 1]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView className="flex-1">
        <VStack className="flex-1">
          {/* Header - Fixed */}
          <VStack className="px-4 py-2">
            <HStack className="items-center justify-between mb-6">
              <HStack className="items-center space-x-4">
                <Pressable className="w-12 h-12 bg-white/10 border border-white/20 backdrop-blur-md rounded-xl items-center justify-center">
                  <ArrowLeft size={24} color="white" />
                </Pressable>
                <VStack>
                  <Text className="text-2xl font-bold text-white leading-8">Share Knowledge Card</Text>
                  <Text className="text-white/60 text-base leading-6">Spread the learning</Text>
                </VStack>
              </HStack>

              <HStack className="space-x-3">
                <Pressable className="w-12 h-12 bg-white/10 border border-white/20 backdrop-blur-md rounded-xl items-center justify-center">
                  <Download size={24} color="white" />
                </Pressable>
                <Pressable className="w-12 h-12 bg-white/10 border border-white/20 backdrop-blur-md rounded-xl items-center justify-center">
                  <Settings size={24} color="white" />
                </Pressable>
              </HStack>
            </HStack>
          </VStack>

          {/* Scrollable Content */}
          <ScrollView className="flex-1 px-4" showsVerticalScrollIndicator={false}>
            <VStack className="space-y-6 pb-6">
              {/* Preview Section */}
              <VStack className="space-y-4">
                <Text className="text-xl font-bold text-white">Preview</Text>
                
                {/* Share Card Preview */}
                <Box
                  className="border border-white/20 backdrop-blur-md rounded-2xl overflow-hidden relative"
                  style={{
                    shadowColor: getThemeShadowColor(selectedTheme),
                    shadowOffset: { width: 0, height: 20 },
                    shadowOpacity: 0.3,
                    shadowRadius: 40,
                    elevation: 20,
                    aspectRatio: getFormatDimensions(selectedFormat).aspectRatio,
                    maxHeight: getFormatDimensions(selectedFormat).maxHeight,
                  }}
                >
                  {/* Format Indicator */}
                  <Box className="absolute top-3 right-3 bg-black/30 backdrop-blur-sm rounded-lg px-2 py-1">
                    <Text className="text-white/80 text-xs font-medium">
                      {selectedFormat.charAt(0).toUpperCase() + selectedFormat.slice(1)}
                    </Text>
                  </Box>
                  <LinearGradient
                    colors={getThemeGradientColors(selectedTheme)}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={{ padding: 32 }}
                  >
                  <VStack className="items-center space-y-6">
                    {/* Icon and Category - Template Dependent */}
                    {selectedTemplate === 'minimal' ? (
                      <VStack className="items-center space-y-3">
                        <Badge action={getThemeBadgeColor(selectedTheme)} className="px-3 py-1 rounded-full">
                          <BadgeText className="text-xs text-white">{cardContent.category}</BadgeText>
                        </Badge>
                      </VStack>
                    ) : (
                      <VStack className="items-center space-y-4">
                        <Box className="w-16 h-16 bg-white/20 rounded-full items-center justify-center">
                          <Lightbulb size={32} color="white" />
                        </Box>
                        <Badge action={getThemeBadgeColor(selectedTheme)} className="px-3 py-1 rounded-full">
                          <BadgeText className="text-xs text-white">{cardContent.category}</BadgeText>
                        </Badge>
                      </VStack>
                    )}

                    {/* Content - Template Dependent */}
                    <VStack className="items-center space-y-4">
                      <Text className={`text-white text-center leading-8 ${
                        selectedTemplate === 'minimal' ? 'text-xl' :
                        selectedTemplate === 'modern' ? 'text-3xl' : 'text-2xl'
                      } ${
                        selectedFont === 'bold' ? 'font-bold' :
                        selectedFont === 'elegant' ? 'font-light' : 'font-semibold'
                      }`}>
                        {cardContent.title}
                      </Text>
                      <Text className={`text-white/80 text-center leading-6 px-4 ${
                        selectedTemplate === 'minimal' ? 'text-base' :
                        selectedTemplate === 'modern' ? 'text-xl' : 'text-lg'
                      } ${
                        selectedFont === 'bold' ? 'font-medium' :
                        selectedFont === 'elegant' ? 'font-light' : 'font-normal'
                      }`}>
                        {cardContent.description}
                      </Text>

                      {/* Additional content for detailed template */}
                      {selectedTemplate === 'detailed' && (
                        <VStack className="items-center space-y-2 pt-2">
                          <HStack className="items-center space-x-4">
                            <Badge action="candyPurple" variant="outline" className="px-2 py-1">
                              <BadgeText className="text-xs text-white">Beginner</BadgeText>
                            </Badge>
                            <Badge action="candyBlue" variant="outline" className="px-2 py-1">
                              <BadgeText className="text-xs text-white">5 min read</BadgeText>
                            </Badge>
                          </HStack>
                        </VStack>
                      )}
                    </VStack>

                    {/* Footer - Conditional */}
                    {(showLogo || showDate) && (
                      <HStack className="items-center space-x-6 pt-4">
                        {showLogo && (
                          <HStack className="items-center space-x-2">
                            <BookOpen size={16} color="rgba(255,255,255,0.6)" />
                            <Text className="text-white/60 text-sm">LearniScan</Text>
                          </HStack>
                        )}
                        {showDate && (
                          <HStack className="items-center space-x-2">
                            <Calendar size={16} color="rgba(255,255,255,0.6)" />
                            <Text className="text-white/60 text-sm">Today</Text>
                          </HStack>
                        )}
                      </HStack>
                    )}
                    </VStack>
                  </LinearGradient>
                </Box>

                {/* Format Options */}
                <Box className="bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-4">
                  <Text className="text-lg font-semibold text-white mb-4">Format</Text>
                  <HStack className="space-x-3">
                    {[
                      { key: 'square', label: 'Square', size: '1080x1080', icon: '⬜' },
                      { key: 'story', label: 'Story', size: '1080x1920', icon: '📱' },
                      { key: 'wide', label: 'Wide', size: '1200x630', icon: '🖥️' }
                    ].map((format) => (
                      <Pressable
                        key={format.key}
                        className={`flex-1 p-4 rounded-lg border-2 ${
                          selectedFormat === format.key 
                            ? 'bg-candyPink/20 border-candyPink shadow-lg' 
                            : 'bg-white/5 border-white/20'
                        }`}
                        onPress={() => setSelectedFormat(format.key as any)}
                      >
                        <VStack className="items-center space-y-2">
                          <Text className="text-2xl">{format.icon}</Text>
                          <Text className="text-white text-sm font-medium">{format.label}</Text>
                          <Text className="text-white/60 text-xs">{format.size}</Text>
                        </VStack>
                      </Pressable>
                    ))}
                  </HStack>
                </Box>
              </VStack>

              {/* Customization Section */}
              <VStack className="space-y-4">
                <Text className="text-xl font-bold text-white">Customize</Text>
                
                {/* Content Editing */}
                <Box className="bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-6">
                  <Text className="text-lg font-semibold text-white mb-4">Edit Content</Text>
                  <VStack className="space-y-4">
                    <VStack className="space-y-2">
                      <Text className="text-white text-sm font-medium">Title</Text>
                      <Input variant="glass" className="bg-white/10 border border-white/20">
                        <InputField
                          value={cardContent.title}
                          onChangeText={(text: string) => setCardContent(prev => ({ ...prev, title: text }))}
                          placeholder="Enter title..."
                          placeholderTextColor="rgba(255,255,255,0.5)"
                          className="text-white"
                        />
                      </Input>
                    </VStack>
                    <VStack className="space-y-2">
                      <Text className="text-white text-sm font-medium">Description</Text>
                      <Textarea variant="glass" className="bg-white/10 border border-white/20 min-h-[80px]">
                        <TextareaInput
                          value={cardContent.description}
                          onChangeText={(text: string) => setCardContent(prev => ({ ...prev, description: text }))}
                          placeholder="Enter description..."
                          placeholderTextColor="rgba(255,255,255,0.5)"
                          className="text-white"
                          multiline
                        />
                      </Textarea>
                    </VStack>
                  </VStack>
                </Box>

                {/* Color Theme Selection */}
                <Box className="bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-6">
                  <Text className="text-lg font-semibold text-white mb-4">Color Theme</Text>
                  <HStack className="space-x-4">
                    {[
                      { key: 'pink-purple', colors: ['#FF6B9D', '#A855F7'] as [string, string], label: 'Pink Purple' },
                      { key: 'blue-cyan', colors: ['#3B82F6', '#06B6D4'] as [string, string], label: 'Blue Cyan' },
                      { key: 'green-yellow', colors: ['#10B981', '#F59E0B'] as [string, string], label: 'Green Yellow' },
                      { key: 'orange-red', colors: ['#F97316', '#EF4444'] as [string, string], label: 'Orange Red' }
                    ].map((theme) => (
                      <Pressable
                        key={theme.key}
                        className={`w-12 h-12 rounded-full border-3 overflow-hidden ${
                          selectedTheme === theme.key
                            ? 'border-white shadow-lg'
                            : 'border-white/30'
                        }`}
                        onPress={() => setSelectedTheme(theme.key as any)}
                      >
                        <LinearGradient
                          colors={theme.colors}
                          start={{ x: 0, y: 0 }}
                          end={{ x: 1, y: 1 }}
                          style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: 24,
                          }}
                        />
                      </Pressable>
                    ))}
                  </HStack>
                </Box>

                {/* Category Selection */}
                <Box className="bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-6">
                  <Text className="text-lg font-semibold text-white mb-4">Category</Text>
                  <VStack className="space-y-3">
                    {[
                      { key: 'Machine Learning', icon: '🤖', color: 'candyPink' },
                      { key: 'Data Science', icon: '📊', color: 'candyPurple' },
                      { key: 'AI Fundamentals', icon: '🧠', color: 'candyBlue' },
                      { key: 'Deep Learning', icon: '🔬', color: 'candyPink' }
                    ].map((category) => (
                      <Pressable
                        key={category.key}
                        className={`p-3 rounded-lg border-2 ${
                          cardContent.category === category.key
                            ? 'bg-candyPink/20 border-candyPink'
                            : 'bg-white/5 border-white/20'
                        }`}
                        onPress={() => setCardContent(prev => ({ ...prev, category: category.key }))}
                      >
                        <HStack className="items-center space-x-3">
                          <Text className="text-2xl">{category.icon}</Text>
                          <Text className="text-white font-medium">{category.key}</Text>
                        </HStack>
                      </Pressable>
                    ))}
                  </VStack>
                </Box>

                {/* Template Selection */}
                <Box className="bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-6">
                  <Text className="text-lg font-semibold text-white mb-4">Template Style</Text>
                  <HStack className="space-x-3">
                    {[
                      { key: 'minimal', label: 'Minimal', description: 'Clean & Simple' },
                      { key: 'detailed', label: 'Detailed', description: 'Rich Content' },
                      { key: 'modern', label: 'Modern', description: 'Stylish Design' }
                    ].map((template) => (
                      <Pressable
                        key={template.key}
                        className={`flex-1 p-4 rounded-lg border-2 ${
                          selectedTemplate === template.key
                            ? 'bg-candyPurple/20 border-candyPurple shadow-lg'
                            : 'bg-white/5 border-white/20'
                        }`}
                        onPress={() => setSelectedTemplate(template.key as any)}
                      >
                        <VStack className="items-center space-y-2">
                          <Text className="text-white text-sm font-medium">{template.label}</Text>
                          <Text className="text-white/60 text-xs text-center">{template.description}</Text>
                        </VStack>
                      </Pressable>
                    ))}
                  </HStack>
                </Box>

                {/* Font & Typography */}
                <Box className="bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-6">
                  <Text className="text-lg font-semibold text-white mb-4">Typography</Text>
                  <VStack className="space-y-4">
                    {/* Font Style */}
                    <VStack className="space-y-2">
                      <Text className="text-white font-medium">Font Style</Text>
                      <HStack className="space-x-3">
                        {[
                          { key: 'default', label: 'Default', preview: 'Aa' },
                          { key: 'bold', label: 'Bold', preview: 'Aa' },
                          { key: 'elegant', label: 'Elegant', preview: 'Aa' }
                        ].map((font) => (
                          <Pressable
                            key={font.key}
                            className={`flex-1 p-3 rounded-lg border-2 ${
                              selectedFont === font.key
                                ? 'bg-candyBlue/20 border-candyBlue'
                                : 'bg-white/5 border-white/20'
                            }`}
                            onPress={() => setSelectedFont(font.key as any)}
                          >
                            <VStack className="items-center space-y-1">
                              <Text className={`text-white text-lg ${
                                font.key === 'bold' ? 'font-bold' :
                                font.key === 'elegant' ? 'font-light' : 'font-normal'
                              }`}>
                                {font.preview}
                              </Text>
                              <Text className="text-white/70 text-xs">{font.label}</Text>
                            </VStack>
                          </Pressable>
                        ))}
                      </HStack>
                    </VStack>

                    {/* Text Size */}
                    <VStack className="space-y-2">
                      <Text className="text-white font-medium">Text Size</Text>
                      <HStack className="space-x-2">
                        {['Small', 'Medium', 'Large'].map((size) => (
                          <Pressable
                            key={size}
                            className={`flex-1 py-2 px-3 rounded-lg ${
                              size === 'Medium'
                                ? 'bg-candyPink/20 border border-candyPink'
                                : 'bg-white/5 border border-white/20'
                            }`}
                          >
                            <Text className="text-white text-sm text-center">{size}</Text>
                          </Pressable>
                        ))}
                      </HStack>
                    </VStack>
                  </VStack>
                </Box>

                {/* Layout & Spacing */}
                <Box className="bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-6">
                  <Text className="text-lg font-semibold text-white mb-4">Layout & Spacing</Text>
                  <VStack className="space-y-4">
                    {/* Alignment */}
                    <VStack className="space-y-2">
                      <Text className="text-white font-medium">Text Alignment</Text>
                      <HStack className="space-x-3">
                        {[
                          { key: 'left', icon: '⬅️', label: 'Left' },
                          { key: 'center', icon: '↔️', label: 'Center' },
                          { key: 'right', icon: '➡️', label: 'Right' }
                        ].map((align) => (
                          <Pressable
                            key={align.key}
                            className={`flex-1 p-3 rounded-lg border-2 ${
                              align.key === 'center'
                                ? 'bg-candyPurple/20 border-candyPurple'
                                : 'bg-white/5 border-white/20'
                            }`}
                          >
                            <VStack className="items-center space-y-1">
                              <Text className="text-lg">{align.icon}</Text>
                              <Text className="text-white/70 text-xs">{align.label}</Text>
                            </VStack>
                          </Pressable>
                        ))}
                      </HStack>
                    </VStack>

                    {/* Padding */}
                    <VStack className="space-y-2">
                      <Text className="text-white font-medium">Card Padding</Text>
                      <HStack className="space-x-2">
                        {['Compact', 'Normal', 'Spacious'].map((padding) => (
                          <Pressable
                            key={padding}
                            className={`flex-1 py-2 px-3 rounded-lg ${
                              padding === 'Normal'
                                ? 'bg-candyBlue/20 border border-candyBlue'
                                : 'bg-white/5 border border-white/20'
                            }`}
                          >
                            <Text className="text-white text-sm text-center">{padding}</Text>
                          </Pressable>
                        ))}
                      </HStack>
                    </VStack>
                  </VStack>
                </Box>

                {/* Advanced Options */}
                <Box className="bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-6">
                  <Text className="text-lg font-semibold text-white mb-4">Advanced Options</Text>
                  <VStack className="space-y-4">
                    {/* Logo Toggle */}
                    <HStack className="items-center justify-between">
                      <VStack>
                        <Text className="text-white font-medium">Show Logo</Text>
                        <Text className="text-white/60 text-sm">Display LearniScan branding</Text>
                      </VStack>
                      <Pressable
                        className={`w-12 h-6 rounded-full ${
                          showLogo ? 'bg-candyPink' : 'bg-white/20'
                        } items-center justify-center`}
                        onPress={() => setShowLogo(!showLogo)}
                      >
                        <Box
                          className={`w-5 h-5 bg-white rounded-full transition-all ${
                            showLogo ? 'ml-auto' : 'mr-auto'
                          }`}
                        />
                      </Pressable>
                    </HStack>

                    {/* Date Toggle */}
                    <HStack className="items-center justify-between">
                      <VStack>
                        <Text className="text-white font-medium">Show Date</Text>
                        <Text className="text-white/60 text-sm">Display creation date</Text>
                      </VStack>
                      <Pressable
                        className={`w-12 h-6 rounded-full ${
                          showDate ? 'bg-candyPink' : 'bg-white/20'
                        } items-center justify-center`}
                        onPress={() => setShowDate(!showDate)}
                      >
                        <Box
                          className={`w-5 h-5 bg-white rounded-full transition-all ${
                            showDate ? 'ml-auto' : 'mr-auto'
                          }`}
                        />
                      </Pressable>
                    </HStack>

                    {/* Quality Setting */}
                    <VStack className="space-y-2">
                      <Text className="text-white font-medium">Export Quality</Text>
                      <HStack className="space-x-2">
                        {(['standard', 'high', 'ultra'] as const).map((quality) => (
                          <Pressable
                            key={quality}
                            className={`flex-1 py-2 px-3 rounded-lg ${
                              exportQuality === quality
                                ? 'bg-candyBlue/20 border border-candyBlue'
                                : 'bg-white/5 border border-white/20'
                            }`}
                            onPress={() => setExportQuality(quality)}
                          >
                            <Text className="text-white text-sm text-center capitalize">{quality}</Text>
                          </Pressable>
                        ))}
                      </HStack>
                    </VStack>

                    {/* Background Effects */}
                    <VStack className="space-y-2">
                      <Text className="text-white font-medium">Background Effects</Text>
                      <HStack className="space-x-2">
                        {['None', 'Blur', 'Glow'].map((effect) => (
                          <Pressable
                            key={effect}
                            className={`flex-1 py-2 px-3 rounded-lg ${
                              effect === 'Glow'
                                ? 'bg-candyPurple/20 border border-candyPurple'
                                : 'bg-white/5 border border-white/20'
                            }`}
                          >
                            <Text className="text-white text-sm text-center">{effect}</Text>
                          </Pressable>
                        ))}
                      </HStack>
                    </VStack>

                    {/* Border Style */}
                    <VStack className="space-y-2">
                      <Text className="text-white font-medium">Border Style</Text>
                      <HStack className="space-x-2">
                        {['None', 'Subtle', 'Bold'].map((border) => (
                          <Pressable
                            key={border}
                            className={`flex-1 py-2 px-3 rounded-lg ${
                              border === 'Subtle'
                                ? 'bg-candyPink/20 border border-candyPink'
                                : 'bg-white/5 border border-white/20'
                            }`}
                          >
                            <Text className="text-white text-sm text-center">{border}</Text>
                          </Pressable>
                        ))}
                      </HStack>
                    </VStack>
                  </VStack>
                </Box>

                {/* Advanced Link Sharing & Export */}
                <Box className="bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-6">
                  <Text className="text-lg font-semibold text-white mb-4">Link Sharing & Export</Text>

                  {/* Link Types */}
                  <VStack className="space-y-4 mb-6">
                    <Text className="text-white/70 text-sm font-medium">Share Links</Text>
                    {[
                      { type: 'public', label: 'Public Link', description: 'Anyone can view', icon: Globe },
                      { type: 'private', label: 'Private Link', description: 'Password protected', icon: Users },
                      { type: 'embed', label: 'Embed Code', description: 'For websites', icon: Share2 }
                    ].map((linkType) => (
                      <HStack key={linkType.type} className="items-center space-x-3">
                        <Box className="w-10 h-10 bg-white/10 rounded-lg items-center justify-center">
                          <linkType.icon size={18} color="white" />
                        </Box>
                        <VStack className="flex-1">
                          <Text className="text-white font-medium">{linkType.label}</Text>
                          <Text className="text-white/60 text-xs">{linkType.description}</Text>
                        </VStack>
                        <Button
                          action="candyBlue"
                          size="sm"
                          onPress={() => {
                            const link = generateShareableLink(linkType.type as any);
                            Clipboard.setStringAsync(link);
                          }}
                          className="py-2 px-3"
                        >
                          <ButtonText className="text-xs font-semibold">Copy</ButtonText>
                        </Button>
                      </HStack>
                    ))}
                  </VStack>

                  {/* Export Options */}
                  <VStack className="space-y-4">
                    <Text className="text-white/70 text-sm font-medium">Export Options</Text>

                    {/* Export Format Selection */}
                    <VStack className="space-y-2">
                      <Text className="text-white font-medium">File Format</Text>
                      <HStack className="space-x-2">
                        {(['png', 'jpg', 'pdf', 'svg'] as const).map((format) => (
                          <Pressable
                            key={format}
                            className={`flex-1 py-2 px-3 rounded-lg ${
                              exportFormat === format
                                ? 'bg-candyPink/20 border border-candyPink'
                                : 'bg-white/5 border border-white/20'
                            }`}
                            onPress={() => setExportFormat(format)}
                          >
                            <Text className="text-white text-sm text-center uppercase">{format}</Text>
                          </Pressable>
                        ))}
                      </HStack>
                    </VStack>

                    {/* Export Actions */}
                    <HStack className="space-x-3">
                      <Button
                        action="candyPurple"
                        className="flex-1 py-3"
                        onPress={() => handleExport(exportFormat)}
                        disabled={isExporting}
                      >
                        <ButtonText className="font-semibold">
                          {isExporting ? 'Exporting...' : `Export ${exportFormat.toUpperCase()}`}
                        </ButtonText>
                      </Button>
                      <Button
                        action="candyBlue"
                        className="py-3 px-4"
                        onPress={() => handleExport('all')}
                      >
                        <ButtonText className="font-semibold">All Formats</ButtonText>
                      </Button>
                    </HStack>
                  </VStack>
                </Box>

                {/* Social Media Sharing */}
                <Box className="bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-6">
                  <Text className="text-lg font-semibold text-white mb-4">Share To Social Media</Text>
                  <VStack className="space-y-4">
                    {/* Primary Social Platforms */}
                    <VStack className="space-y-3">
                      <Text className="text-white/70 text-sm font-medium">Popular Platforms</Text>
                      <HStack className="space-x-3">
                        {[
                          {
                            key: 'instagram',
                            label: 'Instagram',
                            icon: MessageCircle,
                            colors: ['#E4405F', '#C13584'],
                            description: 'Stories & Posts'
                          },
                          {
                            key: 'twitter',
                            label: 'Twitter',
                            icon: Send,
                            colors: ['#1DA1F2', '#0d8bd9'],
                            description: 'Tweet & Share'
                          }
                        ].map((platform) => (
                          <Pressable
                            key={platform.key}
                            className="flex-1 rounded-xl overflow-hidden"
                            onPress={() => handleSocialShare(platform.key)}
                          >
                            <LinearGradient
                              colors={platform.colors as [string, string]}
                              start={{ x: 0, y: 0 }}
                              end={{ x: 1, y: 1 }}
                              style={{ padding: 16 }}
                            >
                              <VStack className="items-center space-y-2">
                                <platform.icon size={24} color="white" />
                                <Text className="text-white font-semibold text-sm">{platform.label}</Text>
                                <Text className="text-white/80 text-xs text-center">{platform.description}</Text>
                              </VStack>
                            </LinearGradient>
                          </Pressable>
                        ))}
                      </HStack>
                    </VStack>

                    {/* Professional Platforms */}
                    <VStack className="space-y-3">
                      <Text className="text-white/70 text-sm font-medium">Professional Networks</Text>
                      <HStack className="space-x-3">
                        {[
                          {
                            key: 'linkedin',
                            label: 'LinkedIn',
                            icon: Users,
                            colors: ['#0077B5', '#005885'],
                            description: 'Professional Network'
                          },
                          {
                            key: 'facebook',
                            label: 'Facebook',
                            icon: Globe,
                            colors: ['#1877F2', '#166FE5'],
                            description: 'Social Network'
                          }
                        ].map((platform) => (
                          <Pressable
                            key={platform.key}
                            className="flex-1 rounded-xl overflow-hidden"
                            onPress={() => handleSocialShare(platform.key)}
                          >
                            <LinearGradient
                              colors={platform.colors as [string, string]}
                              start={{ x: 0, y: 0 }}
                              end={{ x: 1, y: 1 }}
                              style={{ padding: 16 }}
                            >
                              <VStack className="items-center space-y-2">
                                <platform.icon size={24} color="white" />
                                <Text className="text-white font-semibold text-sm">{platform.label}</Text>
                                <Text className="text-white/80 text-xs text-center">{platform.description}</Text>
                              </VStack>
                            </LinearGradient>
                          </Pressable>
                        ))}
                      </HStack>
                    </VStack>

                    {/* Messaging Platforms */}
                    <VStack className="space-y-3">
                      <Text className="text-white/70 text-sm font-medium">Messaging Apps</Text>
                      <HStack className="space-x-3">
                        {[
                          {
                            key: 'whatsapp',
                            label: 'WhatsApp',
                            icon: MessageCircle,
                            colors: ['#25D366', '#128C7E'],
                            description: 'Direct Message'
                          },
                          {
                            key: 'telegram',
                            label: 'Telegram',
                            icon: Send,
                            colors: ['#0088CC', '#006699'],
                            description: 'Instant Share'
                          }
                        ].map((platform) => (
                          <Pressable
                            key={platform.key}
                            className="flex-1 rounded-xl overflow-hidden"
                            onPress={() => handleSocialShare(platform.key)}
                          >
                            <LinearGradient
                              colors={platform.colors as [string, string]}
                              start={{ x: 0, y: 0 }}
                              end={{ x: 1, y: 1 }}
                              style={{ padding: 16 }}
                            >
                              <VStack className="items-center space-y-2">
                                <platform.icon size={24} color="white" />
                                <Text className="text-white font-semibold text-sm">{platform.label}</Text>
                                <Text className="text-white/80 text-xs text-center">{platform.description}</Text>
                              </VStack>
                            </LinearGradient>
                          </Pressable>
                        ))}
                      </HStack>
                    </VStack>

                    {/* Quick Share Options */}
                    <VStack className="space-y-3">
                      <Text className="text-white/70 text-sm font-medium">Quick Actions</Text>
                      <HStack className="space-x-3">
                        <Pressable className="flex-1 bg-white/10 border border-white/20 rounded-xl p-4">
                          <VStack className="items-center space-y-2">
                            <Share2 size={20} color="white" />
                            <Text className="text-white text-sm font-medium">More Apps</Text>
                          </VStack>
                        </Pressable>
                        <Pressable className="flex-1 bg-white/10 border border-white/20 rounded-xl p-4">
                          <VStack className="items-center space-y-2">
                            <Download size={20} color="white" />
                            <Text className="text-white text-sm font-medium">Save Image</Text>
                          </VStack>
                        </Pressable>
                      </HStack>
                    </VStack>
                  </VStack>
                </Box>

                {/* Quick Share & Analytics */}
                <Box className="bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-6">
                  <Text className="text-lg font-semibold text-white mb-4">Quick Share & Analytics</Text>

                  <VStack className="space-y-4">
                    {/* QR Code Section */}
                    <VStack className="space-y-3">
                      <Text className="text-white/70 text-sm font-medium">QR Code</Text>
                      <HStack className="space-x-4">
                        <Box className="w-20 h-20 bg-white rounded-lg items-center justify-center">
                          <Text className="text-black text-xs font-bold text-center">QR{'\n'}CODE</Text>
                        </Box>
                        <VStack className="flex-1 space-y-2">
                          <Text className="text-white font-medium">Scan to Share</Text>
                          <Text className="text-white/60 text-sm">Generate QR code for easy mobile sharing</Text>
                          <Button action="candyPink" size="sm" className="py-2">
                            <ButtonText className="text-xs font-semibold">Generate QR</ButtonText>
                          </Button>
                        </VStack>
                      </HStack>
                    </VStack>

                    {/* Share Analytics */}
                    <VStack className="space-y-3">
                      <Text className="text-white/70 text-sm font-medium">Share Analytics</Text>
                      <HStack className="space-x-3">
                        <Box className="flex-1 bg-white/5 rounded-lg p-3">
                          <VStack className="items-center">
                            <Text className="text-2xl font-bold text-candyPink">24</Text>
                            <Text className="text-white/60 text-xs">Views</Text>
                          </VStack>
                        </Box>
                        <Box className="flex-1 bg-white/5 rounded-lg p-3">
                          <VStack className="items-center">
                            <Text className="text-2xl font-bold text-candyPurple">8</Text>
                            <Text className="text-white/60 text-xs">Shares</Text>
                          </VStack>
                        </Box>
                        <Box className="flex-1 bg-white/5 rounded-lg p-3">
                          <VStack className="items-center">
                            <Text className="text-2xl font-bold text-candyBlue">3</Text>
                            <Text className="text-white/60 text-xs">Saves</Text>
                          </VStack>
                        </Box>
                      </HStack>
                    </VStack>

                    {/* Collaboration */}
                    <VStack className="space-y-3">
                      <Text className="text-white/70 text-sm font-medium">Collaboration</Text>
                      <HStack className="space-x-3">
                        <Button action="candyBlue" className="flex-1 py-3">
                          <ButtonText className="font-semibold">Invite Collaborators</ButtonText>
                        </Button>
                        <Button action="candyPurple" className="py-3 px-4">
                          <ButtonText className="font-semibold">Create Collection</ButtonText>
                        </Button>
                      </HStack>
                    </VStack>
                  </VStack>
                </Box>
              </VStack>
            </VStack>
          </ScrollView>
        </VStack>
      </SafeAreaView>
    </LinearGradient>
  );
}

export default Share;
