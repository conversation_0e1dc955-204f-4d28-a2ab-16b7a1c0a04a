/**
 * AI-Enhanced Knowledge Cards Search Screen
 * 
 * Integrates AI-powered search with the Knowledge Cards system
 * Following LearniScan development workflow rules
 */

import React, { useState, useCallback } from 'react';
import { ScrollView, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Brain, BookOpen, Filter } from 'lucide-react-native';
import { router } from 'expo-router';

// UI Components
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Pressable } from '@/components/ui/pressable';

// AI Search Components
import { AISearchBar } from '@/components/learni-scan/AISearchBar';
import { AISearchResults } from '@/components/learni-scan/AISearchResults';

// Knowledge Cards Context
import { useKnowledgeCards } from '@/lib/contexts/KnowledgeCardsContext';
import { useAuth } from '@/lib/contexts/AuthContext';

// Types
import type { 
  AISearchResponse, 
  SemanticSearchResult, 
  SmartSuggestion 
} from '@/lib/services/ai-search.service';

const AISearchCardsScreen: React.FC = () => {
  const { user } = useAuth();
  const { state: cardsState, getCard } = useKnowledgeCards();
  
  const [searchResults, setSearchResults] = useState<AISearchResponse | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [currentQuery, setCurrentQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const handleSearch = useCallback(async (query: string, results: AISearchResponse) => {
    setCurrentQuery(query);
    setSearchResults(results);
    setIsSearching(false);
  }, []);

  const handleSuggestionPress = useCallback((suggestion: SmartSuggestion) => {
    // Auto-search with the suggestion
    setCurrentQuery(suggestion.text);
    setIsSearching(true);
    
    // The AISearchBar will handle the actual search
    console.log('Suggestion selected:', suggestion.text);
  }, []);

  const handleResultPress = useCallback(async (result: SemanticSearchResult) => {
    try {
      // Get the full knowledge card data
      const fullCard = await getCard(result.id);
      
      if (fullCard) {
        // Navigate to card detail view or show card modal
        Alert.alert(
          fullCard.card.title,
          `Category: ${fullCard.card.category}\nDifficulty: ${fullCard.card.difficulty}\n\n${fullCard.content?.summary || 'No summary available'}`,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'View Card', onPress: () => {
              // Navigate to card detail screen
              console.log('Navigate to card:', fullCard.card.$id);
            }}
          ]
        );
      } else {
        Alert.alert('Card Not Found', 'This knowledge card could not be loaded.');
      }
    } catch (error) {
      console.error('Failed to load card:', error);
      Alert.alert('Error', 'Failed to load the knowledge card.');
    }
  }, [getCard]);

  const handleRelatedQueryPress = useCallback((query: string) => {
    setCurrentQuery(query);
    setIsSearching(true);
    // The search will be triggered by the AISearchBar
  }, []);

  const clearResults = () => {
    setSearchResults(null);
    setCurrentQuery('');
  };

  if (!user) {
    return (
      <LinearGradient
        colors={['rgb(16, 185, 129)', 'rgb(6, 182, 212)', 'rgb(139, 92, 246)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={{ flex: 1 }}>
          <VStack className="flex-1 justify-center items-center px-8">
            <Text className="text-white text-xl font-semibold text-center">
              Please log in to search your knowledge cards
            </Text>
          </VStack>
        </SafeAreaView>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['rgb(16, 185, 129)', 'rgb(6, 182, 212)', 'rgb(139, 92, 246)']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView style={{ flex: 1 }}>
        <VStack className="flex-1">
          {/* Header */}
          <HStack className="px-4 py-3 justify-between items-center">
            <Pressable
              className="w-10 h-10 rounded-full bg-white/10 items-center justify-center"
              onPress={() => router.back()}
            >
              <ArrowLeft size={20} color="white" />
            </Pressable>

            <VStack className="flex-1 items-center">
              <Text className="text-white text-lg font-semibold">
                AI Search
              </Text>
              <Text className="text-white/70 text-sm">
                {cardsState.totalCards} knowledge cards
              </Text>
            </VStack>

            <Pressable
              className="w-10 h-10 rounded-full bg-white/10 items-center justify-center"
              onPress={() => setShowFilters(!showFilters)}
            >
              <Filter size={20} color="white" />
            </Pressable>
          </HStack>

          {/* AI Search Bar */}
          <Box className="px-4 mb-4">
            <AISearchBar
              userId={user.$id}
              searchType="knowledge_cards"
              onSearch={handleSearch}
              onSuggestionPress={handleSuggestionPress}
              placeholder="Search your knowledge cards with AI..."
              showAIFeatures={true}
              showSuggestions={true}
              variant="glass"
              size="md"
            />
          </Box>

          {/* Quick Stats */}
          {!searchResults && (
            <Box className="px-4 mb-4">
              <Box
                className="p-4 rounded-xl"
                style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
              >
                <HStack className="justify-around">
                  <VStack className="items-center">
                    <Text className="text-white text-2xl font-bold">
                      {cardsState.totalCards}
                    </Text>
                    <Text className="text-white/70 text-xs">Total Cards</Text>
                  </VStack>
                  <VStack className="items-center">
                    <Text className="text-white text-2xl font-bold">
                      {Object.keys(cardsState.statistics.cardsByCategory).length}
                    </Text>
                    <Text className="text-white/70 text-xs">Categories</Text>
                  </VStack>
                  <VStack className="items-center">
                    <Text className="text-white text-2xl font-bold">
                      {cardsState.statistics.reviewsDue}
                    </Text>
                    <Text className="text-white/70 text-xs">Due for Review</Text>
                  </VStack>
                </HStack>
              </Box>
            </Box>
          )}

          {/* Content Area */}
          <Box className="flex-1">
            {searchResults ? (
              <AISearchResults
                searchResponse={searchResults}
                onResultPress={handleResultPress}
                onSuggestionPress={handleSuggestionPress}
                onRelatedQueryPress={handleRelatedQueryPress}
                showMetadata={true}
                showRelatedConcepts={true}
                className="flex-1"
              />
            ) : (
              <ScrollView className="flex-1" contentContainerStyle={{ flex: 1 }}>
                <VStack className="flex-1 justify-center items-center px-8">
                  {/* Welcome State */}
                  <Box
                    className="w-20 h-20 rounded-full items-center justify-center mb-6"
                    style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                  >
                    <Brain size={40} color="#06B6D4" />
                  </Box>

                  <Text className="text-white text-2xl font-bold text-center mb-4">
                    AI-Powered Knowledge Search
                  </Text>

                  <Text className="text-white/80 text-base text-center mb-8 leading-6">
                    Search your {cardsState.totalCards} knowledge cards with AI understanding. 
                    Find content by meaning, not just keywords.
                  </Text>

                  {/* Feature Highlights */}
                  <VStack space="md" className="w-full">
                    <Box
                      className="p-4 rounded-xl"
                      style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                    >
                      <HStack className="items-center" space="sm">
                        <Box
                          className="w-8 h-8 rounded-full items-center justify-center"
                          style={{ backgroundColor: 'rgba(16, 185, 129, 0.3)' }}
                        >
                          <Text className="text-white text-sm font-bold">🧠</Text>
                        </Box>
                        <VStack className="flex-1">
                          <Text className="text-white font-semibold">Semantic Understanding</Text>
                          <Text className="text-white/70 text-sm">
                            Finds cards by meaning and context
                          </Text>
                        </VStack>
                      </HStack>
                    </Box>

                    <Box
                      className="p-4 rounded-xl"
                      style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                    >
                      <HStack className="items-center" space="sm">
                        <Box
                          className="w-8 h-8 rounded-full items-center justify-center"
                          style={{ backgroundColor: 'rgba(6, 182, 212, 0.3)' }}
                        >
                          <Text className="text-white text-sm font-bold">🎯</Text>
                        </Box>
                        <VStack className="flex-1">
                          <Text className="text-white font-semibold">Smart Suggestions</Text>
                          <Text className="text-white/70 text-sm">
                            Discovers related concepts and topics
                          </Text>
                        </VStack>
                      </HStack>
                    </Box>

                    <Box
                      className="p-4 rounded-xl"
                      style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                    >
                      <HStack className="items-center" space="sm">
                        <Box
                          className="w-8 h-8 rounded-full items-center justify-center"
                          style={{ backgroundColor: 'rgba(139, 92, 246, 0.3)' }}
                        >
                          <Text className="text-white text-sm font-bold">📚</Text>
                        </Box>
                        <VStack className="flex-1">
                          <Text className="text-white font-semibold">Personal Knowledge Base</Text>
                          <Text className="text-white/70 text-sm">
                            Searches only your knowledge cards
                          </Text>
                        </VStack>
                      </HStack>
                    </Box>
                  </VStack>

                  {/* Sample Queries */}
                  <VStack space="sm" className="w-full mt-8">
                    <Text className="text-white/80 text-sm font-medium text-center">
                      Try searching for:
                    </Text>
                    
                    <VStack space="xs">
                      {[
                        'concepts I need to review',
                        'beginner level topics',
                        'advanced programming concepts',
                        'recent additions to my knowledge'
                      ].map((query, index) => (
                        <Pressable
                          key={index}
                          className="p-3 rounded-lg"
                          style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                          onPress={() => {
                            setCurrentQuery(query);
                            setIsSearching(true);
                          }}
                        >
                          <Text className="text-white/90 text-sm text-center">
                            "{query}"
                          </Text>
                        </Pressable>
                      ))}
                    </VStack>
                  </VStack>
                </VStack>
              </ScrollView>
            )}
          </Box>

          {/* Status Bar */}
          {searchResults && (
            <Box className="px-4 py-2">
              <Box
                className="p-3 rounded-lg"
                style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
              >
                <HStack className="items-center justify-between">
                  <HStack className="items-center" space="sm">
                    <Brain size={16} color="#06B6D4" />
                    <Text className="text-white/80 text-sm">
                      "{currentQuery}"
                    </Text>
                  </HStack>
                  <HStack className="items-center" space="sm">
                    <Text className="text-white/60 text-xs">
                      {searchResults.totalResults} results • {searchResults.processingTime}ms
                    </Text>
                    <Button
                      onPress={clearResults}
                      className="bg-white/10 px-3 py-1"
                      size="sm"
                    >
                      <ButtonText className="text-white text-xs">Clear</ButtonText>
                    </Button>
                  </HStack>
                </HStack>
              </Box>
            </Box>
          )}
        </VStack>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default AISearchCardsScreen;
