import React, { useState } from 'react';
import { Pressable } from 'react-native';
import { Plus, ChevronLeft, ChevronRight } from 'lucide-react-native';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Button, ButtonText } from '@/components/ui/button';
import { KnowledgeCard, IKnowledgeCard, ViewMode, ICardStatus } from './KnowledgeCard';

interface ICardsListProps {
  cards: IKnowledgeCard[];
  viewMode: ViewMode;
  onCardPress?: (card: IKnowledgeCard) => void;
  onStatusChange?: (cardId: string, status: ICardStatus) => void;
  onAddCard?: () => void;
}

export const CardsList: React.FC<ICardsListProps> = ({
  cards,
  viewMode,
  onCardPress,
  onStatusChange,
  onAddCard
}) => {
  const [currentCardIndex, setCurrentCardIndex] = useState(0);

  if (viewMode === 'study') {
    if (!cards || cards.length === 0) {
      return (
        <VStack className="items-center justify-center flex-1 px-4">
          <Text className="text-white text-xl font-semibold mb-4">No cards to study</Text>
          <Text className="text-white/70 text-center mb-8">Add some cards to start studying</Text>
          <AddNewCard onPress={onAddCard} />
        </VStack>
      );
    }

    const currentCard = cards[currentCardIndex];
    const canGoPrevious = currentCardIndex > 0;
    const canGoNext = currentCardIndex < cards.length - 1;

    const handlePrevious = () => {
      if (canGoPrevious) {
        setCurrentCardIndex(prev => prev - 1);
      }
    };

    const handleNext = () => {
      if (canGoNext) {
        setCurrentCardIndex(prev => prev + 1);
      }
    };

    const handleStatusChange = (cardId: string, status: ICardStatus) => {
      onStatusChange?.(cardId, status);
      // Auto-advance to next card after answering
      if (canGoNext) {
        setTimeout(() => {
          handleNext();
        }, 500);
      }
    };

    return (
      <VStack className="flex-1">
        <KnowledgeCard
          card={currentCard}
          viewMode={viewMode}
          onPress={onCardPress}
          onStatusChange={handleStatusChange}
          currentIndex={currentCardIndex + 1}
          totalCards={cards.length}
        />
        
        {/* Navigation Controls */}
        <HStack className="justify-between items-center px-4 mt-6">
          <Button
            variant="outline"
            size="lg"
            className={`px-6 py-4 rounded-2xl border-white/30 ${!canGoPrevious ? 'opacity-50' : 'bg-white/10'}`}
            onPress={handlePrevious}
            disabled={!canGoPrevious}
          >
            <HStack className="items-center space-x-2">
              <ChevronLeft size={20} color="white" />
              <ButtonText className="text-white font-semibold">Previous</ButtonText>
            </HStack>
          </Button>

          <VStack className="items-center">
            <Text className="text-white/70 text-sm">Card Progress</Text>
            <Text className="text-white font-semibold">{currentCardIndex + 1} of {cards.length}</Text>
          </VStack>

          <Button
            variant="outline"
            size="lg"
            className={`px-6 py-4 rounded-2xl border-white/30 ${!canGoNext ? 'opacity-50' : 'bg-white/10'}`}
            onPress={handleNext}
            disabled={!canGoNext}
          >
            <HStack className="items-center space-x-2">
              <ButtonText className="text-white font-semibold">Next</ButtonText>
              <ChevronRight size={20} color="white" />
            </HStack>
          </Button>
        </HStack>
      </VStack>
    );
  }

  // Grid mode - show all cards
  return (
    <VStack className="gap-4 pb-6">
      {cards.map((card) => (
        <KnowledgeCard
          key={card.id}
          card={card}
          viewMode={viewMode}
          onPress={onCardPress}
          onStatusChange={onStatusChange}
        />
      ))}
      
      <AddNewCard onPress={onAddCard} />
    </VStack>
  );
};

// Add New Card Component
const AddNewCard: React.FC<{ onPress?: () => void }> = ({ onPress }) => (
  <Pressable 
    onPress={onPress}
    className="bg-white/5 border-2 border-dashed border-white/30 p-6 rounded-2xl min-h-[140px]"
  >
    <VStack className="items-center justify-center flex-1">
      <Box className="w-12 h-12 bg-white/10 rounded-full items-center justify-center mb-4">
        <Plus size={24} color="#FF6B9D" />
      </Box>
      <Text className="text-lg font-semibold text-white mb-2 leading-6">Create New Card</Text>
      <Text className="text-white/60 text-sm text-center leading-5 px-2">
        Add a new knowledge card to your collection
      </Text>
    </VStack>
  </Pressable>
);

export default CardsList;
