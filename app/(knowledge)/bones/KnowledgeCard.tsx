import React from 'react';
import { Pressable } from 'react-native';
import { CheckCircle, Clock, Plus } from 'lucide-react-native';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Badge, BadgeText } from '@/components/ui/badge';

// Types
export type ICardStatus = 'new' | 'learning' | 'mastered';

export interface IKnowledgeCard {
  id: string;
  title: string;
  description: string;
  category: string;
  status: ICardStatus;
  createdAt: Date;
  answer?: string; // For study mode
  difficulty: number;
  tags: string[];
}

export type ViewMode = 'grid' | 'study';

export interface IKnowledgeCardProps {
  card: IKnowledgeCard;
  viewMode: ViewMode;
  onPress?: (card: IKnowledgeCard) => void;
  onStatusChange?: (cardId: string, status: ICardStatus) => void;
  currentIndex?: number;
  totalCards?: number;
}

// Helper functions
const getStatusIcon = (status: ICardStatus) => {
  switch (status) {
    case 'mastered': return CheckCircle;
    case 'learning': return Clock;
    case 'new': return Plus;
    default: return Clock;
  }
};

const getCategoryColor = (category: string): 'candyPink' | 'candyPurple' | 'candyBlue' => {
  switch (category) {
    case 'ai-basics': return 'candyPink';
    case 'learning-types': return 'candyPurple';
    case 'algorithms': return 'candyBlue';
    case 'applications': return 'candyBlue';
    default: return 'candyPink';
  }
};

const formatTimeAgo = (date: Date): string => {
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
  
  if (diffInHours < 24) {
    return `${diffInHours} hours ago`;
  } else {
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }
};

export const KnowledgeCard: React.FC<IKnowledgeCardProps> = ({
  card,
  viewMode,
  onPress,
  onStatusChange,
  currentIndex,
  totalCards
}) => {
  const StatusIcon = getStatusIcon(card.status);

  if (viewMode === 'study') {
    return (
      <StudyCard 
        card={card}
        StatusIcon={StatusIcon}
        onPress={onPress}
        onStatusChange={onStatusChange}
        currentIndex={currentIndex}
        totalCards={totalCards}
      />
    );
  }

  return (
    <GridCard 
      card={card} 
      StatusIcon={StatusIcon}
      onPress={onPress}
      formatTimeAgo={formatTimeAgo}
      getCategoryColor={getCategoryColor}
    />
  );
};

// Grid Card Component
const GridCard: React.FC<{
  card: IKnowledgeCard;
  StatusIcon: any;
  onPress?: (card: IKnowledgeCard) => void;
  formatTimeAgo: (date: Date) => string;
  getCategoryColor: (category: string) => 'candyPink' | 'candyPurple' | 'candyBlue';
}> = ({ card, StatusIcon, onPress, formatTimeAgo, getCategoryColor }) => (
  <Pressable onPress={() => onPress?.(card)}>
    <Box className="bg-white/10 border border-white/20 backdrop-blur-md p-6 rounded-2xl">
      <HStack className="items-start justify-between mb-3">
        <Badge action={getCategoryColor(card.category)} className="px-2 py-1 rounded-full">
          <BadgeText className="text-xs capitalize">
            {card.category.replace('-', ' ')}
          </BadgeText>
        </Badge>
        <Badge
          action={card.status === 'mastered' ? 'candyPink' : card.status === 'learning' ? 'candyPurple' : 'candyBlue'}
          variant="solid"
          size="sm"
          className="px-2 py-1 rounded-full"
        >
          <HStack className="items-center space-x-1">
            <StatusIcon size={12} color="white" />
            <BadgeText className="text-xs text-white capitalize">
              {card.status}
            </BadgeText>
          </HStack>
        </Badge>
      </HStack>
      
      <Text className="text-lg font-bold text-white mb-3 leading-7">{card.title}</Text>
      <Text className="text-white/70 text-sm mb-4 leading-6 flex-1">
        {card.description}
      </Text>

      <HStack className="items-center justify-between mt-2">
        <Text className="text-xs text-white/50 leading-5">
          Created {formatTimeAgo(card.createdAt)}
        </Text>
        <Text className="text-xs text-white/50 leading-5">Click to flip</Text>
      </HStack>
    </Box>
  </Pressable>
);

// Study Card Component
const StudyCard: React.FC<{
  card: IKnowledgeCard;
  StatusIcon: any;
  onPress?: (card: IKnowledgeCard) => void;
  onStatusChange?: (cardId: string, status: ICardStatus) => void;
  currentIndex?: number;
  totalCards?: number;
}> = ({ card, StatusIcon, onPress, onStatusChange, currentIndex = 1, totalCards = 1 }) => {
  const [flipped, setFlipped] = React.useState(false);
  const progress = (currentIndex / totalCards) * 100;

  return (
    <VStack className="gap-4">
      {/* Study Mode Progress */}
      <HStack className="items-center justify-between px-4">
        <Text className="text-white/70 text-sm">Card {currentIndex} of {totalCards}</Text>
        <Badge action="candyBlue" className="px-3 py-1 rounded-full">
          <BadgeText className="text-xs text-white">Study Mode</BadgeText>
        </Badge>
      </HStack>

      {/* Progress Bar */}
      <Box className="mx-4 h-2 bg-white/20 rounded-full overflow-hidden">
        <Box 
          className="h-full bg-candyPink rounded-full transition-all duration-300" 
          style={{ width: `${progress}%` }}
        />
      </Box>

      {/* Main Study Card */}
      <Pressable 
        onPress={() => setFlipped(!flipped)}
        className="mx-4 bg-white/10 border border-white/20 backdrop-blur-md rounded-3xl p-8 min-h-96"
      >
        <VStack className="items-center justify-center flex-1 space-y-6">
          {!flipped ? (
            // Front of card - Question
            <>
              <Badge action={getCategoryColor(card.category)} className="px-3 py-1 rounded-full mb-4">
                <BadgeText className="text-xs capitalize">
                  {card.category.replace('-', ' ')}
                </BadgeText>
              </Badge>
              <Text className="text-2xl font-bold text-white text-center leading-8 mb-4">
                {card.title}
              </Text>
              <Text className="text-white/80 text-center leading-6 px-4 mb-8">
                {card.description}
              </Text>
              <Text className="text-white/50 text-sm text-center">
                Tap to reveal answer
              </Text>
            </>
          ) : (
            // Back of card - Answer  
            <>
              <Text className="text-xl font-semibold text-candyPink text-center leading-7 mb-4">
                Answer:
              </Text>
              <Text className="text-white/90 text-center leading-6 px-4 mb-8">
                {card.answer || `This covers the fundamental concept: ${card.description}`}
              </Text>
              <VStack className="items-center space-y-2">
                <Text className="text-white/60 text-sm text-center">
                  Difficulty: {Array(card.difficulty).fill('⭐').join('')}
                </Text>
                <Text className="text-white/60 text-xs text-center">
                  Tags: {card.tags.join(', ')}
                </Text>
              </VStack>
            </>
          )}
        </VStack>
      </Pressable>

      {/* Study Actions - Only show when flipped */}
      {flipped && (
        <VStack className="px-4 gap-3">
          <Text className="text-white/70 text-center text-sm mb-2">
            How well do you know this?
          </Text>
          <HStack className="justify-between gap-3">
            <Pressable 
              className="flex-1 bg-red-500/20 border border-red-500/30 rounded-2xl py-4"
              onPress={() => onStatusChange?.(card.id, 'new')}
            >
              <Text className="text-red-400 font-semibold text-center">Don't Know</Text>
            </Pressable>
            
            <Pressable 
              className="flex-1 bg-yellow-500/20 border border-yellow-500/30 rounded-2xl py-4"
              onPress={() => onStatusChange?.(card.id, 'learning')}
            >
              <Text className="text-yellow-400 font-semibold text-center">Partially</Text>
            </Pressable>
            
            <Pressable 
              className="flex-1 bg-green-500/20 border border-green-500/30 rounded-2xl py-4"
              onPress={() => onStatusChange?.(card.id, 'mastered')}
            >
              <Text className="text-green-400 font-semibold text-center">Know Well</Text>
            </Pressable>
          </HStack>
        </VStack>
      )}
    </VStack>
  );
};

export default KnowledgeCard;
