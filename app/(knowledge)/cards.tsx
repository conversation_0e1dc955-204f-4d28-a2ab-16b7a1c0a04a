import { useState, useEffect } from 'react';
import { CardsList } from './bones/CardsList';
import { IKnowledgeCard, ICardStatus, ViewMode } from './bones/KnowledgeCard';
// Fixed import paths - trigger rebuild
import { useKnowledgeCards } from '@/lib/contexts/KnowledgeCardsContext';
import { ScrollView, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';
import { z } from 'zod';
import clx from 'classnames';
import { ArrowLeft, Search, Plus, CheckCircle, Clock, SparklesIcon } from 'lucide-react-native';

// UI Components
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';

import { Badge, BadgeText } from '@/components/ui/badge';
import { Pressable } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

// Zod Schemas (Rule #11)
const KnowledgeCardStatusSchema = z.enum(['new', 'learning', 'mastered']);
const KnowledgeCardCategorySchema = z.enum(['ai-basics', 'learning-types', 'algorithms', 'applications']);
const ViewModeSchema = z.enum(['grid', 'study']);

const KnowledgeCardSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  category: KnowledgeCardCategorySchema,
  status: KnowledgeCardStatusSchema,
  createdAt: z.date(),
  lastReviewed: z.date().optional(),
  difficulty: z.number().min(1).max(5),
  tags: z.array(z.string()),
});

const CardsStateSchema = z.object({
  viewMode: ViewModeSchema,
  searchQuery: z.string(),
  selectedCategory: KnowledgeCardCategorySchema.optional(),
  cards: z.array(KnowledgeCardSchema),
  stats: z.object({
    total: z.number(),
    mastered: z.number(),
    learning: z.number(),
    new: z.number(),
  }),
});

// TypeScript Types
// type IKnowledgeCard = z.infer<typeof KnowledgeCardSchema>;
type ICardsState = z.infer<typeof CardsStateSchema>;

// type ICardStatus = z.infer<typeof KnowledgeCardStatusSchema>;

// Mock Data
const mockCards: IKnowledgeCard[] = [
  {
    id: '1',
    title: 'What is Machine Learning?',
    description: 'A subset of AI that enables systems to learn and improve from experience without explicit programming.',
    answer: 'Machine Learning is a method of data analysis that automates analytical model building. It uses algorithms that iteratively learn from data, allowing computers to find hidden insights without being explicitly programmed where to look.',
    category: 'ai-basics',
    status: 'mastered',
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    difficulty: 2,
    tags: ['fundamentals', 'ai', 'introduction'],
  },
  {
    id: '2',
    title: 'Supervised Learning',
    description: 'Learning with labeled examples to make predictions on new, unseen data.',
    answer: 'Supervised learning uses labeled datasets to train algorithms that classify data or predict outcomes accurately. It includes classification (predicting categories) and regression (predicting continuous values).',
    category: 'learning-types',
    status: 'learning',
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
    difficulty: 3,
    tags: ['supervised', 'classification', 'regression'],
  },
  {
    id: '3',
    title: 'Neural Networks',
    description: 'Computing systems inspired by biological neural networks that process information.',
    answer: 'Neural networks are computing systems with interconnected nodes (neurons) that process information using connectionist approaches to computation. They can recognize patterns and classify data through layers of weighted connections.',
    category: 'algorithms',
    status: 'new',
    createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
    difficulty: 4,
    tags: ['neural-networks', 'deep-learning', 'algorithms'],
  },
  {
    id: '4',
    title: 'Unsupervised Learning',
    description: 'Finding hidden patterns in data without labeled examples.',
    answer: 'Unsupervised learning analyzes and clusters unlabeled datasets to discover hidden patterns. Common techniques include clustering (grouping similar data) and association rules (finding relationships).',
    category: 'learning-types',
    status: 'new',
    createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
    difficulty: 3,
    tags: ['unsupervised', 'clustering', 'patterns'],
  },
  {
    id: '5',
    title: 'Natural Language Processing',
    description: 'AI that helps computers understand and generate human language.',
    answer: 'NLP combines computational linguistics with statistical, machine learning, and deep learning models to enable computers to process and analyze large amounts of natural language data.',
    category: 'applications',
    status: 'learning',
    createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
    difficulty: 4,
    tags: ['nlp', 'language', 'text-processing'],
  },
];

const Cards: React.FC = () => {
  // State Management (Rule #11) - Using Knowledge Cards Context
  const [viewMode, setViewMode] = useState<ICardsState['viewMode']>('grid');
  const { state: cardsState, loadCards, createCard } = useKnowledgeCards();

  // Get route parameters for post-scan navigation
  const params = useLocalSearchParams();
  const isFromScan = params.source === 'scan' || params.source === 'hub';
  const highlightNew = params.highlightNew === 'true';
  const newCardIds = params.newCardIds ? JSON.parse(params.newCardIds as string) : [];
  const focusCardId = params.focusCardId as string;
  
  // Convert AppWrite cards to local interface format
  const cards: IKnowledgeCard[] = cardsState.cards.map(card => ({
    id: card.card.$id,
    title: card.card.title,
    description: card.content?.summary || card.content?.content?.substring(0, 100) + '...' || '',
    category: card.card.category as any, // Type conversion needed
    status: 'new' as ICardStatus, // Default status - could be enhanced
    createdAt: new Date(card.card.$createdAt),
    lastReviewed: card.reviews?.reviewCount ? new Date() : undefined,
    progress: card.reviews?.reviewCount || 0,
    nextReview: card.nextReviewDate || new Date(),
  }));

  // Load cards on component mount
  useEffect(() => {
    loadCards(true);
  }, []);
  
  // Calculate stats
  const stats = {
    total: cards.length,
    mastered: cards.filter(card => card.status === 'mastered').length,
    learning: cards.filter(card => card.status === 'learning').length,
    new: cards.filter(card => card.status === 'new').length,
  };
  // Helper functions are now in KnowledgeCard component

  return (
    <LinearGradient
      colors={[
        'rgb(255, 107, 157)', // candy-pink
        'rgb(168, 85, 247)',  // candy-purple
        'rgb(59, 130, 246)'   // candy-blue
      ]}
      locations={[0, 0.6, 1]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView className="flex-1">
        <VStack className="flex-1">
          {/* Header - Fixed */}
            <HStack className="items-center justify-between mb-6 px-4">
              <HStack className="items-center space-x-4">
                <Pressable className="w-12 h-12 bg-white/10 border border-white/20 backdrop-blur-md rounded-xl items-center justify-center">
                  <ArrowLeft size={24} color="white" />
                </Pressable>
                <VStack className="ml-4">
                  <Text className="text-2xl font-bold text-white leading-8">Knowledge Cards</Text>
                  <Text className="text-white/60 text-base leading-6">Machine Learning Basics</Text>
                </VStack>
              </HStack>

              <HStack className="space-x-3">
                <Pressable className="w-12 h-12 bg-white/10 border border-white/20 backdrop-blur-md rounded-xl items-center justify-center">
                  <Search size={24} color="white" />
                </Pressable>
                <Pressable className="w-12 h-12 ml-4 bg-white/10 border border-white/20 backdrop-blur-md rounded-xl items-center justify-center">
                  <Plus size={24} color="white" />
                </Pressable>
              </HStack>
            </HStack>

          {/* Scrollable Content */}
          <ScrollView className="flex-1 px-4" showsVerticalScrollIndicator={false}>
            {/* Stats Overview - 2x2 Grid */}
            <VStack className="mb-8 gap-4">
              <HStack className="justify-between gap-4">
                <Box className="flex-1 bg-white/10 border border-white/20 backdrop-blur-md rounded-2xl p-6">
                  <VStack className="items-center">
                    <Text className="text-3xl font-bold text-white mb-2">{stats.total}</Text>
                    <Text className="text-white/70 text-sm font-medium">Total Cards</Text>
                  </VStack>
                </Box>
                <Box className="flex-1 bg-white/10 border border-white/20 backdrop-blur-md rounded-2xl p-6">
                  <VStack className="items-center">
                    <Text className="text-3xl font-bold text-green-400 mb-2">{stats.mastered}</Text>
                    <Text className="text-white/70 text-sm font-medium">Mastered</Text>
                  </VStack>
                </Box>
              </HStack>
              <HStack className="justify-between gap-4">
                <Box className="flex-1 bg-white/10 border border-white/20 backdrop-blur-md rounded-2xl p-6">
                  <VStack className="items-center">
                    <Text className="text-3xl font-bold text-yellow-400 mb-2">{stats.learning}</Text>
                    <Text className="text-white/70 text-sm font-medium">Learning</Text>
                  </VStack>
                </Box>
                <Box className="flex-1 bg-white/10 border border-white/20 backdrop-blur-md rounded-2xl p-6">
                  <VStack className="items-center">
                    <Text className="text-3xl font-bold text-blue-400 mb-2">{stats.new}</Text>
                    <Text className="text-white/70 text-sm font-medium">New</Text>
                  </VStack>
                </Box>
              </HStack>
            </VStack>

            {/* View Toggle */}
            <HStack className="items-center justify-between mb-8">
              {/* Left: Mode Toggle */}
              <HStack className="gap-0 bg-white/10 border border-white/20 backdrop-blur-md p-1 rounded-2xl">
                <Pressable
                  onPress={() => setViewMode('grid')}
                  className={`px-6 py-2 rounded-l-xl ${
                    viewMode === 'grid' 
                      ? 'bg-pink-500' 
                      : 'bg-transparent'
                  }`}
                >
                  <Text className={`${
                    viewMode === 'grid' 
                      ? 'text-white font-semibold' 
                      : 'text-white/70'
                  }`}>
                    Grid
                  </Text>
                </Pressable>
                <Pressable
                  onPress={() => setViewMode('study')}
                  className={`px-6 py-2 rounded-r-xl ${
                    viewMode === 'study' 
                      ? 'bg-pink-500' 
                      : 'bg-transparent'
                  }`}
                >
                  <Text className={`${
                    viewMode === 'study' 
                      ? 'text-white font-semibold' 
                      : 'text-white/70'
                  }`}>
                    Study
                  </Text>
                </Pressable>
              </HStack>

              {/* Right: Action Buttons */}
              <HStack className="gap-2">
                <Pressable className="px-4 py-2 rounded-xl bg-white/5 border border-white/20 backdrop-blur-md">
                  <Text className="text-white font-semibold text-sm">View Graph</Text>
                </Pressable>
                <Pressable className="px-4 py-2 rounded-xl bg-white/5 border border-white/20 backdrop-blur-md">
                  <Text className="text-white font-semibold text-sm">Roadmap</Text>
                </Pressable>
              </HStack>
            </HStack>

            {/* Cards Display */}
            {viewMode === 'grid' ? (
              <CardsList
                cards={cards}
                viewMode={viewMode}
                onCardPress={(card) => console.log('Card pressed:', card)}
                onStatusChange={(cardId, status) => console.log('Status changed:', cardId, status)}
                onAddCard={() => console.log('Add new card')}
              />
            ) : (
              <CardsList
                cards={cards}
                viewMode={viewMode}
                onCardPress={(card) => console.log('Card pressed:', card)}
                onStatusChange={(cardId, status) => console.log('Status changed:', cardId, status)}
                onAddCard={() => console.log('Add new card')}
              />
            )}
          </ScrollView>
        </VStack>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default Cards;
