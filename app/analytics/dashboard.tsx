import React, { useEffect, useState } from 'react';
import { ScrollView, RefreshControl } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { ArrowLeftIcon } from 'lucide-react-native';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { Button, ButtonText } from '@/components/ui/button';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import AnalyticsDashboard from '@/components/analytics/AnalyticsDashboard';
import { analyticsService } from '@/lib/services/analytics.service';
import { performanceMonitoringService } from '@/lib/services/performance-monitoring.service';

/**
 * Analytics Dashboard Screen
 * Following Rule 6: UI Design Consistency with Candy Color Theme
 * Following Rule 9: Safe Area Management
 */
export default function AnalyticsDashboardScreen() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    initializeAnalytics();
  }, []);

  const initializeAnalytics = async () => {
    try {
      setIsLoading(true);
      
      // Track screen view
      await analyticsService.trackEvent('user_action', {
        action: 'screen_view',
        screen_name: 'analytics_dashboard',
        timestamp: Date.now(),
      });

      // Initialize performance monitoring
      await performanceMonitoringService.startMonitoring();
      
      setIsLoading(false);
    } catch (error) {
      console.warn('Failed to initialize analytics:', error);
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await initializeAnalytics();
    setRefreshing(false);
  };

  const handleGoBack = () => {
    router.back();
  };

  return (
    <Box className="flex-1">
      {/* Background Gradient */}
      <LinearGradient
        colors={['#1e1b4b', '#312e81', '#4338ca', '#6366f1']}
        style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0 }}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <VStack space="md" className="pt-16 px-6 pb-4">
          <HStack className="items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onPress={handleGoBack}
              className="bg-white/10 border-white/20"
            >
              <HStack className="items-center space-x-2">
                <ArrowLeftIcon size={16} color="white" />
                <ButtonText className="text-white text-sm">Back</ButtonText>
              </HStack>
            </Button>
          </HStack>

          <VStack space="sm">
            <Text className="text-white text-3xl font-bold">
              Analytics Dashboard
            </Text>
            <Text className="text-white/70 text-base">
              Monitor your learning progress and system performance
            </Text>
          </VStack>
        </VStack>

        {/* Analytics Dashboard Component */}
        {!isLoading && (
          <AnalyticsDashboard className="px-0" />
        )}

        {/* Loading State */}
        {isLoading && (
          <VStack space="md" className="px-6 py-8">
            <Box className="bg-white/10 rounded-xl p-6 items-center">
              <Text className="text-white text-lg font-medium mb-2">
                Loading Analytics...
              </Text>
              <Text className="text-white/70 text-sm text-center">
                Gathering your learning data and performance metrics
              </Text>
            </Box>
          </VStack>
        )}

        {/* Quick Actions */}
        <VStack space="md" className="px-6 py-6">
          <Text className="text-white text-xl font-semibold mb-4">
            Quick Actions
          </Text>

          <VStack space="sm">
            <Button
              onPress={() => router.push('/analytics/performance')}
              className="bg-white/10 border border-white/20 rounded-xl p-4"
            >
              <HStack className="items-center justify-between w-full">
                <VStack space="xs" className="flex-1">
                  <Text className="text-white text-base font-medium">
                    Performance Monitor
                  </Text>
                  <Text className="text-white/70 text-sm">
                    View detailed system performance metrics
                  </Text>
                </VStack>
                <ArrowLeftIcon 
                  size={20} 
                  color="white" 
                  style={{ transform: [{ rotate: '180deg' }] }} 
                />
              </HStack>
            </Button>

            <Button
              onPress={() => router.push('/analytics/cost-optimization')}
              className="bg-white/10 border border-white/20 rounded-xl p-4"
            >
              <HStack className="items-center justify-between w-full">
                <VStack space="xs" className="flex-1">
                  <Text className="text-white text-base font-medium">
                    Cost Optimization
                  </Text>
                  <Text className="text-white/70 text-sm">
                    Manage AI usage costs and optimization settings
                  </Text>
                </VStack>
                <ArrowLeftIcon 
                  size={20} 
                  color="white" 
                  style={{ transform: [{ rotate: '180deg' }] }} 
                />
              </HStack>
            </Button>

            <Button
              onPress={() => router.push('/analytics/export')}
              className="bg-white/10 border border-white/20 rounded-xl p-4"
            >
              <HStack className="items-center justify-between w-full">
                <VStack space="xs" className="flex-1">
                  <Text className="text-white text-base font-medium">
                    Export Data
                  </Text>
                  <Text className="text-white/70 text-sm">
                    Export analytics data for external analysis
                  </Text>
                </VStack>
                <ArrowLeftIcon 
                  size={20} 
                  color="white" 
                  style={{ transform: [{ rotate: '180deg' }] }} 
                />
              </HStack>
            </Button>
          </VStack>
        </VStack>

        {/* Footer Spacing */}
        <Box className="h-8" />
      </ScrollView>
    </Box>
  );
}
