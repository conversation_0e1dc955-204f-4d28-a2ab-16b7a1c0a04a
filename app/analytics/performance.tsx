import React, { useEffect, useState } from 'react';
import { ScrollView, RefreshControl, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { 
  ArrowLeftIcon, 
  ActivityIcon, 
  AlertTriangleIcon, 
  CheckCircleIcon,
  TrendingUpIcon,
  MemoryStickIcon,
  ZapIcon
} from 'lucide-react-native';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { Button, ButtonText } from '@/components/ui/button';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { performanceMonitoringService, type PerformanceSummary, type PerformanceAlert } from '@/lib/services/performance-monitoring.service';

/**
 * Performance Monitoring Screen
 * Following Rule 6: UI Design Consistency with Candy Color Theme
 * Following Rule 12: Validation and Error Handling
 */
export default function PerformanceMonitoringScreen() {
  const router = useRouter();
  const [performanceSummary, setPerformanceSummary] = useState<PerformanceSummary | null>(null);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadPerformanceData();
  }, []);

  const loadPerformanceData = async () => {
    try {
      setIsLoading(true);
      
      const [summary, currentAlerts] = await Promise.all([
        performanceMonitoringService.getPerformanceSummary('day'),
        performanceMonitoringService.getCurrentAlerts(),
      ]);

      setPerformanceSummary(summary);
      setAlerts(currentAlerts);
      setIsLoading(false);
    } catch (error) {
      console.warn('Failed to load performance data:', error);
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadPerformanceData();
    setRefreshing(false);
  };

  const handleOptimizePerformance = async () => {
    try {
      Alert.alert(
        'Optimize Performance',
        'This will clear caches and optimize system performance. Continue?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Optimize', 
            onPress: async () => {
              await performanceMonitoringService.optimizePerformance();
              await loadPerformanceData();
              Alert.alert('Success', 'Performance optimization completed!');
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to optimize performance. Please try again.');
    }
  };

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <AlertTriangleIcon size={20} color="#EF4444" />;
      case 'warning':
        return <AlertTriangleIcon size={20} color="#F59E0B" />;
      default:
        return <CheckCircleIcon size={20} color="#10B981" />;
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box className="flex-1">
      {/* Background Gradient */}
      <LinearGradient
        colors={['#1e1b4b', '#312e81', '#4338ca', '#6366f1']}
        style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0 }}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <VStack space="md" className="pt-16 px-6 pb-4">
          <HStack className="items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onPress={() => router.back()}
              className="bg-white/10 border-white/20"
            >
              <HStack className="items-center space-x-2">
                <ArrowLeftIcon size={16} color="white" />
                <ButtonText className="text-white text-sm">Back</ButtonText>
              </HStack>
            </Button>

            <Button
              variant="outline"
              size="sm"
              onPress={handleOptimizePerformance}
              className="bg-emerald-600/20 border-emerald-400/30"
            >
              <ButtonText className="text-emerald-300 text-sm">Optimize</ButtonText>
            </Button>
          </HStack>

          <VStack space="sm">
            <Text className="text-white text-3xl font-bold">
              Performance Monitor
            </Text>
            <Text className="text-white/70 text-base">
              Real-time system performance and optimization
            </Text>
          </VStack>
        </VStack>

        {/* Alerts Section */}
        {alerts.length > 0 && (
          <VStack space="md" className="px-6 mb-6">
            <Text className="text-white text-xl font-semibold">
              Active Alerts ({alerts.length})
            </Text>
            
            <VStack space="sm">
              {alerts.slice(0, 3).map((alert) => (
                <Box
                  key={alert.id}
                  className="bg-red-500/20 border border-red-400/30 rounded-xl p-4"
                >
                  <HStack className="items-center space-x-3">
                    {getAlertIcon(alert.severity)}
                    <VStack space="xs" className="flex-1">
                      <Text className="text-red-300 text-sm font-medium">
                        {alert.message}
                      </Text>
                      <Text className="text-red-400/70 text-xs">
                        {new Date(alert.timestamp).toLocaleTimeString()}
                      </Text>
                    </VStack>
                  </HStack>
                </Box>
              ))}
            </VStack>
          </VStack>
        )}

        {/* Performance Metrics */}
        {performanceSummary && (
          <VStack space="md" className="px-6 mb-6">
            <Text className="text-white text-xl font-semibold">
              Performance Metrics
            </Text>

            <VStack space="sm">
              {/* Memory Usage */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center space-x-3 mb-3">
                  <MemoryStickIcon size={24} color="#10B981" />
                  <Text className="text-white text-lg font-medium">Memory Usage</Text>
                </HStack>
                <VStack space="xs">
                  <HStack className="justify-between">
                    <Text className="text-white/70 text-sm">Average</Text>
                    <Text className="text-white text-sm font-medium">
                      {formatBytes(performanceSummary.metrics.memoryUsage.average * 1024 * 1024)}
                    </Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-white/70 text-sm">Peak</Text>
                    <Text className="text-white text-sm font-medium">
                      {formatBytes(performanceSummary.metrics.memoryUsage.peak * 1024 * 1024)}
                    </Text>
                  </HStack>
                </VStack>
              </Box>

              {/* Cache Performance */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center space-x-3 mb-3">
                  <ZapIcon size={24} color="#A855F7" />
                  <Text className="text-white text-lg font-medium">Cache Performance</Text>
                </HStack>
                <VStack space="xs">
                  <HStack className="justify-between">
                    <Text className="text-white/70 text-sm">Hit Rate</Text>
                    <Text className="text-white text-sm font-medium">
                      {performanceSummary.metrics.cachePerformance.hitRate.toFixed(1)}%
                    </Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-white/70 text-sm">Total Requests</Text>
                    <Text className="text-white text-sm font-medium">
                      {performanceSummary.metrics.cachePerformance.totalRequests}
                    </Text>
                  </HStack>
                </VStack>
              </Box>

              {/* User Engagement */}
              <Box className="bg-white/10 border border-white/20 rounded-xl p-4">
                <HStack className="items-center space-x-3 mb-3">
                  <TrendingUpIcon size={24} color="#F59E0B" />
                  <Text className="text-white text-lg font-medium">User Engagement</Text>
                </HStack>
                <VStack space="xs">
                  <HStack className="justify-between">
                    <Text className="text-white/70 text-sm">Session Duration</Text>
                    <Text className="text-white text-sm font-medium">
                      {Math.round(performanceSummary.metrics.userEngagement.sessionDuration / 60000)}m
                    </Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-white/70 text-sm">Features Used</Text>
                    <Text className="text-white text-sm font-medium">
                      {performanceSummary.metrics.userEngagement.featuresUsed}
                    </Text>
                  </HStack>
                </VStack>
              </Box>
            </VStack>
          </VStack>
        )}

        {/* Recommendations */}
        {performanceSummary?.recommendations && performanceSummary.recommendations.length > 0 && (
          <VStack space="md" className="px-6 mb-6">
            <Text className="text-white text-xl font-semibold">
              Optimization Recommendations
            </Text>
            
            <VStack space="sm">
              {performanceSummary.recommendations.map((recommendation, index) => (
                <Box
                  key={index}
                  className="bg-blue-500/20 border border-blue-400/30 rounded-xl p-4"
                >
                  <Text className="text-blue-300 text-sm">
                    • {recommendation}
                  </Text>
                </Box>
              ))}
            </VStack>
          </VStack>
        )}

        {/* Loading State */}
        {isLoading && (
          <VStack space="md" className="px-6 py-8">
            <Box className="bg-white/10 rounded-xl p-6 items-center">
              <ActivityIcon size={32} color="white" />
              <Text className="text-white text-lg font-medium mt-4 mb-2">
                Loading Performance Data...
              </Text>
              <Text className="text-white/70 text-sm text-center">
                Analyzing system performance and gathering metrics
              </Text>
            </Box>
          </VStack>
        )}

        {/* Footer Spacing */}
        <Box className="h-8" />
      </ScrollView>
    </Box>
  );
}
