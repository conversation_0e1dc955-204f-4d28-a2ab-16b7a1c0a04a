// // import { Drawer } from 'expo-router/drawer';
import { useColorScheme } from 'react-native';
import { Stack } from 'expo-router';
import { 
  SettingsIcon,
  HelpCircleIcon,
  InfoIcon,
  ShieldIcon,
  CloudIcon,
  BellIcon,
  StarIcon,
  UserIcon
} from 'lucide-react-native';

export default function DrawerLayout() {
  const colorScheme = useColorScheme();

  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: colorScheme === 'dark' ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.95)',
        },
        headerTintColor: colorScheme === 'dark' ? '#F3F4F6' : '#111827',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        headerShadowVisible: false,
        headerTransparent: true,
        headerBlurEffect: 'regular',
      }}
    >
      <Stack.Screen
        name="settings"
        options={{
          title: 'Settings',
        }}
      />
      <Stack.Screen
        name="account"
        options={{
          title: 'Account',
        }}
      />
      <Stack.Screen
        name="premium"
        options={{
          title: 'Premium Features',
        }}
      />
      <Stack.Screen
        name="cloud-sync"
        options={{
          title: 'Cloud Sync',
        }}
      />
      <Stack.Screen
        name="notifications"
        options={{
          title: 'Notifications',
        }}
      />
      <Stack.Screen
        name="privacy"
        options={{
          title: 'Privacy & Security',
        }}
      />
      <Stack.Screen
        name="help"
        options={{
          title: 'Help & Support',
        }}
      />
      <Stack.Screen
        name="about"
        options={{
          title: 'About',
        }}
      />
    </Stack>
  );
}