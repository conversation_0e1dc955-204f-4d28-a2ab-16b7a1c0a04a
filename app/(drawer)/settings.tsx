import React, { useState } from 'react';
import { ScrollView, View } from 'react-native';
import { Text } from '../../components/ui/text';
import { VStack } from '../../components/ui/vstack';
import { HStack } from '../../components/ui/hstack';
import { Box } from '../../components/ui/box';
import { Button, ButtonText } from '../../components/ui/button';
import { Switch } from '../../components/ui/switch';
import { useRouter } from 'expo-router';
import {
  SettingsIcon,
  CameraIcon,
  CloudIcon,
  ShieldIcon,
  WrenchIcon,
  BarChart3Icon,
  ActivityIcon,
  MicIcon,
  AccessibilityIcon
} from 'lucide-react-native';

export default function SettingsScreen() {
  const router = useRouter();
  
  const [settings, setSettings] = useState({
    autoEnhance: true,
    scanQuality: 'high' as const,
    autoSave: true,
    darkMode: false,
    notifications: true,
    cloudSync: true,
    autoBackup: true,
    wifiOnly: false
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <ScrollView className="flex-1 bg-background-0">
      <VStack space="lg" className="p-6 pb-24">
        {/* Scan Settings */}
        <VStack space="md">
          <HStack className="items-center space-x-3">
            <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyPink/20">
              <CameraIcon size={20} color="#FF6B9D" />
            </Box>
            <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
              Scan Settings
            </Text>
          </HStack>
          
          <VStack space="sm">
            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Auto Enhancement
                  </Text>
                  <Text color="secondary" size="sm">
                    Automatically enhance scanned images
                  </Text>
                </VStack>
                <Switch
                  value={settings.autoEnhance}
                  onValueChange={(value) => handleSettingChange('autoEnhance', value)}
                />
              </HStack>
            </Box>

            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Auto Save
                  </Text>
                  <Text color="secondary" size="sm">
                    Automatically save scanned documents
                  </Text>
                </VStack>
                <Switch
                  value={settings.autoSave}
                  onValueChange={(value) => handleSettingChange('autoSave', value)}
                />
              </HStack>
            </Box>
          </VStack>
        </VStack>

        {/* App Settings */}
        <VStack space="md">
          <HStack className="items-center space-x-3">
            <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyPurple/20">
              <SettingsIcon size={20} color="#A855F7" />
            </Box>
            <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
              App Settings
            </Text>
          </HStack>
          
          <VStack space="sm">
            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Dark Mode
                  </Text>
                  <Text color="secondary" size="sm">
                    Use dark theme
                  </Text>
                </VStack>
                <Switch
                  value={settings.darkMode}
                  onValueChange={(value) => handleSettingChange('darkMode', value)}
                />
              </HStack>
            </Box>

            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Notifications
                  </Text>
                  <Text color="secondary" size="sm">
                    Receive app notifications
                  </Text>
                </VStack>
                <Switch
                  value={settings.notifications}
                  onValueChange={(value) => handleSettingChange('notifications', value)}
                />
              </HStack>
            </Box>
          </VStack>
        </VStack>

        {/* Cloud Settings */}
        <VStack space="md">
          <HStack className="items-center space-x-3">
            <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyBlue/20">
              <CloudIcon size={20} color="#3B82F6" />
            </Box>
            <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
              Cloud & Sync
            </Text>
          </HStack>
          
          <VStack space="sm">
            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Cloud Sync
                  </Text>
                  <Text color="secondary" size="sm">
                    Sync documents across devices
                  </Text>
                </VStack>
                <Switch
                  value={settings.cloudSync}
                  onValueChange={(value) => handleSettingChange('cloudSync', value)}
                />
              </HStack>
            </Box>

            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Auto Backup
                  </Text>
                  <Text color="secondary" size="sm">
                    Automatically backup to cloud
                  </Text>
                </VStack>
                <Switch
                  value={settings.autoBackup}
                  onValueChange={(value) => handleSettingChange('autoBackup', value)}
                />
              </HStack>
            </Box>
          </VStack>
        </VStack>

        {/* Analytics & Monitoring */}
        <VStack space="md">
          <HStack className="items-center space-x-3">
            <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyPurple/20">
              <BarChart3Icon size={20} color="#A855F7" />
            </Box>
            <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
              Analytics & Monitoring
            </Text>
          </HStack>

          <VStack space="sm">
            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <VStack space="sm">
                <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                  Analytics Dashboard
                </Text>
                <Text color="secondary" size="sm">
                  View learning progress, AI usage, and performance metrics
                </Text>
                <Button
                  onPress={() => router.push('/analytics/dashboard')}
                  className="bg-candyPurple hover:bg-purple-700 mt-2"
                >
                  <ButtonText className="text-white">
                    View Analytics
                  </ButtonText>
                </Button>
              </VStack>
            </Box>

            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <VStack space="sm">
                <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                  Performance Monitoring
                </Text>
                <Text color="secondary" size="sm">
                  Monitor system performance and optimize resource usage
                </Text>
                <Button
                  onPress={() => router.push('/analytics/performance')}
                  className="bg-emerald-600 hover:bg-emerald-700 mt-2"
                >
                  <ButtonText className="text-white">
                    View Performance
                  </ButtonText>
                </Button>
              </VStack>
            </Box>
          </VStack>
        </VStack>

        {/* Accessibility & Voice */}
        <VStack space="md">
          <HStack className="items-center space-x-3">
            <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyBlue/20">
              <AccessibilityIcon size={20} color="#3B82F6" />
            </Box>
            <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
              Accessibility & Voice
            </Text>
          </HStack>

          <VStack space="sm">
            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <VStack space="sm">
                <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                  Voice Input Settings
                </Text>
                <Text color="secondary" size="sm">
                  Configure voice input and speech recognition
                </Text>
                <Button
                  onPress={() => router.push('/settings/voice')}
                  className="bg-candyBlue hover:bg-blue-700 mt-2"
                >
                  <ButtonText className="text-white">
                    Voice Settings
                  </ButtonText>
                </Button>
              </VStack>
            </Box>

            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <VStack space="sm">
                <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                  Accessibility Options
                </Text>
                <Text color="secondary" size="sm">
                  Configure accessibility features and preferences
                </Text>
                <Button
                  onPress={() => router.push('/settings/accessibility')}
                  className="bg-green-600 hover:bg-green-700 mt-2"
                >
                  <ButtonText className="text-white">
                    Accessibility
                  </ButtonText>
                </Button>
              </VStack>
            </Box>
          </VStack>
        </VStack>

        {/* Development Settings */}
        {__DEV__ && (
          <VStack space="md">
            <HStack className="items-center space-x-3">
              <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyOrange/20">
                <WrenchIcon size={20} color="#F97316" />
              </Box>
              <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                Development
              </Text>
            </HStack>

            <VStack space="sm">
              <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
                <VStack space="sm">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    AI Service Test
                  </Text>
                  <Text color="secondary" size="sm">
                    Test AI service configuration and connectivity
                  </Text>
                  <Button
                    onPress={() => router.push('/test/ai-service')}
                    className="bg-emerald-600 hover:bg-emerald-700 mt-2"
                  >
                    <ButtonText className="text-white">
                      Run AI Tests
                    </ButtonText>
                  </Button>
                </VStack>
              </Box>
            </VStack>
          </VStack>
        )}
      </VStack>
    </ScrollView>
  );
}