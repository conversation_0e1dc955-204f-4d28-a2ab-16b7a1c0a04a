# Pocket Manus Project Overview

## Architecture Diagram

```mermaid
%% Store and App Architecture
flowchart TD
    subgraph "Store Architecture"
        Z[Zustand Store] -->|creates| US[Store State]
        Z -->|provides| UA[Store Actions]
        Z -->|computes| UC[Derived State]
    end
    subgraph "State Management Pattern"
        US -->|read by| C[Components]
        UA -->|called by| C
        UC -->|used by| C
        C -->|dispatches| UA
        UA -->|updates| US
    end
    subgraph "Store Files"
        UIStore["UIStore.ts\n(UI Settings)"]
        ChatSessionStore["ChatSessionStore.ts\n(Chat Sessions)"]
        BenchmarkStore["BenchmarkStore.ts\n(Benchmarks)"]
        todoStore["todoStore.ts\n(Todo List)"]
    end
    subgraph "Store Dependencies"
        ChatSessionStore --> UIStore
        BenchmarkStore --> UIStore
        todoStore --> UIStore
    end
    subgraph "App Structure"
        App["Expo App"]
        Router["expo-router"]
        Pages["app/(tabs)/*, app/login, app/models, etc."]
        Components["components/*"]
        Lib["lib/*"]
        Store["store/*"]
        App --> Router
        Router --> Pages
        Pages --> Components
        Pages --> Store
        Components --> Lib
        Components --> Store
    end
    subgraph "Helpers & Utilities"
        Helpers["lib/helpers/*, utils/*"]
        Lib --> Helpers
        Store --> Helpers
    end
    subgraph "Persistence"
        AsyncStorage["AsyncStorage (Persistent)"]
        Memory["Memory (Volatile)"]
        UIStore -->|persists subset| AsyncStorage
        ChatSessionStore -->|persists subset| AsyncStorage
        BenchmarkStore -->|persists subset| AsyncStorage
        todoStore -->|persists subset| AsyncStorage
    end
```

---

## 3rd-Party Libraries

| Library | Use Case | Example |
|---------|----------|---------|
| **expo** | Core React Native framework | App bootstrapping, device APIs |
| **expo-router** | File-based routing | `app/(tabs)/home.tsx` navigation |
| **zustand** | State management | `store/todoStore.ts`, `store/ChatSessionStore.ts` |
| **@ai-sdk/openai, ai** | AI/LLM API integration | Chat, model inference |
| **@dr.pogodin/react-native-fs** | File system access | Session storage, file utilities |
| **@gluestack-ui/** | UI components | Buttons, overlays, spinners |
| **nativewind, tailwindcss** | Utility-first styling | Responsive, theme-aware UI |
| **@react-navigation/** | Navigation | Tab, drawer, stack navigation |
| **react-native-gesture-handler, reanimated** | Gestures/animations | Smooth UI interactions |
| **@react-native-async-storage/async-storage** | Persistent storage | User/session data |
| **axios** | HTTP requests | API calls, OAuth |
| **date-fns** | Date utilities | Formatting, time ago |
| **zod** | Schema validation | Data validation |
| **i18n-js, expo-localization** | Internationalization | Multi-language support |
| **lucide-react-native, @expo/vector-icons** | Icons | UI icons |
| **swr** | Data fetching/caching | Remote data hooks |
| **radash, clsx, tailwind-merge** | Utilities | Data, class merging |

---

## Project Helpers / Tools

| Helper/Tool | Path | Purpose |
|-------------|------|---------|
| **deepMerge** | `lib/helpers/deepMerge.ts` | Deep object merging |
| **formatBytes** | `lib/helpers/formatBytes.ts` | Human-readable byte formatting |
| **formatNumber** | `lib/helpers/formatNumber.ts` | Number formatting (K/M/B) |
| **timeAgo** | `lib/helpers/timeAgo.ts` | Relative time strings |
| **generateAPIUrl** | `lib/helpers/generateAPIUrl.ts` | API URL builder |
| **getSHA256Hash** | `lib/helpers/getSHA256Hash.ts` | File hash calculation |
| **polyfill.js** | `lib/helpers/polyfill.js` | Polyfills for RN/web |
| **testGetApi/testPostApi** | `lib/helpers/testGetApi.ts` | API test utilities |
| **storeHelpers** | `lib/helpers/storeHelpers.ts`, `utils/storeHelpers.ts` | Store initialization |
| **chat.ts** | `utils/chat.ts` | Chat formatting, template application |
| **types.ts** | `utils/types.ts` | Shared types/interfaces |
| **cn** | `lib/utils.ts` | Class name merging |

---

## Main Modules

| Module | Path | Responsibility |
|--------|------|---------------|
| **UIStore** | `store/UIStore.ts` | UI state, theme, preferences |
| **ChatSessionStore** | `store/ChatSessionStore.ts` | Chat session management |
| **BenchmarkStore** | `store/BenchmarkStore.ts` | Model benchmark results |
| **todoStore** | `store/todoStore.ts` | Todo list state |
| **api** | `lib/api/index.ts` | App API, OAuth, user profile |
| **githubApi** | `lib/api/github.ts` | GitHub OAuth/user info |
| **helpers** | `lib/helpers/*` | Utility functions |
| **components** | `components/*` | UI components (Chat, Themed, etc.) |
| **constants** | `constants/Colors.ts` | Color palette |
| **i18n** | `i18n/*` | Localization setup |
| **types/interfaces** | `types/*`, `interfaces/*` | Shared types/interfaces |

---

> This README is auto-generated to reflect the current architecture, dependencies, helpers, and modules of the Pocket Manus project.
