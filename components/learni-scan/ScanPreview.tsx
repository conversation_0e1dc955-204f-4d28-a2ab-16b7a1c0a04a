import React, { useState } from 'react';
import { View, Image, Dimensions, StyleSheet } from 'react-native';
import { Text } from '../ui/text';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Pressable } from '../ui/pressable';
import { Slider, SliderTrack, SliderFilledTrack, SliderThumb } from '../ui/slider';
import { 
  CheckIcon, 
  XIcon, 
  RotateCcwIcon, 
  CropIcon,
  SlidersHorizontalIcon,
  SparklesIcon,
  DownloadIcon
} from 'lucide-react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ScanPreviewProps {
  imageUri: string;
  onAccept?: (processedImageUri: string, settings: EnhancementSettings) => void;
  onRetake?: () => void;
  onCancel?: () => void;
  initialSettings?: Partial<EnhancementSettings>;
  className?: string;
}

interface EnhancementSettings {
  brightness: number;
  contrast: number;
  saturation: number;
  sharpness: number;
  autoEnhance: boolean;
  documentMode: 'auto' | 'text' | 'photo' | 'drawing';
}

export function ScanPreview({
  imageUri,
  onAccept,
  onRetake,
  onCancel,
  initialSettings = {},
  className
}: ScanPreviewProps) {
  const [settings, setSettings] = useState<EnhancementSettings>({
    brightness: 50,
    contrast: 50,
    saturation: 50,
    sharpness: 50,
    autoEnhance: true,
    documentMode: 'auto',
    ...initialSettings
  });

  const [showEnhancements, setShowEnhancements] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState('');

  const documentModes = [
    { key: 'auto', label: 'Auto', color: '#FF6B9D' },
    { key: 'text', label: 'Text', color: '#A855F7' },
    { key: 'photo', label: 'Photo', color: '#3B82F6' },
    { key: 'drawing', label: 'Drawing', color: '#10B981' }
  ];

  const handleAccept = async () => {
    setIsProcessing(true);
    
    // Simulate AI processing steps
    const steps = [
      'Analyzing document...',
      'Applying enhancements...',
      'Optimizing quality...',
      'Finalizing scan...'
    ];

    for (let i = 0; i < steps.length; i++) {
      setProcessingStep(steps[i]);
      await new Promise(resolve => setTimeout(resolve, 800));
    }

    // In a real app, this would process the image with the settings
    onAccept?.(imageUri, settings);
    setIsProcessing(false);
  };

  const resetSettings = () => {
    setSettings({
      brightness: 50,
      contrast: 50,
      saturation: 50,
      sharpness: 50,
      autoEnhance: true,
      documentMode: 'auto'
    });
  };

  if (isProcessing) {
    return (
      <Box className="flex-1 bg-black justify-center items-center">
        <VStack space="lg" style={{ alignItems: 'center' }}>
          <Box className="w-20 h-20 justify-center items-center rounded-full bg-glass-bg-primary border-2 border-glass-border-primary">
            <SparklesIcon size={40} color="#FF6B9D" />
          </Box>
          
          <VStack space="md" style={{ alignItems: 'center' }}>
            <Text color="primary" size="xl" style={{ fontWeight: 'bold' }}>
              Processing Document
            </Text>
            <Text color="candyPink" size="md">
              {processingStep}
            </Text>
          </VStack>

          <Box className="w-64 h-2 bg-glass-bg-secondary rounded-full overflow-hidden">
            <Box 
              className="h-full bg-gradient-to-r from-candyPink to-candyPurple rounded-full"
              style={{ 
                width: `${((steps.indexOf(processingStep) + 1) / steps.length) * 100}%`,
                transition: 'width 0.3s ease'
              }}
            />
          </Box>
        </VStack>
      </Box>
    );
  }

  return (
    <View style={[styles.container, className && { className }]}>
      {/* Header */}
      <Box className="absolute top-0 left-0 right-0 z-10 bg-glass-bg-primary/90 backdrop-blur-md border-b border-glass-border-primary">
        <HStack className="justify-between items-center p-4 pt-12">
          <Pressable variant="glass" onPress={onCancel}>
            <Box className="p-2 rounded-full">
              <XIcon size={24} color="#FFFFFF" />
            </Box>
          </Pressable>
          
          <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
            Scan Preview
          </Text>
          
          <Pressable variant="glass" onPress={() => setShowEnhancements(!showEnhancements)}>
            <Box className="p-2 rounded-full">
              <AdjustmentsHorizontalIcon size={24} color={showEnhancements ? "#FF6B9D" : "#FFFFFF"} />
            </Box>
          </Pressable>
        </HStack>
      </Box>

      {/* Image Preview */}
      <Box className="flex-1 justify-center items-center bg-black" style={{ marginTop: 100 }}>
        <Image 
          source={{ uri: imageUri }} 
          style={styles.previewImage}
          resizeMode="contain"
        />
        
        {/* Quality Indicator Overlay */}
        <Box className="absolute top-4 right-4 bg-glass-bg-card border border-glass-border-secondary rounded-full px-3 py-1">
          <Text color="candyPink" size="sm" style={{ fontWeight: '600' }}>
            HIGH QUALITY
          </Text>
        </Box>
      </Box>

      {/* Enhancement Panel */}
      {showEnhancements && (
        <Box className="absolute bottom-20 left-0 right-0 bg-glass-bg-primary/95 backdrop-blur-md border-t border-glass-border-primary">
          <VStack space="lg" className="p-6">
            {/* Document Mode Selection */}
            <VStack space="sm">
              <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                Document Mode:
              </Text>
              <HStack className="justify-between">
                {documentModes.map((mode) => (
                  <Pressable
                    key={mode.key}
                    variant="enhanced"
                    onPress={() => setSettings(prev => ({ ...prev, documentMode: mode.key as any }))}
                  >
                    <Box 
                      className={`px-3 py-2 rounded-full border ${
                        settings.documentMode === mode.key 
                          ? 'bg-glass-bg-card border-glass-border-accent' 
                          : 'bg-glass-bg-secondary border-glass-border-primary'
                      }`}
                    >
                      <Text 
                        color={settings.documentMode === mode.key ? "primary" : "secondary"} 
                        size="sm"
                        style={{ fontWeight: settings.documentMode === mode.key ? '600' : 'normal' }}
                      >
                        {mode.label}
                      </Text>
                    </Box>
                  </Pressable>
                ))}
              </HStack>
            </VStack>

            {/* Enhancement Sliders */}
            <VStack space="md">
              {/* Brightness */}
              <VStack space="xs">
                <HStack className="justify-between">
                  <Text color="tertiary" size="sm">Brightness</Text>
                  <Text color="candyPink" size="sm">{settings.brightness}%</Text>
                </HStack>
                <Slider
                  value={settings.brightness}
                  onChange={(value) => setSettings(prev => ({ ...prev, brightness: value }))}
                  minValue={0}
                  maxValue={100}
                  size="sm"
                >
                  <SliderTrack>
                    <SliderFilledTrack color="candyPink" />
                  </SliderTrack>
                  <SliderThumb color="candyPink" />
                </Slider>
              </VStack>

              {/* Contrast */}
              <VStack space="xs">
                <HStack className="justify-between">
                  <Text color="tertiary" size="sm">Contrast</Text>
                  <Text color="candyPurple" size="sm">{settings.contrast}%</Text>
                </HStack>
                <Slider
                  value={settings.contrast}
                  onChange={(value) => setSettings(prev => ({ ...prev, contrast: value }))}
                  minValue={0}
                  maxValue={100}
                  size="sm"
                >
                  <SliderTrack>
                    <SliderFilledTrack color="candyPurple" />
                  </SliderTrack>
                  <SliderThumb color="candyPurple" />
                </Slider>
              </VStack>

              {/* Sharpness */}
              <VStack space="xs">
                <HStack className="justify-between">
                  <Text color="tertiary" size="sm">Sharpness</Text>
                  <Text color="candyBlue" size="sm">{settings.sharpness}%</Text>
                </HStack>
                <Slider
                  value={settings.sharpness}
                  onChange={(value) => setSettings(prev => ({ ...prev, sharpness: value }))}
                  minValue={0}
                  maxValue={100}
                  size="sm"
                >
                  <SliderTrack>
                    <SliderFilledTrack color="candyBlue" />
                  </SliderTrack>
                  <SliderThumb color="candyBlue" />
                </Slider>
              </VStack>
            </VStack>

            {/* Reset Button */}
            <HStack className="justify-center">
              <Pressable variant="glass" onPress={resetSettings}>
                <HStack className="items-center space-x-2 px-4 py-2 rounded-full bg-glass-bg-secondary border border-glass-border-primary">
                  <RotateCcwIcon size={16} color="#FFFFFF" />
                  <Text color="primary" size="sm">Reset</Text>
                </HStack>
              </Pressable>
            </HStack>
          </VStack>
        </Box>
      )}

      {/* Bottom Controls */}
      <Box className="absolute bottom-0 left-0 right-0 bg-glass-bg-primary/90 backdrop-blur-md border-t border-glass-border-primary">
        <HStack className="justify-between items-center p-6 pb-8">
          {/* Retake */}
          <Button action="glass" variant="outline" onPress={onRetake}>
            <ButtonText>Retake</ButtonText>
          </Button>

          {/* Accept */}
          <Button action="candyPink" onPress={handleAccept} style={{ minWidth: 120 }}>
            <HStack className="items-center space-x-2">
              <CheckIcon size={20} color="#FFFFFF" />
              <ButtonText>Accept</ButtonText>
            </HStack>
          </Button>
        </HStack>
      </Box>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  previewImage: {
    width: screenWidth * 0.9,
    height: screenHeight * 0.6,
    borderRadius: 12,
  },
});

export default ScanPreview;