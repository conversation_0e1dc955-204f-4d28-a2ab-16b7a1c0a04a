import React, { useEffect, useState } from 'react';
import { View, Animated } from 'react-native';
import { Text } from '../ui/text';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import {
  CheckCircleIcon,
  AlertTriangleIcon,
  XCircleIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  MinusIcon,
  BrainIcon,
  EyeIcon,
  ZapIcon,
  SparklesIcon,
  type LucideIcon
} from 'lucide-react-native';

// Type-safe icon wrapper for LucideIcon compatibility
const createIconComponent = (IconComponent: LucideIcon): React.ComponentType<{ size: number; color: string }> => {
  return ({ size, color }) => <IconComponent size={size} color={color} />;
};

interface ConfidenceScoreProps {
  score: number; // 0-100
  previousScore?: number;
  category?: string;
  breakdown?: ConfidenceBreakdown[];
  showTrend?: boolean;
  showBreakdown?: boolean;
  animated?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'glass' | 'glassCard';
  className?: string;
}

interface ConfidenceBreakdown {
  category: string;
  score: number;
  weight: number;
  icon?: React.ComponentType<{ size: number; color: string }>;
  description?: string;
}

const defaultBreakdown: ConfidenceBreakdown[] = [
  {
    category: 'Text Recognition',
    score: 92,
    weight: 0.4,
    icon: createIconComponent(EyeIcon),
    description: 'OCR accuracy and character recognition'
  },
  {
    category: 'Image Quality',
    score: 85,
    weight: 0.3,
    icon: createIconComponent(SparklesIcon),
    description: 'Image clarity, focus, and lighting'
  },
  {
    category: 'Document Structure',
    score: 78,
    weight: 0.2,
    icon: createIconComponent(BrainIcon),
    description: 'Layout analysis and content organization'
  },
  {
    category: 'Processing Speed',
    score: 95,
    weight: 0.1,
    icon: createIconComponent(ZapIcon),
    description: 'AI processing efficiency'
  }
];

export function ConfidenceScore({
  score,
  previousScore,
  category = 'Overall',
  breakdown = defaultBreakdown,
  showTrend = true,
  showBreakdown = false,
  animated = true,
  size = 'md',
  variant = 'glass',
  className
}: ConfidenceScoreProps) {
  const [animatedScore] = useState(new Animated.Value(0));
  const [displayScore, setDisplayScore] = useState(0);
  const [animatedBreakdown] = useState(
    breakdown.map(() => new Animated.Value(0))
  );

  useEffect(() => {
    if (animated) {
      // Animate main score
      Animated.timing(animatedScore, {
        toValue: score,
        duration: 1500,
        useNativeDriver: false,
      }).start();

      const listener = animatedScore.addListener(({ value }) => {
        setDisplayScore(Math.round(value));
      });

      // Animate breakdown scores
      breakdown.forEach((item, index) => {
        Animated.timing(animatedBreakdown[index], {
          toValue: item.score,
          duration: 1500 + (index * 200),
          useNativeDriver: false,
        }).start();
      });

      return () => animatedScore.removeListener(listener);
    } else {
      setDisplayScore(score);
    }
  }, [score, animated]);

  const getScoreLevel = (score: number) => {
    if (score >= 90) return { level: 'excellent', color: '#10B981', bgColor: 'rgba(16, 185, 129, 0.1)' };
    if (score >= 80) return { level: 'very-good', color: '#059669', bgColor: 'rgba(5, 150, 105, 0.1)' };
    if (score >= 70) return { level: 'good', color: '#FF6B9D', bgColor: 'rgba(255, 107, 157, 0.1)' };
    if (score >= 60) return { level: 'fair', color: '#F59E0B', bgColor: 'rgba(245, 158, 11, 0.1)' };
    if (score >= 40) return { level: 'poor', color: '#EF4444', bgColor: 'rgba(239, 68, 68, 0.1)' };
    return { level: 'very-poor', color: '#DC2626', bgColor: 'rgba(220, 38, 38, 0.1)' };
  };

  const getScoreIcon = (score: number) => {
    if (score >= 80) return CheckCircleIcon;
    if (score >= 60) return AlertTriangleIcon;
    return XCircleIcon;
  };

  const getScoreText = (score: number) => {
    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Very Good';
    if (score >= 70) return 'Good';
    if (score >= 60) return 'Fair';
    if (score >= 40) return 'Poor';
    return 'Very Poor';
  };

  const getTrendIcon = () => {
    if (!previousScore) return MinusIcon;
    if (score > previousScore) return TrendingUpIcon;
    if (score < previousScore) return TrendingDownIcon;
    return MinusIcon;
  };

  const getTrendColor = () => {
    if (!previousScore) return '#9CA3AF';
    if (score > previousScore) return '#10B981';
    if (score < previousScore) return '#EF4444';
    return '#9CA3AF';
  };

  const getTrendText = () => {
    if (!previousScore) return 'No previous data';
    const diff = score - previousScore;
    if (diff > 0) return `+${diff} points`;
    if (diff < 0) return `${diff} points`;
    return 'No change';
  };

  const scoreLevel = getScoreLevel(displayScore);
  const ScoreIcon = getScoreIcon(displayScore);
  const scoreText = getScoreText(displayScore);
  const TrendIcon = getTrendIcon();
  const trendColor = getTrendColor();
  const trendText = getTrendText();

  const sizeConfig = {
    sm: { 
      circleSize: 80, 
      iconSize: 20, 
      textSize: 'sm' as const, 
      scoreSize: 'lg' as const,
      padding: 'p-3',
      spacing: 'sm' as const
    },
    md: { 
      circleSize: 120, 
      iconSize: 24, 
      textSize: 'md' as const, 
      scoreSize: 'xl' as const,
      padding: 'p-4',
      spacing: 'md' as const
    },
    lg: { 
      circleSize: 160, 
      iconSize: 32, 
      textSize: 'lg' as const, 
      scoreSize: '2xl' as const,
      padding: 'p-6',
      spacing: 'lg' as const
    }
  };

  const config = sizeConfig[size];

  const containerClasses = {
    default: 'bg-background-0 border border-outline-100',
    glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md',
    glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md'
  };

  return (
    <View className={className}>
      <VStack space={config.spacing}>
        {/* Main Score Display */}
        <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
          <VStack space="lg" style={{ alignItems: 'center' }}>
            {/* Score Circle */}
            <Box className="relative">
              <Box 
                className="justify-center items-center rounded-full border-4"
                style={{
                  width: config.circleSize,
                  height: config.circleSize,
                  backgroundColor: scoreLevel.bgColor,
                  borderColor: scoreLevel.color,
                }}
              >
                <VStack space="xs" style={{ alignItems: 'center' }}>
                  <Text 
                    color="primary" 
                    size={config.scoreSize} 
                    style={{ fontWeight: 'bold', color: scoreLevel.color }}
                  >
                    {displayScore}
                  </Text>
                  <ScoreIcon size={config.iconSize} color={scoreLevel.color} />
                </VStack>
              </Box>

              {/* Animated Progress Ring */}
              {animated && (
                <Box 
                  className="absolute inset-0 rounded-full border-4 border-transparent"
                  style={{
                    borderTopColor: scoreLevel.color,
                    transform: [{ rotate: `${(displayScore / 100) * 360}deg` }]
                  }}
                />
              )}
            </Box>

            {/* Score Label */}
            <VStack space="xs" style={{ alignItems: 'center' }}>
              <Text color="primary" size={config.textSize} style={{ fontWeight: 'bold' }}>
                {category} Confidence
              </Text>
              <Box 
                className="px-3 py-1 rounded-full border"
                style={{
                  backgroundColor: scoreLevel.bgColor,
                  borderColor: scoreLevel.color,
                }}
              >
                <Text 
                  size="sm" 
                  style={{ fontWeight: '600', color: scoreLevel.color }}
                >
                  {scoreText}
                </Text>
              </Box>
            </VStack>

            {/* Trend Indicator */}
            {showTrend && previousScore !== undefined && (
              <HStack className="items-center space-x-2">
                <TrendIcon size={16} color={trendColor} />
                <Text 
                  size="sm" 
                  style={{ color: trendColor, fontWeight: '600' }}
                >
                  {trendText}
                </Text>
              </HStack>
            )}
          </VStack>
        </Box>

        {/* Confidence Breakdown */}
        {showBreakdown && (
          <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
            <VStack space="lg">
              <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                Confidence Breakdown:
              </Text>
              
              {breakdown.map((item, index) => {
                const itemLevel = getScoreLevel(item.score);
                const ItemIcon = item.icon || BrainIcon;
                const [animatedValue] = useState(new Animated.Value(0));
                
                useEffect(() => {
                  if (animated) {
                    Animated.timing(animatedValue, {
                      toValue: item.score,
                      duration: 1000 + (index * 200),
                      useNativeDriver: false,
                    }).start();
                  }
                }, [item.score, animated]);
                
                return (
                  <Box 
                    key={item.category}
                    className="bg-glass-bg-secondary border border-glass-border-primary rounded-lg p-3"
                  >
                    <VStack space="sm">
                      <HStack className="justify-between items-center">
                        <HStack className="items-center space-x-3 flex-1">
                          <Box 
                            className="w-8 h-8 justify-center items-center rounded-full"
                            style={{ backgroundColor: itemLevel.bgColor }}
                          >
                            <ItemIcon size={16} color={itemLevel.color} />
                          </Box>
                          
                          <VStack space="xs" className="flex-1">
                            <Text color="primary" size="sm" style={{ fontWeight: '600' }}>
                              {item.category}
                            </Text>
                            {item.description && (
                              <Text color="secondary" size="xs">
                                {item.description}
                              </Text>
                            )}
                          </VStack>
                        </HStack>
                        
                        <VStack space="xs" style={{ alignItems: 'flex-end' }}>
                          <Text 
                            size="sm" 
                            style={{ fontWeight: 'bold', color: itemLevel.color }}
                          >
                            {item.score}%
                          </Text>
                          <Text color="tertiary" size="xs">
                            Weight: {Math.round(item.weight * 100)}%
                          </Text>
                        </VStack>
                      </HStack>
                      
                      {/* Progress Bar */}
                      <Box className="w-full h-2 bg-glass-bg-primary rounded-full overflow-hidden">
                        <Animated.View 
                          style={{
                            height: '100%',
                            backgroundColor: itemLevel.color,
                            borderRadius: 4,
                            width: animated ? animatedValue.interpolate({
                              inputRange: [0, 100],
                              outputRange: ['0%', '100%'],
                              extrapolate: 'clamp',
                            }) : `${item.score}%`
                          }}
                        />
                      </Box>
                    </VStack>
                  </Box>
                );
              })}
            </VStack>
          </Box>
        )}

        {/* Score Interpretation */}
        <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
          <VStack space="md">
            <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
              Score Interpretation:
            </Text>
            
            <VStack space="sm">
              <HStack className="justify-between">
                <Text color="secondary" size="sm">90-100%:</Text>
                <Text color="candyPink" size="sm" style={{ fontWeight: '600' }}>Excellent</Text>
              </HStack>
              <HStack className="justify-between">
                <Text color="secondary" size="sm">80-89%:</Text>
                <Text color="candyPink" size="sm" style={{ fontWeight: '600' }}>Very Good</Text>
              </HStack>
              <HStack className="justify-between">
                <Text color="secondary" size="sm">70-79%:</Text>
                <Text color="candyPink" size="sm" style={{ fontWeight: '600' }}>Good</Text>
              </HStack>
              <HStack className="justify-between">
                <Text color="secondary" size="sm">60-69%:</Text>
                <Text color="candyPurple" size="sm" style={{ fontWeight: '600' }}>Fair</Text>
              </HStack>
              <HStack className="justify-between">
                <Text color="secondary" size="sm">Below 60%:</Text>
                <Text color="candyBlue" size="sm" style={{ fontWeight: '600' }}>Needs Improvement</Text>
              </HStack>
            </VStack>
          </VStack>
        </Box>

        {/* Recommendations */}
        {displayScore < 80 && (
          <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
            <VStack space="md">
              <HStack className="items-center space-x-2">
                <SparklesIcon size={16} color="#A855F7" />
                <Text color="candyPurple" size="sm" style={{ fontWeight: '600' }}>
                  Improvement Suggestions:
                </Text>
              </HStack>
              
              <VStack space="xs">
                {displayScore < 70 && (
                  <Text color="secondary" size="xs">
                    • Ensure better lighting and document positioning
                  </Text>
                )}
                {breakdown.find(b => b.category.includes('Text') && b.score < 80) && (
                  <Text color="secondary" size="xs">
                    • Use higher resolution or better focus for text clarity
                  </Text>
                )}
                {breakdown.find(b => b.category.includes('Image') && b.score < 80) && (
                  <Text color="secondary" size="xs">
                    • Improve image quality with better lighting and stability
                  </Text>
                )}
                <Text color="secondary" size="xs">
                  • Try the AI auto-enhancement feature for better results
                </Text>
              </VStack>
            </VStack>
          </Box>
        )}
      </VStack>
    </View>
  );
}

export default ConfidenceScore;