import React, { useState } from 'react';
import { View, ScrollView } from 'react-native';
import { Text } from '../ui/text';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Badge, BadgeText } from '../ui/badge';
import { Pressable } from '../ui/pressable';
import { 
  CrownIcon, 
  SparklesIcon, 
  ZapIcon,
  CloudIcon,
  ShieldIcon,
  XIcon,
  CheckCircleIcon,
  StarIcon,
  TrendingUpIcon,
  InfinityIcon,
  LockIcon,
  GiftIcon
} from 'lucide-react-native';

interface UpgradePromptProps {
  onUpgrade?: (plan: PremiumPlan) => void;
  onDismiss?: () => void;
  onRestorePurchases?: () => void;
  currentPlan?: 'free' | 'premium' | 'pro';
  trigger?: 'limit_reached' | 'feature_locked' | 'manual' | 'trial_ending';
  variant?: 'modal' | 'banner' | 'fullscreen';
  showDismiss?: boolean;
  className?: string;
}

interface PremiumPlan {
  id: string;
  name: string;
  price: string;
  period: string;
  originalPrice?: string;
  discount?: string;
  color: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  features: PremiumFeature[];
  popular?: boolean;
  trial?: {
    duration: string;
    description: string;
  };
}

interface PremiumFeature {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  highlight?: boolean;
}

const premiumFeatures: PremiumFeature[] = [
  {
    id: 'unlimited_scans',
    title: 'Unlimited Scans',
    description: 'Scan as many documents as you need',
    icon: InfinityIcon,
    highlight: true
  },
  {
    id: 'ai_enhancement',
    title: 'Advanced AI Enhancement',
    description: 'Premium AI algorithms for perfect results',
    icon: SparklesIcon,
    highlight: true
  },
  {
    id: 'cloud_storage',
    title: 'Unlimited Cloud Storage',
    description: 'Store all your documents securely in the cloud',
    icon: CloudIcon
  },
  {
    id: 'batch_processing',
    title: 'Batch Processing',
    description: 'Process multiple documents simultaneously',
    icon: ZapIcon
  },
  {
    id: 'priority_support',
    title: 'Priority Support',
    description: '24/7 premium customer support',
    icon: ShieldIcon
  },
  {
    id: 'advanced_search',
    title: 'Advanced Search',
    description: 'Powerful search with filters and AI insights',
    icon: TrendingUpIcon
  }
];

const premiumPlans: PremiumPlan[] = [
  {
    id: 'premium_monthly',
    name: 'Premium',
    price: '$9.99',
    period: 'month',
    color: '#FF6B9D',
    icon: StarIcon,
    features: premiumFeatures.slice(0, 4),
    trial: {
      duration: '7 days free',
      description: 'Then $9.99/month'
    }
  },
  {
    id: 'premium_yearly',
    name: 'Premium Annual',
    price: '$79.99',
    period: 'year',
    originalPrice: '$119.88',
    discount: '33% OFF',
    color: '#A855F7',
    icon: CrownIcon,
    features: premiumFeatures,
    popular: true,
    trial: {
      duration: '7 days free',
      description: 'Then $79.99/year'
    }
  },
  {
    id: 'premium_lifetime',
    name: 'Lifetime Pro',
    price: '$199.99',
    period: 'lifetime',
    originalPrice: '$299.99',
    discount: 'LIMITED TIME',
    color: '#10B981',
    icon: InfinityIcon,
    features: [...premiumFeatures, {
      id: 'lifetime_updates',
      title: 'Lifetime Updates',
      description: 'All future features included forever',
      icon: GiftIcon,
      highlight: true
    }]
  }
];

const triggerMessages = {
  limit_reached: {
    title: 'Scan Limit Reached',
    subtitle: 'Upgrade to continue scanning',
    description: 'You\'ve reached your free scan limit. Upgrade to Premium for unlimited scans and advanced features.'
  },
  feature_locked: {
    title: 'Premium Feature',
    subtitle: 'Unlock advanced capabilities',
    description: 'This feature is available with Premium. Upgrade now to access all advanced AI features.'
  },
  manual: {
    title: 'Upgrade to Premium',
    subtitle: 'Unlock the full potential',
    description: 'Get unlimited scans, advanced AI, and premium features with LearniScan Premium.'
  },
  trial_ending: {
    title: 'Trial Ending Soon',
    subtitle: 'Continue your premium experience',
    description: 'Your free trial ends in 2 days. Upgrade now to keep all premium features.'
  }
};

export function UpgradePrompt({
  onUpgrade,
  onDismiss,
  onRestorePurchases,
  currentPlan = 'free',
  trigger = 'manual',
  variant = 'modal',
  showDismiss = true,
  className
}: UpgradePromptProps) {
  const [selectedPlan, setSelectedPlan] = useState<string>(premiumPlans[1].id); // Default to yearly

  const triggerMessage = triggerMessages[trigger];

  const handleUpgrade = () => {
    const plan = premiumPlans.find(p => p.id === selectedPlan);
    if (plan) {
      onUpgrade?.(plan);
    }
  };

  const containerClasses = {
    modal: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl',
    banner: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md rounded-lg',
    fullscreen: 'bg-glass-bg-primary backdrop-blur-md'
  };

  const PlanCard = ({ plan }: { plan: PremiumPlan }) => {
    const isSelected = selectedPlan === plan.id;
    const PlanIcon = plan.icon;
    
    return (
      <Pressable 
        onPress={() => setSelectedPlan(plan.id)}
        variant="enhanced"
      >
        <Box 
          className={`border rounded-xl p-4 ${
            isSelected 
              ? 'bg-glass-bg-card border-glass-border-accent' 
              : 'bg-glass-bg-secondary border-glass-border-primary'
          }`}
          style={{
            borderColor: isSelected ? plan.color : undefined,
            backgroundColor: isSelected ? plan.color + '10' : undefined
          }}
        >
          <VStack space="md">
            {/* Header */}
            <HStack className="justify-between items-start">
              <HStack className="items-center space-x-3">
                <Box 
                  className="w-10 h-10 justify-center items-center rounded-full"
                  style={{ backgroundColor: plan.color + '20' }}
                >
                  <PlanIcon size={20} color={plan.color} />
                </Box>
                
                <VStack space="xs">
                  <Text color="primary" size="md" style={{ fontWeight: 'bold' }}>
                    {plan.name}
                  </Text>
                  {plan.trial && (
                    <Text color="success" size="xs" style={{ fontWeight: '600' }}>
                      {plan.trial.duration}
                    </Text>
                  )}
                </VStack>
              </HStack>
              
              <VStack space="xs" style={{ alignItems: 'flex-end' }}>
                {plan.popular && (
                  <Badge action="candyPink" variant="solid" size="sm">
                    <BadgeText>Most Popular</BadgeText>
                  </Badge>
                )}
                {plan.discount && (
                  <Badge action="success" variant="solid" size="sm">
                    <BadgeText>{plan.discount}</BadgeText>
                  </Badge>
                )}
              </VStack>
            </HStack>

            {/* Pricing */}
            <VStack space="xs">
              <HStack className="items-baseline space-x-2">
                <Text 
                  color="primary" 
                  size="2xl" 
                  style={{ fontWeight: 'bold', color: plan.color }}
                >
                  {plan.price}
                </Text>
                <Text color="secondary" size="sm">
                  /{plan.period}
                </Text>
              </HStack>
              
              {plan.originalPrice && (
                <HStack className="items-center space-x-2">
                  <Text 
                    color="tertiary" 
                    size="sm" 
                    style={{ textDecorationLine: 'line-through' }}
                  >
                    {plan.originalPrice}
                  </Text>
                  <Text color="success" size="sm" style={{ fontWeight: '600' }}>
                    Save {plan.discount}
                  </Text>
                </HStack>
              )}
              
              {plan.trial && (
                <Text color="secondary" size="xs">
                  {plan.trial.description}
                </Text>
              )}
            </VStack>

            {/* Features */}
            <VStack space="xs">
              {plan.features.slice(0, 4).map((feature) => {
                const FeatureIcon = feature.icon;
                return (
                  <HStack key={feature.id} className="items-center space-x-2">
                    <FeatureIcon 
                      size={14} 
                      color={feature.highlight ? plan.color : '#10B981'} 
                    />
                    <Text 
                      color="primary" 
                      size="sm"
                      style={{ 
                        fontWeight: feature.highlight ? '600' : 'normal',
                        flex: 1
                      }}
                    >
                      {feature.title}
                    </Text>
                  </HStack>
                );
              })}
              
              {plan.features.length > 4 && (
                <Text color="tertiary" size="xs">
                  +{plan.features.length - 4} more features
                </Text>
              )}
            </VStack>
          </VStack>
        </Box>
      </Pressable>
    );
  };

  if (variant === 'banner') {
    return (
      <Box className={`${containerClasses[variant]} p-4 m-4 ${className}`}>
        <HStack className="justify-between items-center">
          <HStack className="items-center space-x-3 flex-1">
            <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyPink/20">
              <CrownIcon size={20} color="#FF6B9D" />
            </Box>
            
            <VStack space="xs" className="flex-1">
              <Text color="primary" size="sm" style={{ fontWeight: 'bold' }}>
                {triggerMessage.title}
              </Text>
              <Text color="secondary" size="xs">
                {triggerMessage.subtitle}
              </Text>
            </VStack>
          </HStack>
          
          <HStack className="space-x-2">
            <Button action="candyPink" size="sm" onPress={handleUpgrade}>
              <ButtonText>Upgrade</ButtonText>
            </Button>
            
            {showDismiss && (
              <Pressable onPress={onDismiss}>
                <XIcon size={20} color="#9CA3AF" />
              </Pressable>
            )}
          </HStack>
        </HStack>
      </Box>
    );
  }

  return (
    <View className={`${variant === 'fullscreen' ? 'flex-1' : ''} ${className}`}>
      <Box className={`${containerClasses[variant]} ${variant === 'modal' ? 'max-h-96' : ''}`}>
        <VStack space="lg" className={variant === 'fullscreen' ? 'flex-1 p-6' : 'p-6'}>
          {/* Header */}
          <VStack space="md">
            <HStack className="justify-between items-start">
              <HStack className="items-center space-x-3">
                <Box className="w-12 h-12 justify-center items-center rounded-full bg-candyPink/20">
                  <CrownIcon size={24} color="#FF6B9D" />
                </Box>
                
                <VStack space="xs">
                  <Text color="primary" size="xl" style={{ fontWeight: 'bold' }}>
                    {triggerMessage.title}
                  </Text>
                  <Text color="candyPink" size="md" style={{ fontWeight: '600' }}>
                    {triggerMessage.subtitle}
                  </Text>
                </VStack>
              </HStack>
              
              {showDismiss && variant !== 'fullscreen' && (
                <Pressable onPress={onDismiss}>
                  <XIcon size={24} color="#9CA3AF" />
                </Pressable>
              )}
            </HStack>
            
            <Text color="secondary" size="sm">
              {triggerMessage.description}
            </Text>
          </VStack>

          {/* Plans */}
          <ScrollView 
            showsVerticalScrollIndicator={false}
            style={{ flex: variant === 'fullscreen' ? 1 : undefined }}
          >
            <VStack space="md">
              {premiumPlans.map((plan) => (
                <PlanCard key={plan.id} plan={plan} />
              ))}
            </VStack>
          </ScrollView>

          {/* Features Highlight */}
          {variant === 'fullscreen' && (
            <Box className="bg-glass-bg-secondary border border-glass-border-primary rounded-lg p-4">
              <VStack space="md">
                <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                  What you get with Premium:
                </Text>
                
                <VStack space="sm">
                  {premiumFeatures.slice(0, 3).map((feature) => {
                    const FeatureIcon = feature.icon;
                    return (
                      <HStack key={feature.id} className="items-start space-x-3">
                        <Box className="w-6 h-6 justify-center items-center rounded-full bg-success/20 mt-0.5">
                          <FeatureIcon size={12} color="#10B981" />
                        </Box>
                        <VStack space="xs" className="flex-1">
                          <Text color="primary" size="sm" style={{ fontWeight: '600' }}>
                            {feature.title}
                          </Text>
                          <Text color="secondary" size="xs">
                            {feature.description}
                          </Text>
                        </VStack>
                      </HStack>
                    );
                  })}
                </VStack>
              </VStack>
            </Box>
          )}

          {/* Actions */}
          <VStack space="md">
            <Button 
              action="candyPink" 
              size="lg" 
              onPress={handleUpgrade}
              style={{ width: '100%' }}
            >
              <HStack className="items-center space-x-2">
                <CrownIcon size={20} color="#FFFFFF" />
                <ButtonText>
                  {premiumPlans.find(p => p.id === selectedPlan)?.trial 
                    ? 'Start Free Trial' 
                    : 'Upgrade Now'}
                </ButtonText>
              </HStack>
            </Button>
            
            <HStack className="justify-center space-x-6">
              {onRestorePurchases && (
                <Pressable onPress={onRestorePurchases}>
                  <Text color="candyPurple" size="sm">
                    Restore Purchases
                  </Text>
                </Pressable>
              )}
              
              {showDismiss && variant === 'fullscreen' && (
                <Pressable onPress={onDismiss}>
                  <Text color="tertiary" size="sm">
                    Maybe Later
                  </Text>
                </Pressable>
              )}
            </HStack>
          </VStack>

          {/* Trust Indicators */}
          <HStack className="justify-center space-x-4">
            <HStack className="items-center space-x-1">
              <ShieldIcon size={12} color="#10B981" />
              <Text color="success" size="xs">Secure</Text>
            </HStack>
            
            <HStack className="items-center space-x-1">
              <XIcon size={12} color="#10B981" />
              <Text color="success" size="xs">Cancel Anytime</Text>
            </HStack>
            
            <HStack className="items-center space-x-1">
              <StarIcon size={12} color="#F59E0B" />
              <Text color="warning" size="xs">4.9★ Rating</Text>
            </HStack>
          </HStack>
        </VStack>
      </Box>
    </View>
  );
}

export default UpgradePrompt;