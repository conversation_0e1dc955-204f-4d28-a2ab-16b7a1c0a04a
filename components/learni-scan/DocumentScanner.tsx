import React, { useState, useRef } from 'react';
import { View, Dimensions, StyleSheet } from 'react-native';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { Text } from '../ui/text';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Pressable } from '../ui/pressable';
import {
  CameraIcon,
  ZapIcon as FlashlightIcon,
  RotateCcwIcon,
  CheckCircleIcon,
  XCircleIcon,
  SettingsIcon
} from 'lucide-react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface DocumentScannerProps {
  onCapture?: (imageUri: string) => void;
  onClose?: () => void;
  onSettings?: () => void;
  quality?: 'low' | 'medium' | 'high' | 'ultra';
  flashMode?: 'on' | 'off' | 'auto';
}

function DocumentScanner({
  onCapture,
  onClose,
  onSettings,
  quality = 'high',
  flashMode = 'auto'
}: DocumentScannerProps) {
  const [permission, requestPermission] = useCameraPermissions();
  const [cameraType, setCameraType] = useState<CameraType>('back');
  const [flash, setFlash] = useState<'off' | 'on' | 'auto'>(flashMode);
  const [isCapturing, setIsCapturing] = useState(false);
  const [showDocumentFrame, setShowDocumentFrame] = useState(true);
  const cameraRef = useRef<CameraView>(null);

  const takePicture = async () => {
    if (cameraRef.current && !isCapturing) {
      setIsCapturing(true);
      try {
        const photo = await cameraRef.current.takePictureAsync({
          quality: quality === 'ultra' ? 1 : quality === 'high' ? 0.8 : quality === 'medium' ? 0.6 : 0.4,
          base64: false,
          exif: false,
        });
        if (photo?.uri) {
          onCapture?.(photo.uri);
        }
      } catch (error) {
        console.error('Error taking picture:', error);
      } finally {
        setIsCapturing(false);
      }
    }
  };

  const toggleFlash = () => {
    setFlash(flash === 'off' ? 'on' : flash === 'on' ? 'auto' : 'off');
  };

  const toggleCameraType = () => {
    setCameraType(cameraType === 'back' ? 'front' : 'back');
  };

  if (!permission) {
    return (
      <Box className="flex-1 justify-center items-center bg-black">
        <Text color="primary" size="lg">Requesting camera permission...</Text>
      </Box>
    );
  }

  if (!permission.granted) {
    return (
      <Box className="flex-1 justify-center items-center bg-black p-6">
        <VStack space="lg" style={{ alignItems: 'center' }}>
          <CameraIcon size={64} color="#FF6B9D" />
          <VStack space="md" style={{ alignItems: 'center' }}>
            <Text color="primary" size="xl" style={{ fontWeight: 'bold', textAlign: 'center' }}>
              Camera Access Required
            </Text>
            <Text color="secondary" size="md" style={{ textAlign: 'center' }}>
              LearniScan needs camera access to scan documents. Please enable camera permissions in your device settings.
            </Text>
          </VStack>
          <Button action="candyPink" onPress={requestPermission}>
            <ButtonText>Grant Permission</ButtonText>
          </Button>
        </VStack>
      </Box>
    );
  }

  return (
    <View style={styles.container}>
      {/* Camera View */}
      <CameraView
        ref={cameraRef}
        style={styles.camera}
        facing={cameraType}
        flash={flash}
      >
        {/* Glass Overlay Header */}
        <Box className="absolute top-0 left-0 right-0 bg-glass-bg-primary/80 backdrop-blur-md border-b border-glass-border-primary">
          <HStack className="justify-between items-center p-4 pt-12">
            <Pressable variant="glass" onPress={onClose}>
              <Box className="p-2 rounded-full">
                <XCircleIcon size={24} color="#FFFFFF" />
              </Box>
            </Pressable>
            
            <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
              Document Scanner
            </Text>
            
            <Pressable variant="glass" onPress={onSettings}>
              <Box className="p-2 rounded-full">
                <SettingsIcon size={24} color="#FFFFFF" />
              </Box>
            </Pressable>
          </HStack>
        </Box>

        {/* Document Frame Overlay */}
        {showDocumentFrame && (
          <Box className="absolute inset-0 justify-center items-center">
            <Box 
              style={styles.documentFrame}
              className="border-2 border-candyPink rounded-lg"
            >
              {/* Corner Indicators */}
              <Box className="absolute -top-1 -left-1 w-6 h-6 border-l-4 border-t-4 border-candyPink rounded-tl-lg" />
              <Box className="absolute -top-1 -right-1 w-6 h-6 border-r-4 border-t-4 border-candyPink rounded-tr-lg" />
              <Box className="absolute -bottom-1 -left-1 w-6 h-6 border-l-4 border-b-4 border-candyPink rounded-bl-lg" />
              <Box className="absolute -bottom-1 -right-1 w-6 h-6 border-r-4 border-b-4 border-candyPink rounded-br-lg" />
            </Box>
            
            {/* Instruction Text */}
            <Box className="absolute bottom-32 left-0 right-0 items-center">
              <Box className="bg-glass-bg-primary/90 backdrop-blur-md border border-glass-border-primary rounded-xl px-4 py-2">
                <Text color="primary" size="sm" style={{ textAlign: 'center' }}>
                  Position document within the frame
                </Text>
              </Box>
            </Box>
          </Box>
        )}

        {/* Glass Control Panel */}
        <Box className="absolute bottom-0 left-0 right-0 bg-glass-bg-primary/80 backdrop-blur-md border-t border-glass-border-primary">
          <VStack space="md" className="p-6 pb-8">
            {/* Quality Indicator */}
            <HStack className="justify-center items-center">
              <Box className="bg-glass-bg-card border border-glass-border-secondary rounded-full px-3 py-1">
                <Text color="candyPink" size="sm" style={{ fontWeight: '600' }}>
                  {quality.toUpperCase()} QUALITY
                </Text>
              </Box>
            </HStack>

            {/* Main Controls */}
            <HStack className="justify-between items-center">
              {/* Flash Toggle */}
              <Pressable variant="enhanced" onPress={toggleFlash}>
                <Box className="w-12 h-12 justify-center items-center rounded-full bg-glass-bg-secondary border border-glass-border-primary">
                  <FlashlightIcon 
                    size={24} 
                    color={flash === 'off' ? '#9CA3AF' : flash === 'on' ? '#FCD34D' : '#A855F7'} 
                  />
                </Box>
              </Pressable>

              {/* Capture Button */}
              <Pressable 
                variant="enhanced" 
                onPress={takePicture}
                disabled={isCapturing}
                style={{ opacity: isCapturing ? 0.6 : 1 }}
              >
                <Box className="w-20 h-20 justify-center items-center rounded-full bg-candyPink border-4 border-white shadow-lg">
                  {isCapturing ? (
                    <Box className="w-6 h-6 bg-white rounded-sm" />
                  ) : (
                    <CameraIcon size={32} color="#FFFFFF" />
                  )}
                </Box>
              </Pressable>

              {/* Camera Flip */}
              <Pressable variant="enhanced" onPress={toggleCameraType}>
                <Box className="w-12 h-12 justify-center items-center rounded-full bg-glass-bg-secondary border border-glass-border-primary">
                  <RotateCcwIcon size={24} color="#FFFFFF" />
                </Box>
              </Pressable>
            </HStack>

            {/* Secondary Controls */}
            <HStack className="justify-center items-center space-x-6">
              <Pressable 
                variant="glass" 
                onPress={() => setShowDocumentFrame(!showDocumentFrame)}
              >
                <HStack className="items-center space-x-2 px-3 py-2 rounded-full bg-glass-bg-secondary border border-glass-border-primary">
                  {showDocumentFrame ? (
                    <CheckCircleIcon size={16} color="#10B981" />
                  ) : (
                    <XCircleIcon size={16} color="#EF4444" />
                  )}
                  <Text color="primary" size="sm">
                    Frame Guide
                  </Text>
                </HStack>
              </Pressable>
            </HStack>
          </VStack>
        </Box>
      </CameraView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  camera: {
    flex: 1,
  },
  documentFrame: {
    width: screenWidth * 0.8,
    height: screenHeight * 0.6,
    backgroundColor: 'transparent',
  },
});

export default DocumentScanner;