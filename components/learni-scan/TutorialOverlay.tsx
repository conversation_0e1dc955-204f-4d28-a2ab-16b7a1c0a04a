import React, { useState, useEffect } from 'react';
import { View, Dimensions, Animated } from 'react-native';
import { Text } from '../ui/text';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Pressable } from '../ui/pressable';
import { 
  HelpCircleIcon, 
  XIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  PlayIcon,
  PauseIcon,
  SkipForwardIcon,
  CheckCircleIcon,
  LightbulbIcon,
  ZapIcon,
  EyeIcon,
  TargetIcon
} from 'lucide-react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface TutorialOverlayProps {
  steps: TutorialStep[];
  currentStep?: number;
  onStepChange?: (step: number) => void;
  onComplete?: () => void;
  onSkip?: () => void;
  onClose?: () => void;
  autoPlay?: boolean;
  autoPlayDelay?: number;
  showProgress?: boolean;
  variant?: 'spotlight' | 'tooltip' | 'fullscreen';
  className?: string;
}

interface TutorialStep {
  id: string;
  title: string;
  description: string;
  target?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center';
  type?: 'info' | 'tip' | 'warning' | 'success';
  icon?: React.ComponentType<{ size: number; color: string }>;
  action?: {
    label: string;
    onPress: () => void;
  };
  animation?: 'pulse' | 'bounce' | 'shake' | 'glow';
}

export function TutorialOverlay({
  steps,
  currentStep = 0,
  onStepChange,
  onComplete,
  onSkip,
  onClose,
  autoPlay = false,
  autoPlayDelay = 3000,
  showProgress = true,
  variant = 'spotlight',
  className
}: TutorialOverlayProps) {
  const [activeStep, setActiveStep] = useState(currentStep);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [animatedValue] = useState(new Animated.Value(0));
  const [spotlightAnimation] = useState(new Animated.Value(0));

  const currentStepData = steps[activeStep];
  const isLastStep = activeStep === steps.length - 1;
  const isFirstStep = activeStep === 0;

  useEffect(() => {
    // Animate step transition
    Animated.timing(animatedValue, {
      toValue: activeStep,
      duration: 300,
      useNativeDriver: true,
    }).start();

    // Animate spotlight
    if (variant === 'spotlight' && currentStepData?.target) {
      Animated.timing(spotlightAnimation, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }

    onStepChange?.(activeStep);
  }, [activeStep, currentStepData, variant]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isPlaying && !isLastStep) {
      interval = setInterval(() => {
        nextStep();
      }, autoPlayDelay);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isPlaying, isLastStep, autoPlayDelay]);

  const nextStep = () => {
    if (isLastStep) {
      onComplete?.();
    } else {
      setActiveStep(prev => prev + 1);
    }
  };

  const previousStep = () => {
    if (!isFirstStep) {
      setActiveStep(prev => prev - 1);
    }
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const skipTutorial = () => {
    setIsPlaying(false);
    onSkip?.();
  };

  const getStepTypeColor = (type: TutorialStep['type']) => {
    switch (type) {
      case 'tip': return '#10B981';
      case 'warning': return '#F59E0B';
      case 'success': return '#10B981';
      default: return '#3B82F6';
    }
  };

  const getStepTypeIcon = (type: TutorialStep['type']) => {
    switch (type) {
      case 'tip': return LightbulbIcon;
      case 'warning': return ZapIcon;
      case 'success': return CheckCircleIcon;
      default: return HelpCircleIcon;
    }
  };

  const getTooltipPosition = () => {
    if (!currentStepData?.target || !currentStepData?.position) {
      return { top: screenHeight / 2 - 100, left: 20, right: 20 };
    }

    const { target, position } = currentStepData;
    const tooltipHeight = 200;
    const tooltipWidth = screenWidth - 40;
    const margin = 20;

    switch (position) {
      case 'top':
        return {
          top: target.y - tooltipHeight - margin,
          left: Math.max(20, target.x + target.width / 2 - tooltipWidth / 2),
          right: undefined
        };
      case 'bottom':
        return {
          top: target.y + target.height + margin,
          left: Math.max(20, target.x + target.width / 2 - tooltipWidth / 2),
          right: undefined
        };
      case 'left':
        return {
          top: target.y + target.height / 2 - tooltipHeight / 2,
          left: 20,
          right: undefined
        };
      case 'right':
        return {
          top: target.y + target.height / 2 - tooltipHeight / 2,
          left: undefined,
          right: 20
        };
      default:
        return {
          top: screenHeight / 2 - tooltipHeight / 2,
          left: 20,
          right: 20
        };
    }
  };

  const tooltipPosition = getTooltipPosition();
  const stepTypeColor = getStepTypeColor(currentStepData?.type);
  const StepIcon = currentStepData?.icon || getStepTypeIcon(currentStepData?.type);

  if (variant === 'fullscreen') {
    return (
      <View className={`absolute inset-0 bg-black/80 z-50 ${className}`}>
        <VStack space="xl" className="flex-1 justify-center items-center p-8">
          {/* Step Content */}
          <VStack space="lg" style={{ alignItems: 'center', maxWidth: 320 }}>
            <Box 
              className="w-20 h-20 justify-center items-center rounded-full"
              style={{ backgroundColor: stepTypeColor + '20' }}
            >
              <StepIcon size={40} color={stepTypeColor} />
            </Box>
            
            <VStack space="md" style={{ alignItems: 'center' }}>
              <Text 
                color="white" 
                size="xl" 
                style={{ fontWeight: 'bold', textAlign: 'center' }}
              >
                {currentStepData?.title}
              </Text>
              
              <Text 
                color="white" 
                size="md" 
                style={{ textAlign: 'center', opacity: 0.8 }}
              >
                {currentStepData?.description}
              </Text>
            </VStack>

            {currentStepData?.action && (
              <Button 
                action="candyPink" 
                onPress={currentStepData.action.onPress}
              >
                <ButtonText>{currentStepData.action.label}</ButtonText>
              </Button>
            )}
          </VStack>

          {/* Progress */}
          {showProgress && (
            <VStack space="md" style={{ alignItems: 'center' }}>
              <HStack className="space-x-2">
                {steps.map((_, index) => (
                  <Box 
                    key={index}
                    className={`w-2 h-2 rounded-full ${
                      index === activeStep ? 'opacity-100' : 'opacity-30'
                    }`}
                    style={{
                      backgroundColor: index === activeStep ? stepTypeColor : '#FFFFFF'
                    }}
                  />
                ))}
              </HStack>
              
              <Text color="white" size="sm" style={{ opacity: 0.6 }}>
                {activeStep + 1} of {steps.length}
              </Text>
            </VStack>
          )}

          {/* Controls */}
          <HStack className="space-x-4">
            <Button 
              action="glass" 
              variant="outline" 
              onPress={previousStep}
              disabled={isFirstStep}
              style={{ opacity: isFirstStep ? 0.5 : 1 }}
            >
              <ChevronLeftIcon size={20} color="#FFFFFF" />
            </Button>

            <Button 
              action="glass" 
              variant="outline" 
              onPress={togglePlayPause}
            >
              {isPlaying ? (
                <PauseIcon size={20} color="#FFFFFF" />
              ) : (
                <PlayIcon size={20} color="#FFFFFF" />
              )}
            </Button>

            <Button 
              action="candyPink" 
              onPress={nextStep}
            >
              {isLastStep ? (
                <CheckCircleIcon size={20} color="#FFFFFF" />
              ) : (
                <ChevronRightIcon size={20} color="#FFFFFF" />
              )}
            </Button>
          </HStack>

          {/* Skip */}
          <Pressable onPress={skipTutorial}>
            <Text color="white" size="sm" style={{ opacity: 0.6 }}>
              Skip Tutorial
            </Text>
          </Pressable>
        </VStack>
      </View>
    );
  }

  return (
    <View className={`absolute inset-0 z-50 ${className}`}>
      {/* Overlay */}
      <View className="absolute inset-0 bg-black/60" />

      {/* Spotlight */}
      {variant === 'spotlight' && currentStepData?.target && (
        <Animated.View
          style={{
            position: 'absolute',
            left: currentStepData.target.x - 10,
            top: currentStepData.target.y - 10,
            width: currentStepData.target.width + 20,
            height: currentStepData.target.height + 20,
            borderRadius: 12,
            borderWidth: 3,
            borderColor: stepTypeColor,
            backgroundColor: 'transparent',
            shadowColor: stepTypeColor,
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 0.8,
            shadowRadius: 10,
            transform: [
              {
                scale: spotlightAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 1.1],
                }),
              },
            ],
          }}
        />
      )}

      {/* Tooltip */}
      <Animated.View
        style={{
          position: 'absolute',
          ...tooltipPosition,
          transform: [
            {
              translateY: animatedValue.interpolate({
                inputRange: [activeStep - 1, activeStep, activeStep + 1],
                outputRange: [20, 0, -20],
                extrapolate: 'clamp',
              }),
            },
            {
              scale: animatedValue.interpolate({
                inputRange: [activeStep - 1, activeStep, activeStep + 1],
                outputRange: [0.9, 1, 0.9],
                extrapolate: 'clamp',
              }),
            },
          ],
        }}
      >
        <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4 max-w-sm">
          <VStack space="md">
            {/* Header */}
            <HStack className="justify-between items-start">
              <HStack className="items-center space-x-3 flex-1">
                <Box 
                  className="w-8 h-8 justify-center items-center rounded-full"
                  style={{ backgroundColor: stepTypeColor + '20' }}
                >
                  <StepIcon size={16} color={stepTypeColor} />
                </Box>
                
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: 'bold' }}>
                    {currentStepData?.title}
                  </Text>
                  {showProgress && (
                    <Text color="tertiary" size="xs">
                      Step {activeStep + 1} of {steps.length}
                    </Text>
                  )}
                </VStack>
              </HStack>
              
              <Pressable onPress={onClose}>
                <XIcon size={20} color="#9CA3AF" />
              </Pressable>
            </HStack>

            {/* Content */}
            <Text color="secondary" size="sm">
              {currentStepData?.description}
            </Text>

            {/* Custom Action */}
            {currentStepData?.action && (
              <Button 
                action="glass" 
                variant="outline" 
                size="sm"
                onPress={currentStepData.action.onPress}
              >
                <ButtonText>{currentStepData.action.label}</ButtonText>
              </Button>
            )}

            {/* Progress Bar */}
            {showProgress && (
              <Box className="w-full h-1 bg-glass-bg-primary rounded-full overflow-hidden">
                <Box 
                  className="h-full rounded-full"
                  style={{
                    backgroundColor: stepTypeColor,
                    width: `${((activeStep + 1) / steps.length) * 100}%`
                  }}
                />
              </Box>
            )}

            {/* Controls */}
            <HStack className="justify-between items-center">
              <HStack className="space-x-2">
                <Button 
                  action="glass" 
                  variant="outline" 
                  size="xs"
                  onPress={previousStep}
                  disabled={isFirstStep}
                  style={{ opacity: isFirstStep ? 0.5 : 1 }}
                >
                  <ChevronLeftIcon size={14} color="#FFFFFF" />
                </Button>

                <Button 
                  action="glass" 
                  variant="outline" 
                  size="xs"
                  onPress={togglePlayPause}
                >
                  {isPlaying ? (
                    <PauseIcon size={14} color="#FFFFFF" />
                  ) : (
                    <PlayIcon size={14} color="#FFFFFF" />
                  )}
                </Button>
              </HStack>

              <HStack className="space-x-2">
                <Button 
                  action="glass" 
                  variant="outline" 
                  size="xs"
                  onPress={skipTutorial}
                >
                  <HStack className="items-center space-x-1">
                    <SkipForwardIcon size={12} color="#FFFFFF" />
                    <ButtonText>Skip</ButtonText>
                  </HStack>
                </Button>

                <Button 
                  action="candyPink" 
                  size="xs"
                  onPress={nextStep}
                >
                  <HStack className="items-center space-x-1">
                    <ButtonText>
                      {isLastStep ? 'Done' : 'Next'}
                    </ButtonText>
                    {isLastStep ? (
                      <CheckCircleIcon size={12} color="#FFFFFF" />
                    ) : (
                      <ChevronRightIcon size={12} color="#FFFFFF" />
                    )}
                  </HStack>
                </Button>
              </HStack>
            </HStack>
          </VStack>
        </Box>
      </Animated.View>

      {/* Step Indicators */}
      {showProgress && variant === 'spotlight' && (
        <View className="absolute bottom-8 left-0 right-0">
          <HStack className="justify-center space-x-2">
            {steps.map((_, index) => (
              <Pressable 
                key={index}
                onPress={() => setActiveStep(index)}
              >
                <Box 
                  className={`w-3 h-3 rounded-full ${
                    index === activeStep ? 'opacity-100' : 'opacity-30'
                  }`}
                  style={{
                    backgroundColor: index === activeStep ? stepTypeColor : '#FFFFFF'
                  }}
                />
              </Pressable>
            ))}
          </HStack>
        </View>
      )}
    </View>
  );
}

export default TutorialOverlay;