/**
 * AI Search Results Display Component
 * 
 * Shows AI-powered search results with:
 * - Semantic relevance scoring
 * - Intent-based result grouping
 * - Smart highlights and snippets
 * - Related concepts and suggestions
 * 
 * Following LearniScan development workflow rules:
 * - Rule 6: Gluestack UI components with candy theme
 * - Rule 8: Smooth animations with react-native-reanimated
 * - Rule 9: Safe area management
 * - Rule 1: Interactive feedback protocol integration
 */

import React, { useState } from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

// Gluestack UI components (Rule 6)
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Pressable } from '@/components/ui/pressable';
import { Badge, BadgeText } from '@/components/ui/badge';

// Icons
import { 
  Brain, 
  Target, 
  TrendingUp, 
  Clock, 
  Tag, 
  FileText,
  BookOpen,
  Lightbulb,
  ArrowRight,
  Star,
  Zap
} from 'lucide-react-native';

// Types
import type { 
  AISearchResponse, 
  SemanticSearchResult, 
  SearchIntent, 
  SmartSuggestion 
} from '@/lib/services/ai-search.service';

interface AISearchResultsProps {
  searchResponse: AISearchResponse;
  onResultPress?: (result: SemanticSearchResult) => void;
  onSuggestionPress?: (suggestion: SmartSuggestion) => void;
  onRelatedQueryPress?: (query: string) => void;
  showMetadata?: boolean;
  showRelatedConcepts?: boolean;
  className?: string;
}

interface ResultCardProps {
  result: SemanticSearchResult;
  onPress?: (result: SemanticSearchResult) => void;
  showMetadata?: boolean;
}

const ResultCard: React.FC<ResultCardProps> = ({ result, onPress, showMetadata = true }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const animationValue = useSharedValue(0);

  const toggleExpanded = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    animationValue.value = withSpring(newExpanded ? 1 : 0);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    height: interpolate(animationValue.value, [0, 1], [0, 100]),
    opacity: animationValue.value,
  }));

  const getTypeIcon = (type: SemanticSearchResult['type']) => {
    const iconProps = { size: 16, color: '#06B6D4' };
    switch (type) {
      case 'knowledge_card': return <BookOpen {...iconProps} />;
      case 'document': return <FileText {...iconProps} />;
      case 'concept': return <Lightbulb {...iconProps} />;
      default: return <FileText {...iconProps} />;
    }
  };

  const getRelevanceColor = (score: number) => {
    if (score >= 0.8) return '#10B981'; // Green
    if (score >= 0.6) return '#06B6D4'; // Cyan
    if (score >= 0.4) return '#8B5CF6'; // Purple
    return '#6B7280'; // Gray
  };

  return (
    <Pressable onPress={() => onPress?.(result)}>
      <Box
        className="mb-3 rounded-xl overflow-hidden"
        style={[styles.glassCard, { borderColor: `${getRelevanceColor(result.relevanceScore)}40` }]}
      >
        <LinearGradient
          colors={['rgba(255, 255, 255, 0.05)', 'rgba(255, 255, 255, 0.02)']}
          style={styles.gradientBackground}
        >
          <VStack space="sm" className="p-4">
            {/* Header */}
            <HStack className="items-center justify-between">
              <HStack className="items-center flex-1" space="sm">
                <Box
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: `${getRelevanceColor(result.relevanceScore)}20` }}
                >
                  {getTypeIcon(result.type)}
                </Box>
                <VStack className="flex-1">
                  <Text className="text-white text-base font-semibold" numberOfLines={2}>
                    {result.title}
                  </Text>
                  <Text className="text-white/60 text-xs capitalize">
                    {result.type.replace('_', ' ')}
                  </Text>
                </VStack>
              </HStack>

              {/* Relevance Score */}
              <VStack className="items-end">
                <Badge
                  className="border"
                  style={{ 
                    backgroundColor: `${getRelevanceColor(result.relevanceScore)}20`,
                    borderColor: `${getRelevanceColor(result.relevanceScore)}40`
                  }}
                >
                  <BadgeText 
                    className="text-xs font-mono"
                    style={{ color: getRelevanceColor(result.relevanceScore) }}
                  >
                    {Math.round(result.relevanceScore * 100)}%
                  </BadgeText>
                </Badge>
                <Text className="text-white/40 text-xs mt-1">
                  relevance
                </Text>
              </VStack>
            </HStack>

            {/* Content Preview */}
            <Text className="text-white/80 text-sm" numberOfLines={3}>
              {result.content}
            </Text>

            {/* Highlights */}
            {result.highlights && result.highlights.length > 0 && (
              <VStack space="xs">
                <Text className="text-white/60 text-xs font-medium">Highlights:</Text>
                <HStack className="flex-wrap gap-1">
                  {result.highlights.slice(0, 3).map((highlight, index) => (
                    <Badge
                      key={index}
                      className="bg-yellow-600/20 border border-yellow-400/30"
                    >
                      <BadgeText className="text-yellow-300 text-xs">
                        {highlight.text}
                      </BadgeText>
                    </Badge>
                  ))}
                </HStack>
              </VStack>
            )}

            {/* Metadata */}
            {showMetadata && result.metadata && (
              <HStack className="items-center justify-between">
                <HStack className="flex-wrap gap-1">
                  {result.metadata.category && (
                    <Badge className="bg-purple-600/20 border border-purple-400/30">
                      <BadgeText className="text-purple-300 text-xs">
                        {result.metadata.category}
                      </BadgeText>
                    </Badge>
                  )}
                  {result.metadata.difficulty && (
                    <Badge className="bg-blue-600/20 border border-blue-400/30">
                      <BadgeText className="text-blue-300 text-xs">
                        {result.metadata.difficulty}
                      </BadgeText>
                    </Badge>
                  )}
                </HStack>

                <HStack className="items-center" space="sm">
                  <Star size={12} color="#FCD34D" />
                  <Text className="text-white/60 text-xs">
                    {Math.round(result.semanticSimilarity * 100)}% match
                  </Text>
                </HStack>
              </HStack>
            )}

            {/* Related Concepts */}
            {result.relatedConcepts && result.relatedConcepts.length > 0 && (
              <Pressable onPress={toggleExpanded}>
                <HStack className="items-center" space="sm">
                  <Text className="text-cyan-300 text-xs font-medium">
                    Related concepts ({result.relatedConcepts.length})
                  </Text>
                  <ArrowRight 
                    size={12} 
                    color="#06B6D4" 
                    style={{ 
                      transform: [{ rotate: isExpanded ? '90deg' : '0deg' }] 
                    }} 
                  />
                </HStack>
              </Pressable>
            )}

            {/* Expanded Related Concepts */}
            {isExpanded && result.relatedConcepts && (
              <Animated.View style={[animatedStyle, { overflow: 'hidden' }]}>
                <HStack className="flex-wrap gap-1 mt-2">
                  {result.relatedConcepts.map((concept, index) => (
                    <Badge
                      key={index}
                      className="bg-cyan-600/20 border border-cyan-400/30"
                    >
                      <BadgeText className="text-cyan-300 text-xs">
                        {concept}
                      </BadgeText>
                    </Badge>
                  ))}
                </HStack>
              </Animated.View>
            )}
          </VStack>
        </LinearGradient>
      </Box>
    </Pressable>
  );
};

export const AISearchResults: React.FC<AISearchResultsProps> = ({
  searchResponse,
  onResultPress,
  onSuggestionPress,
  onRelatedQueryPress,
  showMetadata = true,
  showRelatedConcepts = true,
  className,
}) => {
  const { query, intent, results, suggestions, relatedQueries, searchMetadata } = searchResponse;

  const getIntentIcon = (intent: SearchIntent['intent']) => {
    const iconProps = { size: 20, color: '#10B981' };
    switch (intent) {
      case 'learn': return <BookOpen {...iconProps} />;
      case 'review': return <Target {...iconProps} />;
      case 'discover': return <Lightbulb {...iconProps} />;
      case 'compare': return <TrendingUp {...iconProps} />;
      case 'explain': return <Brain {...iconProps} />;
      default: return <Brain {...iconProps} />;
    }
  };

  return (
    <ScrollView 
      className={className}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      <VStack space="lg" className="px-4">
        {/* Search Intent Summary */}
        <Box
          className="rounded-xl overflow-hidden"
          style={[styles.glassCard, { backgroundColor: 'rgba(16, 185, 129, 0.1)' }]}
        >
          <VStack space="sm" className="p-4">
            <HStack className="items-center" space="sm">
              {getIntentIcon(intent.intent)}
              <VStack className="flex-1">
                <Text className="text-white text-base font-semibold">
                  AI understood your intent: {intent.intent}
                </Text>
                <Text className="text-white/70 text-sm">
                  Confidence: {Math.round(intent.confidence * 100)}%
                </Text>
              </VStack>
            </HStack>

            {/* Extracted Entities */}
            {intent.entities.length > 0 && (
              <VStack space="xs">
                <Text className="text-white/80 text-sm font-medium">Key concepts found:</Text>
                <HStack className="flex-wrap gap-1">
                  {intent.entities.map((entity, index) => (
                    <Badge
                      key={index}
                      className="bg-green-600/20 border border-green-400/30"
                    >
                      <BadgeText className="text-green-300 text-xs">
                        {entity.text} ({entity.type})
                      </BadgeText>
                    </Badge>
                  ))}
                </HStack>
              </VStack>
            )}
          </VStack>
        </Box>

        {/* Search Results */}
        <VStack space="sm">
          <HStack className="items-center justify-between">
            <HStack className="items-center" space="sm">
              <Zap size={20} color="#06B6D4" />
              <Text className="text-white text-lg font-semibold">
                Results ({results.length})
              </Text>
            </HStack>
            <Text className="text-white/60 text-xs">
              {searchMetadata.processingTime}ms
            </Text>
          </HStack>

          {results.map((result) => (
            <ResultCard
              key={result.id}
              result={result}
              onPress={onResultPress}
              showMetadata={showMetadata}
            />
          ))}
        </VStack>

        {/* Smart Suggestions */}
        {suggestions.length > 0 && (
          <VStack space="sm">
            <HStack className="items-center" space="sm">
              <Lightbulb size={20} color="#8B5CF6" />
              <Text className="text-white text-lg font-semibold">
                You might also like
              </Text>
            </HStack>

            <HStack className="flex-wrap gap-2">
              {suggestions.map((suggestion) => (
                <Pressable
                  key={suggestion.id}
                  onPress={() => onSuggestionPress?.(suggestion)}
                >
                  <Box
                    className="p-3 rounded-xl"
                    style={[styles.glassCard, { backgroundColor: 'rgba(139, 92, 246, 0.1)' }]}
                  >
                    <HStack className="items-center" space="sm">
                      <Text className="text-purple-300 text-sm font-medium">
                        {suggestion.text}
                      </Text>
                      <Badge className="bg-purple-600/20 border border-purple-400/30">
                        <BadgeText className="text-purple-300 text-xs">
                          {Math.round(suggestion.confidence * 100)}%
                        </BadgeText>
                      </Badge>
                    </HStack>
                  </Box>
                </Pressable>
              ))}
            </HStack>
          </VStack>
        )}

        {/* Related Queries */}
        {relatedQueries.length > 0 && (
          <VStack space="sm">
            <HStack className="items-center" space="sm">
              <TrendingUp size={20} color="#F59E0B" />
              <Text className="text-white text-lg font-semibold">
                Related searches
              </Text>
            </HStack>

            <VStack space="xs">
              {relatedQueries.map((relatedQuery, index) => (
                <Pressable
                  key={index}
                  onPress={() => onRelatedQueryPress?.(relatedQuery)}
                  className="p-3 rounded-lg"
                  style={{ backgroundColor: 'rgba(245, 158, 11, 0.1)' }}
                >
                  <HStack className="items-center justify-between">
                    <Text className="text-amber-300 text-sm flex-1">
                      {relatedQuery}
                    </Text>
                    <ArrowRight size={16} color="#F59E0B" />
                  </HStack>
                </Pressable>
              ))}
            </VStack>
          </VStack>
        )}

        {/* Search Metadata */}
        {searchMetadata.semanticSearchUsed && (
          <Box
            className="p-3 rounded-lg"
            style={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
          >
            <HStack className="items-center justify-between">
              <HStack className="items-center" space="sm">
                <Brain size={16} color="#06B6D4" />
                <Text className="text-white/70 text-xs">
                  Powered by semantic AI search
                </Text>
              </HStack>
              <Text className="text-white/50 text-xs">
                {searchMetadata.embeddingModel}
              </Text>
            </HStack>
          </Box>
        )}
      </VStack>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  glassCard: {
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
    elevation: 4,
  },
  gradientBackground: {
    flex: 1,
  },
});
