import React, { useEffect, useState } from 'react';
import { View, Animated } from 'react-native';
import { Text } from '../ui/text';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Progress, ProgressFilledTrack } from '../ui/progress';
import { 
  SparklesIcon, 
  EyeIcon, 
  ZapIcon,
  CheckCircleIcon,
  CloudUploadIcon,
  FileTextIcon,
  ImageIcon,
  BrainIcon
} from 'lucide-react-native';

interface ScanProgressProps {
  currentStep?: ProcessingStep | number; // ProcessingStep object or 0-based index for backward compatibility
  steps?: ProcessingStep[];
  onComplete?: () => void;
  showDetails?: boolean;
  animated?: boolean;
  className?: string;
  variant?: 'default' | 'glass';
}

interface ProcessingStep {
  id: string;
  title: string;
  description: string;
  icon?: React.ComponentType<{ size: number; color: string }>;
  color?: string;
  estimatedTime?: number; // in milliseconds
  progress?: number; // 0-100
  status?: 'pending' | 'processing' | 'completed' | 'error';
}

const defaultSteps: ProcessingStep[] = [
  {
    id: 'capture',
    title: 'Image Capture',
    description: 'Processing captured image',
    icon: ImageIcon,
    color: '#FF6B9D',
    estimatedTime: 500
  },
  {
    id: 'enhance',
    title: 'AI Enhancement',
    description: 'Applying intelligent enhancements',
    icon: SparklesIcon,
    color: '#A855F7',
    estimatedTime: 1500
  },
  {
    id: 'ocr',
    title: 'Text Recognition',
    description: 'Extracting text with OCR',
    icon: EyeIcon,
    color: '#3B82F6',
    estimatedTime: 2000
  },
  {
    id: 'analysis',
    title: 'Document Analysis',
    description: 'Analyzing document structure',
    icon: BrainIcon,
    color: '#10B981',
    estimatedTime: 1000
  },
  {
    id: 'finalize',
    title: 'Finalizing',
    description: 'Preparing final document',
    icon: FileTextIcon,
    color: '#F59E0B',
    estimatedTime: 800
  }
];

export function ScanProgress({
  currentStep,
  steps = defaultSteps,
  onComplete,
  showDetails = true,
  animated = true,
  className,
  variant = 'default'
}: ScanProgressProps) {
  const [animatedProgress] = useState(new Animated.Value(0));
  const [stepProgress, setStepProgress] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  // Handle both new ProcessingStep object and legacy number index
  const isNewFormat = typeof currentStep === 'object' && currentStep !== null;
  const currentStepData = isNewFormat ? currentStep as ProcessingStep : null;
  const currentStepIndex = isNewFormat ? 0 : (currentStep as number || 0);

  const totalSteps = steps.length;
  const overallProgress = isNewFormat
    ? (currentStepData?.progress || 0)
    : ((currentStepIndex + stepProgress) / totalSteps) * 100;

  useEffect(() => {
    if (isNewFormat) {
      // Handle new format with ProcessingStep object
      if (currentStepData?.status === 'completed') {
        setIsComplete(true);
        onComplete?.();
      }
      return;
    }

    // Legacy handling for number-based currentStep
    if (currentStepIndex >= totalSteps) {
      setIsComplete(true);
      onComplete?.();
      return;
    }

    // Animate step progress
    if (animated) {
      Animated.timing(animatedProgress, {
        toValue: 1,
        duration: steps[currentStepIndex]?.estimatedTime || 1000,
        useNativeDriver: false,
      }).start(() => {
        setStepProgress(1);
      });

      const listener = animatedProgress.addListener(({ value }) => {
        setStepProgress(value);
      });

      return () => animatedProgress.removeListener(listener);
    } else {
      setStepProgress(1);
    }
  }, [currentStep, animated, isNewFormat]);

  useEffect(() => {
    if (!isNewFormat && stepProgress >= 1 && currentStepIndex < totalSteps - 1) {
      // Reset for next step
      animatedProgress.setValue(0);
      setStepProgress(0);
    }
  }, [stepProgress, currentStepIndex, isNewFormat]);

  const getCurrentStepInfo = () => {
    if (isComplete) {
      return {
        title: 'Complete!',
        description: 'Document processing finished',
        icon: CheckCircleIcon,
        color: '#10B981'
      };
    }

    if (isNewFormat && currentStepData) {
      return {
        title: currentStepData.title,
        description: currentStepData.description,
        icon: currentStepData.icon || SparklesIcon,
        color: currentStepData.color || '#FF6B9D'
      };
    }

    return steps[currentStepIndex] || steps[0];
  };

  const currentStepInfo = getCurrentStepInfo();

  return (
    <View className={className}>
      <VStack space="lg" style={{ alignItems: 'center' }}>
        {/* Main Progress Circle */}
        <Box className="relative justify-center items-center">
          {/* Outer Progress Ring */}
          <Box 
            className="w-32 h-32 rounded-full border-4 justify-center items-center"
            style={{
              borderColor: isComplete ? '#10B981' : currentStepInfo.color,
              backgroundColor: isComplete 
                ? 'rgba(16, 185, 129, 0.1)' 
                : `${currentStepInfo.color}20`
            }}
          >
            {/* Inner Content */}
            <VStack space="xs" style={{ alignItems: 'center' }}>
              <Box 
                className="w-16 h-16 rounded-full justify-center items-center"
                style={{
                  backgroundColor: isComplete 
                    ? 'rgba(16, 185, 129, 0.2)' 
                    : `${currentStepInfo.color}30`
                }}
              >
                <currentStepInfo.icon 
                  size={32} 
                  color={isComplete ? '#10B981' : currentStepInfo.color} 
                />
              </Box>
              
              <Text 
                size="lg" 
                style={{ 
                  fontWeight: 'bold', 
                  color: isComplete ? '#10B981' : currentStepInfo.color 
                }}
              >
                {Math.round(overallProgress)}%
              </Text>
            </VStack>
          </Box>

          {/* Animated Progress Ring Overlay */}
          {!isComplete && (
            <Box 
              className="absolute inset-0 w-32 h-32 rounded-full border-4 border-transparent"
              style={{
                borderTopColor: currentStepInfo.color,
                transform: [{ rotate: `${(overallProgress / 100) * 360}deg` }]
              }}
            />
          )}
        </Box>

        {/* Current Step Info */}
        <VStack space="sm" style={{ alignItems: 'center' }}>
          <Text color="primary" size="xl" style={{ fontWeight: 'bold' }}>
            {currentStepInfo.title}
          </Text>
          <Text color="secondary" size="md" style={{ textAlign: 'center' }}>
            {currentStepInfo.description}
          </Text>
        </VStack>

        {/* Overall Progress Bar */}
        <Box className="w-full max-w-xs">
          <Progress value={overallProgress} size="md">
            <ProgressFilledTrack 
              color={isComplete ? "success" : "candyPink"} 
            />
          </Progress>
        </Box>

        {/* Step Details */}
        {showDetails && (
          <VStack space="md" className="w-full">
            <Text color="tertiary" size="sm" style={{ fontWeight: '600', textAlign: 'center' }}>
              Processing Steps:
            </Text>
            
            {steps.map((step, index) => {
              const isCurrentStep = index === currentStep;
              const isCompleted = index < currentStep || isComplete;
              const isFuture = index > currentStep && !isComplete;
              
              return (
                <Box 
                  key={step.id}
                  className={`border rounded-lg p-3 ${
                    isCurrentStep 
                      ? 'bg-glass-bg-card border-glass-border-accent' 
                      : isCompleted
                      ? 'bg-glass-bg-secondary border-glass-border-primary'
                      : 'bg-glass-bg-primary border-glass-border-primary opacity-50'
                  }`}
                >
                  <HStack className="justify-between items-center">
                    <HStack className="items-center space-x-3 flex-1">
                      <Box 
                        className={`w-10 h-10 justify-center items-center rounded-full ${
                          isCompleted ? 'bg-success/20' : `bg-${step.color}/20`
                        }`}
                      >
                        {isCompleted ? (
                          <CheckCircleIcon size={20} color="#10B981" />
                        ) : (
                          <step.icon 
                            size={20} 
                            color={isCurrentStep ? step.color : '#9CA3AF'} 
                          />
                        )}
                      </Box>
                      
                      <VStack space="xs" className="flex-1">
                        <Text 
                          color={isCurrentStep ? "primary" : isCompleted ? "success" : "muted"} 
                          size="sm" 
                          style={{ fontWeight: '600' }}
                        >
                          {step.title}
                        </Text>
                        <Text 
                          color={isCurrentStep ? "secondary" : "muted"} 
                          size="xs"
                        >
                          {step.description}
                        </Text>
                      </VStack>
                    </HStack>
                    
                    {/* Step Progress Indicator */}
                    {isCurrentStep && !isComplete && (
                      <Box className="w-16 h-1 bg-glass-bg-primary rounded-full overflow-hidden">
                        <Box 
                          className="h-full rounded-full"
                          style={{ 
                            width: `${stepProgress * 100}%`,
                            backgroundColor: step.color 
                          }}
                        />
                      </Box>
                    )}
                    
                    {isCompleted && (
                      <CheckCircleIcon size={16} color="#10B981" />
                    )}
                  </HStack>
                </Box>
              );
            })}
          </VStack>
        )}

        {/* Completion Message */}
        {isComplete && (
          <Box className="bg-glass-bg-card border border-glass-border-secondary rounded-lg p-4 w-full">
            <VStack space="sm" style={{ alignItems: 'center' }}>
              <CheckCircleIcon size={24} color="#10B981" />
              <Text color="success" size="lg" style={{ fontWeight: 'bold' }}>
                Processing Complete!
              </Text>
              <Text color="secondary" size="sm" style={{ textAlign: 'center' }}>
                Your document has been successfully processed and enhanced with AI.
              </Text>
            </VStack>
          </Box>
        )}
      </VStack>
    </View>
  );
}

export default ScanProgress;