// LearniScan Custom Component Types

// Priority 1: Document Scanning Components
export interface DocumentScannerProps {
  onCapture?: (imageUri: string) => void;
  onClose?: () => void;
  onSettings?: () => void;
  quality?: 'low' | 'medium' | 'high' | 'ultra';
  autoFocus?: boolean;
  flashMode?: 'on' | 'off' | 'auto';
  className?: string;
}

export interface ScanPreviewProps {
  imageUri: string;
  onAccept?: (processedImageUri: string, settings: EnhancementSettings) => void;
  onRetake?: () => void;
  onCancel?: () => void;
  initialSettings?: Partial<EnhancementSettings>;
  className?: string;
}

export interface EnhancementSettings {
  brightness: number;
  contrast: number;
  saturation: number;
  sharpness: number;
  gamma: number;
  highlights: number;
  shadows: number;
  vibrance: number;
  clarity: number;
  autoEnhance: boolean;
  documentMode: 'auto' | 'text' | 'photo' | 'drawing' | 'handwriting';
  colorSpace: 'sRGB' | 'adobeRGB' | 'displayP3';
  noiseReduction: boolean;
  edgeEnhancement: boolean;
}

export interface QualityIndicatorProps {
  score: number; // 0-100
  factors?: QualityFactor[];
  showDetails?: boolean;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  className?: string;
}

export interface QualityFactor {
  name: string;
  score: number;
  icon?: React.ComponentType<{ size: number; color: string }>;
  description?: string;
}

export interface ScanProgressProps {
  currentStep: number; // 0-based index
  steps?: ProcessingStep[];
  onComplete?: () => void;
  showDetails?: boolean;
  animated?: boolean;
  className?: string;
}

export interface ProcessingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  color: string;
  estimatedTime: number; // in milliseconds
}

// Priority 2: AI Processing Components
export interface AIProcessingCardProps {
  operation: AIOperation;
  onPause?: () => void;
  onResume?: () => void;
  onCancel?: () => void;
  onViewDetails?: () => void;
  showControls?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export interface AIOperation {
  id: string;
  type: 'ocr' | 'enhancement' | 'analysis' | 'translation' | 'summarization';
  title: string;
  description: string;
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled';
  progress: number; // 0-100
  estimatedTimeRemaining?: number; // in seconds
  startTime: Date;
  completedTime?: Date;
  confidence?: number; // 0-100
  details?: {
    processedItems?: number;
    totalItems?: number;
    currentStep?: string;
    errorMessage?: string;
  };
}

export interface TextRecognitionDisplayProps {
  recognizedText: RecognizedText[];
  confidence: number;
  onTextSelect?: (selectedText: string, bounds: TextBounds) => void;
  onTextEdit?: (textId: string, newText: string) => void;
  onCopy?: (text: string) => void;
  onShare?: (text: string) => void;
  onSearch?: (text: string) => void;
  showConfidence?: boolean;
  editable?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  className?: string;
}

export interface RecognizedText {
  id: string;
  text: string;
  confidence: number;
  bounds: TextBounds;
  type: 'word' | 'line' | 'paragraph' | 'block';
  language?: string;
  isEdited?: boolean;
}

export interface TextBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface EnhancementControlsProps {
  initialSettings?: Partial<EnhancementSettings>;
  onSettingsChange?: (settings: EnhancementSettings) => void;
  onPresetApply?: (preset: EnhancementPreset) => void;
  onReset?: () => void;
  onAutoEnhance?: () => void;
  showPresets?: boolean;
  showAdvanced?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export interface EnhancementPreset {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  color: string;
  settings: Partial<EnhancementSettings>;
}

export interface ConfidenceScoreProps {
  score: number; // 0-100
  previousScore?: number;
  category?: string;
  breakdown?: ConfidenceBreakdown[];
  showTrend?: boolean;
  showBreakdown?: boolean;
  animated?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'glass' | 'glassCard';
  className?: string;
}

export interface ConfidenceBreakdown {
  category: string;
  score: number;
  weight: number;
  icon?: React.ComponentType<{ size: number; color: string }>;
  description?: string;
}

// Priority 3: Document Management Components
export interface DocumentCardProps {
  document: DocumentInfo;
  onPress?: (document: DocumentInfo) => void;
  onShare?: (document: DocumentInfo) => void;
  onDownload?: (document: DocumentInfo) => void;
  onEdit?: (document: DocumentInfo) => void;
  onDelete?: (document: DocumentInfo) => void;
  onFavorite?: (document: DocumentInfo) => void;
  showActions?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  size?: 'sm' | 'md' | 'lg';
  layout?: 'card' | 'list';
  className?: string;
}

export interface DocumentInfo {
  id: string;
  title: string;
  description?: string;
  thumbnailUri?: string;
  type: 'document' | 'image' | 'pdf' | 'text';
  size: number; // in bytes
  createdAt: Date;
  modifiedAt: Date;
  tags: string[];
  folder?: string;
  isFavorite: boolean;
  confidence?: number;
  pageCount?: number;
  ocrStatus: 'pending' | 'processing' | 'completed' | 'failed';
  metadata?: {
    language?: string;
    wordCount?: number;
    resolution?: string;
    colorSpace?: string;
  };
}

export interface FolderGridProps {
  folders: FolderInfo[];
  onFolderPress?: (folder: FolderInfo) => void;
  onCreateFolder?: () => void;
  onEditFolder?: (folder: FolderInfo) => void;
  onDeleteFolder?: (folder: FolderInfo) => void;
  onShareFolder?: (folder: FolderInfo) => void;
  onArchiveFolder?: (folder: FolderInfo) => void;
  showActions?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  columns?: number;
  className?: string;
}

export interface FolderInfo {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon?: React.ComponentType<{ size: number; color: string }>;
  documentCount: number;
  totalSize: number; // in bytes
  createdAt: Date;
  modifiedAt: Date;
  tags: string[];
  isArchived: boolean;
  isFavorite: boolean;
  isShared: boolean;
  recentActivity?: {
    type: 'added' | 'modified' | 'shared';
    count: number;
    timestamp: Date;
  };
  metadata?: {
    ocrCompleted: number;
    averageConfidence: number;
    languages: string[];
  };
}

export interface SearchBarProps {
  onSearch?: (query: string, filters: SearchFilters) => void;
  onFilterChange?: (filters: SearchFilters) => void;
  placeholder?: string;
  recentSearches?: string[];
  suggestions?: SearchSuggestion[];
  showFilters?: boolean;
  showRecentSearches?: boolean;
  showSuggestions?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export interface SearchFilters {
  documentType?: 'all' | 'document' | 'image' | 'pdf' | 'text';
  dateRange?: 'all' | 'today' | 'week' | 'month' | 'year';
  folder?: string;
  tags?: string[];
  confidence?: 'all' | 'high' | 'medium' | 'low';
  ocrStatus?: 'all' | 'completed' | 'processing' | 'pending' | 'failed';
  isFavorite?: boolean;
  isShared?: boolean;
  sortBy?: 'relevance' | 'date' | 'name' | 'size' | 'confidence';
  sortOrder?: 'asc' | 'desc';
}

export interface SearchSuggestion {
  id: string;
  text: string;
  type: 'query' | 'tag' | 'folder' | 'document';
  icon?: React.ComponentType<{ size: number; color: string }>;
  count?: number;
}

export interface FilterPanelProps {
  filters: DocumentFilters;
  onFiltersChange?: (filters: DocumentFilters) => void;
  onApplyFilters?: (filters: DocumentFilters) => void;
  onResetFilters?: () => void;
  availableTags?: string[];
  availableFolders?: string[];
  showApplyButton?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  className?: string;
}

export interface DocumentFilters {
  // Basic Filters
  documentType: 'all' | 'document' | 'image' | 'pdf' | 'text';
  dateRange: 'all' | 'today' | 'yesterday' | 'week' | 'month' | 'year' | 'custom';
  customDateStart?: Date;
  customDateEnd?: Date;

  // Content Filters
  tags: string[];
  folder?: string;
  hasText: boolean;
  language?: string;

  // Quality Filters
  confidenceRange: [number, number]; // 0-100
  ocrStatus: 'all' | 'completed' | 'processing' | 'pending' | 'failed';

  // Size Filters
  sizeRange: [number, number]; // in MB
  pageCountRange?: [number, number];

  // Status Filters
  isFavorite: boolean;
  isShared: boolean;
  isArchived: boolean;

  // Sort Options
  sortBy: 'relevance' | 'date' | 'name' | 'size' | 'confidence' | 'modified';
  sortOrder: 'asc' | 'desc';
}

// Priority 4: User Experience Components
export interface OnboardingFlowProps {
  onComplete?: () => void;
  onSkip?: () => void;
  onStepChange?: (step: number) => void;
  steps?: OnboardingStep[];
  showSkip?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  className?: string;
}

export interface OnboardingStep {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  color: string;
  animation?: 'fade' | 'slide' | 'scale' | 'bounce';
  features?: string[];
  action?: {
    label: string;
    onPress: () => void;
  };
}

export interface UpgradePromptProps {
  onUpgrade?: (plan: PremiumPlan) => void;
  onDismiss?: () => void;
  onRestorePurchases?: () => void;
  currentPlan?: 'free' | 'premium' | 'pro';
  trigger?: 'limit_reached' | 'feature_locked' | 'manual' | 'trial_ending';
  variant?: 'modal' | 'banner' | 'fullscreen';
  showDismiss?: boolean;
  className?: string;
}

export interface PremiumPlan {
  id: string;
  name: string;
  price: string;
  period: string;
  originalPrice?: string;
  discount?: string;
  color: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  features: PremiumFeature[];
  popular?: boolean;
  trial?: {
    duration: string;
    description: string;
  };
}

export interface PremiumFeature {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  highlight?: boolean;
}

export interface TutorialOverlayProps {
  steps: TutorialStep[];
  currentStep?: number;
  onStepChange?: (step: number) => void;
  onComplete?: () => void;
  onSkip?: () => void;
  onClose?: () => void;
  autoPlay?: boolean;
  autoPlayDelay?: number;
  showProgress?: boolean;
  variant?: 'spotlight' | 'tooltip' | 'fullscreen';
  className?: string;
}

export interface TutorialStep {
  id: string;
  title: string;
  description: string;
  target?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center';
  type?: 'info' | 'tip' | 'warning' | 'success';
  icon?: React.ComponentType<{ size: number; color: string }>;
  action?: {
    label: string;
    onPress: () => void;
  };
  animation?: 'pulse' | 'bounce' | 'shake' | 'glow';
}

export interface StatusToastProps {
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info' | 'loading';
  duration?: number;
  position?: 'top' | 'bottom' | 'center';
  action?: ToastAction;
  onDismiss?: () => void;
  persistent?: boolean;
  showProgress?: boolean;
  variant?: 'default' | 'glass' | 'minimal';
  className?: string;
}

export interface ToastAction {
  label: string;
  onPress: () => void;
  icon?: React.ComponentType<{ size: number; color: string }>;
}