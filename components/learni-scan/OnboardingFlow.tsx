import React, { useState, useRef } from 'react';
import { View, ScrollView, Dimensions, Animated } from 'react-native';
import { Text } from '../ui/text';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Pressable } from '../ui/pressable';
import { 
  CameraIcon, 
  SparklesIcon, 
  FolderIcon,
  ShareIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  CheckCircleIcon,
  EyeIcon,
  ZapIcon,
  CloudIcon,
  StarIcon,
  ArrowRightIcon,
  type LucideIcon
} from 'lucide-react-native';

// Type-safe icon wrapper for LucideIcon compatibility
const createIconComponent = (IconComponent: LucideIcon): React.ComponentType<{ size: number; color: string }> => {
  return ({ size, color }) => <IconComponent size={size} color={color} />;
};

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface OnboardingFlowProps {
  onComplete?: () => void;
  onSkip?: () => void;
  onStepChange?: (step: number) => void;
  steps?: OnboardingStep[];
  showSkip?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  className?: string;
}

interface OnboardingStep {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  color: string;
  animation?: 'fade' | 'slide' | 'scale' | 'bounce';
  features?: string[];
  action?: {
    label: string;
    onPress: () => void;
  };
}

const defaultSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to LearniScan',
    subtitle: 'AI-Powered Document Scanning',
    description: 'Transform any document into searchable, editable text with our advanced AI technology.',
    icon: createIconComponent(SparklesIcon),
    color: '#FF6B9D',
    animation: 'bounce',
    features: [
      'Instant document scanning',
      'AI-powered text recognition',
      'Cloud synchronization',
      'Advanced search capabilities'
    ]
  },
  {
    id: 'scan',
    title: 'Scan Documents',
    subtitle: 'Professional Quality Results',
    description: 'Capture documents with our intelligent camera that automatically detects and enhances your scans.',
    icon: createIconComponent(CameraIcon),
    color: '#A855F7',
    animation: 'slide',
    features: [
      'Auto-detect document edges',
      'Real-time quality feedback',
      'Multiple format support',
      'Batch scanning capabilities'
    ]
  },
  {
    id: 'enhance',
    title: 'AI Enhancement',
    subtitle: 'Crystal Clear Results',
    description: 'Our AI automatically enhances your scans for optimal readability and text recognition accuracy.',
    icon: createIconComponent(EyeIcon),
    color: '#3B82F6',
    animation: 'scale',
    features: [
      'Automatic image enhancement',
      'Text recognition (OCR)',
      'Multiple language support',
      'Confidence scoring'
    ]
  },
  {
    id: 'organize',
    title: 'Smart Organization',
    subtitle: 'Find Anything Instantly',
    description: 'Organize your documents with smart folders, tags, and powerful search capabilities.',
    icon: createIconComponent(FolderIcon),
    color: '#10B981',
    animation: 'fade',
    features: [
      'Smart folder organization',
      'Tag-based categorization',
      'Advanced search filters',
      'Favorites and sharing'
    ]
  },
  {
    id: 'sync',
    title: 'Cloud Sync',
    subtitle: 'Access Everywhere',
    description: 'Your documents are automatically synced across all your devices with secure cloud storage.',
    icon: createIconComponent(CloudIcon),
    color: '#F59E0B',
    animation: 'slide',
    features: [
      'Automatic cloud backup',
      'Cross-device synchronization',
      'Secure encryption',
      'Offline access'
    ]
  }
];

export function OnboardingFlow({
  onComplete,
  onSkip,
  onStepChange,
  steps = defaultSteps,
  showSkip = true,
  variant = 'glass',
  className
}: OnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [animatedValue] = useState(new Animated.Value(0));
  const scrollViewRef = useRef<ScrollView>(null);

  const currentStepData = steps[currentStep];
  const isLastStep = currentStep === steps.length - 1;
  const isFirstStep = currentStep === 0;

  const nextStep = () => {
    if (isLastStep) {
      onComplete?.();
    } else {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      onStepChange?.(newStep);
      
      // Animate to next step
      Animated.timing(animatedValue, {
        toValue: newStep,
        duration: 300,
        useNativeDriver: true,
      }).start();
      
      // Scroll to next step
      scrollViewRef.current?.scrollTo({
        x: newStep * screenWidth,
        animated: true
      });
    }
  };

  const previousStep = () => {
    if (!isFirstStep) {
      const newStep = currentStep - 1;
      setCurrentStep(newStep);
      onStepChange?.(newStep);
      
      // Animate to previous step
      Animated.timing(animatedValue, {
        toValue: newStep,
        duration: 300,
        useNativeDriver: true,
      }).start();
      
      // Scroll to previous step
      scrollViewRef.current?.scrollTo({
        x: newStep * screenWidth,
        animated: true
      });
    }
  };

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex);
    onStepChange?.(stepIndex);
    
    Animated.timing(animatedValue, {
      toValue: stepIndex,
      duration: 300,
      useNativeDriver: true,
    }).start();
    
    scrollViewRef.current?.scrollTo({
      x: stepIndex * screenWidth,
      animated: true
    });
  };

  const containerClasses = {
    default: 'bg-background-0',
    glass: 'bg-glass-bg-primary backdrop-blur-md',
    glassCard: 'bg-glass-bg-card backdrop-blur-md'
  };

  const StepContent = ({ step, index }: { step: OnboardingStep; index: number }) => {
    const StepIcon = step.icon;
    
    return (
      <View style={{ width: screenWidth, flex: 1 }}>
        <VStack space="xl" className="flex-1 justify-center items-center p-8">
          {/* Icon Animation */}
          <Animated.View
            style={{
              transform: [
                {
                  scale: animatedValue.interpolate({
                    inputRange: [index - 1, index, index + 1],
                    outputRange: [0.8, 1.2, 0.8],
                    extrapolate: 'clamp',
                  }),
                },
                {
                  rotate: animatedValue.interpolate({
                    inputRange: [index - 1, index, index + 1],
                    outputRange: ['-10deg', '0deg', '10deg'],
                    extrapolate: 'clamp',
                  }),
                },
              ],
            }}
          >
            <Box 
              className="w-32 h-32 justify-center items-center rounded-full"
              style={{
                backgroundColor: step.color + '20',
                borderWidth: 3,
                borderColor: step.color + '40'
              }}
            >
              <StepIcon size={64} color={step.color} />
            </Box>
          </Animated.View>

          {/* Content */}
          <VStack space="lg" style={{ alignItems: 'center', maxWidth: 320 }}>
            <VStack space="md" style={{ alignItems: 'center' }}>
              <Text 
                color="primary" 
                size="2xl" 
                style={{ fontWeight: 'bold', textAlign: 'center' }}
              >
                {step.title}
              </Text>
              
              <Text 
                size="lg" 
                style={{ 
                  color: step.color, 
                  fontWeight: '600', 
                  textAlign: 'center' 
                }}
              >
                {step.subtitle}
              </Text>
            </VStack>
            
            <Text 
              color="secondary" 
              size="md" 
              style={{ textAlign: 'center', lineHeight: 24 }}
            >
              {step.description}
            </Text>

            {/* Features List */}
            {step.features && (
              <VStack space="sm" style={{ alignItems: 'center' }}>
                {step.features.map((feature, featureIndex) => (
                  <HStack key={featureIndex} className="items-center space-x-3">
                    <CheckCircleIcon size={16} color={step.color} />
                    <Text color="primary" size="sm">
                      {feature}
                    </Text>
                  </HStack>
                ))}
              </VStack>
            )}

            {/* Custom Action */}
            {step.action && (
              <Button 
                action="glass" 
                variant="outline" 
                onPress={step.action.onPress}
                style={{ marginTop: 16 }}
              >
                <ButtonText>{step.action.label}</ButtonText>
              </Button>
            )}
          </VStack>
        </VStack>
      </View>
    );
  };

  return (
    <View className={`flex-1 ${containerClasses[variant]} ${className}`}>
      <VStack space="lg" className="flex-1">
        {/* Header */}
        <Box className="pt-12 px-6">
          <HStack className="justify-between items-center">
            <Text color="tertiary" size="sm">
              {currentStep + 1} of {steps.length}
            </Text>
            
            {showSkip && !isLastStep && (
              <Pressable onPress={onSkip}>
                <Text color="candyPink" size="sm" style={{ fontWeight: '600' }}>
                  Skip
                </Text>
              </Pressable>
            )}
          </HStack>
        </Box>

        {/* Progress Indicator */}
        <Box className="px-6">
          <HStack className="space-x-2">
            {steps.map((_, index) => (
              <Pressable 
                key={index} 
                onPress={() => goToStep(index)}
                style={{ flex: 1 }}
              >
                <Box 
                  className="h-1 rounded-full"
                  style={{
                    backgroundColor: index <= currentStep 
                      ? currentStepData.color 
                      : 'rgba(156, 163, 175, 0.3)'
                  }}
                />
              </Pressable>
            ))}
          </HStack>
        </Box>

        {/* Content Carousel */}
        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          scrollEnabled={false}
          style={{ flex: 1 }}
        >
          {steps.map((step, index) => (
            <StepContent key={step.id} step={step} index={index} />
          ))}
        </ScrollView>

        {/* Navigation */}
        <Box className="px-6 pb-8">
          <VStack space="md">
            {/* Step Indicators */}
            <HStack className="justify-center space-x-2">
              {steps.map((step, index) => (
                <Pressable 
                  key={index} 
                  onPress={() => goToStep(index)}
                >
                  <Box 
                    className={`w-3 h-3 rounded-full ${
                      index === currentStep ? 'opacity-100' : 'opacity-30'
                    }`}
                    style={{
                      backgroundColor: index === currentStep 
                        ? currentStepData.color 
                        : '#9CA3AF'
                    }}
                  />
                </Pressable>
              ))}
            </HStack>

            {/* Navigation Buttons */}
            <HStack className="justify-between items-center">
              <Button 
                action="glass" 
                variant="outline" 
                onPress={previousStep}
                disabled={isFirstStep}
                style={{ 
                  opacity: isFirstStep ? 0.5 : 1,
                  minWidth: 100
                }}
              >
                <HStack className="items-center space-x-2">
                  <ChevronLeftIcon size={16} color="#FFFFFF" />
                  <ButtonText>Back</ButtonText>
                </HStack>
              </Button>

              <Button 
                action={isLastStep ? "candyPink" : "candyPurple"}
                onPress={nextStep}
                style={{ minWidth: 120 }}
              >
                <HStack className="items-center space-x-2">
                  <ButtonText>
                    {isLastStep ? 'Get Started' : 'Next'}
                  </ButtonText>
                  {isLastStep ? (
                    <StarIcon size={16} color="#FFFFFF" />
                  ) : (
                    <ChevronRightIcon size={16} color="#FFFFFF" />
                  )}
                </HStack>
              </Button>
            </HStack>
          </VStack>
        </Box>
      </VStack>
    </View>
  );
}

export default OnboardingFlow;