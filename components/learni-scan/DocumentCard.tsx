import React, { useState } from 'react';
import { View, Image, TouchableOpacity } from 'react-native';
import { Text } from '../ui/text';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Badge, BadgeText } from '../ui/badge';
import { Pressable } from '../ui/pressable';
import { 
  FileTextIcon, 
  ImageIcon, 
  MoreVerticalIcon,
  ShareIcon,
  DownloadIcon,
  EditIcon,
  TrashIcon,
  StarIcon,
  ClockIcon,
  EyeIcon,
  TagIcon,
  FolderIcon
} from 'lucide-react-native';

interface DocumentCardProps {
  document: DocumentInfo;
  onPress?: (document: DocumentInfo) => void;
  onShare?: (document: DocumentInfo) => void;
  onDownload?: (document: DocumentInfo) => void;
  onEdit?: (document: DocumentInfo) => void;
  onDelete?: (document: DocumentInfo) => void;
  onFavorite?: (document: DocumentInfo) => void;
  showActions?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  size?: 'sm' | 'md' | 'lg';
  layout?: 'card' | 'list';
  className?: string;
}

interface DocumentInfo {
  id: string;
  title: string;
  description?: string;
  thumbnailUri?: string;
  type: 'document' | 'image' | 'pdf' | 'text';
  size: number; // in bytes
  createdAt: Date;
  modifiedAt: Date;
  tags: string[];
  folder?: string;
  isFavorite: boolean;
  confidence?: number;
  pageCount?: number;
  ocrStatus: 'pending' | 'processing' | 'completed' | 'failed';
  metadata?: {
    language?: string;
    wordCount?: number;
    resolution?: string;
    colorSpace?: string;
  };
}

const documentTypeIcons = {
  document: FileTextIcon,
  image: ImageIcon,
  pdf: FileTextIcon,
  text: FileTextIcon
};

const documentTypeColors = {
  document: '#FF6B9D',
  image: '#A855F7',
  pdf: '#3B82F6',
  text: '#10B981'
};

export function DocumentCard({
  document,
  onPress,
  onShare,
  onDownload,
  onEdit,
  onDelete,
  onFavorite,
  showActions = true,
  variant = 'glass',
  size = 'md',
  layout = 'card',
  className
}: DocumentCardProps) {
  const [showMenu, setShowMenu] = useState(false);

  const DocumentIcon = documentTypeIcons[document.type];
  const documentColor = documentTypeColors[document.type];

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  const getOCRStatusColor = (status: DocumentInfo['ocrStatus']) => {
    switch (status) {
      case 'completed': return '#10B981';
      case 'processing': return '#F59E0B';
      case 'failed': return '#EF4444';
      default: return '#9CA3AF';
    }
  };

  const getOCRStatusText = (status: DocumentInfo['ocrStatus']) => {
    switch (status) {
      case 'completed': return 'OCR Complete';
      case 'processing': return 'Processing...';
      case 'failed': return 'OCR Failed';
      default: return 'Pending OCR';
    }
  };

  const containerClasses = {
    default: 'bg-background-0 border border-outline-100',
    glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md',
    glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md'
  };

  const sizeConfig = {
    sm: {
      padding: 'p-3',
      spacing: 'sm' as const,
      thumbnailSize: 60,
      titleSize: 'sm' as const,
      metaSize: 'xs' as const
    },
    md: {
      padding: 'p-4',
      spacing: 'md' as const,
      thumbnailSize: 80,
      titleSize: 'md' as const,
      metaSize: 'sm' as const
    },
    lg: {
      padding: 'p-6',
      spacing: 'lg' as const,
      thumbnailSize: 100,
      titleSize: 'lg' as const,
      metaSize: 'md' as const
    }
  };

  const config = sizeConfig[size];

  if (layout === 'list') {
    return (
      <Pressable 
        variant={variant === 'default' ? 'default' : 'glass'} 
        onPress={() => onPress?.(document)}
        className={className}
      >
        <Box className={`${containerClasses[variant]} rounded-lg ${config.padding}`}>
          <HStack className="items-center space-x-3">
            {/* Thumbnail/Icon */}
            <Box className="relative">
              {document.thumbnailUri ? (
                <Image 
                  source={{ uri: document.thumbnailUri }}
                  style={{
                    width: config.thumbnailSize,
                    height: config.thumbnailSize,
                    borderRadius: 8
                  }}
                  resizeMode="cover"
                />
              ) : (
                <Box 
                  className="justify-center items-center rounded-lg"
                  style={{
                    width: config.thumbnailSize,
                    height: config.thumbnailSize,
                    backgroundColor: `${documentColor}20`
                  }}
                >
                  <DocumentIcon size={config.thumbnailSize * 0.4} color={documentColor} />
                </Box>
              )}
              
              {/* Favorite Badge */}
              {document.isFavorite && (
                <Box className="absolute -top-1 -right-1">
                  <StarIcon size={16} color="#F59E0B" fill="#F59E0B" />
                </Box>
              )}
            </Box>

            {/* Content */}
            <VStack space="xs" className="flex-1">
              <HStack className="justify-between items-start">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size={config.titleSize} style={{ fontWeight: 'bold' }}>
                    {document.title}
                  </Text>
                  {document.description && (
                    <Text color="secondary" size={config.metaSize} numberOfLines={1}>
                      {document.description}
                    </Text>
                  )}
                </VStack>
                
                {showActions && (
                  <Pressable onPress={() => setShowMenu(!showMenu)}>
                    <MoreVerticalIcon size={20} color="#9CA3AF" />
                  </Pressable>
                )}
              </HStack>

              {/* Metadata */}
              <HStack className="items-center space-x-4">
                <Text color="tertiary" size="xs">
                  {formatFileSize(document.size)}
                </Text>
                <Text color="tertiary" size="xs">
                  {formatDate(document.modifiedAt)}
                </Text>
                {document.pageCount && (
                  <Text color="tertiary" size="xs">
                    {document.pageCount} pages
                  </Text>
                )}
              </HStack>

              {/* Tags */}
              {document.tags.length > 0 && (
                <HStack className="space-x-1 flex-wrap">
                  {document.tags.slice(0, 3).map((tag, index) => (
                    <Badge key={index} action="muted" variant="outline" size="sm">
                      <BadgeText>{tag}</BadgeText>
                    </Badge>
                  ))}
                  {document.tags.length > 3 && (
                    <Text color="tertiary" size="xs">
                      +{document.tags.length - 3} more
                    </Text>
                  )}
                </HStack>
              )}
            </VStack>
          </HStack>
        </Box>
      </Pressable>
    );
  }

  // Card layout
  return (
    <Pressable 
      variant={variant === 'default' ? 'default' : 'glass'} 
      onPress={() => onPress?.(document)}
      className={className}
    >
      <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
        <VStack space={config.spacing}>
          {/* Header */}
          <HStack className="justify-between items-start">
            <HStack className="items-center space-x-2 flex-1">
              <Box 
                className="w-8 h-8 justify-center items-center rounded-full"
                style={{ backgroundColor: `${documentColor}20` }}
              >
                <DocumentIcon size={16} color={documentColor} />
              </Box>
              
              <VStack space="xs" className="flex-1">
                <Text color="primary" size={config.titleSize} style={{ fontWeight: 'bold' }} numberOfLines={1}>
                  {document.title}
                </Text>
                {document.folder && (
                  <HStack className="items-center space-x-1">
                    <FolderIcon size={12} color="#9CA3AF" />
                    <Text color="tertiary" size="xs">
                      {document.folder}
                    </Text>
                  </HStack>
                )}
              </VStack>
            </HStack>
            
            <HStack className="items-center space-x-2">
              {document.isFavorite && (
                <StarIcon size={16} color="#F59E0B" fill="#F59E0B" />
              )}
              
              {showActions && (
                <Pressable onPress={() => setShowMenu(!showMenu)}>
                  <MoreVerticalIcon size={20} color="#9CA3AF" />
                </Pressable>
              )}
            </HStack>
          </HStack>

          {/* Thumbnail */}
          <Box className="relative">
            {document.thumbnailUri ? (
              <Image 
                source={{ uri: document.thumbnailUri }}
                style={{
                  width: '100%',
                  height: 120,
                  borderRadius: 8
                }}
                resizeMode="cover"
              />
            ) : (
              <Box 
                className="w-full h-30 justify-center items-center rounded-lg"
                style={{ backgroundColor: `${documentColor}10` }}
              >
                <DocumentIcon size={40} color={documentColor} />
              </Box>
            )}
            
            {/* OCR Status Badge */}
            <Box className="absolute top-2 right-2">
              <Badge 
                action={document.ocrStatus === 'completed' ? 'success' : 
                       document.ocrStatus === 'processing' ? 'warning' : 
                       document.ocrStatus === 'failed' ? 'error' : 'muted'}
                variant="solid"
                size="sm"
              >
                <BadgeText>{getOCRStatusText(document.ocrStatus)}</BadgeText>
              </Badge>
            </Box>
          </Box>

          {/* Description */}
          {document.description && (
            <Text color="secondary" size={config.metaSize} numberOfLines={2}>
              {document.description}
            </Text>
          )}

          {/* Metadata Grid */}
          <VStack space="sm">
            <HStack className="justify-between">
              <Text color="tertiary" size="xs">Size:</Text>
              <Text color="secondary" size="xs">{formatFileSize(document.size)}</Text>
            </HStack>
            
            <HStack className="justify-between">
              <Text color="tertiary" size="xs">Modified:</Text>
              <Text color="secondary" size="xs">{formatDate(document.modifiedAt)}</Text>
            </HStack>
            
            {document.confidence && (
              <HStack className="justify-between">
                <Text color="tertiary" size="xs">Confidence:</Text>
                <Text 
                  size="xs" 
                  style={{ 
                    fontWeight: '600',
                    color: document.confidence >= 80 ? '#10B981' : 
                           document.confidence >= 60 ? '#F59E0B' : '#EF4444'
                  }}
                >
                  {document.confidence}%
                </Text>
              </HStack>
            )}
            
            {document.metadata?.wordCount && (
              <HStack className="justify-between">
                <Text color="tertiary" size="xs">Words:</Text>
                <Text color="secondary" size="xs">{document.metadata.wordCount.toLocaleString()}</Text>
              </HStack>
            )}
          </VStack>

          {/* Tags */}
          {document.tags.length > 0 && (
            <VStack space="xs">
              <HStack className="items-center space-x-1">
                <TagIcon size={12} color="#9CA3AF" />
                <Text color="tertiary" size="xs">Tags:</Text>
              </HStack>
              <HStack className="space-x-1 flex-wrap">
                {document.tags.map((tag, index) => (
                  <Badge key={index} action="muted" variant="outline" size="sm">
                    <BadgeText>{tag}</BadgeText>
                  </Badge>
                ))}
              </HStack>
            </VStack>
          )}

          {/* Quick Actions */}
          {showActions && (
            <HStack className="space-x-2">
              <Button action="candyPink" size="xs" style={{ flex: 1 }} onPress={() => onPress?.(document)}>
                <HStack className="items-center space-x-1">
                  <EyeIcon size={12} color="#FFFFFF" />
                  <ButtonText>View</ButtonText>
                </HStack>
              </Button>
              
              <Button action="glass" variant="outline" size="xs" onPress={() => onShare?.(document)}>
                <ShareIcon size={12} color="#FFFFFF" />
              </Button>
              
              <Button action="glass" variant="outline" size="xs" onPress={() => onDownload?.(document)}>
                <DownloadIcon size={12} color="#FFFFFF" />
              </Button>
            </HStack>
          )}
        </VStack>

        {/* Action Menu */}
        {showMenu && (
          <Box className="absolute top-12 right-4 bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-lg p-2 z-10">
            <VStack space="xs">
              <Pressable onPress={() => { onEdit?.(document); setShowMenu(false); }}>
                <HStack className="items-center space-x-2 p-2 rounded">
                  <EditIcon size={16} color="#A855F7" />
                  <Text color="primary" size="sm">Edit</Text>
                </HStack>
              </Pressable>
              
              <Pressable onPress={() => { onFavorite?.(document); setShowMenu(false); }}>
                <HStack className="items-center space-x-2 p-2 rounded">
                  <StarIcon size={16} color="#F59E0B" />
                  <Text color="primary" size="sm">
                    {document.isFavorite ? 'Unfavorite' : 'Favorite'}
                  </Text>
                </HStack>
              </Pressable>
              
              <Pressable onPress={() => { onDelete?.(document); setShowMenu(false); }}>
                <HStack className="items-center space-x-2 p-2 rounded">
                  <TrashIcon size={16} color="#EF4444" />
                  <Text color="candyPink" size="sm">Delete</Text>
                </HStack>
              </Pressable>
            </VStack>
          </Box>
        )}
      </Box>
    </Pressable>
  );
}

export default DocumentCard;