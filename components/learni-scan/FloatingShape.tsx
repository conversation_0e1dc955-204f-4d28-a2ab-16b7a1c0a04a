import React, { useEffect, useMemo } from 'react';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withDelay,
  interpolate,
  runOnUI,
} from 'react-native-reanimated';

interface FloatingShapeProps {
  size: number;
  top?: number;
  left?: number;
  right?: number;
  bottom?: number;
  animationDuration: number;
  delay?: number;
}

export const FloatingShape: React.FC<FloatingShapeProps> = ({
  size,
  top,
  left,
  right,
  bottom,
  animationDuration,
  delay = 0
}) => {
  const animatedValue = useSharedValue(0);

  const animationConfig = useMemo(() => 
    withDelay(
      delay,
      withRepeat(
        withTiming(1, { duration: animationDuration }),
        -1, // -1 means infinite repeats
        true // reverse: true means it will go back and forth (0 → 1 → 0)
      )
    ), [delay, animationDuration]
  );

  useEffect(() => {
    runOnUI(() => {
      animatedValue.value = animationConfig;
    })();
  }, [animatedValue, animationConfig]);

  const animatedStyle = useAnimatedStyle(() => {
    const translateY = interpolate(animatedValue.value, [0, 1], [0, -10]);
    const scale = interpolate(animatedValue.value, [0, 0.5, 1], [1, 1.1, 1]);
    const skewX = interpolate(animatedValue.value, [0, 0.5, 1], [0, 2, 0]);
    const skewY = interpolate(animatedValue.value, [0, 0.5, 1], [0, 1, 0]);

    return {
      transform: [
        { translateY },
        { scale },
        { skewX: `${skewX}deg` },
        { skewY: `${skewY}deg` },
      ],
    };
  });

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          width: size,
          height: size,
          top: top,
          left: left,
          right: right,
          bottom: bottom,
          borderRadius: size / 2,
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          borderColor: 'rgba(255, 255, 255, 0.2)',
        },
        animatedStyle,
      ]}
    />
  );
};
