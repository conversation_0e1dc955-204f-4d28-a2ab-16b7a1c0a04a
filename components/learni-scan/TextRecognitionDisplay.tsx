import React, { useState } from 'react';
import { View, ScrollView, TouchableOpacity } from 'react-native';
import { Text } from '../ui/text';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Pressable } from '../ui/pressable';
import { 
  CopyIcon, 
  EditIcon, 
  ShareIcon,
  SearchIcon,
  HighlighterIcon,
  EyeIcon,
  CheckCircleIcon,
  AlertTriangleIcon,
  ZapIcon,
  FileTextIcon
} from 'lucide-react-native';

interface TextRecognitionDisplayProps {
  recognizedText: RecognizedText[];
  confidence: number;
  onTextSelect?: (selectedText: string, bounds: TextBounds) => void;
  onTextEdit?: (textId: string, newText: string) => void;
  onCopy?: (text: string) => void;
  onShare?: (text: string) => void;
  onSearch?: (text: string) => void;
  showConfidence?: boolean;
  editable?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  className?: string;
}

interface RecognizedText {
  id: string;
  text: string;
  confidence: number;
  bounds: TextBounds;
  type: 'word' | 'line' | 'paragraph' | 'block';
  language?: string;
  isEdited?: boolean;
}

interface TextBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export function TextRecognitionDisplay({
  recognizedText,
  confidence,
  onTextSelect,
  onTextEdit,
  onCopy,
  onShare,
  onSearch,
  showConfidence = true,
  editable = true,
  variant = 'glass',
  className
}: TextRecognitionDisplayProps) {
  const [selectedTextIds, setSelectedTextIds] = useState<Set<string>>(new Set());
  const [editingTextId, setEditingTextId] = useState<string | null>(null);
  const [editingText, setEditingText] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedTerms, setHighlightedTerms] = useState<Set<string>>(new Set());

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return '#10B981';
    if (confidence >= 70) return '#FF6B9D';
    if (confidence >= 50) return '#F59E0B';
    return '#EF4444';
  };

  const getConfidenceLevel = (confidence: number) => {
    if (confidence >= 90) return 'Excellent';
    if (confidence >= 70) return 'Good';
    if (confidence >= 50) return 'Fair';
    return 'Poor';
  };

  const handleTextPress = (textItem: RecognizedText) => {
    if (editingTextId) return;

    const newSelected = new Set(selectedTextIds);
    if (newSelected.has(textItem.id)) {
      newSelected.delete(textItem.id);
    } else {
      newSelected.add(textItem.id);
    }
    setSelectedTextIds(newSelected);
    
    if (onTextSelect) {
      const selectedText = getSelectedText(newSelected);
      onTextSelect(selectedText, textItem.bounds);
    }
  };

  const handleTextLongPress = (textItem: RecognizedText) => {
    if (!editable) return;
    
    setEditingTextId(textItem.id);
    setEditingText(textItem.text);
  };

  const handleEditSave = () => {
    if (editingTextId && onTextEdit) {
      onTextEdit(editingTextId, editingText);
    }
    setEditingTextId(null);
    setEditingText('');
  };

  const handleEditCancel = () => {
    setEditingTextId(null);
    setEditingText('');
  };

  const getSelectedText = (selectedIds?: Set<string>) => {
    const ids = selectedIds || selectedTextIds;
    return recognizedText
      .filter(item => ids.has(item.id))
      .map(item => item.text)
      .join(' ');
  };

  const handleCopySelected = () => {
    const selectedText = getSelectedText();
    if (selectedText && onCopy) {
      onCopy(selectedText);
      setSelectedTextIds(new Set());
    }
  };

  const handleShareSelected = () => {
    const selectedText = getSelectedText();
    if (selectedText && onShare) {
      onShare(selectedText);
      setSelectedTextIds(new Set());
    }
  };

  const handleSearchSelected = () => {
    const selectedText = getSelectedText();
    if (selectedText && onSearch) {
      onSearch(selectedText);
      setSelectedTextIds(new Set());
    }
  };

  const highlightSearchTerms = (text: string) => {
    if (!searchTerm || !highlightedTerms.has(searchTerm.toLowerCase())) {
      return text;
    }

    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '**$1**'); // Simple highlighting marker
  };

  const containerClasses = {
    default: 'bg-background-0 border border-outline-100',
    glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md',
    glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md'
  };

  const overallConfidenceColor = getConfidenceColor(confidence);
  const overallConfidenceLevel = getConfidenceLevel(confidence);

  return (
    <View className={className}>
      <VStack space="lg">
        {/* Header with Confidence */}
        {showConfidence && (
          <Box className={`${containerClasses[variant]} rounded-xl p-4`}>
            <HStack className="justify-between items-center">
              <HStack className="items-center space-x-3">
                <Box 
                  className="w-10 h-10 justify-center items-center rounded-full"
                  style={{ backgroundColor: `${overallConfidenceColor}20` }}
                >
                  <EyeIcon size={20} color={overallConfidenceColor} />
                </Box>
                
                <VStack space="xs">
                  <Text color="primary" size="md" style={{ fontWeight: 'bold' }}>
                    Text Recognition
                  </Text>
                  <Text color="secondary" size="sm">
                    {recognizedText.length} text elements detected
                  </Text>
                </VStack>
              </HStack>
              
              <VStack space="xs" style={{ alignItems: 'flex-end' }}>
                <Text 
                  size="lg" 
                  style={{ fontWeight: 'bold', color: overallConfidenceColor }}
                >
                  {confidence}%
                </Text>
                <Text 
                  size="xs" 
                  style={{ color: overallConfidenceColor }}
                >
                  {overallConfidenceLevel}
                </Text>
              </VStack>
            </HStack>
          </Box>
        )}

        {/* Selection Controls */}
        {selectedTextIds.size > 0 && (
          <Box className={`${containerClasses[variant]} rounded-xl p-4`}>
            <VStack space="md">
              <HStack className="justify-between items-center">
                <Text color="primary" size="sm" style={{ fontWeight: '600' }}>
                  {selectedTextIds.size} text element(s) selected
                </Text>
                <Pressable onPress={() => setSelectedTextIds(new Set())}>
                  <Text color="candyPink" size="sm">Clear</Text>
                </Pressable>
              </HStack>
              
              <HStack className="space-x-2">
                <Button action="candyPink" size="xs" onPress={handleCopySelected}>
                  <HStack className="items-center space-x-1">
                    <CopyIcon size={12} color="#FFFFFF" />
                    <ButtonText>Copy</ButtonText>
                  </HStack>
                </Button>
                
                <Button action="candyPurple" size="xs" onPress={handleShareSelected}>
                  <HStack className="items-center space-x-1">
                    <ShareIcon size={12} color="#FFFFFF" />
                    <ButtonText>Share</ButtonText>
                  </HStack>
                </Button>
                
                <Button action="candyBlue" size="xs" onPress={handleSearchSelected}>
                  <HStack className="items-center space-x-1">
                    <SearchIcon size={12} color="#FFFFFF" />
                    <ButtonText>Search</ButtonText>
                  </HStack>
                </Button>
              </HStack>
            </VStack>
          </Box>
        )}

        {/* Text Content */}
        <Box className={`${containerClasses[variant]} rounded-xl`}>
          <ScrollView className="max-h-96 p-4">
            <VStack space="md">
              {recognizedText
                .filter(item => item.type === 'paragraph' || item.type === 'block')
                .map((textItem) => {
                  const isSelected = selectedTextIds.has(textItem.id);
                  const isEditing = editingTextId === textItem.id;
                  const itemConfidenceColor = getConfidenceColor(textItem.confidence);
                  
                  return (
                    <Box key={textItem.id}>
                      {isEditing ? (
                        <VStack space="sm">
                          <Box className="bg-glass-bg-secondary border border-glass-border-primary rounded-lg p-3">
                            <Text 
                              color="primary"
                              size="md"
                              style={{ lineHeight: 24 }}
                            >
                              {editingText}
                            </Text>
                          </Box>
                          
                          <HStack className="justify-end space-x-2">
                            <Button action="glass" variant="outline" size="xs" onPress={handleEditCancel}>
                              <ButtonText>Cancel</ButtonText>
                            </Button>
                            <Button action="candyPink" size="xs" onPress={handleEditSave}>
                              <ButtonText>Save</ButtonText>
                            </Button>
                          </HStack>
                        </VStack>
                      ) : (
                        <TouchableOpacity
                          onPress={() => handleTextPress(textItem)}
                          onLongPress={() => handleTextLongPress(textItem)}
                          activeOpacity={0.7}
                        >
                          <Box 
                            className={`p-3 rounded-lg border ${
                              isSelected 
                                ? 'bg-candyPink/10 border-candyPink/30' 
                                : 'bg-glass-bg-secondary border-glass-border-primary'
                            }`}
                          >
                            <VStack space="sm">
                              <HStack className="justify-between items-start">
                                <Text 
                                  color={isSelected ? "candyPink" : "primary"}
                                  size="md"
                                  style={{ 
                                    lineHeight: 24,
                                    flex: 1,
                                    fontWeight: isSelected ? '600' : 'normal'
                                  }}
                                >
                                  {highlightSearchTerms(textItem.text)}
                                </Text>
                                
                                {showConfidence && (
                                  <Box className="ml-2">
                                    <Text 
                                      size="xs" 
                                      style={{ 
                                        color: itemConfidenceColor,
                                        fontWeight: '600'
                                      }}
                                    >
                                      {textItem.confidence}%
                                    </Text>
                                  </Box>
                                )}
                              </HStack>
                              
                              {/* Text Metadata */}
                              <HStack className="justify-between items-center">
                                <HStack className="items-center space-x-2">
                                  {textItem.isEdited && (
                                    <Box className="flex-row items-center space-x-1">
                                      <EditIcon size={10} color="#A855F7" />
                                      <Text color="candyPurple" size="xs">Edited</Text>
                                    </Box>
                                  )}
                                  
                                  {textItem.language && (
                                    <Text color="tertiary" size="xs">
                                      {textItem.language.toUpperCase()}
                                    </Text>
                                  )}
                                  
                                  <Text color="tertiary" size="xs">
                                    {textItem.type}
                                  </Text>
                                </HStack>
                                
                                {textItem.confidence < 70 && (
                                  <HStack className="items-center space-x-1">
                                    <AlertTriangleIcon size={10} color="#F59E0B" />
                                    <Text color="warning" size="xs">Low confidence</Text>
                                  </HStack>
                                )}
                              </HStack>
                            </VStack>
                          </Box>
                        </TouchableOpacity>
                      )}
                    </Box>
                  );
                })}
            </VStack>
          </ScrollView>
        </Box>

        {/* Quick Actions */}
        <Box className={`${containerClasses[variant]} rounded-xl p-4`}>
          <VStack space="md">
            <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
              Quick Actions:
            </Text>
            
            <HStack className="space-x-2 flex-wrap">
              <Button 
                action="candyPink" 
                size="sm" 
                onPress={() => onCopy?.(recognizedText.map(t => t.text).join('\n'))}
              >
                <HStack className="items-center space-x-2">
                  <CopyIcon size={16} color="#FFFFFF" />
                  <ButtonText>Copy All</ButtonText>
                </HStack>
              </Button>
              
              <Button 
                action="candyPurple" 
                size="sm" 
                onPress={() => onShare?.(recognizedText.map(t => t.text).join('\n'))}
              >
                <HStack className="items-center space-x-2">
                  <ShareIcon size={16} color="#FFFFFF" />
                  <ButtonText>Share All</ButtonText>
                </HStack>
              </Button>
              
              <Button 
                action="glass" 
                variant="outline" 
                size="sm" 
                onPress={() => {
                  // Toggle highlight mode
                  const newHighlighted = new Set(highlightedTerms);
                  if (searchTerm && !newHighlighted.has(searchTerm.toLowerCase())) {
                    newHighlighted.add(searchTerm.toLowerCase());
                  } else {
                    newHighlighted.clear();
                  }
                  setHighlightedTerms(newHighlighted);
                }}
              >
                <HStack className="items-center space-x-2">
                  <HighlighterIcon size={16} color="#FFFFFF" />
                  <ButtonText>Highlight</ButtonText>
                </HStack>
              </Button>
            </HStack>
          </VStack>
        </Box>

        {/* Statistics */}
        <Box className={`${containerClasses[variant]} rounded-xl p-4`}>
          <VStack space="md">
            <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
              Recognition Statistics:
            </Text>
            
            <VStack space="sm">
              <HStack className="justify-between">
                <Text color="secondary" size="sm">Total Characters:</Text>
                <Text color="primary" size="sm" style={{ fontWeight: '600' }}>
                  {recognizedText.reduce((sum, item) => sum + item.text.length, 0)}
                </Text>
              </HStack>
              
              <HStack className="justify-between">
                <Text color="secondary" size="sm">Words:</Text>
                <Text color="primary" size="sm" style={{ fontWeight: '600' }}>
                  {recognizedText.reduce((sum, item) => sum + item.text.split(' ').length, 0)}
                </Text>
              </HStack>
              
              <HStack className="justify-between">
                <Text color="secondary" size="sm">Average Confidence:</Text>
                <Text 
                  size="sm" 
                  style={{ 
                    fontWeight: '600',
                    color: overallConfidenceColor
                  }}
                >
                  {confidence}%
                </Text>
              </HStack>
              
              <HStack className="justify-between">
                <Text color="secondary" size="sm">Languages Detected:</Text>
                <Text color="primary" size="sm" style={{ fontWeight: '600' }}>
                  {new Set(recognizedText.map(t => t.language).filter(Boolean)).size || 1}
                </Text>
              </HStack>
            </VStack>
          </VStack>
        </Box>
      </VStack>
    </View>
  );
}

export default TextRecognitionDisplay;