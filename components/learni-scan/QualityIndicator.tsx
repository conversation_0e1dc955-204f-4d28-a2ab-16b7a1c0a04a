import React, { useEffect, useState } from 'react';
import { View, Animated } from 'react-native';
import { Text } from '../ui/text';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { 
  CheckCircleIcon, 
  AlertTriangleIcon, 
  XCircleIcon,
  SparklesIcon,
  EyeIcon,
  ZapIcon,
  type LucideIcon
} from 'lucide-react-native';

// Type-safe icon wrapper for LucideIcon compatibility
const createIconComponent = (IconComponent: LucideIcon): React.ComponentType<{ size: number; color: string }> => {
  return ({ size, color }) => <IconComponent size={size} color={color} />;
};

interface QualityIndicatorProps {
  score: number; // 0-100
  factors?: QualityFactor[];
  showDetails?: boolean;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  className?: string;
}

interface QualityFactor {
  name: string;
  score: number;
  icon?: React.ComponentType<{ size: number; color: string }>;
  description?: string;
}

const defaultFactors: QualityFactor[] = [
  {
    name: 'Lighting',
    score: 85,
    icon: createIconComponent(ZapIcon),
    description: 'Document lighting and visibility'
  },
  {
    name: 'Focus',
    score: 92,
    icon: createIconComponent(EyeIcon),
    description: 'Image sharpness and clarity'
  },
  {
    name: 'Angle',
    score: 78,
    icon: createIconComponent(SparklesIcon),
    description: 'Document positioning and perspective'
  }
];

export function QualityIndicator({
  score,
  factors = defaultFactors,
  showDetails = false,
  size = 'md',
  animated = true,
  className
}: QualityIndicatorProps) {
  const [animatedScore] = useState(new Animated.Value(0));
  const [displayScore, setDisplayScore] = useState(0);

  useEffect(() => {
    if (animated) {
      Animated.timing(animatedScore, {
        toValue: score,
        duration: 1000,
        useNativeDriver: false,
      }).start();

      const listener = animatedScore.addListener(({ value }) => {
        setDisplayScore(Math.round(value));
      });

      return () => animatedScore.removeListener(listener);
    } else {
      setDisplayScore(score);
    }
  }, [score, animated]);

  const getQualityLevel = (score: number) => {
    if (score >= 80) return { level: 'excellent', color: '#10B981', bgColor: 'rgba(16, 185, 129, 0.1)' };
    if (score >= 60) return { level: 'good', color: '#FF6B9D', bgColor: 'rgba(255, 107, 157, 0.1)' };
    if (score >= 40) return { level: 'fair', color: '#F59E0B', bgColor: 'rgba(245, 158, 11, 0.1)' };
    return { level: 'poor', color: '#EF4444', bgColor: 'rgba(239, 68, 68, 0.1)' };
  };

  const getQualityIcon = (score: number) => {
    if (score >= 80) return CheckCircleIcon;
    if (score >= 40) return AlertTriangleIcon;
    return XCircleIcon;
  };

  const getQualityText = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Poor';
  };

  const quality = getQualityLevel(displayScore);
  const QualityIcon = getQualityIcon(displayScore);
  const qualityText = getQualityText(displayScore);

  const sizeConfig = {
    sm: { 
      circleSize: 60, 
      iconSize: 20, 
      textSize: 'sm' as const, 
      scoreSize: 'lg' as const,
      padding: 12 
    },
    md: { 
      circleSize: 80, 
      iconSize: 24, 
      textSize: 'md' as const, 
      scoreSize: 'xl' as const,
      padding: 16 
    },
    lg: { 
      circleSize: 100, 
      iconSize: 32, 
      textSize: 'lg' as const, 
      scoreSize: '2xl' as const,
      padding: 20 
    }
  };

  const config = sizeConfig[size];

  return (
    <View className={className}>
      <VStack space="md" style={{ alignItems: 'center' }}>
        {/* Main Quality Circle */}
        <Box 
          className="justify-center items-center rounded-full border-4"
          style={{
            width: config.circleSize,
            height: config.circleSize,
            backgroundColor: quality.bgColor,
            borderColor: quality.color,
          }}
        >
          <VStack space="xs" style={{ alignItems: 'center' }}>
            <Text 
              color="primary" 
              size={config.scoreSize} 
              style={{ fontWeight: 'bold', color: quality.color }}
            >
              {displayScore}
            </Text>
            <QualityIcon size={config.iconSize} color={quality.color} />
          </VStack>
        </Box>

        {/* Quality Label */}
        <Box 
          className="px-3 py-1 rounded-full border"
          style={{
            backgroundColor: quality.bgColor,
            borderColor: quality.color,
          }}
        >
          <Text 
            size={config.textSize} 
            style={{ fontWeight: '600', color: quality.color }}
          >
            {qualityText} Quality
          </Text>
        </Box>

        {/* Detailed Factors */}
        {showDetails && (
          <VStack space="sm" className="w-full">
            <Text color="tertiary" size="sm" style={{ fontWeight: '600', textAlign: 'center' }}>
              Quality Factors:
            </Text>
            
            {factors.map((factor, index) => {
              const factorQuality = getQualityLevel(factor.score);
              const FactorIcon = factor.icon || createIconComponent(SparklesIcon);
              
              return (
                <Box 
                  key={index}
                  className="bg-glass-bg-secondary border border-glass-border-primary rounded-lg p-3"
                >
                  <HStack className="justify-between items-center">
                    <HStack className="items-center space-x-3 flex-1">
                      <Box 
                        className="w-8 h-8 justify-center items-center rounded-full"
                        style={{ backgroundColor: factorQuality.bgColor }}
                      >
                        <FactorIcon size={16} color={factorQuality.color} />
                      </Box>
                      
                      <VStack space="xs" className="flex-1">
                        <Text color="primary" size="sm" style={{ fontWeight: '600' }}>
                          {factor.name}
                        </Text>
                        {factor.description && (
                          <Text color="secondary" size="xs">
                            {factor.description}
                          </Text>
                        )}
                      </VStack>
                    </HStack>
                    
                    <VStack space="xs" style={{ alignItems: 'flex-end' }}>
                      <Text 
                        size="sm" 
                        style={{ fontWeight: 'bold', color: factorQuality.color }}
                      >
                        {factor.score}%
                      </Text>
                      
                      {/* Mini Progress Bar */}
                      <Box className="w-16 h-1 bg-glass-bg-primary rounded-full overflow-hidden">
                        <Box 
                          className="h-full rounded-full"
                          style={{ 
                            width: `${factor.score}%`,
                            backgroundColor: factorQuality.color 
                          }}
                        />
                      </Box>
                    </VStack>
                  </HStack>
                </Box>
              );
            })}
          </VStack>
        )}

        {/* Improvement Suggestions */}
        {displayScore < 80 && showDetails && (
          <Box className="bg-glass-bg-card border border-glass-border-secondary rounded-lg p-4 w-full">
            <VStack space="sm">
              <HStack className="items-center space-x-2">
                <SparklesIcon size={16} color="#A855F7" />
                <Text color="candyPurple" size="sm" style={{ fontWeight: '600' }}>
                  Improvement Tips:
                </Text>
              </HStack>
              
              <VStack space="xs">
                {displayScore < 60 && (
                  <Text color="secondary" size="xs">
                    • Ensure adequate lighting on the document
                  </Text>
                )}
                {factors.find(f => f.name === 'Focus')?.score < 70 && (
                  <Text color="secondary" size="xs">
                    • Hold device steady and tap to focus
                  </Text>
                )}
                {factors.find(f => f.name === 'Angle')?.score < 70 && (
                  <Text color="secondary" size="xs">
                    • Position document flat and straight
                  </Text>
                )}
                <Text color="secondary" size="xs">
                  • Use the document frame guide for best results
                </Text>
              </VStack>
            </VStack>
          </Box>
        )}
      </VStack>
    </View>
  );
}

export default QualityIndicator;