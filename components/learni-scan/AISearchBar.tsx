/**
 * AI-Enhanced Search Bar Component
 * 
 * Provides AI-powered search functionality with:
 * - Semantic search capabilities
 * - Intent understanding
 * - Smart suggestions and autocomplete
 * - Real-time search results
 * 
 * Following LearniScan development workflow rules:
 * - Rule 6: Gluestack UI components with candy theme
 * - Rule 8: Smooth animations with react-native-reanimated
 * - Rule 9: Safe area management
 * - Rule 1: Interactive feedback protocol integration
 */

import React, { useState, useEffect, useCallback } from 'react';
import { StyleSheet, Alert } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

// Permission system integration
import { FeatureGate } from '@/components/auth/FeatureGate';
import { useUsageLimitedFeature, usePermissions, PERMISSIONS } from '@/hooks/usePermissions';

// Gluestack UI components (Rule 6)
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Input, InputField } from '@/components/ui/input';
import { Pressable } from '@/components/ui/pressable';
import { Badge, BadgeText } from '@/components/ui/badge';

// Icons
import { 
  Search, 
  Brain, 
  Sparkles, 
  TrendingUp, 
  Clock, 
  Tag, 
  Lightbulb,
  ArrowRight,
  X
} from 'lucide-react-native';

// AI Search Service
import { 
  aiSearchService, 
  type SemanticSearchInput, 
  type AISearchResponse, 
  type SearchProgress,
  type SmartSuggestion 
} from '@/lib/services/ai-search.service';

interface AISearchBarProps {
  onSearch?: (query: string, results: AISearchResponse) => void;
  onSuggestionPress?: (suggestion: SmartSuggestion) => void;
  placeholder?: string;
  userId: string;
  searchType?: 'knowledge_cards' | 'documents' | 'all';
  showAIFeatures?: boolean;
  showSuggestions?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const AISearchBar: React.FC<AISearchBarProps> = ({
  onSearch,
  onSuggestionPress,
  placeholder = 'Ask me anything about your knowledge...',
  userId,
  searchType = 'all',
  showAIFeatures = true,
  showSuggestions = true,
  variant = 'glass',
  size = 'md',
  className,
}) => {
  // State management
  const [query, setQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchProgress, setSearchProgress] = useState<SearchProgress | null>(null);
  const [suggestions, setSuggestions] = useState<SmartSuggestion[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  // Permission system integration (Rule 11: State management)
  const { canUseFeature, useFeature, usage, isUnlimited } = useUsageLimitedFeature('ai_searches');
  const { hasPermission } = usePermissions();

  // Animation values (Rule 8)
  const searchAnimation = useSharedValue(0);
  const suggestionAnimation = useSharedValue(0);
  const progressAnimation = useSharedValue(0);

  // Update progress animation
  useEffect(() => {
    if (searchProgress) {
      progressAnimation.value = withTiming(searchProgress.progress / 100, { duration: 300 });
    }
  }, [searchProgress]);

  // Show/hide suggestions animation
  useEffect(() => {
    suggestionAnimation.value = withSpring(showDropdown ? 1 : 0);
  }, [showDropdown]);

  // Animated styles
  const searchAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: interpolate(searchAnimation.value, [0, 1], [1, 0.98]) }],
  }));

  const suggestionAnimatedStyle = useAnimatedStyle(() => ({
    opacity: suggestionAnimation.value,
    transform: [{ translateY: interpolate(suggestionAnimation.value, [0, 1], [-10, 0]) }],
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${progressAnimation.value * 100}%`,
  }));

  // Handle search execution
  const handleSearch = useCallback(async () => {
    if (!query.trim() || isSearching) return;

    // Check permission and usage limits (Rule 12: Error handling)
    if (!canUseFeature) {
      Alert.alert(
        'Search Limit Reached',
        'You have reached your daily AI search limit. Upgrade to Premium for unlimited searches!',
        [
          { text: 'Maybe Later', style: 'cancel' },
          { text: 'Upgrade Now', onPress: () => {
            // Navigate to upgrade screen - you can customize this
            console.log('Navigate to upgrade screen');
          }}
        ]
      );
      return;
    }

    // Use feature and track usage
    const success = await useFeature();
    if (!success) return;

    searchAnimation.value = withSpring(1, {}, () => {
      searchAnimation.value = withSpring(0);
    });

    setIsSearching(true);
    setShowDropdown(false);

    try {
      const searchInput: SemanticSearchInput = {
        query: query.trim(),
        context: {
          userId,
          searchType,
          limit: 20,
          includeRelated: true,
        },
        options: {
          enableSemanticSimilarity: true,
          enableIntentUnderstanding: true,
          enableSmartSuggestions: showSuggestions,
          similarityThreshold: 0.7,
        },
      };

      const results = await aiSearchService.semanticSearch(
        searchInput,
        (progress) => {
          setSearchProgress(progress);
          
          // Update suggestions from partial results
          if (progress.partialResults?.suggestions) {
            setSuggestions(progress.partialResults.suggestions);
          }
        }
      );

      // Update recent searches
      setRecentSearches(prev => {
        const updated = [query.trim(), ...prev.filter(s => s !== query.trim())].slice(0, 5);
        return updated;
      });

      // Update suggestions
      setSuggestions(results.suggestions);

      // Notify parent component
      onSearch?.(query.trim(), results);

    } catch (error) {
      console.error('Search failed:', error);
      Alert.alert(
        'Search Failed',
        'Unable to perform AI search. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSearching(false);
      setSearchProgress(null);
    }
  }, [query, userId, searchType, showSuggestions, onSearch]);

  // Handle query input changes
  const handleQueryChange = useCallback((text: string) => {
    setQuery(text);
    setShowDropdown(text.length > 0 && (suggestions.length > 0 || recentSearches.length > 0));
  }, [suggestions.length, recentSearches.length]);

  // Handle suggestion press
  const handleSuggestionPress = useCallback((suggestion: SmartSuggestion) => {
    setQuery(suggestion.text);
    setShowDropdown(false);
    onSuggestionPress?.(suggestion);
    
    // Auto-search if it's a query suggestion
    if (suggestion.type === 'query') {
      setTimeout(() => handleSearch(), 100);
    }
  }, [onSuggestionPress, handleSearch]);

  // Get suggestion icon
  const getSuggestionIcon = (type: SmartSuggestion['type']) => {
    const iconProps = { size: 16, color: '#06B6D4' };
    switch (type) {
      case 'concept': return <Lightbulb {...iconProps} />;
      case 'topic': return <Tag {...iconProps} />;
      case 'related': return <ArrowRight {...iconProps} />;
      case 'trending': return <TrendingUp {...iconProps} />;
      default: return <Search {...iconProps} />;
    }
  };

  return (
    <FeatureGate
      permission={PERMISSIONS.AI_SEARCH}
      feature="ai_searches"
      showUpgradePrompt={true}
    >
      <Box className={className}>
        <VStack space="sm">
          {/* Usage Indicator */}
          {!isUnlimited && (
            <HStack className="justify-between items-center px-2">
              <Text size="sm" className="text-white/70">
                AI Search
              </Text>
              <Badge
                action={usage.remaining > 2 ? 'success' : 'warning'}
                size="sm"
              >
                <BadgeText>
                  {usage.remaining} searches remaining
                </BadgeText>
              </Badge>
            </HStack>
          )}

          {/* Main Search Input */}
        <Animated.View style={searchAnimatedStyle}>
          <Box
            className="rounded-2xl overflow-hidden"
            style={[styles.glassCard, { backgroundColor: 'rgba(255, 255, 255, 0.1)' }]}
          >
            <LinearGradient
              colors={['rgba(16, 185, 129, 0.1)', 'rgba(6, 182, 212, 0.1)']}
              style={styles.gradientBackground}
            >
              <VStack space="md" className="p-4">
                <HStack className="items-center" space="md">
                  {/* AI Indicator */}
                  {showAIFeatures && (
                    <Box
                      className="p-2 rounded-xl"
                      style={{ backgroundColor: 'rgba(6, 182, 212, 0.2)' }}
                    >
                      <Brain size={20} color="#06B6D4" />
                    </Box>
                  )}

                  {/* Search Input */}
                  <Box className="flex-1">
                    <Input variant="outline" size={size}>
                      <InputField
                        placeholder={placeholder}
                        value={query}
                        onChangeText={handleQueryChange}
                        onSubmitEditing={handleSearch}
                        onFocus={() => setShowDropdown(query.length > 0)}
                        onBlur={() => setTimeout(() => setShowDropdown(false), 200)}
                        style={{ color: '#FFFFFF' }}
                        placeholderTextColor="rgba(255, 255, 255, 0.6)"
                      />
                    </Input>
                  </Box>

                  {/* Search Button */}
                  <Button
                    onPress={handleSearch}
                    disabled={isSearching || !query.trim()}
                    className="px-4 py-3"
                    style={styles.primaryButton}
                  >
                    <HStack className="items-center" space="sm">
                      {isSearching ? (
                        <Box className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      ) : (
                        <Search size={20} color="#FFFFFF" />
                      )}
                      {showAIFeatures && !isSearching && (
                        <Sparkles size={16} color="#FFFFFF" />
                      )}
                    </HStack>
                  </Button>
                </HStack>

                {/* Progress Bar */}
                {isSearching && searchProgress && (
                  <VStack space="xs">
                    <HStack className="items-center justify-between">
                      <Text className="text-white/80 text-xs">
                        {searchProgress.message}
                      </Text>
                      <Text className="text-white/60 text-xs font-mono">
                        {Math.round(searchProgress.progress)}%
                      </Text>
                    </HStack>
                    
                    <Box className="h-2 bg-white/20 rounded-full overflow-hidden">
                      <Animated.View
                        style={[
                          progressAnimatedStyle,
                          {
                            height: '100%',
                            borderRadius: 4,
                          },
                        ]}
                      >
                        <LinearGradient
                          colors={['#10B981', '#06B6D4', '#8B5CF6']}
                          start={{ x: 0, y: 0 }}
                          end={{ x: 1, y: 0 }}
                          style={{ flex: 1 }}
                        />
                      </Animated.View>
                    </Box>
                  </VStack>
                )}
              </VStack>
            </LinearGradient>
          </Box>
        </Animated.View>

        {/* Suggestions Dropdown */}
        {showDropdown && (suggestions.length > 0 || recentSearches.length > 0) && (
          <Animated.View style={suggestionAnimatedStyle}>
            <Box
              className="rounded-xl overflow-hidden"
              style={[styles.glassCard, { backgroundColor: 'rgba(255, 255, 255, 0.05)' }]}
            >
              <VStack space="xs" className="p-3">
                {/* Recent Searches */}
                {recentSearches.length > 0 && (
                  <VStack space="xs">
                    <HStack className="items-center" space="sm">
                      <Clock size={14} color="#FFFFFF60" />
                      <Text className="text-white/60 text-xs font-medium">Recent</Text>
                    </HStack>
                    {recentSearches.slice(0, 3).map((search, index) => (
                      <Pressable
                        key={index}
                        onPress={() => {
                          setQuery(search);
                          setShowDropdown(false);
                          setTimeout(() => handleSearch(), 100);
                        }}
                        className="p-2 rounded-lg"
                        style={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                      >
                        <Text className="text-white/80 text-sm">{search}</Text>
                      </Pressable>
                    ))}
                  </VStack>
                )}

                {/* AI Suggestions */}
                {suggestions.length > 0 && (
                  <VStack space="xs">
                    <HStack className="items-center" space="sm">
                      <Sparkles size={14} color="#06B6D4" />
                      <Text className="text-cyan-300 text-xs font-medium">AI Suggestions</Text>
                    </HStack>
                    {suggestions.slice(0, 5).map((suggestion) => (
                      <Pressable
                        key={suggestion.id}
                        onPress={() => handleSuggestionPress(suggestion)}
                        className="p-2 rounded-lg"
                        style={{ backgroundColor: 'rgba(6, 182, 212, 0.1)' }}
                      >
                        <HStack className="items-center justify-between">
                          <HStack className="items-center flex-1" space="sm">
                            {getSuggestionIcon(suggestion.type)}
                            <Text className="text-white/90 text-sm flex-1">
                              {suggestion.text}
                            </Text>
                          </HStack>
                          <Badge className="bg-cyan-600/20 border border-cyan-400/30">
                            <BadgeText className="text-cyan-300 text-xs">
                              {Math.round(suggestion.confidence * 100)}%
                            </BadgeText>
                          </Badge>
                        </HStack>
                      </Pressable>
                    ))}
                  </VStack>
                )}
              </VStack>
            </Box>
          </Animated.View>
        )}
      </VStack>
    </Box>
    </FeatureGate>
  );
};

const styles = StyleSheet.create({
  glassCard: {
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 8,
  },
  gradientBackground: {
    flex: 1,
  },
  primaryButton: {
    backgroundColor: 'rgba(16, 185, 129, 0.8)',
    borderWidth: 1,
    borderColor: 'rgba(16, 185, 129, 0.3)',
  },
});
