import React, { useState } from 'react';
import { View } from 'react-native';
import { Text } from '../ui/text';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Accordion, AccordionItem, AccordionHeader, AccordionTrigger, AccordionContent } from '../ui/accordion';
import { Badge, BadgeText } from '../ui/badge';
import { Slider, SliderTrack, SliderFilledTrack, SliderThumb } from '../ui/slider';
import { Switch } from '../ui/switch';
import { Pressable } from '../ui/pressable';
import { 
  FilterIcon, 
  ChevronDownIcon,
  ChevronUpIcon,
  XIcon,
  CalendarIcon,
  FileTextIcon,
  TagIcon,
  FolderIcon,
  StarIcon,
  ShareIcon,
  EyeIcon,
  SlidersHorizontalIcon,
  RotateCcwIcon,
  CheckCircleIcon,
  ClockIcon,
  AlertTriangleIcon,
  XCircleIcon,
  type LucideIcon
} from 'lucide-react-native';

// Type-safe icon wrapper for LucideIcon compatibility
const createIconComponent = (IconComponent: LucideIcon): React.ComponentType<{ size: number; color: string }> => {
  return ({ size, color }) => <IconComponent size={size} color={color} />;
};

interface FilterPanelProps {
  filters: DocumentFilters;
  onFiltersChange?: (filters: DocumentFilters) => void;
  onApplyFilters?: (filters: DocumentFilters) => void;
  onResetFilters?: () => void;
  availableTags?: string[];
  availableFolders?: string[];
  showApplyButton?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  className?: string;
}

interface DocumentFilters {
  // Basic Filters
  documentType: 'all' | 'document' | 'image' | 'pdf' | 'text';
  dateRange: 'all' | 'today' | 'yesterday' | 'week' | 'month' | 'year' | 'custom';
  customDateStart?: Date;
  customDateEnd?: Date;
  
  // Content Filters
  tags: string[];
  folder?: string;
  hasText: boolean;
  language?: string;
  
  // Quality Filters
  confidenceRange: [number, number]; // 0-100
  ocrStatus: 'all' | 'completed' | 'processing' | 'pending' | 'failed';
  
  // Size Filters
  sizeRange: [number, number]; // in MB
  pageCountRange?: [number, number];
  
  // Status Filters
  isFavorite: boolean;
  isShared: boolean;
  isArchived: boolean;
  
  // Sort Options
  sortBy: 'relevance' | 'date' | 'name' | 'size' | 'confidence' | 'modified';
  sortOrder: 'asc' | 'desc';
}

const defaultFilters: DocumentFilters = {
  documentType: 'all',
  dateRange: 'all',
  tags: [],
  hasText: false,
  confidenceRange: [0, 100],
  ocrStatus: 'all',
  sizeRange: [0, 100],
  isFavorite: false,
  isShared: false,
  isArchived: false,
  sortBy: 'relevance',
  sortOrder: 'desc'
};

export function FilterPanel({
  filters,
  onFiltersChange,
  onApplyFilters,
  onResetFilters,
  availableTags = [],
  availableFolders = [],
  showApplyButton = true,
  variant = 'glass',
  className
}: FilterPanelProps) {
  const [localFilters, setLocalFilters] = useState<DocumentFilters>(filters);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['basic']));

  const updateFilter = <K extends keyof DocumentFilters>(
    key: K,
    value: DocumentFilters[K]
  ) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const toggleTag = (tag: string) => {
    const newTags = localFilters.tags.includes(tag)
      ? localFilters.tags.filter(t => t !== tag)
      : [...localFilters.tags, tag];
    updateFilter('tags', newTags);
  };

  const resetFilters = () => {
    setLocalFilters(defaultFilters);
    onFiltersChange?.(defaultFilters);
    onResetFilters?.();
  };

  const applyFilters = () => {
    onApplyFilters?.(localFilters);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (localFilters.documentType !== 'all') count++;
    if (localFilters.dateRange !== 'all') count++;
    if (localFilters.tags.length > 0) count++;
    if (localFilters.folder) count++;
    if (localFilters.hasText) count++;
    if (localFilters.confidenceRange[0] > 0 || localFilters.confidenceRange[1] < 100) count++;
    if (localFilters.ocrStatus !== 'all') count++;
    if (localFilters.sizeRange[0] > 0 || localFilters.sizeRange[1] < 100) count++;
    if (localFilters.isFavorite) count++;
    if (localFilters.isShared) count++;
    if (localFilters.isArchived) count++;
    return count;
  };

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const containerClasses = {
    default: 'bg-background-0 border border-outline-100',
    glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md',
    glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md'
  };

  const activeFiltersCount = getActiveFiltersCount();

  const FilterSection = ({ 
    id, 
    title, 
    icon: Icon, 
    children 
  }: { 
    id: string; 
    title: string; 
    icon: React.ComponentType<{ size: number; color: string }>; 
    children: React.ReactNode;
  }) => {
    const isExpanded = expandedSections.has(id);
    
    return (
      <Box className={`${containerClasses[variant]} rounded-lg`}>
        <Pressable onPress={() => toggleSection(id)} variant="enhanced">
          <HStack className="justify-between items-center p-4">
            <HStack className="items-center space-x-3">
              <Icon size={20} color="#A855F7" />
              <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                {title}
              </Text>
            </HStack>
            
            {isExpanded ? (
              <ChevronUpIcon size={20} color="#9CA3AF" />
            ) : (
              <ChevronDownIcon size={20} color="#9CA3AF" />
            )}
          </HStack>
        </Pressable>
        
        {isExpanded && (
          <Box className="px-4 pb-4">
            {children}
          </Box>
        )}
      </Box>
    );
  };

  return (
    <View className={className}>
      <VStack space="lg">
        {/* Header */}
        <Box className={`${containerClasses[variant]} rounded-xl p-4`}>
          <HStack className="justify-between items-center">
            <HStack className="items-center space-x-3">
              <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyPurple/20">
                <FilterIcon size={20} color="#A855F7" />
              </Box>
              <VStack space="xs">
                <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                  Document Filters
                </Text>
                <Text color="secondary" size="sm">
                  {activeFiltersCount} active filter{activeFiltersCount !== 1 ? 's' : ''}
                </Text>
              </VStack>
            </HStack>
            
            <HStack className="space-x-2">
              <Button action="glass" variant="outline" size="sm" onPress={resetFilters}>
                <HStack className="items-center space-x-1">
                  <RotateCcwIcon size={14} color="#FFFFFF" />
                  <ButtonText>Reset</ButtonText>
                </HStack>
              </Button>
              
              {showApplyButton && (
                <Button action="candyPurple" size="sm" onPress={applyFilters}>
                  <HStack className="items-center space-x-1">
                    <CheckCircleIcon size={14} color="#FFFFFF" />
                    <ButtonText>Apply</ButtonText>
                  </HStack>
                </Button>
              )}
            </HStack>
          </HStack>
        </Box>

        {/* Basic Filters */}
        <FilterSection id="basic" title="Basic Filters" icon={createIconComponent(SlidersHorizontalIcon)}>
          <VStack space="lg">
            {/* Document Type */}
            <VStack space="sm">
              <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                Document Type:
              </Text>
              <HStack className="space-x-2 flex-wrap">
                {['all', 'document', 'image', 'pdf', 'text'].map((type) => (
                  <Pressable
                    key={type}
                    onPress={() => updateFilter('documentType', type as any)}
                    variant="enhanced"
                  >
                    <Box 
                      className={`px-3 py-2 rounded-full border ${
                        localFilters.documentType === type 
                          ? 'bg-candyPink border-candyPink' 
                          : 'bg-glass-bg-secondary border-glass-border-primary'
                      }`}
                    >
                      <Text 
                        color={localFilters.documentType === type ? "primary" : "secondary"}
                        size="sm"
                        style={{ 
                          fontWeight: localFilters.documentType === type ? '600' : 'normal',
                          textTransform: 'capitalize'
                        }}
                      >
                        {type}
                      </Text>
                    </Box>
                  </Pressable>
                ))}
              </HStack>
            </VStack>

            {/* Date Range */}
            <VStack space="sm">
              <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                Date Range:
              </Text>
              <HStack className="space-x-2 flex-wrap">
                {['all', 'today', 'yesterday', 'week', 'month', 'year'].map((range) => (
                  <Pressable
                    key={range}
                    onPress={() => updateFilter('dateRange', range as any)}
                    variant="enhanced"
                  >
                    <Box 
                      className={`px-3 py-2 rounded-full border ${
                        localFilters.dateRange === range 
                          ? 'bg-candyPurple border-candyPurple' 
                          : 'bg-glass-bg-secondary border-glass-border-primary'
                      }`}
                    >
                      <Text 
                        color={localFilters.dateRange === range ? "primary" : "secondary"}
                        size="sm"
                        style={{ 
                          fontWeight: localFilters.dateRange === range ? '600' : 'normal',
                          textTransform: 'capitalize'
                        }}
                      >
                        {range}
                      </Text>
                    </Box>
                  </Pressable>
                ))}
              </HStack>
            </VStack>
          </VStack>
        </FilterSection>

        {/* Content Filters */}
        <FilterSection id="content" title="Content Filters" icon={createIconComponent(FileTextIcon)}>
          <VStack space="lg">
            {/* Tags */}
            {availableTags.length > 0 && (
              <VStack space="sm">
                <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                  Tags ({localFilters.tags.length} selected):
                </Text>
                <HStack className="space-x-2 flex-wrap">
                  {availableTags.map((tag) => {
                    const isSelected = localFilters.tags.includes(tag);
                    return (
                      <Pressable
                        key={tag}
                        onPress={() => toggleTag(tag)}
                        variant="enhanced"
                      >
                        <Box 
                          className={`px-3 py-2 rounded-full border ${
                            isSelected 
                              ? 'bg-candyBlue border-candyBlue' 
                              : 'bg-glass-bg-secondary border-glass-border-primary'
                          }`}
                        >
                          <HStack className="items-center space-x-1">
                            <TagIcon size={12} color={isSelected ? "#FFFFFF" : "#9CA3AF"} />
                            <Text 
                              color={isSelected ? "primary" : "secondary"}
                              size="sm"
                              style={{ fontWeight: isSelected ? '600' : 'normal' }}
                            >
                              {tag}
                            </Text>
                          </HStack>
                        </Box>
                      </Pressable>
                    );
                  })}
                </HStack>
              </VStack>
            )}

            {/* Folders */}
            {availableFolders.length > 0 && (
              <VStack space="sm">
                <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                  Folder:
                </Text>
                <HStack className="space-x-2 flex-wrap">
                  <Pressable
                    onPress={() => updateFilter('folder', undefined)}
                    variant="enhanced"
                  >
                    <Box 
                      className={`px-3 py-2 rounded-full border ${
                        !localFilters.folder 
                          ? 'bg-success border-success' 
                          : 'bg-glass-bg-secondary border-glass-border-primary'
                      }`}
                    >
                      <Text 
                        color={!localFilters.folder ? "primary" : "secondary"}
                        size="sm"
                        style={{ fontWeight: !localFilters.folder ? '600' : 'normal' }}
                      >
                        All Folders
                      </Text>
                    </Box>
                  </Pressable>
                  
                  {availableFolders.map((folder) => {
                    const isSelected = localFilters.folder === folder;
                    return (
                      <Pressable
                        key={folder}
                        onPress={() => updateFilter('folder', folder)}
                        variant="enhanced"
                      >
                        <Box 
                          className={`px-3 py-2 rounded-full border ${
                            isSelected 
                              ? 'bg-candyPurple border-candyPurple' 
                              : 'bg-glass-bg-secondary border-glass-border-primary'
                          }`}
                        >
                          <HStack className="items-center space-x-1">
                            <FolderIcon size={12} color={isSelected ? "#FFFFFF" : "#9CA3AF"} />
                            <Text 
                              color={isSelected ? "primary" : "secondary"}
                              size="sm"
                              style={{ fontWeight: isSelected ? '600' : 'normal' }}
                            >
                              {folder}
                            </Text>
                          </HStack>
                        </Box>
                      </Pressable>
                    );
                  })}
                </HStack>
              </VStack>
            )}

            {/* Has Text Toggle */}
            <HStack className="justify-between items-center">
              <HStack className="items-center space-x-2">
                <EyeIcon size={16} color="#9CA3AF" />
                <Text color="primary" size="sm">Documents with recognized text</Text>
              </HStack>
              <Switch
                value={localFilters.hasText}
                onValueChange={(value) => updateFilter('hasText', value)}
                size="md"
              />
            </HStack>
          </VStack>
        </FilterSection>

        {/* Quality Filters */}
        <FilterSection id="quality" title="Quality Filters" icon={createIconComponent(EyeIcon)}>
          <VStack space="lg">
            {/* Confidence Range */}
            <VStack space="sm">
              <HStack className="justify-between">
                <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                  OCR Confidence:
                </Text>
                <Text color="candyPink" size="sm" style={{ fontWeight: '600' }}>
                  {localFilters.confidenceRange[0]}% - {localFilters.confidenceRange[1]}%
                </Text>
              </HStack>
              
              <VStack space="md">
                <VStack space="xs">
                  <Text color="secondary" size="xs">Minimum Confidence:</Text>
                  <Slider
                    value={localFilters.confidenceRange[0]}
                    onChange={(value) => updateFilter('confidenceRange', [value, localFilters.confidenceRange[1]])}
                    minValue={0}
                    maxValue={100}
                    size="sm"
                  >
                    <SliderTrack>
                      <SliderFilledTrack color="candyPink" />
                    </SliderTrack>
                    <SliderThumb color="candyPink" />
                  </Slider>
                </VStack>
                
                <VStack space="xs">
                  <Text color="secondary" size="xs">Maximum Confidence:</Text>
                  <Slider
                    value={localFilters.confidenceRange[1]}
                    onChange={(value) => updateFilter('confidenceRange', [localFilters.confidenceRange[0], value])}
                    minValue={0}
                    maxValue={100}
                    size="sm"
                  >
                    <SliderTrack>
                      <SliderFilledTrack color="candyPink" />
                    </SliderTrack>
                    <SliderThumb color="candyPink" />
                  </Slider>
                </VStack>
              </VStack>
            </VStack>

            {/* OCR Status */}
            <VStack space="sm">
              <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                OCR Status:
              </Text>
              <HStack className="space-x-2 flex-wrap">
                {[
                  { key: 'all', label: 'All', icon: SlidersHorizontalIcon },
                  { key: 'completed', label: 'Completed', icon: CheckCircleIcon },
                  { key: 'processing', label: 'Processing', icon: ClockIcon },
                  { key: 'pending', label: 'Pending', icon: AlertTriangleIcon },
                  { key: 'failed', label: 'Failed', icon: XCircleIcon }
                ].map((status) => {
                  const isSelected = localFilters.ocrStatus === status.key;
                  const StatusIcon = status.icon;
                  
                  return (
                    <Pressable
                      key={status.key}
                      onPress={() => updateFilter('ocrStatus', status.key as any)}
                      variant="enhanced"
                    >
                      <Box 
                        className={`px-3 py-2 rounded-full border ${
                          isSelected 
                            ? 'bg-candyBlue border-candyBlue' 
                            : 'bg-glass-bg-secondary border-glass-border-primary'
                        }`}
                      >
                        <HStack className="items-center space-x-1">
                          <StatusIcon size={12} color={isSelected ? "#FFFFFF" : "#9CA3AF"} />
                          <Text 
                            color={isSelected ? "primary" : "secondary"}
                            size="sm"
                            style={{ fontWeight: isSelected ? '600' : 'normal' }}
                          >
                            {status.label}
                          </Text>
                        </HStack>
                      </Box>
                    </Pressable>
                  );
                })}
              </HStack>
            </VStack>
          </VStack>
        </FilterSection>

        {/* Status Filters */}
        <FilterSection id="status" title="Status Filters" icon={createIconComponent(StarIcon)}>
          <VStack space="md">
            <HStack className="justify-between items-center">
              <HStack className="items-center space-x-2">
                <StarIcon size={16} color="#F59E0B" />
                <Text color="primary" size="sm">Favorites Only</Text>
              </HStack>
              <Switch
                value={localFilters.isFavorite}
                onValueChange={(value) => updateFilter('isFavorite', value)}
                size="md"
              />
            </HStack>
            
            <HStack className="justify-between items-center">
              <HStack className="items-center space-x-2">
                <ShareIcon size={16} color="#3B82F6" />
                <Text color="primary" size="sm">Shared Documents</Text>
              </HStack>
              <Switch
                value={localFilters.isShared}
                onValueChange={(value) => updateFilter('isShared', value)}
                size="md"
              />
            </HStack>
            
            <HStack className="justify-between items-center">
              <HStack className="items-center space-x-2">
                <XIcon size={16} color="#9CA3AF" />
                <Text color="primary" size="sm">Include Archived</Text>
              </HStack>
              <Switch
                value={localFilters.isArchived}
                onValueChange={(value) => updateFilter('isArchived', value)}
                size="md"
              />
            </HStack>
          </VStack>
        </FilterSection>

        {/* Sort Options */}
        <FilterSection id="sort" title="Sort Options" icon={createIconComponent(SlidersHorizontalIcon)}>
          <VStack space="lg">
            <VStack space="sm">
              <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                Sort By:
              </Text>
              <HStack className="space-x-2 flex-wrap">
                {['relevance', 'date', 'name', 'size', 'confidence', 'modified'].map((sort) => (
                  <Pressable
                    key={sort}
                    onPress={() => updateFilter('sortBy', sort as any)}
                    variant="enhanced"
                  >
                    <Box 
                      className={`px-3 py-2 rounded-full border ${
                        localFilters.sortBy === sort 
                          ? 'bg-success border-success' 
                          : 'bg-glass-bg-secondary border-glass-border-primary'
                      }`}
                    >
                      <Text 
                        color={localFilters.sortBy === sort ? "primary" : "secondary"}
                        size="sm"
                        style={{ 
                          fontWeight: localFilters.sortBy === sort ? '600' : 'normal',
                          textTransform: 'capitalize'
                        }}
                      >
                        {sort}
                      </Text>
                    </Box>
                  </Pressable>
                ))}
              </HStack>
            </VStack>

            <VStack space="sm">
              <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                Sort Order:
              </Text>
              <HStack className="space-x-2">
                {[
                  { key: 'desc', label: 'Descending (Z-A, Newest)' },
                  { key: 'asc', label: 'Ascending (A-Z, Oldest)' }
                ].map((order) => (
                  <Pressable
                    key={order.key}
                    onPress={() => updateFilter('sortOrder', order.key as any)}
                    variant="enhanced"
                  >
                    <Box 
                      className={`px-3 py-2 rounded-full border ${
                        localFilters.sortOrder === order.key 
                          ? 'bg-warning border-warning' 
                          : 'bg-glass-bg-secondary border-glass-border-primary'
                      }`}
                    >
                      <Text 
                        color={localFilters.sortOrder === order.key ? "primary" : "secondary"}
                        size="sm"
                        style={{ fontWeight: localFilters.sortOrder === order.key ? '600' : 'normal' }}
                      >
                        {order.label}
                      </Text>
                    </Box>
                  </Pressable>
                ))}
              </HStack>
            </VStack>
          </VStack>
        </FilterSection>

        {/* Active Filters Summary */}
        {activeFiltersCount > 0 && (
          <Box className={`${containerClasses[variant]} rounded-xl p-4`}>
            <VStack space="md">
              <HStack className="justify-between items-center">
                <Text color="primary" size="md" style={{ fontWeight: 'bold' }}>
                  Active Filters ({activeFiltersCount})
                </Text>
                <Button action="glass" variant="outline" size="xs" onPress={resetFilters}>
                  <ButtonText>Clear All</ButtonText>
                </Button>
              </HStack>
              
              <HStack className="space-x-2 flex-wrap">
                {localFilters.documentType !== 'all' && (
                  <Badge action="candyPink" variant="solid" size="sm">
                    <BadgeText>Type: {localFilters.documentType}</BadgeText>
                  </Badge>
                )}
                
                {localFilters.dateRange !== 'all' && (
                  <Badge action="candyPurple" variant="solid" size="sm">
                    <BadgeText>Date: {localFilters.dateRange}</BadgeText>
                  </Badge>
                )}
                
                {localFilters.tags.length > 0 && (
                  <Badge action="candyBlue" variant="solid" size="sm">
                    <BadgeText>{localFilters.tags.length} tag{localFilters.tags.length !== 1 ? 's' : ''}</BadgeText>
                  </Badge>
                )}
                
                {localFilters.folder && (
                  <Badge action="success" variant="solid" size="sm">
                    <BadgeText>Folder: {localFilters.folder}</BadgeText>
                  </Badge>
                )}
                
                {localFilters.isFavorite && (
                  <Badge action="warning" variant="solid" size="sm">
                    <BadgeText>Favorites</BadgeText>
                  </Badge>
                )}
                
                {localFilters.isShared && (
                  <Badge action="info" variant="solid" size="sm">
                    <BadgeText>Shared</BadgeText>
                  </Badge>
                )}
              </HStack>
            </VStack>
          </Box>
        )}
      </VStack>
    </View>
  );
}

export default FilterPanel;