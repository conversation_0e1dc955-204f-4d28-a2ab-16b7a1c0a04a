import React, { useState, useEffect } from 'react';
import { View, Animated } from 'react-native';
import { Text } from '../ui/text';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Progress, ProgressFilledTrack } from '../ui/progress';
import { Pressable } from '../ui/pressable';
import { 
  BrainIcon, 
  EyeIcon, 
  SparklesIcon,
  CheckCircleIcon,
  AlertTriangleIcon,
  XCircleIcon,
  ClockIcon,
  ZapIcon,
  PauseIcon,
  PlayIcon,
  SquareIcon
} from 'lucide-react-native';

interface AIProcessingCardProps {
  operation: AIOperation;
  onPause?: () => void;
  onResume?: () => void;
  onCancel?: () => void;
  onViewDetails?: () => void;
  showControls?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

interface AIOperation {
  id: string;
  type: 'ocr' | 'enhancement' | 'analysis' | 'translation' | 'summarization';
  title: string;
  description: string;
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled';
  progress: number; // 0-100
  estimatedTimeRemaining?: number; // in seconds
  startTime: Date;
  completedTime?: Date;
  confidence?: number; // 0-100
  details?: {
    processedItems?: number;
    totalItems?: number;
    currentStep?: string;
    errorMessage?: string;
  };
}

const operationIcons = {
  ocr: EyeIcon,
  enhancement: SparklesIcon,
  analysis: BrainIcon,
  translation: ZapIcon,
  summarization: ClockIcon
};

const operationColors = {
  ocr: '#3B82F6',
  enhancement: '#A855F7',
  analysis: '#FF6B9D',
  translation: '#10B981',
  summarization: '#F59E0B'
};

export function AIProcessingCard({
  operation,
  onPause,
  onResume,
  onCancel,
  onViewDetails,
  showControls = true,
  variant = 'glass',
  size = 'md',
  className
}: AIProcessingCardProps) {
  const [animatedProgress] = useState(new Animated.Value(operation.progress));
  const [timeElapsed, setTimeElapsed] = useState(0);

  const OperationIcon = operationIcons[operation.type];
  const operationColor = operationColors[operation.type];

  useEffect(() => {
    Animated.timing(animatedProgress, {
      toValue: operation.progress,
      duration: 500,
      useNativeDriver: false,
    }).start();
  }, [operation.progress]);

  useEffect(() => {
    if (operation.status === 'running') {
      const interval = setInterval(() => {
        setTimeElapsed(prev => prev + 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [operation.status]);

  const getStatusColor = (status: AIOperation['status']) => {
    switch (status) {
      case 'completed': return '#10B981';
      case 'failed': return '#EF4444';
      case 'cancelled': return '#6B7280';
      case 'paused': return '#F59E0B';
      case 'running': return operationColor;
      default: return '#9CA3AF';
    }
  };

  const getStatusIcon = (status: AIOperation['status']) => {
    switch (status) {
      case 'completed': return CheckCircleIcon;
      case 'failed': return XCircleIcon;
      case 'cancelled': return XCircleIcon;
      case 'paused': return PauseIcon;
      case 'running': return OperationIcon;
      default: return ClockIcon;
    }
  };

  const getStatusText = (status: AIOperation['status']) => {
    switch (status) {
      case 'pending': return 'Pending';
      case 'running': return 'Processing';
      case 'paused': return 'Paused';
      case 'completed': return 'Completed';
      case 'failed': return 'Failed';
      case 'cancelled': return 'Cancelled';
      default: return 'Unknown';
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatEstimatedTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const mins = Math.floor(seconds / 60);
    const remainingSecs = seconds % 60;
    return remainingSecs > 0 ? `${mins}m ${remainingSecs}s` : `${mins}m`;
  };

  const statusColor = getStatusColor(operation.status);
  const StatusIcon = getStatusIcon(operation.status);
  const statusText = getStatusText(operation.status);

  const containerClasses = {
    default: 'bg-background-0 border border-outline-100',
    glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md',
    glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md'
  };

  const sizeConfig = {
    sm: { padding: 'p-3', iconSize: 20, spacing: 'sm' as const },
    md: { padding: 'p-4', iconSize: 24, spacing: 'md' as const },
    lg: { padding: 'p-6', iconSize: 28, spacing: 'lg' as const }
  };

  const config = sizeConfig[size];

  return (
    <Pressable 
      variant={variant === 'default' ? 'default' : 'glass'} 
      onPress={onViewDetails}
      className={className}
    >
      <Box 
        className={`${containerClasses[variant]} rounded-xl ${config.padding}`}
      >
        <VStack space={config.spacing}>
          {/* Header */}
          <HStack className="justify-between items-start">
            <HStack className="items-center space-x-3 flex-1">
              <Box 
                className="justify-center items-center rounded-full"
                style={{
                  width: config.iconSize + 16,
                  height: config.iconSize + 16,
                  backgroundColor: `${operationColor}20`
                }}
              >
                <OperationIcon size={config.iconSize} color={operationColor} />
              </Box>
              
              <VStack space="xs" className="flex-1">
                <Text color="primary" size={size === 'sm' ? 'sm' : 'md'} style={{ fontWeight: 'bold' }}>
                  {operation.title}
                </Text>
                <Text color="secondary" size={size === 'lg' ? 'sm' : 'xs'}>
                  {operation.description}
                </Text>
              </VStack>
            </HStack>
            
            {/* Status Badge */}
            <Box 
              className="px-2 py-1 rounded-full border"
              style={{
                backgroundColor: `${statusColor}20`,
                borderColor: `${statusColor}40`
              }}
            >
              <HStack className="items-center space-x-1">
                <StatusIcon size={12} color={statusColor} />
                <Text 
                  size="xs" 
                  style={{ fontWeight: '600', color: statusColor }}
                >
                  {statusText}
                </Text>
              </HStack>
            </Box>
          </HStack>

          {/* Progress Section */}
          {(operation.status === 'running' || operation.status === 'paused') && (
            <VStack space="sm">
              <HStack className="justify-between items-center">
                <Text color="tertiary" size="xs">
                  Progress: {operation.progress}%
                </Text>
                {operation.estimatedTimeRemaining && (
                  <Text color="tertiary" size="xs">
                    ~{formatEstimatedTime(operation.estimatedTimeRemaining)} remaining
                  </Text>
                )}
              </HStack>
              
              <Progress value={operation.progress} size="sm">
                <ProgressFilledTrack 
                  color={operation.type === 'ocr' ? 'candyBlue' : 
                         operation.type === 'enhancement' ? 'candyPurple' : 
                         'candyPink'} 
                />
              </Progress>
              
              {operation.details?.currentStep && (
                <Text color="secondary" size="xs">
                  {operation.details.currentStep}
                </Text>
              )}
            </VStack>
          )}

          {/* Details Section */}
          <VStack space="xs">
            <HStack className="justify-between">
              <Text color="tertiary" size="xs">Started:</Text>
              <Text color="secondary" size="xs">
                {operation.startTime.toLocaleTimeString()}
              </Text>
            </HStack>
            
            {operation.status === 'running' && (
              <HStack className="justify-between">
                <Text color="tertiary" size="xs">Elapsed:</Text>
                <Text color="secondary" size="xs">
                  {formatTime(timeElapsed)}
                </Text>
              </HStack>
            )}
            
            {operation.completedTime && (
              <HStack className="justify-between">
                <Text color="tertiary" size="xs">Completed:</Text>
                <Text color="candyPink" size="xs">
                  {operation.completedTime.toLocaleTimeString()}
                </Text>
              </HStack>
            )}
            
            {operation.confidence !== undefined && (
              <HStack className="justify-between">
                <Text color="tertiary" size="xs">Confidence:</Text>
                <Text 
                  size="xs" 
                  style={{ 
                    fontWeight: '600',
                    color: operation.confidence >= 80 ? '#10B981' : 
                           operation.confidence >= 60 ? '#F59E0B' : '#EF4444'
                  }}
                >
                  {operation.confidence}%
                </Text>
              </HStack>
            )}
            
            {operation.details?.processedItems !== undefined && operation.details?.totalItems && (
              <HStack className="justify-between">
                <Text color="tertiary" size="xs">Items:</Text>
                <Text color="secondary" size="xs">
                  {operation.details.processedItems} / {operation.details.totalItems}
                </Text>
              </HStack>
            )}
          </VStack>

          {/* Error Message */}
          {operation.status === 'failed' && operation.details?.errorMessage && (
            <Box className="bg-error/10 border border-error/20 rounded-lg p-3">
              <HStack className="items-start space-x-2">
                <AlertTriangleIcon size={16} color="#EF4444" />
                <Text color="candyPink" size="xs" style={{ flex: 1 }}>
                  {operation.details.errorMessage}
                </Text>
              </HStack>
            </Box>
          )}

          {/* Controls */}
          {showControls && (operation.status === 'running' || operation.status === 'paused') && (
            <HStack className="justify-end space-x-2">
              {operation.status === 'running' && onPause && (
                <Button action="glass" variant="outline" size="xs" onPress={onPause}>
                  <HStack className="items-center space-x-1">
                    <PauseIcon size={12} color="#FFFFFF" />
                    <ButtonText>Pause</ButtonText>
                  </HStack>
                </Button>
              )}
              
              {operation.status === 'paused' && onResume && (
                <Button action="candyPurple" size="xs" onPress={onResume}>
                  <HStack className="items-center space-x-1">
                    <PlayIcon size={12} color="#FFFFFF" />
                    <ButtonText>Resume</ButtonText>
                  </HStack>
                </Button>
              )}
              
              {onCancel && (
                <Button action="negative" variant="outline" size="xs" onPress={onCancel}>
                  <HStack className="items-center space-x-1">
                    <SquareIcon size={12} color="#EF4444" />
                    <ButtonText>Cancel</ButtonText>
                  </HStack>
                </Button>
              )}
            </HStack>
          )}

          {/* Completed Actions */}
          {operation.status === 'completed' && onViewDetails && (
            <Button action="candyPink" size="sm" onPress={onViewDetails}>
              <ButtonText>View Results</ButtonText>
            </Button>
          )}
        </VStack>
      </Box>
    </Pressable>
  );
}

export default AIProcessingCard;