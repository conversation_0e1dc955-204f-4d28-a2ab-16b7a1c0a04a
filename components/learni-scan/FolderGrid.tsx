import React, { useState } from 'react';
import { View, ScrollView } from 'react-native';
import { Text } from '../ui/text';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Grid, GridItem } from '../ui/grid';
import { Badge, BadgeText } from '../ui/badge';
import { Pressable } from '../ui/pressable';
import { 
  FolderIcon, 
  FolderPlusIcon,
  MoreVerticalIcon,
  EditIcon,
  TrashIcon,
  ShareIcon,
  ArchiveIcon,
  StarIcon,
  ClockIcon,
  FileTextIcon,
  TagIcon,
  TrendingUpIcon
} from 'lucide-react-native';

interface FolderGridProps {
  folders: FolderInfo[];
  onFolderPress?: (folder: FolderInfo) => void;
  onCreateFolder?: () => void;
  onEditFolder?: (folder: FolderInfo) => void;
  onDeleteFolder?: (folder: FolderInfo) => void;
  onShareFolder?: (folder: FolderInfo) => void;
  onArchiveFolder?: (folder: FolderInfo) => void;
  showActions?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  columns?: number;
  className?: string;
}

interface FolderInfo {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon?: React.ComponentType<{ size: number; color: string }>;
  documentCount: number;
  totalSize: number; // in bytes
  createdAt: Date;
  modifiedAt: Date;
  tags: string[];
  isArchived: boolean;
  isFavorite: boolean;
  isShared: boolean;
  recentActivity?: {
    type: 'added' | 'modified' | 'shared';
    count: number;
    timestamp: Date;
  };
  metadata?: {
    ocrCompleted: number;
    averageConfidence: number;
    languages: string[];
  };
}

const folderColors = [
  { name: 'Candy Pink', value: '#FF6B9D', bg: 'rgba(255, 107, 157, 0.1)' },
  { name: 'Candy Purple', value: '#A855F7', bg: 'rgba(168, 85, 247, 0.1)' },
  { name: 'Candy Blue', value: '#3B82F6', bg: 'rgba(59, 130, 246, 0.1)' },
  { name: 'Success Green', value: '#10B981', bg: 'rgba(16, 185, 129, 0.1)' },
  { name: 'Warning Orange', value: '#F59E0B', bg: 'rgba(245, 158, 11, 0.1)' },
  { name: 'Error Red', value: '#EF4444', bg: 'rgba(239, 68, 68, 0.1)' }
];

export function FolderGrid({
  folders,
  onFolderPress,
  onCreateFolder,
  onEditFolder,
  onDeleteFolder,
  onShareFolder,
  onArchiveFolder,
  showActions = true,
  variant = 'glass',
  columns = 2,
  className
}: FolderGridProps) {
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  const getActivityText = (activity: FolderInfo['recentActivity']) => {
    if (!activity) return null;
    
    switch (activity.type) {
      case 'added':
        return `+${activity.count} documents`;
      case 'modified':
        return `${activity.count} updated`;
      case 'shared':
        return `Shared ${activity.count} items`;
      default:
        return null;
    }
  };

  const getActivityColor = (activity: FolderInfo['recentActivity']) => {
    if (!activity) return '#9CA3AF';
    
    switch (activity.type) {
      case 'added': return '#10B981';
      case 'modified': return '#F59E0B';
      case 'shared': return '#3B82F6';
      default: return '#9CA3AF';
    }
  };

  const containerClasses = {
    default: 'bg-background-0 border border-outline-100',
    glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md',
    glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md'
  };

  const FolderCard = ({ folder }: { folder: FolderInfo }) => {
    const FolderIconComponent = folder.icon || FolderIcon;
    const isSelected = selectedFolder === folder.id;
    
    return (
      <Pressable 
        variant={variant === 'default' ? 'default' : 'glass'} 
        onPress={() => onFolderPress?.(folder)}
        onLongPress={() => setSelectedFolder(isSelected ? null : folder.id)}
      >
        <Box 
          className={`${containerClasses[variant]} rounded-xl p-4 ${
            isSelected ? 'border-candyPink' : ''
          }`}
          style={{
            backgroundColor: isSelected 
              ? 'rgba(255, 107, 157, 0.1)' 
              : variant === 'glass' 
                ? undefined 
                : folder.color + '10'
          }}
        >
          <VStack space="md">
            {/* Header */}
            <HStack className="justify-between items-start">
              <Box 
                className="w-12 h-12 justify-center items-center rounded-xl"
                style={{ backgroundColor: folder.color + '20' }}
              >
                <FolderIconComponent size={24} color={folder.color} />
              </Box>
              
              <HStack className="items-center space-x-1">
                {folder.isFavorite && (
                  <StarIcon size={14} color="#F59E0B" fill="#F59E0B" />
                )}
                {folder.isShared && (
                  <ShareIcon size={14} color="#3B82F6" />
                )}
                {folder.isArchived && (
                  <ArchiveIcon size={14} color="#9CA3AF" />
                )}
                
                {showActions && (
                  <Pressable onPress={() => setSelectedFolder(isSelected ? null : folder.id)}>
                    <MoreVerticalIcon size={16} color="#9CA3AF" />
                  </Pressable>
                )}
              </HStack>
            </HStack>

            {/* Folder Info */}
            <VStack space="xs">
              <Text color="primary" size="md" style={{ fontWeight: 'bold' }} numberOfLines={1}>
                {folder.name}
              </Text>
              
              {folder.description && (
                <Text color="secondary" size="sm" numberOfLines={2}>
                  {folder.description}
                </Text>
              )}
            </VStack>

            {/* Statistics */}
            <VStack space="sm">
              <HStack className="justify-between">
                <Text color="tertiary" size="xs">Documents:</Text>
                <Text color="primary" size="xs" style={{ fontWeight: '600' }}>
                  {folder.documentCount}
                </Text>
              </HStack>
              
              <HStack className="justify-between">
                <Text color="tertiary" size="xs">Size:</Text>
                <Text color="secondary" size="xs">
                  {formatFileSize(folder.totalSize)}
                </Text>
              </HStack>
              
              {folder.metadata?.averageConfidence && (
                <HStack className="justify-between">
                  <Text color="tertiary" size="xs">Avg Quality:</Text>
                  <Text 
                    size="xs" 
                    style={{ 
                      fontWeight: '600',
                      color: folder.metadata.averageConfidence >= 80 ? '#10B981' : 
                             folder.metadata.averageConfidence >= 60 ? '#F59E0B' : '#EF4444'
                    }}
                  >
                    {folder.metadata.averageConfidence}%
                  </Text>
                </HStack>
              )}
            </VStack>

            {/* Recent Activity */}
            {folder.recentActivity && (
              <Box 
                className="p-2 rounded-lg border"
                style={{
                  backgroundColor: getActivityColor(folder.recentActivity) + '10',
                  borderColor: getActivityColor(folder.recentActivity) + '20'
                }}
              >
                <HStack className="items-center space-x-2">
                  <TrendingUpIcon 
                    size={12} 
                    color={getActivityColor(folder.recentActivity)} 
                  />
                  <Text 
                    size="xs" 
                    style={{ 
                      color: getActivityColor(folder.recentActivity),
                      fontWeight: '600'
                    }}
                  >
                    {getActivityText(folder.recentActivity)}
                  </Text>
                </HStack>
              </Box>
            )}

            {/* Tags */}
            {folder.tags.length > 0 && (
              <HStack className="space-x-1 flex-wrap">
                {folder.tags.slice(0, 2).map((tag, index) => (
                  <Badge key={index} action="muted" variant="outline" size="sm">
                    <BadgeText>{tag}</BadgeText>
                  </Badge>
                ))}
                {folder.tags.length > 2 && (
                  <Text color="tertiary" size="xs">
                    +{folder.tags.length - 2}
                  </Text>
                )}
              </HStack>
            )}

            {/* Last Modified */}
            <HStack className="items-center space-x-1">
              <ClockIcon size={10} color="#9CA3AF" />
              <Text color="tertiary" size="xs">
                {formatDate(folder.modifiedAt)}
              </Text>
            </HStack>
          </VStack>

          {/* Action Menu */}
          {isSelected && showActions && (
            <Box className="absolute top-2 right-2 bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-lg p-2 z-10">
              <VStack space="xs">
                <Pressable onPress={() => { onEditFolder?.(folder); setSelectedFolder(null); }}>
                  <HStack className="items-center space-x-2 p-2 rounded">
                    <EditIcon size={14} color="#A855F7" />
                    <Text color="primary" size="xs">Edit</Text>
                  </HStack>
                </Pressable>
                
                <Pressable onPress={() => { onShareFolder?.(folder); setSelectedFolder(null); }}>
                  <HStack className="items-center space-x-2 p-2 rounded">
                    <ShareIcon size={14} color="#3B82F6" />
                    <Text color="primary" size="xs">Share</Text>
                  </HStack>
                </Pressable>
                
                <Pressable onPress={() => { onArchiveFolder?.(folder); setSelectedFolder(null); }}>
                  <HStack className="items-center space-x-2 p-2 rounded">
                    <ArchiveIcon size={14} color="#F59E0B" />
                    <Text color="primary" size="xs">
                      {folder.isArchived ? 'Unarchive' : 'Archive'}
                    </Text>
                  </HStack>
                </Pressable>
                
                <Pressable onPress={() => { onDeleteFolder?.(folder); setSelectedFolder(null); }}>
                  <HStack className="items-center space-x-2 p-2 rounded">
                    <TrashIcon size={14} color="#EF4444" />
                    <Text color="candyPink" size="xs">Delete</Text>
                  </HStack>
                </Pressable>
              </VStack>
            </Box>
          )}
        </Box>
      </Pressable>
    );
  };

  return (
    <View className={className}>
      <VStack space="lg">
        {/* Header */}
        <HStack className="justify-between items-center">
          <VStack space="xs">
            <Text color="primary" size="xl" style={{ fontWeight: 'bold' }}>
              Document Folders
            </Text>
            <Text color="secondary" size="sm">
              {folders.length} folders • {folders.reduce((sum, f) => sum + f.documentCount, 0)} documents
            </Text>
          </VStack>
          
          {onCreateFolder && (
            <Button action="candyPink" onPress={onCreateFolder}>
              <HStack className="items-center space-x-2">
                <FolderPlusIcon size={16} color="#FFFFFF" />
                <ButtonText>New Folder</ButtonText>
              </HStack>
            </Button>
          )}
        </HStack>

        {/* Folder Grid */}
        <Grid variant={variant} numColumns={columns} gap={16}>
          {folders.map((folder) => (
            <GridItem key={folder.id} colSpan={1}>
              <FolderCard folder={folder} />
            </GridItem>
          ))}
          
          {/* Create Folder Card */}
          {onCreateFolder && (
            <GridItem colSpan={1}>
              <Pressable onPress={onCreateFolder} variant="enhanced">
                <Box 
                  className={`${containerClasses[variant]} rounded-xl p-4 border-dashed`}
                  style={{ borderColor: '#9CA3AF' }}
                >
                  <VStack space="md" style={{ alignItems: 'center', minHeight: 200, justifyContent: 'center' }}>
                    <Box className="w-12 h-12 justify-center items-center rounded-xl bg-glass-bg-secondary">
                      <FolderPlusIcon size={24} color="#9CA3AF" />
                    </Box>
                    
                    <VStack space="xs" style={{ alignItems: 'center' }}>
                      <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                        Create Folder
                      </Text>
                      <Text color="secondary" size="sm" style={{ textAlign: 'center' }}>
                        Organize your documents into folders
                      </Text>
                    </VStack>
                  </VStack>
                </Box>
              </Pressable>
            </GridItem>
          )}
        </Grid>

        {/* Summary Statistics */}
        <Box className={`${containerClasses[variant]} rounded-xl p-4`}>
          <VStack space="md">
            <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
              Folder Statistics:
            </Text>
            
            <Grid numColumns={2} gap={12}>
              <GridItem colSpan={1}>
                <VStack space="xs" style={{ alignItems: 'center' }}>
                  <Text color="candyPink" size="xl" style={{ fontWeight: 'bold' }}>
                    {folders.reduce((sum, f) => sum + f.documentCount, 0)}
                  </Text>
                  <Text color="secondary" size="sm">Total Documents</Text>
                </VStack>
              </GridItem>
              
              <GridItem colSpan={1}>
                <VStack space="xs" style={{ alignItems: 'center' }}>
                  <Text color="candyPurple" size="xl" style={{ fontWeight: 'bold' }}>
                    {formatFileSize(folders.reduce((sum, f) => sum + f.totalSize, 0))}
                  </Text>
                  <Text color="secondary" size="sm">Total Size</Text>
                </VStack>
              </GridItem>
              
              <GridItem colSpan={1}>
                <VStack space="xs" style={{ alignItems: 'center' }}>
                  <Text color="candyBlue" size="xl" style={{ fontWeight: 'bold' }}>
                    {folders.filter(f => f.isFavorite).length}
                  </Text>
                  <Text color="secondary" size="sm">Favorites</Text>
                </VStack>
              </GridItem>
              
              <GridItem colSpan={1}>
                <VStack space="xs" style={{ alignItems: 'center' }}>
                  <Text color="candyPink" size="xl" style={{ fontWeight: 'bold' }}>
                    {folders.filter(f => f.isShared).length}
                  </Text>
                  <Text color="secondary" size="sm">Shared</Text>
                </VStack>
              </GridItem>
            </Grid>
          </VStack>
        </Box>

        {/* Color Legend */}
        <Box className={`${containerClasses[variant]} rounded-xl p-4`}>
          <VStack space="md">
            <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
              Folder Colors:
            </Text>
            
            <HStack className="space-x-2 flex-wrap">
              {folderColors.map((color, index) => (
                <HStack key={index} className="items-center space-x-2">
                  <Box 
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: color.value }}
                  />
                  <Text color="secondary" size="xs">
                    {color.name}
                  </Text>
                </HStack>
              ))}
            </HStack>
          </VStack>
        </Box>
      </VStack>
    </View>
  );
}

export default FolderGrid;