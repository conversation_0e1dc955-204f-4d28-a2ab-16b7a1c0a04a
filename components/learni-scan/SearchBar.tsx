import React, { useState, useEffect } from 'react';
import { View } from 'react-native';
import { Text } from '../ui/text';
import { Input, InputField } from '../ui/input';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Badge, BadgeText } from '../ui/badge';
import { Pressable } from '../ui/pressable';
import { 
  SearchIcon, 
  FilterIcon,
  XIcon,
  ClockIcon,
  TrendingUpIcon,
  FileTextIcon,
  TagIcon,
  CalendarIcon,
  FolderIcon,
  StarIcon,
  SlidersHorizontalIcon
} from 'lucide-react-native';

interface SearchBarProps {
  onSearch?: (query: string, filters: SearchFilters) => void;
  onFilterChange?: (filters: SearchFilters) => void;
  placeholder?: string;
  recentSearches?: string[];
  suggestions?: SearchSuggestion[];
  showFilters?: boolean;
  showRecentSearches?: boolean;
  showSuggestions?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

interface SearchFilters {
  documentType?: 'all' | 'document' | 'image' | 'pdf' | 'text';
  dateRange?: 'all' | 'today' | 'week' | 'month' | 'year';
  folder?: string;
  tags?: string[];
  confidence?: 'all' | 'high' | 'medium' | 'low';
  ocrStatus?: 'all' | 'completed' | 'processing' | 'pending' | 'failed';
  isFavorite?: boolean;
  isShared?: boolean;
  sortBy?: 'relevance' | 'date' | 'name' | 'size' | 'confidence';
  sortOrder?: 'asc' | 'desc';
}

interface SearchSuggestion {
  id: string;
  text: string;
  type: 'query' | 'tag' | 'folder' | 'document';
  icon?: React.ComponentType<{ size: number; color: string }>;
  count?: number;
}

const defaultFilters: SearchFilters = {
  documentType: 'all',
  dateRange: 'all',
  confidence: 'all',
  ocrStatus: 'all',
  isFavorite: false,
  isShared: false,
  sortBy: 'relevance',
  sortOrder: 'desc'
};

export function SearchBar({
  onSearch,
  onFilterChange,
  placeholder = 'Search documents, text, or tags...',
  recentSearches = [],
  suggestions = [],
  showFilters = true,
  showRecentSearches = true,
  showSuggestions = true,
  variant = 'glass',
  size = 'md',
  className
}: SearchBarProps) {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>(defaultFilters);
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  useEffect(() => {
    // Count active filters
    let count = 0;
    if (filters.documentType !== 'all') count++;
    if (filters.dateRange !== 'all') count++;
    if (filters.folder) count++;
    if (filters.tags && filters.tags.length > 0) count++;
    if (filters.confidence !== 'all') count++;
    if (filters.ocrStatus !== 'all') count++;
    if (filters.isFavorite) count++;
    if (filters.isShared) count++;
    
    setActiveFiltersCount(count);
    onFilterChange?.(filters);
  }, [filters, onFilterChange]);

  const handleSearch = () => {
    if (query.trim()) {
      onSearch?.(query.trim(), filters);
      setShowDropdown(false);
    }
  };

  const handleQueryChange = (text: string) => {
    setQuery(text);
    setShowDropdown(text.length > 0 && (showRecentSearches || showSuggestions));
  };

  const handleSuggestionPress = (suggestion: SearchSuggestion) => {
    if (suggestion.type === 'query') {
      setQuery(suggestion.text);
      handleSearch();
    } else if (suggestion.type === 'tag') {
      const newTags = [...(filters.tags || [])];
      if (!newTags.includes(suggestion.text)) {
        newTags.push(suggestion.text);
        updateFilter('tags', newTags);
      }
    } else if (suggestion.type === 'folder') {
      updateFilter('folder', suggestion.text);
    }
    setShowDropdown(false);
  };

  const updateFilter = <K extends keyof SearchFilters>(
    key: K,
    value: SearchFilters[K]
  ) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters(defaultFilters);
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = filters.tags?.filter(tag => tag !== tagToRemove) || [];
    updateFilter('tags', newTags.length > 0 ? newTags : undefined);
  };

  const containerClasses = {
    default: 'bg-background-0 border border-outline-100',
    glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md',
    glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md'
  };

  const sizeConfig = {
    sm: { padding: 'p-3', spacing: 'sm', inputSize: 'sm' as const },
    md: { padding: 'p-4', spacing: 'md', inputSize: 'md' as const },
    lg: { padding: 'p-6', spacing: 'lg', inputSize: 'lg' as const }
  };

  const config = sizeConfig[size];

  const getSuggestionIcon = (suggestion: SearchSuggestion) => {
    if (suggestion.icon) return suggestion.icon;
    
    switch (suggestion.type) {
      case 'tag': return TagIcon;
      case 'folder': return FolderIcon;
      case 'document': return FileTextIcon;
      default: return SearchIcon;
    }
  };

  const getSuggestionColor = (suggestion: SearchSuggestion) => {
    switch (suggestion.type) {
      case 'tag': return '#FF6B9D';
      case 'folder': return '#A855F7';
      case 'document': return '#3B82F6';
      default: return '#9CA3AF';
    }
  };

  return (
    <View className={className}>
      <VStack space={config.spacing}>
        {/* Main Search Input */}
        <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
          <VStack space="md">
            <HStack className="items-center space-x-3">
              <Box className="flex-1">
                <Input variant={variant} size={config.inputSize}>
                  <InputField
                    placeholder={placeholder}
                    value={query}
                    onChangeText={handleQueryChange}
                    onSubmitEditing={handleSearch}
                    onFocus={() => setShowDropdown(query.length > 0)}
                    onBlur={() => setTimeout(() => setShowDropdown(false), 200)}
                  />
                </Input>
              </Box>
              
              <Button action="candyPink" size={config.inputSize} onPress={handleSearch}>
                <SearchIcon size={20} color="#FFFFFF" />
              </Button>
              
              {showFilters && (
                <Pressable onPress={() => setShowFilterPanel(!showFilterPanel)}>
                  <Box className="relative">
                    <Box className="p-2 rounded-lg bg-glass-bg-secondary border border-glass-border-primary">
                      <SlidersHorizontalIcon size={20} color="#FFFFFF" />
                    </Box>
                    
                    {activeFiltersCount > 0 && (
                      <Box className="absolute -top-1 -right-1 w-5 h-5 bg-candyPink rounded-full justify-center items-center">
                        <Text color="white" size="xs" style={{ fontWeight: 'bold' }}>
                          {activeFiltersCount}
                        </Text>
                      </Box>
                    )}
                  </Box>
                </Pressable>
              )}
            </HStack>

            {/* Active Filters */}
            {activeFiltersCount > 0 && (
              <VStack space="sm">
                <HStack className="justify-between items-center">
                  <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                    Active Filters ({activeFiltersCount}):
                  </Text>
                  <Pressable onPress={clearFilters}>
                    <Text color="candyPink" size="sm">Clear All</Text>
                  </Pressable>
                </HStack>
                
                <HStack className="space-x-2 flex-wrap">
                  {filters.documentType !== 'all' && (
                    <Badge action="candyPink" variant="solid" size="sm">
                      <BadgeText>Type: {filters.documentType}</BadgeText>
                    </Badge>
                  )}
                  
                  {filters.dateRange !== 'all' && (
                    <Badge action="candyPurple" variant="solid" size="sm">
                      <BadgeText>Date: {filters.dateRange}</BadgeText>
                    </Badge>
                  )}
                  
                  {filters.folder && (
                    <Badge action="candyBlue" variant="solid" size="sm">
                      <BadgeText>Folder: {filters.folder}</BadgeText>
                    </Badge>
                  )}
                  
                  {filters.tags?.map((tag, index) => (
                    <Badge key={index} action="candyPink" variant="solid" size="sm">
                      <HStack className="items-center space-x-1">
                        <BadgeText>#{tag}</BadgeText>
                        <Pressable onPress={() => removeTag(tag)}>
                          <XIcon size={12} color="#FFFFFF" />
                        </Pressable>
                      </HStack>
                    </Badge>
                  ))}
                  
                  {filters.isFavorite && (
                    <Badge action="candyPurple" variant="solid" size="sm">
                      <BadgeText>Favorites</BadgeText>
                    </Badge>
                  )}

                  {filters.isShared && (
                    <Badge action="candyBlue" variant="solid" size="sm">
                      <BadgeText>Shared</BadgeText>
                    </Badge>
                  )}
                </HStack>
              </VStack>
            )}
          </VStack>
        </Box>

        {/* Search Dropdown */}
        {showDropdown && (
          <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
            <VStack space="md">
              {/* Recent Searches */}
              {showRecentSearches && recentSearches.length > 0 && (
                <VStack space="sm">
                  <HStack className="items-center space-x-2">
                    <ClockIcon size={16} color="#9CA3AF" />
                    <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                      Recent Searches:
                    </Text>
                  </HStack>
                  
                  <VStack space="xs">
                    {recentSearches.slice(0, 5).map((search, index) => (
                      <Pressable 
                        key={index}
                        onPress={() => {
                          setQuery(search);
                          handleSearch();
                        }}
                        variant="enhanced"
                      >
                        <HStack className="items-center space-x-3 p-2 rounded">
                          <ClockIcon size={14} color="#9CA3AF" />
                          <Text color="secondary" size="sm">{search}</Text>
                        </HStack>
                      </Pressable>
                    ))}
                  </VStack>
                </VStack>
              )}

              {/* Suggestions */}
              {showSuggestions && suggestions.length > 0 && (
                <VStack space="sm">
                  <HStack className="items-center space-x-2">
                    <TrendingUpIcon size={16} color="#9CA3AF" />
                    <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                      Suggestions:
                    </Text>
                  </HStack>
                  
                  <VStack space="xs">
                    {suggestions.slice(0, 8).map((suggestion) => {
                      const SuggestionIcon = getSuggestionIcon(suggestion);
                      const suggestionColor = getSuggestionColor(suggestion);
                      
                      return (
                        <Pressable 
                          key={suggestion.id}
                          onPress={() => handleSuggestionPress(suggestion)}
                          variant="enhanced"
                        >
                          <HStack className="items-center space-x-3 p-2 rounded">
                            <SuggestionIcon size={14} color={suggestionColor} />
                            <Text color="primary" size="sm" style={{ flex: 1 }}>
                              {suggestion.text}
                            </Text>
                            {suggestion.count && (
                              <Text color="tertiary" size="xs">
                                {suggestion.count}
                              </Text>
                            )}
                          </HStack>
                        </Pressable>
                      );
                    })}
                  </VStack>
                </VStack>
              )}
            </VStack>
          </Box>
        )}

        {/* Filter Panel */}
        {showFilterPanel && (
          <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
            <VStack space="lg">
              <HStack className="justify-between items-center">
                <Text color="primary" size="md" style={{ fontWeight: 'bold' }}>
                  Search Filters
                </Text>
                <Pressable onPress={() => setShowFilterPanel(false)}>
                  <XIcon size={20} color="#9CA3AF" />
                </Pressable>
              </HStack>

              {/* Document Type Filter */}
              <VStack space="sm">
                <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                  Document Type:
                </Text>
                <HStack className="space-x-2 flex-wrap">
                  {['all', 'document', 'image', 'pdf', 'text'].map((type) => (
                    <Pressable
                      key={type}
                      onPress={() => updateFilter('documentType', type as any)}
                      variant="enhanced"
                    >
                      <Box 
                        className={`px-3 py-2 rounded-full border ${
                          filters.documentType === type 
                            ? 'bg-candyPink border-candyPink' 
                            : 'bg-glass-bg-secondary border-glass-border-primary'
                        }`}
                      >
                        <Text 
                          color={filters.documentType === type ? "white" : "secondary"} 
                          size="sm"
                          style={{ 
                            fontWeight: filters.documentType === type ? '600' : 'normal',
                            textTransform: 'capitalize'
                          }}
                        >
                          {type}
                        </Text>
                      </Box>
                    </Pressable>
                  ))}
                </HStack>
              </VStack>

              {/* Date Range Filter */}
              <VStack space="sm">
                <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                  Date Range:
                </Text>
                <HStack className="space-x-2 flex-wrap">
                  {['all', 'today', 'week', 'month', 'year'].map((range) => (
                    <Pressable
                      key={range}
                      onPress={() => updateFilter('dateRange', range as any)}
                      variant="enhanced"
                    >
                      <Box 
                        className={`px-3 py-2 rounded-full border ${
                          filters.dateRange === range 
                            ? 'bg-candyPurple border-candyPurple' 
                            : 'bg-glass-bg-secondary border-glass-border-primary'
                        }`}
                      >
                        <Text 
                          color={filters.dateRange === range ? "white" : "secondary"} 
                          size="sm"
                          style={{ 
                            fontWeight: filters.dateRange === range ? '600' : 'normal',
                            textTransform: 'capitalize'
                          }}
                        >
                          {range}
                        </Text>
                      </Box>
                    </Pressable>
                  ))}
                </HStack>
              </VStack>

              {/* Confidence Filter */}
              <VStack space="sm">
                <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                  OCR Confidence:
                </Text>
                <HStack className="space-x-2 flex-wrap">
                  {['all', 'high', 'medium', 'low'].map((confidence) => (
                    <Pressable
                      key={confidence}
                      onPress={() => updateFilter('confidence', confidence as any)}
                      variant="enhanced"
                    >
                      <Box 
                        className={`px-3 py-2 rounded-full border ${
                          filters.confidence === confidence 
                            ? 'bg-candyBlue border-candyBlue' 
                            : 'bg-glass-bg-secondary border-glass-border-primary'
                        }`}
                      >
                        <Text 
                          color={filters.confidence === confidence ? "white" : "secondary"} 
                          size="sm"
                          style={{ 
                            fontWeight: filters.confidence === confidence ? '600' : 'normal',
                            textTransform: 'capitalize'
                          }}
                        >
                          {confidence}
                        </Text>
                      </Box>
                    </Pressable>
                  ))}
                </HStack>
              </VStack>

              {/* Sort Options */}
              <VStack space="sm">
                <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                  Sort By:
                </Text>
                <HStack className="space-x-2 flex-wrap">
                  {['relevance', 'date', 'name', 'size', 'confidence'].map((sort) => (
                    <Pressable
                      key={sort}
                      onPress={() => updateFilter('sortBy', sort as any)}
                      variant="enhanced"
                    >
                      <Box 
                        className={`px-3 py-2 rounded-full border ${
                          filters.sortBy === sort
                            ? 'bg-candyPink border-candyPink'
                            : 'bg-glass-bg-secondary border-glass-border-primary'
                        }`}
                      >
                        <Text 
                          color={filters.sortBy === sort ? "white" : "secondary"} 
                          size="sm"
                          style={{ 
                            fontWeight: filters.sortBy === sort ? '600' : 'normal',
                            textTransform: 'capitalize'
                          }}
                        >
                          {sort}
                        </Text>
                      </Box>
                    </Pressable>
                  ))}
                </HStack>
              </VStack>

              {/* Toggle Filters */}
              <VStack space="sm">
                <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                  Additional Filters:
                </Text>
                
                <VStack space="sm">
                  <Pressable 
                    onPress={() => updateFilter('isFavorite', !filters.isFavorite)}
                    variant="enhanced"
                  >
                    <HStack className="items-center space-x-3 p-2 rounded">
                      <Box 
                        className={`w-5 h-5 rounded border-2 justify-center items-center ${
                          filters.isFavorite 
                            ? 'bg-warning border-warning' 
                            : 'border-glass-border-primary'
                        }`}
                      >
                        {filters.isFavorite && (
                          <StarIcon size={12} color="#FFFFFF" fill="#FFFFFF" />
                        )}
                      </Box>
                      <Text color="primary" size="sm">Favorites Only</Text>
                    </HStack>
                  </Pressable>
                  
                  <Pressable 
                    onPress={() => updateFilter('isShared', !filters.isShared)}
                    variant="enhanced"
                  >
                    <HStack className="items-center space-x-3 p-2 rounded">
                      <Box 
                        className={`w-5 h-5 rounded border-2 justify-center items-center ${
                          filters.isShared 
                            ? 'bg-info border-info' 
                            : 'border-glass-border-primary'
                        }`}
                      >
                        {filters.isShared && (
                          <Text color="white" size="xs" style={{ fontWeight: 'bold' }}>
                            ✓
                          </Text>
                        )}
                      </Box>
                      <Text color="primary" size="sm">Shared Documents</Text>
                    </HStack>
                  </Pressable>
                </VStack>
              </VStack>
            </VStack>
          </Box>
        )}
      </VStack>
    </View>
  );
}

export default SearchBar;