import React from 'react';
import { type ViewStyle, type TextStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import MaskedView from '@react-native-masked-view/masked-view';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';

interface GradientTextProps {
  children: React.ReactNode;
  colors: readonly [string, string, ...string[]];
  fontSize?: number;
  fontWeight?: TextStyle['fontWeight'];
  className?: string;
  gradientDirection?: 'horizontal' | 'vertical' | 'diagonal';
  start?: { x: number; y: number };
  end?: { x: number; y: number };
  locations?: readonly [number, number, ...number[]] | null;
  containerStyle?: ViewStyle;
  textStyle?: TextStyle;
}

export const GradientText: React.FC<GradientTextProps> = ({
  children,
  colors,
  fontSize = 48,
  fontWeight = 'bold',
  className = '',
  gradientDirection = 'horizontal',
  start,
  end,
  locations = null,
  containerStyle,
  textStyle
}) => {
  // Define gradient directions
  const getGradientPoints = () => {
    if (start && end) {
      return { start, end };
    }

    switch (gradientDirection) {
      case 'vertical':
        return { start: { x: 0, y: 0 }, end: { x: 0, y: 1 } };
      case 'diagonal':
        return { start: { x: 0, y: 0 }, end: { x: 1, y: 1 } };
      case 'horizontal':
      default:
        return { start: { x: 0, y: 0 }, end: { x: 1, y: 0 } };
    }
  };

  const { start: gradientStart, end: gradientEnd } = getGradientPoints();

  // Calculate dimensions based on text - be more generous with width
  const textHeight = Math.ceil(fontSize * 1.5);
  const estimatedWidth = Math.ceil(String(children).length * fontSize * 0.8);

  return (
    <Box className="items-center justify-center" style={containerStyle}>
      <MaskedView
        style={{
          width: estimatedWidth,
          height: textHeight,
        }}
        maskElement={
          <Box
            className="items-center justify-center"
            style={{
              backgroundColor: 'transparent',
              width: estimatedWidth,
              height: textHeight,
            }}
          >
            <Text
              style={[
                {
                  fontSize,
                  fontWeight,
                  color: 'black', // This color doesn't matter for the mask
                  textAlign: 'center',
                  includeFontPadding: false,
                  lineHeight: textHeight,
                },
                textStyle
              ]}
            >
              {children}
            </Text>
          </Box>
        }
      >
        <LinearGradient
          colors={colors}
          start={gradientStart}
          end={gradientEnd}
          locations={locations}
          style={{
            width: estimatedWidth,
            height: textHeight,
          }}
        />
      </MaskedView>
    </Box>
  );
};

export default GradientText;
