import React, { useState, useEffect } from 'react';
import { View, Animated, Dimensions } from 'react-native';
import { Text } from '../ui/text';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Pressable } from '../ui/pressable';
import { 
  CheckCircleIcon, 
  AlertTriangleIcon, 
  XCircleIcon,
  InfoIcon,
  XIcon,
  RefreshCwIcon,
  ExternalLinkIcon,
  DownloadIcon,
  ShareIcon,
  EyeIcon,
  ZapIcon,
  ClockIcon,
  WifiOffIcon,
  CloudIcon
} from 'lucide-react-native';

const { width: screenWidth } = Dimensions.get('window');

interface StatusToastProps {
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info' | 'loading';
  duration?: number;
  position?: 'top' | 'bottom' | 'center';
  action?: ToastAction;
  onDismiss?: () => void;
  persistent?: boolean;
  showProgress?: boolean;
  variant?: 'default' | 'glass' | 'minimal';
  className?: string;
}

interface ToastAction {
  label: string;
  onPress: () => void;
  icon?: React.ComponentType<{ size: number; color: string }>;
}

interface ToastState {
  visible: boolean;
  progress: number;
}

export function StatusToast({
  message,
  type = 'info',
  duration = 4000,
  position = 'top',
  action,
  onDismiss,
  persistent = false,
  showProgress = false,
  variant = 'glass',
  className
}: StatusToastProps) {
  const [state, setState] = useState<ToastState>({ visible: true, progress: 0 });
  const [animatedValue] = useState(new Animated.Value(0));
  const [progressValue] = useState(new Animated.Value(0));

  useEffect(() => {
    // Show animation
    Animated.spring(animatedValue, {
      toValue: 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();

    // Progress animation
    if (showProgress && !persistent && duration > 0) {
      Animated.timing(progressValue, {
        toValue: 1,
        duration: duration,
        useNativeDriver: false,
      }).start();
    }

    // Auto dismiss
    if (!persistent && duration > 0) {
      const timer = setTimeout(() => {
        dismiss();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, []);

  const dismiss = () => {
    Animated.spring(animatedValue, {
      toValue: 0,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start(() => {
      setState(prev => ({ ...prev, visible: false }));
      onDismiss?.();
    });
  };

  const getTypeConfig = (type: StatusToastProps['type']) => {
    switch (type) {
      case 'success':
        return {
          icon: CheckCircleIcon,
          color: '#10B981',
          bgColor: 'rgba(16, 185, 129, 0.1)',
          borderColor: 'rgba(16, 185, 129, 0.3)'
        };
      case 'error':
        return {
          icon: XCircleIcon,
          color: '#EF4444',
          bgColor: 'rgba(239, 68, 68, 0.1)',
          borderColor: 'rgba(239, 68, 68, 0.3)'
        };
      case 'warning':
        return {
          icon: AlertTriangleIcon,
          color: '#F59E0B',
          bgColor: 'rgba(245, 158, 11, 0.1)',
          borderColor: 'rgba(245, 158, 11, 0.3)'
        };
      case 'loading':
        return {
          icon: RefreshCwIcon,
          color: '#3B82F6',
          bgColor: 'rgba(59, 130, 246, 0.1)',
          borderColor: 'rgba(59, 130, 246, 0.3)'
        };
      default:
        return {
          icon: InfoIcon,
          color: '#6B7280',
          bgColor: 'rgba(107, 114, 128, 0.1)',
          borderColor: 'rgba(107, 114, 128, 0.3)'
        };
    }
  };

  const getPositionStyle = () => {
    const baseStyle = {
      position: 'absolute' as const,
      left: 16,
      right: 16,
      zIndex: 1000,
    };

    switch (position) {
      case 'top':
        return { ...baseStyle, top: 60 };
      case 'bottom':
        return { ...baseStyle, bottom: 60 };
      case 'center':
        return { 
          ...baseStyle, 
          top: '50%',
          transform: [{ translateY: -50 }]
        };
      default:
        return { ...baseStyle, top: 60 };
    }
  };

  const typeConfig = getTypeConfig(type);
  const TypeIcon = typeConfig.icon;
  const positionStyle = getPositionStyle();

  const containerClasses = {
    default: 'bg-background-0 border border-outline-100',
    glass: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md',
    minimal: 'bg-background-50 border border-outline-50'
  };

  if (!state.visible) return null;

  return (
    <Animated.View
      style={[
        positionStyle,
        {
          transform: [
            {
              translateY: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: position === 'bottom' ? [100, 0] : [-100, 0],
              }),
            },
            {
              scale: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [0.9, 1],
              }),
            },
          ],
          opacity: animatedValue,
        },
      ]}
      className={className}
    >
      <Box 
        className={`${containerClasses[variant]} rounded-xl shadow-lg`}
        style={{
          backgroundColor: variant === 'glass' ? undefined : typeConfig.bgColor,
          borderColor: typeConfig.borderColor,
        }}
      >
        <VStack space="sm">
          {/* Progress Bar */}
          {showProgress && !persistent && (
            <Animated.View
              style={{
                height: 3,
                backgroundColor: typeConfig.color,
                borderTopLeftRadius: 12,
                borderTopRightRadius: 12,
                width: progressValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%'],
                }),
              }}
            />
          )}

          {/* Content */}
          <Box className="p-4">
            <HStack className="items-start space-x-3">
              {/* Icon */}
              <Box className="mt-0.5">
                {type === 'loading' ? (
                  <Animated.View
                    style={{
                      transform: [
                        {
                          rotate: animatedValue.interpolate({
                            inputRange: [0, 1],
                            outputRange: ['0deg', '360deg'],
                          }),
                        },
                      ],
                    }}
                  >
                    <TypeIcon size={20} color={typeConfig.color} />
                  </Animated.View>
                ) : (
                  <TypeIcon size={20} color={typeConfig.color} />
                )}
              </Box>

              {/* Message */}
              <VStack space="xs" className="flex-1">
                <Text 
                  color="primary" 
                  size="sm" 
                  style={{ fontWeight: '600', lineHeight: 20 }}
                >
                  {message}
                </Text>

                {/* Action Button */}
                {action && (
                  <Pressable onPress={action.onPress}>
                    <HStack className="items-center space-x-2 mt-2">
                      {action.icon && (
                        <action.icon size={14} color={typeConfig.color} />
                      )}
                      <Text 
                        size="sm" 
                        style={{ 
                          color: typeConfig.color, 
                          fontWeight: '600' 
                        }}
                      >
                        {action.label}
                      </Text>
                    </HStack>
                  </Pressable>
                )}
              </VStack>

              {/* Dismiss Button */}
              {!persistent && (
                <Pressable onPress={dismiss} className="mt-0.5">
                  <XIcon size={16} color="#9CA3AF" />
                </Pressable>
              )}
            </HStack>
          </Box>
        </VStack>
      </Box>
    </Animated.View>
  );
}

// Toast Manager Hook
export function useStatusToast() {
  const [toasts, setToasts] = useState<Array<StatusToastProps & { id: string }>>([]);

  const showToast = (toast: Omit<StatusToastProps, 'onDismiss'>) => {
    const id = Date.now().toString();
    const newToast = {
      ...toast,
      id,
      onDismiss: () => removeToast(id),
    };

    setToasts(prev => [...prev, newToast]);

    // Auto remove after duration
    if (!toast.persistent && toast.duration !== 0) {
      setTimeout(() => {
        removeToast(id);
      }, toast.duration || 4000);
    }

    return id;
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const clearAllToasts = () => {
    setToasts([]);
  };

  // Predefined toast methods
  const success = (message: string, options?: Partial<StatusToastProps>) => 
    showToast({ ...options, message, type: 'success' });

  const error = (message: string, options?: Partial<StatusToastProps>) => 
    showToast({ ...options, message, type: 'error' });

  const warning = (message: string, options?: Partial<StatusToastProps>) => 
    showToast({ ...options, message, type: 'warning' });

  const info = (message: string, options?: Partial<StatusToastProps>) => 
    showToast({ ...options, message, type: 'info' });

  const loading = (message: string, options?: Partial<StatusToastProps>) => 
    showToast({ ...options, message, type: 'loading', persistent: true });

  return {
    toasts,
    showToast,
    removeToast,
    clearAllToasts,
    success,
    error,
    warning,
    info,
    loading,
  };
}

// Toast Container Component
export function ToastContainer() {
  const { toasts } = useStatusToast();

  return (
    <View style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, pointerEvents: 'box-none' }}>
      {toasts.map((toast, index) => (
        <StatusToast
          key={toast.id}
          {...toast}
          style={{
            zIndex: 1000 + index,
          }}
        />
      ))}
    </View>
  );
}

// Predefined Toast Components
export const SuccessToast = ({ message, ...props }: Omit<StatusToastProps, 'type'>) => (
  <StatusToast type="success" message={message} {...props} />
);

export const ErrorToast = ({ message, ...props }: Omit<StatusToastProps, 'type'>) => (
  <StatusToast type="error" message={message} {...props} />
);

export const WarningToast = ({ message, ...props }: Omit<StatusToastProps, 'type'>) => (
  <StatusToast type="warning" message={message} {...props} />
);

export const InfoToast = ({ message, ...props }: Omit<StatusToastProps, 'type'>) => (
  <StatusToast type="info" message={message} {...props} />
);

export const LoadingToast = ({ message, ...props }: Omit<StatusToastProps, 'type'>) => (
  <StatusToast type="loading" message={message} persistent={true} {...props} />
);

// Common Toast Presets
export const ToastPresets = {
  scanComplete: (documentName: string) => ({
    message: `"${documentName}" scanned successfully`,
    type: 'success' as const,
    action: {
      label: 'View Document',
      icon: EyeIcon,
      onPress: () => console.log('View document'),
    },
  }),

  ocrProcessing: (documentName: string) => ({
    message: `Processing "${documentName}" with AI...`,
    type: 'loading' as const,
    persistent: true,
    showProgress: true,
  }),

  uploadFailed: (error: string) => ({
    message: `Upload failed: ${error}`,
    type: 'error' as const,
    action: {
      label: 'Retry',
      icon: RefreshCwIcon,
      onPress: () => console.log('Retry upload'),
    },
    duration: 6000,
  }),

  offlineMode: () => ({
    message: 'You\'re offline. Changes will sync when connected.',
    type: 'warning' as const,
    action: {
      label: 'Learn More',
      icon: WifiOffIcon,
      onPress: () => console.log('Learn about offline mode'),
    },
    persistent: true,
  }),

  premiumFeature: () => ({
    message: 'This feature requires Premium',
    type: 'info' as const,
    action: {
      label: 'Upgrade',
      icon: ZapIcon,
      onPress: () => console.log('Show upgrade prompt'),
    },
  }),

  documentShared: (recipientCount: number) => ({
    message: `Document shared with ${recipientCount} ${recipientCount === 1 ? 'person' : 'people'}`,
    type: 'success' as const,
    action: {
      label: 'View',
      icon: ShareIcon,
      onPress: () => console.log('View shared document'),
    },
  }),

  backupComplete: () => ({
    message: 'All documents backed up to cloud',
    type: 'success' as const,
    action: {
      label: 'View Backup',
      icon: CloudIcon,
      onPress: () => console.log('View backup status'),
    },
  }),
};

export default StatusToast;