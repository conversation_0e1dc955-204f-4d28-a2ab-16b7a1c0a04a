import React, { useState, useEffect } from 'react';
import { View } from 'react-native';
import { Text } from '../ui/text';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Slider, SliderTrack, SliderFilledTrack, SliderThumb } from '../ui/slider';
import { Switch } from '../ui/switch';
import { Pressable } from '../ui/pressable';
import { 
  SunIcon, 
  ContrastIcon, 
  PaletteIcon,
  FocusIcon,
  RotateCcwIcon,
  SparklesIcon,
  EyeIcon,
  ZapIcon,
  ImageIcon,
  SettingsIcon,
  CheckCircleIcon,
  XCircleIcon,
  type LucideIcon
} from 'lucide-react-native';

// Type-safe icon wrapper for LucideIcon compatibility
const createIconComponent = (IconComponent: LucideIcon): React.ComponentType<{ size: number; color: string }> => {
  return ({ size, color }) => <IconComponent size={size} color={color} />;
};

interface EnhancementControlsProps {
  initialSettings?: Partial<EnhancementSettings>;
  onSettingsChange?: (settings: EnhancementSettings) => void;
  onPresetApply?: (preset: EnhancementPreset) => void;
  onReset?: () => void;
  onAutoEnhance?: () => void;
  showPresets?: boolean;
  showAdvanced?: boolean;
  variant?: 'default' | 'glass' | 'glassCard';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

interface EnhancementSettings {
  brightness: number;
  contrast: number;
  saturation: number;
  sharpness: number;
  gamma: number;
  highlights: number;
  shadows: number;
  vibrance: number;
  clarity: number;
  autoEnhance: boolean;
  documentMode: 'auto' | 'text' | 'photo' | 'drawing' | 'handwriting';
  colorSpace: 'sRGB' | 'adobeRGB' | 'displayP3';
  noiseReduction: boolean;
  edgeEnhancement: boolean;
}

interface EnhancementPreset {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  color: string;
  settings: Partial<EnhancementSettings>;
}

const defaultSettings: EnhancementSettings = {
  brightness: 50,
  contrast: 50,
  saturation: 50,
  sharpness: 50,
  gamma: 50,
  highlights: 50,
  shadows: 50,
  vibrance: 50,
  clarity: 50,
  autoEnhance: false,
  documentMode: 'auto',
  colorSpace: 'sRGB',
  noiseReduction: true,
  edgeEnhancement: false
};

const enhancementPresets: EnhancementPreset[] = [
  {
    id: 'document',
    name: 'Document',
    description: 'Optimized for text documents',
    icon: createIconComponent(ImageIcon),
    color: '#FF6B9D',
    settings: {
      brightness: 55,
      contrast: 65,
      saturation: 30,
      sharpness: 70,
      clarity: 60,
      documentMode: 'text',
      edgeEnhancement: true
    }
  },
  {
    id: 'photo',
    name: 'Photo',
    description: 'Enhanced for photographs',
    icon: createIconComponent(EyeIcon),
    color: '#A855F7',
    settings: {
      brightness: 50,
      contrast: 55,
      saturation: 65,
      sharpness: 45,
      vibrance: 60,
      documentMode: 'photo',
      noiseReduction: true
    }
  },
  {
    id: 'handwriting',
    name: 'Handwriting',
    description: 'Optimized for handwritten text',
    icon: createIconComponent(FocusIcon),
    color: '#3B82F6',
    settings: {
      brightness: 60,
      contrast: 70,
      saturation: 25,
      sharpness: 80,
      clarity: 75,
      documentMode: 'handwriting',
      edgeEnhancement: true
    }
  },
  {
    id: 'auto',
    name: 'Auto',
    description: 'AI-powered automatic enhancement',
    icon: createIconComponent(SparklesIcon),
    color: '#10B981',
    settings: {
      autoEnhance: true,
      documentMode: 'auto'
    }
  }
];

export function EnhancementControls({
  initialSettings = {},
  onSettingsChange,
  onPresetApply,
  onReset,
  onAutoEnhance,
  showPresets = true,
  showAdvanced = false,
  variant = 'glass',
  size = 'md',
  className
}: EnhancementControlsProps) {
  const [settings, setSettings] = useState<EnhancementSettings>({
    ...defaultSettings,
    ...initialSettings
  });
  const [showAdvancedControls, setShowAdvancedControls] = useState(showAdvanced);
  const [activePreset, setActivePreset] = useState<string | null>(null);

  useEffect(() => {
    onSettingsChange?.(settings);
  }, [settings, onSettingsChange]);

  const updateSetting = <K extends keyof EnhancementSettings>(
    key: K,
    value: EnhancementSettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setActivePreset(null); // Clear active preset when manually adjusting
  };

  const applyPreset = (preset: EnhancementPreset) => {
    const newSettings = { ...settings, ...preset.settings };
    setSettings(newSettings);
    setActivePreset(preset.id);
    onPresetApply?.(preset);
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
    setActivePreset(null);
    onReset?.();
  };

  const handleAutoEnhance = () => {
    updateSetting('autoEnhance', !settings.autoEnhance);
    onAutoEnhance?.();
  };

  const containerClasses = {
    default: 'bg-background-0 border border-outline-100',
    glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md',
    glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md'
  };

  const sizeConfig = {
    sm: { padding: 'p-3', spacing: 'sm' as const, sliderSize: 'sm' as const },
    md: { padding: 'p-4', spacing: 'md' as const, sliderSize: 'md' as const },
    lg: { padding: 'p-6', spacing: 'lg' as const, sliderSize: 'lg' as const }
  };

  const config = sizeConfig[size];

  const SliderControl = ({ 
    label, 
    value, 
    onChange, 
    color, 
    icon: Icon,
    min = 0,
    max = 100 
  }: {
    label: string;
    value: number;
    onChange: (value: number) => void;
    color: 'candyPink' | 'candyPurple' | 'candyBlue';
    icon: React.ComponentType<{ size: number; color: string }>;
    min?: number;
    max?: number;
  }) => (
    <VStack space="xs">
      <HStack className="justify-between items-center">
        <HStack className="items-center space-x-2">
          <Icon size={16} color={color === 'candyPink' ? '#FF6B9D' : color === 'candyPurple' ? '#A855F7' : '#3B82F6'} />
          <Text color="tertiary" size="sm">{label}</Text>
        </HStack>
        <Text 
          color={color} 
          size="sm" 
          style={{ fontWeight: '600' }}
        >
          {value}%
        </Text>
      </HStack>
      <Slider
        value={value}
        onChange={onChange}
        minValue={min}
        maxValue={max}
        size={config.sliderSize}
      >
        <SliderTrack>
          <SliderFilledTrack color={color} />
        </SliderTrack>
        <SliderThumb color={color} />
      </Slider>
    </VStack>
  );

  return (
    <View className={className}>
      <VStack space={config.spacing}>
        {/* Header */}
        <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
          <HStack className="justify-between items-center">
            <HStack className="items-center space-x-3">
              <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyPurple/20">
                <SettingsIcon size={20} color="#A855F7" />
              </Box>
              <VStack space="xs">
                <Text color="primary" size="md" style={{ fontWeight: 'bold' }}>
                  Enhancement Controls
                </Text>
                <Text color="secondary" size="sm">
                  Adjust image quality and appearance
                </Text>
              </VStack>
            </HStack>
            
            <HStack className="space-x-2">
              <Pressable onPress={() => setShowAdvancedControls(!showAdvancedControls)}>
                <Text color="candyPurple" size="sm">
                  {showAdvancedControls ? 'Simple' : 'Advanced'}
                </Text>
              </Pressable>
            </HStack>
          </HStack>
        </Box>

        {/* Auto Enhancement Toggle */}
        <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
          <HStack className="justify-between items-center">
            <HStack className="items-center space-x-3">
              <SparklesIcon size={20} color="#10B981" />
              <VStack space="xs">
                <Text color="primary" size="sm" style={{ fontWeight: '600' }}>
                  AI Auto Enhancement
                </Text>
                <Text color="secondary" size="xs">
                  Let AI automatically optimize your image
                </Text>
              </VStack>
            </HStack>
            
            <Switch
              value={settings.autoEnhance}
              onValueChange={handleAutoEnhance}
              size="md"
            />
          </HStack>
        </Box>

        {/* Presets */}
        {showPresets && (
          <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
            <VStack space="md">
              <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                Enhancement Presets:
              </Text>
              
              <HStack className="space-x-2 flex-wrap">
                {enhancementPresets.map((preset) => {
                  const isActive = activePreset === preset.id;
                  const PresetIcon = preset.icon;
                  
                  return (
                    <Pressable
                      key={preset.id}
                      onPress={() => applyPreset(preset)}
                      variant="enhanced"
                    >
                      <Box 
                        className={`px-3 py-2 rounded-lg border ${
                          isActive 
                            ? 'bg-glass-bg-card border-glass-border-accent' 
                            : 'bg-glass-bg-secondary border-glass-border-primary'
                        }`}
                      >
                        <VStack space="xs" style={{ alignItems: 'center', minWidth: 60 }}>
                          <PresetIcon 
                            size={20} 
                            color={isActive ? preset.color : '#9CA3AF'} 
                          />
                          <Text 
                            color={isActive ? "primary" : "secondary"} 
                            size="xs"
                            style={{ 
                              fontWeight: isActive ? '600' : 'normal',
                              textAlign: 'center'
                            }}
                          >
                            {preset.name}
                          </Text>
                        </VStack>
                      </Box>
                    </Pressable>
                  );
                })}
              </HStack>
            </VStack>
          </Box>
        )}

        {/* Basic Controls */}
        <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
          <VStack space="lg">
            <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
              Basic Adjustments:
            </Text>
            
            <VStack space="md">
              <SliderControl
                label="Brightness"
                value={settings.brightness}
                onChange={(value) => updateSetting('brightness', value)}
                color="candyPink"
                icon={createIconComponent(SunIcon)}
              />
              
              <SliderControl
                label="Contrast"
                value={settings.contrast}
                onChange={(value) => updateSetting('contrast', value)}
                color="candyPurple"
                icon={createIconComponent(ContrastIcon)}
              />
              
              <SliderControl
                label="Saturation"
                value={settings.saturation}
                onChange={(value) => updateSetting('saturation', value)}
                color="candyBlue"
                icon={createIconComponent(PaletteIcon)}
              />
              
              <SliderControl
                label="Sharpness"
                value={settings.sharpness}
                onChange={(value) => updateSetting('sharpness', value)}
                color="candyPink"
                icon={createIconComponent(FocusIcon)}
              />
            </VStack>
          </VStack>
        </Box>

        {/* Advanced Controls */}
        {showAdvancedControls && (
          <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
            <VStack space="lg">
              <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                Advanced Adjustments:
              </Text>
              
              <VStack space="md">
                <SliderControl
                  label="Gamma"
                  value={settings.gamma}
                  onChange={(value) => updateSetting('gamma', value)}
                  color="candyPurple"
                  icon={createIconComponent(ZapIcon)}
                />
                
                <SliderControl
                  label="Highlights"
                  value={settings.highlights}
                  onChange={(value) => updateSetting('highlights', value)}
                  color="candyBlue"
                  icon={createIconComponent(SunIcon)}
                />
                
                <SliderControl
                  label="Shadows"
                  value={settings.shadows}
                  onChange={(value) => updateSetting('shadows', value)}
                  color="candyPink"
                  icon={createIconComponent(ContrastIcon)}
                />
                
                <SliderControl
                  label="Vibrance"
                  value={settings.vibrance}
                  onChange={(value) => updateSetting('vibrance', value)}
                  color="candyPurple"
                  icon={createIconComponent(PaletteIcon)}
                />
                
                <SliderControl
                  label="Clarity"
                  value={settings.clarity}
                  onChange={(value) => updateSetting('clarity', value)}
                  color="candyBlue"
                  icon={createIconComponent(EyeIcon)}
                />
              </VStack>
            </VStack>
          </Box>
        )}

        {/* Document Mode & Options */}
        <Box className={`${containerClasses[variant]} rounded-xl ${config.padding}`}>
          <VStack space="md">
            <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
              Document Mode:
            </Text>
            
            <HStack className="space-x-2 flex-wrap">
              {['auto', 'text', 'photo', 'drawing', 'handwriting'].map((mode) => (
                <Pressable
                  key={mode}
                  onPress={() => updateSetting('documentMode', mode as any)}
                  variant="enhanced"
                >
                  <Box 
                    className={`px-3 py-2 rounded-full border ${
                      settings.documentMode === mode 
                        ? 'bg-glass-bg-card border-glass-border-accent' 
                        : 'bg-glass-bg-secondary border-glass-border-primary'
                    }`}
                  >
                    <Text 
                      color={settings.documentMode === mode ? "primary" : "secondary"} 
                      size="sm"
                      style={{ 
                        fontWeight: settings.documentMode === mode ? '600' : 'normal',
                        textTransform: 'capitalize'
                      }}
                    >
                      {mode}
                    </Text>
                  </Box>
                </Pressable>
              ))}
            </HStack>
            
            {/* Additional Options */}
            <VStack space="sm">
              <HStack className="justify-between items-center">
                <Text color="secondary" size="sm">Noise Reduction</Text>
                <Switch
                  value={settings.noiseReduction}
                  onValueChange={(value) => updateSetting('noiseReduction', value)}
                  size="sm"
                />
              </HStack>
              
              <HStack className="justify-between items-center">
                <Text color="secondary" size="sm">Edge Enhancement</Text>
                <Switch
                  value={settings.edgeEnhancement}
                  onValueChange={(value) => updateSetting('edgeEnhancement', value)}
                  size="sm"
                />
              </HStack>
            </VStack>
          </VStack>
        </Box>

        {/* Action Buttons */}
        <HStack className="space-x-3">
          <Button action="glass" variant="outline" onPress={resetSettings} style={{ flex: 1 }}>
            <HStack className="items-center space-x-2">
              <RotateCcwIcon size={16} color="#FFFFFF" />
              <ButtonText>Reset</ButtonText>
            </HStack>
          </Button>
          
          <Button action="candyPink" onPress={handleAutoEnhance} style={{ flex: 1 }}>
            <HStack className="items-center space-x-2">
              <SparklesIcon size={16} color="#FFFFFF" />
              <ButtonText>Auto Enhance</ButtonText>
            </HStack>
          </Button>
        </HStack>
      </VStack>
    </View>
  );
}

export default EnhancementControls;