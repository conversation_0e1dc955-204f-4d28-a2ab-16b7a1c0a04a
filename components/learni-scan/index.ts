// LearniScan Custom Components
// Phase 3: Document scanning and AI processing components

// Priority 1: Document Scanning Components
export { default as DocumentScanner } from './DocumentScanner';
export { default as ScanPreview } from './ScanPreview';
export { QualityIndicator } from './QualityIndicator';
export { ScanProgress } from './ScanProgress';

// Priority 2: AI Processing Components
export { AIProcessingCard } from './AIProcessingCard';
export { TextRecognitionDisplay } from './TextRecognitionDisplay';
export { EnhancementControls } from './EnhancementControls';
export { ConfidenceScore } from './ConfidenceScore';

// Priority 3: Document Management Components
export { DocumentCard } from './DocumentCard';
export { FolderGrid } from './FolderGrid';
export { SearchBar } from './SearchBar';
export { FilterPanel } from './FilterPanel';

// Priority 4: User Experience Components
export { OnboardingFlow } from './OnboardingFlow';
export { UpgradePrompt } from './UpgradePrompt';
export { TutorialOverlay } from './TutorialOverlay';
export { StatusToast, useStatusToast, ToastContainer, ToastPresets } from './StatusToast';

// Component Types
export type {
  // Priority 1 Types
  DocumentScannerProps,
  ScanPreviewProps,
  QualityIndicatorProps,
  ScanProgressProps,
  QualityFactor,
  ProcessingStep,
  EnhancementSettings,
  // Priority 2 Types
  AIProcessingCardProps,
  AIOperation,
  TextRecognitionDisplayProps,
  RecognizedText,
  TextBounds,
  EnhancementControlsProps,
  EnhancementPreset,
  ConfidenceScoreProps,
  ConfidenceBreakdown,
  // Priority 3 Types
  DocumentCardProps,
  DocumentInfo,
  FolderGridProps,
  FolderInfo,
  SearchBarProps,
  SearchFilters,
  SearchSuggestion,
  FilterPanelProps,
  DocumentFilters,
  // Priority 4 Types
  OnboardingFlowProps,
  OnboardingStep,
  UpgradePromptProps,
  PremiumPlan,
  PremiumFeature,
  TutorialOverlayProps,
  TutorialStep,
  StatusToastProps,
  ToastAction
} from './types';

// Re-export for convenience
import DocumentScannerComponent from './DocumentScanner';
import ScanPreviewComponent from './ScanPreview';
import { QualityIndicator } from './QualityIndicator';
import { ScanProgress } from './ScanProgress';
import { AIProcessingCard } from './AIProcessingCard';
import { TextRecognitionDisplay } from './TextRecognitionDisplay';
import { EnhancementControls } from './EnhancementControls';
import { ConfidenceScore } from './ConfidenceScore';
import { DocumentCard } from './DocumentCard';
import { FolderGrid } from './FolderGrid';
import { SearchBar } from './SearchBar';
import { FilterPanel } from './FilterPanel';
import { OnboardingFlow } from './OnboardingFlow';
import { TutorialOverlay } from './TutorialOverlay';
import { StatusToast } from './StatusToast';
import { UpgradePrompt } from './UpgradePrompt';

export default {
  // Priority 1: Document Scanning
  DocumentScanner: DocumentScannerComponent,
  ScanPreview: ScanPreviewComponent,
  QualityIndicator,
  ScanProgress,
  // Priority 2: AI Processing
  AIProcessingCard,
  TextRecognitionDisplay,
  EnhancementControls,
  ConfidenceScore,
  // Priority 3: Document Management
  DocumentCard,
  FolderGrid,
  SearchBar,
  FilterPanel,
  // Priority 4: User Experience
  OnboardingFlow,
  UpgradePrompt,
  TutorialOverlay,
  StatusToast
};