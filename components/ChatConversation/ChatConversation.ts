import { ChatMessage, ConversationOptions, ConversationService } from './types';

/**
 * ChatConversation module for managing AI chat conversations
 * This module maintains conversation history and interfaces with AI models
 */
export class ChatConversation implements ConversationService {
  private history: ChatMessage[] = [];
  private systemPrompt: string;
  private maxHistoryLength: number;
  private modelConfig: { temperature: number; maxTokens: number };
  private aiService: any; // This should be injected

  /**
   * Initialize a new chat conversation
   * @param aiService - The AI service implementation to use for generating responses
   * @param options - Configuration options for the conversation
   */
  constructor(
    aiService: any,
    options: ConversationOptions = {}
  ) {
    this.aiService = aiService;
    this.systemPrompt = options.initialSystemPrompt || "You are a helpful assistant.";
    this.maxHistoryLength = options.maxHistoryLength || 10;
    this.modelConfig = {
      temperature: options.modelConfig?.temperature || 0.7,
      maxTokens: options.modelConfig?.maxTokens || 1000
    };
    
    // Initialize with system prompt
    this.history.push({ role: 'system', content: this.systemPrompt });
  }

  /**
   * Generate a response to the user's message
   * @param userMessage - The user's input message
   * @returns Promise resolving to the assistant's response
   */
  public async generateResponse(userMessage: string): Promise<string> {
    try {
      // Add user message to history
      this.history.push({ role: 'user', content: userMessage });

      // Maintain history size
      if (this.history.length > this.maxHistoryLength + 1) { // +1 for system prompt
        this.history = [
          this.history[0], // Keep system prompt
          ...this.history.slice(-(this.maxHistoryLength))
        ];
      }

      // Generate response using the provided AI service
      const responseContent = await this.aiService.getCompletion(this.history, this.modelConfig);
      
      // Add assistant response to history
      this.history.push({ role: 'assistant', content: responseContent });
      
      return responseContent;
    } catch (error) {
      console.error('Error generating chat response:', error);
      throw new Error('Failed to generate response. Please try again.');
    }
  }

  /**
   * Clear conversation history except for system prompt
   */
  public clearHistory(): void {
    this.history = [{ role: 'system', content: this.systemPrompt }];
  }

  /**
   * Get the current conversation history
   */
  public getHistory(): ChatMessage[] {
    return [...this.history]; // Return a copy to prevent external mutations
  }

  /**
   * Update the system prompt
   * @param newPrompt - The new system prompt to use
   */
  public updateSystemPrompt(newPrompt: string): void {
    this.systemPrompt = newPrompt;
    if (this.history.length > 0 && this.history[0].role === 'system') {
      this.history[0].content = newPrompt;
    } else {
      this.history.unshift({ role: 'system', content: newPrompt });
    }
  }
}
