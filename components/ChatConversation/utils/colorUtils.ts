/**
 * State layer opacity values for different interaction states
 */
export const stateLayerOpacity = {
  hover: 0.08,
  pressed: 0.12,
  dragged: 0.16,
  focus: 0.12,
};

type IRgbaColor = `rgba(${number}, ${number}, ${number}, ${number})`;

/**
 * Adds opacity to a color
 * @param color - Color in hex (#RRGGBB or #RRGGBBAA), rgb(), or rgba() format
 * @param opacity - Opacity value between 0 and 1
 * @returns Color in rgba format with applied opacity
 */
export const withOpacity = (color: string, opacity: number): IRgbaColor => {
  // If color is already in rgba format, just replace the opacity
  if (color.startsWith('rgba')) {
    return color.replace(/[\d.]+\)$/, `${opacity})`) as IRgbaColor;
  }
  
  // If color is in rgb format, convert to rgba
  if (color.startsWith('rgb(')) {
    return color.replace('rgb(', 'rgba(').replace(')', `, ${opacity})`) as IRgbaColor;
  }
  
  // If color is in hex format, convert to rgba
  if (color.startsWith('#')) {
    const hexColor = color.slice(1);
    const r = parseInt(hexColor.slice(0, 2), 16);
    const g = parseInt(hexColor.slice(2, 4), 16);
    const b = parseInt(hexColor.slice(4, 6), 16);
    
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  
  // For named colors or other formats, default to applying opacity via rgba
  return `rgba(${color}, ${opacity})` as IRgbaColor;
};