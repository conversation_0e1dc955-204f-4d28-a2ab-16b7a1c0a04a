import { fetch as expoFetch } from 'expo/fetch';
import { type MessageType, type User } from './types';

type Nullable<T> = T | null | undefined;

export const getUserName = ({ firstName, lastName }: User) =>
  `${firstName ?? ''} ${lastName ?? ''}`.trim();

export const excludeDerivedMessageProps = (
  message: MessageType.DerivedMessage,
) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { nextMessageInGroup, offset, showName, showStatus, ...rest } = message;
  return { ...rest } as MessageType.Any;
};

export function oneOf<T, U>(
  first: ((param1: T, param2: number, param3: boolean) => U) | undefined,
  second: U,
): (param1: T, param2: number, param3?: boolean) => U {
  return (param1: T, param2: number, param3: boolean = false) =>
    first ? first(param1, param2, param3) : second;
}

export const formatMessageTime = (timestamp?: number) => {
  if (!timestamp) {
    return '-no-timestamp-';
  }

  const dateTime = new Date(timestamp);
  const hours = dateTime.getHours();
  const minutes = dateTime.getMinutes();
  const seconds = dateTime.getSeconds();
  return `${hours}:${minutes}:${seconds}`;
};

import React from 'react';

export const getNodes = (nodes: React.ReactNode): React.ReactElement[] => {
    return React.Children.map<any, any>(nodes, (n) => React.isValidElement(n) ? n : null).filter((n: any) => n);
};

export const getNodesByType = <T extends React.ComponentType<any>>(
    nodes: React.ReactElement[],
    type: T,
): Array<React.ReactElement<React.ComponentProps<T>, T>> => {
    return nodes.filter((n) => n.type === type) as any[];
};

export const getNodeByType = <T extends React.ComponentType<any>>(
    nodes: React.ReactElement[],
    type: T,
): Nullable<React.ReactElement<React.ComponentProps<T>, T>> => {
    return getNodesByType(nodes, type).pop();
};


interface PreviewData {
  title?: string;
  description?: string;
  imageUrl?: string;
  siteName?: string;
}

export const fetchPreviewData = async (url: string): Promise<PreviewData> => {
  try {
    // Fetch the HTML content
    const response = await expoFetch(url);
    const html = await response.text();
    
    // Extract metadata using regex
    const extractMetaContent = (name: string): string | undefined => {
      // Try Open Graph metadata first
      const ogMatch = new RegExp(`<meta\\s+property=["']og:${name}["']\\s+content=["']([^"']*)["']`, 'i').exec(html);
      if (ogMatch && ogMatch[1]) return ogMatch[1];
      
      // Try standard meta tags
      const metaMatch = new RegExp(`<meta\\s+name=["']${name}["']\\s+content=["']([^"']*)["']`, 'i').exec(html);
      if (metaMatch && metaMatch[1]) return metaMatch[1];
      
      return undefined;
    };
    
    // Extract title
    let title = extractMetaContent('title');
    if (!title) {
      const titleMatch = /<title>(.*?)<\/title>/i.exec(html);
      title = titleMatch ? titleMatch[1] : undefined;
    }
    
    // Extract other metadata
    const description = extractMetaContent('description');
    const imageUrl = extractMetaContent('image');
    const siteName = extractMetaContent('site_name');
    
    return {
      title,
      description,
      imageUrl,
      siteName
    };
  } catch (error: unknown) {
    throw new Error(`Error fetching preview data: ${error?.toString()}`);
  }
};

export const formatFileSize = (size: number) => {
  const i = Math.floor(Math.log(size) / Math.log(1024));
  return `${(size / Math.pow(1024, i)).toFixed(2)} ${['B', 'KB', 'MB', 'GB', 'TB'][i]}`;
}

/**
 * Detects URLs in a given text and returns them as an array
 * @param text The text to scan for URLs
 * @returns Array of URLs found in the text
 */
export const detectLinks = (text: string): string[] => {
  if (!text) return [];
  
  // Regular expression to match URLs
  // Matches http://, https://, www. URLs and also URLs starting with just a domain
  const urlRegex = /(https?:\/\/|www\.)[^\s]+\.[^\s]+/gi;
  
  // Find all matches
  const matches = text.match(urlRegex);
  
  if (!matches) return [];
  
  // Process matches to ensure they have proper format
  return matches.map(url => {
    // If URL starts with 'www.' but not with 'http', prepend 'https://'
    if (url.startsWith('www.') && !url.startsWith('http')) {
      return `https://${url}`;
    }
    return url;
  });
};