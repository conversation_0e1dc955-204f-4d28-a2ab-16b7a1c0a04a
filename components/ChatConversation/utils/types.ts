import { ReactNode } from 'react';
import { Animated, ColorValue, FlatListProps, StyleProp, TextStyle, ViewStyle } from 'react-native';

export interface Theme {
  colors: {
    primary: string;
    onPrimary: string;
    surface: string;
    onSurface: string;
    background: string;
    error: string;
    shadow: string;
    [key: string]: string;
  };
  fonts: {
    dateDividerTextStyle: StyleProp<TextStyle>;
    [key: string]: any;
  };
}

export interface User {
  id: string;
  firstName?: string;
  lastName?: string;
  [key: string]: any;
}

export namespace MessageType {
  export interface Any extends Base {
    type: 'custom' | 'file' | 'image' | 'text' | 'dateHeader';
  }

  export interface DerivedAny extends Any {
    nextMessageInGroup?: boolean;
    offset?: number;
    showName?: boolean;
    showStatus?: boolean;
  }

  export interface DerivedMessage extends DerivedAny {
    nextMessageInGroup: boolean;
    offset: number;
    showName: boolean;
    showStatus: boolean;
  }

  export interface Base {
    author: User;
    createdAt?: number;
    id: string;
    metadata?: Record<string, any>;
    status?: Status;
    type: string;
  }

  export interface Custom extends Base {
    type: 'custom';
  }

  export interface DateHeader {
    id: string;
    text: string;
    type: 'dateHeader';
  }

  export interface File extends Base {
    fileName: string;
    mimeType?: string;
    size?: number;
    type: 'file';
    uri: string;
  }

  export interface Image extends Base {
    height?: number;
    metadata?: {
      height?: number;
      width?: number;
    };
    name?: string;
    size?: number;
    type: 'image';
    uri: string;
    width?: number;
  }

  export interface PartialImage extends Partial<Image> {
    uri: string;
  }

  export interface PartialText extends Partial<Text> {
    text: string;
  }

  export interface Text extends Base {
    metadata?: Record<string, any>;
    text: string;
    type: 'text';
  }

  export type Status = 'delivered' | 'error' | 'seen' | 'sending';
}

export interface PreviewImage {
  id: string;
  uri: string;
}

export interface ChatConversationViewProps {
  chatMessages: MessageType.DerivedAny[];
  user: User;
  listPaddingBottom: Animated.AnimatedInterpolation<number>;
  showUserAvatars?: boolean;
  enableAnimation?: boolean;
  isThinking?: boolean;
  isNextPageLoading?: boolean;
  isLastPage?: boolean;
  size: { width: number; height: number };
  bottomComponentHeight: number;
  flatListProps?: Partial<FlatListProps<MessageType.DerivedAny[]>>;
  onEndReached?: () => Promise<void>;
  onMessagePress?: (message: MessageType.Any) => void;
  onMessageLongPress?: (message: MessageType.Any, event: any) => void;
  onPreviewDataFetched?: (messageId: string, previewData: any) => void;
  renderBubble?: (payload: {
    child: ReactNode;
    message: MessageType.Any;
    nextMessageInGroup: boolean;
    scale?: Animated.Value;
  }) => ReactNode;
  renderCustomMessage?: (message: MessageType.Custom, messageWidth: number) => ReactNode;
  renderFileMessage?: (message: MessageType.File, messageWidth: number) => ReactNode;
  renderImageMessage?: (message: MessageType.Image, messageWidth: number) => ReactNode;
  renderTextMessage?: (message: MessageType.Text, messageWidth: number, showName: boolean) => ReactNode;
  usePreviewData?: boolean;
  onSelectModel?: () => void;
  translateY?: Animated.AnimatedInterpolation<number>;
}

export interface MessageProps {
  enableAnimation?: boolean;
  message: MessageType.DerivedAny;
  messageWidth: number;
  roundBorder: boolean;
  showAvatar: boolean;
  showName: boolean;
  showStatus: boolean;
  showUserAvatars?: boolean;
  onMessageLongPress?: (message: MessageType.Any, event?: any) => void;
  onMessagePress?: (message: MessageType.Any, event?: any) => void;
  onPreviewDataFetched?: (messageId: string, previewData: any) => void;
  renderBubble?: (payload: {
    child: ReactNode;
    message: MessageType.Any;
    nextMessageInGroup: boolean;
    scale?: Animated.Value;
  }) => ReactNode;
  renderCustomMessage?: (message: MessageType.Custom, messageWidth: number) => ReactNode;
  renderFileMessage?: (message: MessageType.File, messageWidth: number) => ReactNode;
  renderImageMessage?: (message: MessageType.Image, messageWidth: number) => ReactNode;
  renderTextMessage?: (message: MessageType.Text, messageWidth: number, showName: boolean) => ReactNode;
  usePreviewData?: boolean;
}
