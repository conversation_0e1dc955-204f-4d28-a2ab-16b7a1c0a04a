import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../hooks';

interface ChatEmptyPlaceholderProps {
  bottomComponentHeight?: number;
  onSelectModel?: () => void;
  emptyMessage?: string;
  actionText?: string;
}

export const ChatEmptyPlaceholder: React.FC<ChatEmptyPlaceholderProps> = ({
  bottomComponentHeight = 0,
  onSelectModel,
  emptyMessage = 'No messages here yet',
  actionText = 'Load a model to chat',
}) => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      alignItems: 'center',
      flex: 1,
      justifyContent: 'center',
      marginBottom: bottomComponentHeight,
      padding: 16,
    },
    emptyText: {
      color: theme.colors.onSurface,
      fontSize: 16,
      marginBottom: 8,
      opacity: 0.5,
      textAlign: 'center',
    },
    button: {
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      paddingHorizontal: 16,
      paddingVertical: 8,
      marginTop: 16,
    },
    buttonText: {
      color: theme.colors.onPrimary,
      fontSize: 14,
      fontWeight: '600',
    },
  });

  return (
    <View style={styles.container}>
      <Text style={styles.emptyText}>{emptyMessage}</Text>
      {onSelectModel && (
        <TouchableOpacity style={styles.button} onPress={onSelectModel}>
          <Text style={styles.buttonText}>{actionText}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};
