import React from "react";
import { View, StyleSheet } from "react-native";
import { LoadingBubble } from "../LoadingBubble";

export interface IChatHeaderProps4Kid {
  isThinking: boolean;
  customContent?: React.ReactNode;
}

export const Kid: React.FC<IChatHeaderProps4Kid> = ({
  isThinking,
  customContent,
}) => {
  // The isThinking will be provided by ChatHeaderPlaceholder
  if (customContent) {
    return <>{customContent}</>;
  }

  if (!isThinking) {
    return null;
  }
  
  return (
    <View style={styles.container}>
      <LoadingBubble />
    </View>
  );
};

interface IChatHeaderProps {
  children: React.ReactElement;
}

export const ChatHeader: React.FC<IChatHeaderProps> = ({
  children,
}) => {
  // Clone the child element (Kid) and pass the required props
  return children;
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
});