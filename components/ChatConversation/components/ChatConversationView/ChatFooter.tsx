import React from "react";
import { View, StyleSheet } from "react-native";
import { CircularActivityIndicator } from "../CircularActivityIndicator";

export interface IChatFooterProps4Kid {
  isNextPageLoading: boolean;
  themeColor: string;
  customContent?: React.ReactNode;
}

export const Kid: React.FC<IChatFooterProps4Kid> = ({
  isNextPageLoading,
  themeColor,
  customContent,
}) => {
  // The isNextPageLoading and themeColor will be provided by <PERSON><PERSON><PERSON>oot<PERSON>
  if (customContent) {
    return <>{customContent}</>;
  }
  
  return isNextPageLoading ? (
    <View style={styles.footerLoadingPage}>
      <CircularActivityIndicator color={themeColor} size={16} />
    </View>
  ) : (
    <View style={styles.footer} />
  );
};

interface ChatFooterProps {
  children: React.ReactElement;
  isNextPageLoading: boolean;
  themeColor: string;
}

export const ChatFooter: React.FC<ChatFooterProps> = ({
  children,
}) => {
  return children;
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  footerLoadingPage: {
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  footer: {
    height: 16,
  },
});