import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";

export interface IChatEmptyPlaceholderProps4Kid {
  bottomComponentHeight: number;
  onSelectModel: () => void;
  emptyMessage: string;
  actionText: string;
}

export const Kid: React.FC<IChatEmptyPlaceholderProps4Kid> = ({
  bottomComponentHeight,
  onSelectModel,
  emptyMessage,
  actionText,
}) => {
  // The bottomComponentHeight and onSelectModel will be provided by ChatEmptyPlaceholder
  return (
    <View style={styles.kidContainer}>
      <Text style={styles.emptyMessage}>{emptyMessage}</Text>
      {
        !onSelectModel || (
          <TouchableOpacity>
          <Text style={styles.actionText}>{actionText}</Text>
          </TouchableOpacity>
        )
      }
    </View>
  );
};



interface ChatEmptyPlaceholderProps {
  children: React.ReactElement;
}

export const ChatEmptyPlaceholder: React.FC<ChatEmptyPlaceholderProps> = ({
  children,
}) => {
  // Clone the child element (Kid) and pass the required props
  return children;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  kidContainer: {
    alignItems: 'center',
  },
  emptyMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
    color: '#666',
  },
  actionText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
});