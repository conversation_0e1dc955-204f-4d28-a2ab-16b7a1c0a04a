import * as React from 'react';
import {
  FlatList,
  FlatListProps,
  View,
  TouchableOpacity,
  Animated,
  NativeSyntheticEvent,
  NativeScrollEvent,
  Text,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

// Import from the appropriate module paths
import { useTheme } from '../../hooks/useTheme';
import { IMessageType } from '../../interfaces/IMessageType';
import { IUser } from '../../interfaces/IUser';
import { getNodeByType, getNodes } from '../../utils/helpers'

// Component imports
import { Bubble, Message } from '../Message';
import { ChatEmptyPlaceholder } from './ChatEmptyPlaceholder';
import { ThemeProvider } from '../../contexts/ThemeProvider';

// Local imports
import { ChatFooter } from './ChatFooter';
import { ChatHeader } from './ChatHeader';
import type { IconProps } from 'react-native-vector-icons/Icon';

// Define the subcomponents

export interface ChatConversationViewProps {
  chatMessages: IMessageType.DerivedAny[];
  user: IUser;
  listPaddingBottom: Animated.AnimatedInterpolation<number>;
  showUserAvatars?: boolean;
  enableAnimation?: boolean;
  isThinking?: boolean;
  isNextPageLoading?: boolean;
  isLastPage?: boolean;
  size: { width: number; height: number };
  bottomComponentHeight: number;
  flatListProps?: Partial<FlatListProps<IMessageType.DerivedAny>>;
  onEndReached?: () => Promise<void>;
  onMessagePress?: (message: IMessageType.Any) => void;
  onMessageLongPress?: (message: IMessageType.Any, event: any) => void;
  onPreviewDataFetched?: (messageId: string, previewData: any) => void;
  renderBubble?: any;
  renderCustomMessage?: any;
  renderFileMessage?: any;
  renderImageMessage?: any;
  renderTextMessage?: any;
  usePreviewData?: boolean;
  onSelectModel?: () => void;
  translateY?: Animated.AnimatedInterpolation<number>;
}

interface ICompoundChatConversationView
  extends React.FC<React.PropsWithChildren<ChatConversationViewProps>> {
  ChatEmptyPlaceholder: typeof ChatEmptyPlaceholder;
  ChatFooter: typeof ChatFooter;
  ChatHeader: typeof ChatHeader;
  ChatBubble: typeof Bubble;
}

const MaterialCommunityIcons = Icon as unknown as React.FC<IconProps>

const MainView: React.FC<React.PropsWithChildren<ChatConversationViewProps>> = (props) => {
  const {
    chatMessages,
    user,
    listPaddingBottom,
    showUserAvatars = false,
    enableAnimation,
    isThinking = false,
    isNextPageLoading = false,
    isLastPage,
    size,
    bottomComponentHeight,
    flatListProps,
    onEndReached,
    onMessagePress,
    onMessageLongPress,
    onPreviewDataFetched,
    renderBubble,
    renderCustomMessage,
    renderFileMessage,
    renderImageMessage,
    renderTextMessage,
    usePreviewData = true,
    onSelectModel,
    translateY,
    children,
  } = props;

  const { theme } = useTheme();
  // const styles = createStyles({ theme });
  const listRef = React.useRef<FlatList<IMessageType.DerivedAny>>(null);
  const [showScrollButton, setShowScrollButton] = React.useState(false);
  const [isUserScrolling, setIsUserScrolling] = React.useState(false);

  const nodes = getNodes(children);
  const ChatEmptyPlaceholderKidProps = getNodeByType(nodes, ChatEmptyPlaceholder)?.props;
  const ChatFooterKidProps = getNodeByType(nodes, ChatFooter)?.props;
  const ChatHeaderKidProps = getNodeByType(nodes, ChatHeader)?.props;

  const chatEmptyPlaceholderElement = React.useMemo(() => {
    return ChatEmptyPlaceholderKidProps ? React.cloneElement(ChatEmptyPlaceholderKidProps.children, props) : null;
  }, [ChatEmptyPlaceholderKidProps, props]);

  const chatFooterElement = React.useMemo(() => {
    return ChatFooterKidProps ? React.cloneElement(ChatFooterKidProps.children, props) : null;
  }, [ChatFooterKidProps, props]);

  const chatHeaderElement = React.useMemo(() => {
    return ChatHeaderKidProps ? React.cloneElement(ChatHeaderKidProps.children, props) : null;
  }, [ChatHeaderKidProps, props]);

  const handleScroll = React.useCallback((event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { contentOffset } = event.nativeEvent;
    const isAtTop = contentOffset.y <= 0;
    setShowScrollButton(!isAtTop);
  }, []);

  const scrollToBottom = React.useCallback(() => {
    listRef.current?.scrollToOffset({
      animated: true,
      offset: 0,
    });
    setIsUserScrolling(false);
  }, []);

  const handleScrollBeginDrag = React.useCallback(() => {
    setIsUserScrolling(true);
  }, []);

  const handleEndReached = React.useCallback(
    async ({ distanceFromEnd }: { distanceFromEnd: number }) => {
      if (
        !onEndReached ||
        isLastPage ||
        distanceFromEnd <= 0 ||
        chatMessages.length === 0 ||
        isNextPageLoading
      ) {
        return;
      }

      await onEndReached?.();
    },
    [isLastPage, isNextPageLoading, chatMessages.length, onEndReached],
  );

  const keyExtractor = React.useCallback(
    ({ id }: IMessageType.DerivedAny) => id,
    [],
  );

  const renderMessage = React.useCallback(
    ({ item: message }: { item: IMessageType.DerivedAny; index: number }) => {
      const messageWidth =
        showUserAvatars &&
        message.type !== 'dateHeader' &&
        message.author?.id !== user.id
          ? Math.floor(Math.min(size.width * 0.9, 440))
          : Math.floor(Math.min(size.width * 0.92, 440));

      const roundBorder =
        message.type !== 'dateHeader' && message.nextMessageInGroup;
      const showAvatar =
        message.type !== 'dateHeader' && !message.nextMessageInGroup;
      const showName = message.type !== 'dateHeader' && message.showName;
      const showStatus = message.type !== 'dateHeader' && message.showStatus;

      return (
        <View>
          <Message
            {...{
              enableAnimation,
              message,
              messageWidth,
              onMessageLongPress,
              onMessagePress,
              onPreviewDataFetched,
              // renderBubble,
              // renderCustomMessage,
              // renderFileMessage,
              // renderImageMessage,
              // renderTextMessage,
              roundBorder,
              showAvatar,
              showName,
              showStatus,
              showUserAvatars,
              usePreviewData,
            }}
          >
            
          </Message>
        </View>
      );
    },
    [
      enableAnimation,
      onMessageLongPress,
      onMessagePress,
      onPreviewDataFetched,
      renderBubble,
      renderCustomMessage,
      renderFileMessage,
      renderImageMessage,
      renderTextMessage,
      showUserAvatars,
      size.width,
      usePreviewData,
      user.id,
    ],
  );

  return (
    <>
      <Animated.FlatList
        automaticallyAdjustContentInsets={false}
        contentContainerStyle={[
          // styles.flatListContentContainer,
          {
            justifyContent:
              chatMessages.length !== 0 ? undefined : 'center',
            paddingTop: listPaddingBottom,
          },
        ]}
        initialNumToRender={10}
        ListEmptyComponent={chatEmptyPlaceholderElement}
        ListFooterComponent={chatFooterElement}
        ListHeaderComponent={chatHeaderElement}
        maxToRenderPerBatch={6}
        onEndReachedThreshold={0.75}
        // style={styles.flatList}
        showsVerticalScrollIndicator={false}
        onScroll={handleScroll}
        {...flatListProps}
        data={chatMessages}
        inverted={chatMessages.length > 0}
        keyboardDismissMode="interactive"
        keyExtractor={keyExtractor}
        onEndReached={handleEndReached}
        ref={listRef}
        renderItem={renderMessage}
        onScrollBeginDrag={handleScrollBeginDrag}
        maintainVisibleContentPosition={
          isUserScrolling ? {
            minIndexForVisible: 1,
          } : undefined
        }
      />
      {showScrollButton && (
        <Animated.View style={translateY ? { transform: [{ translateY }] } : undefined}>
          <TouchableOpacity
            style={[
              // styles.scrollToBottomButton,
              { bottom: bottomComponentHeight + 20 },
            ]}
            onPress={scrollToBottom}>
            <MaterialCommunityIcons
              name="chevron-down"
              size={24}
              color={theme.colors.onPrimary}
            />
          </TouchableOpacity>
        </Animated.View>
      )}
    </>
  );
};

export const ChatConversationView = MainView as ICompoundChatConversationView;
ChatConversationView.ChatEmptyPlaceholder = ChatEmptyPlaceholder;
ChatConversationView.ChatFooter = ChatFooter;
ChatConversationView.ChatHeader = ChatHeader;