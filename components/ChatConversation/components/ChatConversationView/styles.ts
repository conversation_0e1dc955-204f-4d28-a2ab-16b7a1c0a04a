import { StyleSheet } from 'react-native';
import { ITheme } from '../../interfaces/ITheme';

export const createStyles = ({ theme }: { theme: ITheme }) =>
  StyleSheet.create({
    flatList: {
      flex: 1,
    },
    flatListContentContainer: {
      flexGrow: 1,
    },
    footer: {
      height: 16,
    },
    footerLoadingPage: {
      alignItems: 'center',
      height: 40,
      justifyContent: 'center',
    },
    scrollToBottomButton: {
      alignItems: 'center',
      backgroundColor: theme.colors.primary,
      borderRadius: 24,
      elevation: 2,
      height: 40,
      justifyContent: 'center',
      position: 'absolute',
      right: 16,
      shadowColor: theme.colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.2,
      shadowRadius: 1.41,
      width: 40,
      zIndex: 2,
    },
  });
