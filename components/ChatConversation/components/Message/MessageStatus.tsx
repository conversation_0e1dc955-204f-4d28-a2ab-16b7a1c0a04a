import * as React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { IMessageType } from '../../interfaces/IMessageType';
import { ITheme } from '../../interfaces/ITheme';
import { formatMessageTime } from '../../utils/helpers';

interface MessageStatusProps {
  message: IMessageType.DerivedMessage;
  theme: ITheme;
}

export const MessageStatus: React.FC<MessageStatusProps> = ({ message, theme }) => {
  const time = formatMessageTime(message.createdAt);
  
  const getStatusIcon = () => {
    if (message.status === 'sending') {
      return <Icon name="clock-outline" size={12} color={theme.colors.onSurfaceVariant} />;
    }
    if (message.status === 'sent') {
      return <Icon name="check" size={12} color={theme.colors.onSurfaceVariant} />;
    }
    if (message.status === 'delivered') {
      return <Icon name="check-all" size={12} color={theme.colors.onSurfaceVariant} />;
    }
    if (message.status === 'seen') {
      return <Icon name="check-all" size={12} color={theme.colors.secondary} />;
    }
    if (message.status === 'error') {
      return <Icon name="alert-circle-outline" size={12} color={theme.colors.error} />;
    }
    return null;
  };

  return (
    <View style={styles.container}>
      <Text style={[styles.timeText, { color: theme.colors.onSurfaceVariant }]}>
        {time}
      </Text>
      {getStatusIcon()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 2,
    marginRight: 4,
  },
  timeText: {
    fontSize: 10,
    marginRight: 4,
  },
});
