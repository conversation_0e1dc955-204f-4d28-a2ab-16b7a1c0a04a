import * as React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Theme, User } from '../../utils/types';

interface AvatarProps {
  author: User;
  currentUserIsAuthor: boolean;
  showAvatar: boolean;
  showUserAvatars?: boolean;
  theme: Theme;
}

export const Avatar = ({
  author,
  currentUserIsAuthor,
  showAvatar,
  showUserAvatars,
  theme,
}: AvatarProps) => {
  const initials = getUserInitials(author);
  const showActualAvatar = !currentUserIsAuthor && showUserAvatars && showAvatar;

  if (!showActualAvatar) {
    return <View style={styles.avatarSpacer} />;
  }

  const color = getUserAvatarColor(author, theme);

  return (
    <View style={[styles.avatarContainer, { backgroundColor: color }]}>
      <Text style={styles.avatarText}>{initials}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  avatarContainer: {
    alignItems: 'center',
    borderRadius: 16,
    height: 32,
    justifyContent: 'center',
    marginRight: 8,
    width: 32,
  },
  avatarSpacer: {
    width: 0,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

function getUserInitials({ firstName, lastName }: User): string {
  return `${firstName?.charAt(0) ?? ''}${lastName?.charAt(0) ?? ''}`
    .toUpperCase()
    .trim();
}

function getUserAvatarColor(user: User, theme: Theme): string {
  // Simple hash function to get consistent color based on user id
  const hash = user.id.split('').reduce((acc, char) => {
    return acc + char.charCodeAt(0);
  }, 0);
  
  // Use hash to get color from theme or fallback to primary color
  const colors = Object.values(theme.colors);
  return colors[hash % colors.length] || theme.colors.primary;
}
