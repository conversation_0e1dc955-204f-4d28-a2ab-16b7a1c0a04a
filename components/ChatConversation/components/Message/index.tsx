import * as React from 'react';
import { View, StyleSheet, Animated, Text } from 'react-native';

import { IMessageType } from '../../interfaces/IMessageType';
import { Avatar } from './Avatar';
import { Bubble } from './Bubble';
import { MessageName } from './MessageName';
import { MessageStatus } from './MessageStatus';
import { useTheme } from '../../hooks';

export interface MessageProps {
  message: IMessageType.DerivedAny;
  messageWidth: number;
  enableAnimation?: boolean;
  roundBorder?: boolean;
  showAvatar?: boolean;
  showName?: boolean;
  showStatus?: boolean;
  showUserAvatars?: boolean;
  usePreviewData?: boolean;
  onMessagePress?: (message: IMessageType.Any) => void;
  onMessageLongPress?: (message: IMessageType.Any, event: any) => void;
  onPreviewDataFetched?: (messageId: string, previewData: any) => void;
  renderBubble?: any;
  renderCustomMessage?: any;
  renderFileMessage?: any;
  renderImageMessage?: any;
  renderTextMessage?: any;
}

export const Message: React.FC<MessageProps> = ({
  message,
  messageWidth,
  enableAnimation = false,
  roundBorder = false,
  showAvatar = false,
  showName = false,
  showStatus = false,
  showUserAvatars = false,
  usePreviewData = false,
  onMessagePress,
  onMessageLongPress,
  onPreviewDataFetched,
  renderBubble,
  renderCustomMessage,
  renderFileMessage,
  renderImageMessage,
  renderTextMessage,
}) => {
  const { theme } = useTheme();
  const fadeAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (enableAnimation) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      fadeAnim.setValue(1);
    }
  }, [enableAnimation, fadeAnim]);

  // For DateHeader type messages
  if (message.type === 'dateHeader') {
    return (
      <View style={styles.dateHeader}>
        {message.text}
      </View>
    );
  }

  const isUser = message.author.id === 'user'; // Assuming 'user' is the ID for the current user
  const currentUserIsAuthor = isUser;

  return (
    <Animated.View
      style={[
        styles.container,
        { opacity: fadeAnim },
        currentUserIsAuthor ? styles.userMessageContainer : styles.botMessageContainer,
      ]}
    >
      <Avatar
        author={message.author}
        currentUserIsAuthor={currentUserIsAuthor}
        showAvatar={showAvatar}
        showUserAvatars={showUserAvatars}
        theme={theme}
      />

      <View style={styles.messageContent}>
        {showName && <MessageName author={message.author} theme={theme} />}
        
        <Text>{message.type} ttttttt</Text>
        <Bubble
          message={message}
          messageWidth={messageWidth}
          roundBorder={roundBorder}
          theme={theme}
          currentUserIsAuthor={currentUserIsAuthor}
          onMessagePress={onMessagePress}
          onMessageLongPress={onMessageLongPress}
          onPreviewDataFetched={onPreviewDataFetched}
          renderBubble={renderBubble}
          renderCustomMessage={renderCustomMessage}
          renderFileMessage={renderFileMessage}
          renderImageMessage={renderImageMessage}
          renderTextMessage={renderTextMessage}
          usePreviewData={usePreviewData}
        />

        {showStatus && <MessageStatus message={message} theme={theme} />}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginVertical: 2,
    paddingHorizontal: 16,
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  botMessageContainer: {
    justifyContent: 'flex-start',
  },
  messageContent: {
    maxWidth: '80%',
  },
  dateHeader: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    opacity: 0.7,
  },
});

export * from './Avatar';
export * from './Bubble';
export * from './MessageName';
export * from './MessageStatus';
