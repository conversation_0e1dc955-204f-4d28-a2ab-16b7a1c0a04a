import * as React from 'react';
import { StyleSheet, Image, View, ActivityIndicator, TouchableOpacity, Text } from 'react-native';
import type { IMessageType } from '../../interfaces/IMessageType';
import type { ITheme } from '../../interfaces/ITheme';

interface IImageMessageProps4Kid {
  message: IMessageType.DerivedImage;
  theme: ITheme;
}

export const Kid: React.FC<IImageMessageProps4Kid> = ({ message, theme }) => {
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(false);
  const imageSource = { uri: message.uri || '' };
  
  const handleLoadStart = () => {
    setLoading(true);
    setError(false);
  };
  
  const handleLoadEnd = () => {
    setLoading(false);
  };
  
  const handleError = () => {
    setLoading(false);
    setError(true);
  };

  const handleImagePress = () => {
    // Handle image fullscreen preview
    // Implement your image viewer logic here
    console.log('Image pressed:', message.uri);
  };
  
  return (
    <TouchableOpacity 
      onPress={handleImagePress} 
      activeOpacity={0.9}
      disabled={loading || error}
    >
      <View style={styles.container}>
        <Image
          source={imageSource}
          style={styles.image}
          resizeMode="cover"
          onLoadStart={handleLoadStart}
          onLoadEnd={handleLoadEnd}
          onError={handleError}
        />
        
        {loading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="small" color={theme.colors.primary} />
          </View>
        )}
        
        {error && (
          <View style={styles.errorOverlay}>
            <Image 
              source={require('@/assets/images/something-went-wrong.svg')}
              style={styles.errorIcon}
            />
          </View>
        )}
      </View>
      
      {message.caption && (
        <View style={styles.captionContainer}>
          <Text style={[styles.caption, { color: theme.colors.onSurface }]}>
            {message.caption}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};


interface IImageMessageProps {
  children: React.ReactElement;
}
export const ImageMessage: React.FC<IImageMessageProps> = ({children}) => {
  return children;
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: '100%',
    height: 200,
    borderRadius: 8,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorIcon: {
    width: 48,
    height: 48,
    tintColor: '#999',
  },
  captionContainer: {
    marginTop: 4,
  },
  caption: {
    fontSize: 14,
  },
});
