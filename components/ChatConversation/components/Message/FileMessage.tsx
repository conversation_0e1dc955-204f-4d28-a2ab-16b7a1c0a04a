import * as React from 'react';
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { IMessageType } from '../../interfaces/IMessageType';
import { ITheme } from '../../interfaces/ITheme';
import { formatFileSize } from '../../utils/helpers';
import type { IconProps } from 'react-native-vector-icons/Icon';

interface IFileMessageProps4Kid {
  message: IMessageType.DerivedFile;
  theme: ITheme;
}

const MaterialCommunityIcons = Icon as unknown as React.FC<IconProps>

export const Kid: React.FC<IFileMessageProps4Kid> = ({ message, theme }) => {
  const getFileIcon = () => {
    return 'file-document-edit-outline';
  };

  const handleFilePress = () => {
    // Add logic to open or download the file
    console.log('File pressed:', message.name);
  };


  const fileIcon = getFileIcon();
  const fileSize = formatFileSize(message.size || 0);

  return (
    <TouchableOpacity onPress={handleFilePress} activeOpacity={0.7}>
      <View style={styles.container}>
        <View style={[styles.iconContainer, { backgroundColor: theme.colors.surfaceVariant }]}>
          <MaterialCommunityIcons name={fileIcon} size={24} color={theme.colors.primary} />
        </View>
        
        <View style={styles.fileInfo}>
          <Text 
            style={[styles.fileName, { color: theme.colors.onSurface }]} 
            numberOfLines={1}
          >
            {message.name}
          </Text>
          
          <Text style={[styles.fileSize, { color: theme.colors.onSurfaceVariant }]}>
            {fileSize}
          </Text>
        </View>
        
        <MaterialCommunityIcons name="download" size={20} color={theme.colors.primary} />
      </View>
    </TouchableOpacity>
  );
};

interface IFileMessageProps {
  children: React.ReactElement;
}
export const FileMessage: React.FC<IFileMessageProps> = ({children}) => {
  return children;
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  fileInfo: {
    flex: 1,
    marginRight: 8,
  },
  fileName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  fileSize: {
    fontSize: 12,
  },
});
