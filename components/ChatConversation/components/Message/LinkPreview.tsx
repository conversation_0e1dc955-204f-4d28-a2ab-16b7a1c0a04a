import * as React from 'react';
import { StyleSheet, Text, View, Image, TouchableOpacity, Linking } from 'react-native';
import { ITheme } from '../../interfaces/ITheme';
import { fetchPreviewData } from '../../utils/helpers';

interface LinkPreviewProps {
  url: string;
  messageId: string;
  theme: ITheme;
  onPreviewDataFetched?: (messageId: string, previewData: any) => void;
}

interface PreviewData {
  title?: string;
  description?: string;
  imageUrl?: string;
  siteName?: string;
}

export const LinkPreview: React.FC<LinkPreviewProps> = ({
  url,
  messageId,
  theme,
  onPreviewDataFetched,
}) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [previewData, setPreviewData] = React.useState<PreviewData | null>(null);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const loadPreviewData = async () => {
      if (!url) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        const data = await fetchPreviewData(url);
        setPreviewData(data);
        onPreviewDataFetched?.(messageId, data);
      } catch (err) {
        setError('Failed to load preview');
        console.error('Link preview error:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPreviewData();
  }, [url, messageId, onPreviewDataFetched]);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={{ color: theme.colors.onSurfaceVariant }}>Loading preview...</Text>
      </View>
    );
  }

  if (error || !previewData) {
    return null;
  }

  const handlePress = () => {
    Linking.openURL(url).catch(err => console.error('Failed to open URL:', err));
  };

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      {previewData.imageUrl && (
        <Image
          source={{ uri: previewData.imageUrl }}
          style={styles.previewImage}
          resizeMode="cover"
        />
      )}
      
      <View style={styles.textContainer}>
        {previewData.siteName && (
          <Text 
            style={[styles.siteName, { color: theme.colors.secondary }]}
            numberOfLines={1}
          >
            {previewData.siteName}
          </Text>
        )}
        
        {previewData.title && (
          <Text 
            style={[styles.title, { color: theme.colors.onSurface }]}
            numberOfLines={2}
          >
            {previewData.title}
          </Text>
        )}
        
        {previewData.description && (
          <Text 
            style={[styles.description, { color: theme.colors.onSurfaceVariant }]}
            numberOfLines={2}
          >
            {previewData.description}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  loadingContainer: {
    padding: 8,
    alignItems: 'center',
  },
  previewImage: {
    height: 150,
    width: '100%',
  },
  textContainer: {
    padding: 8,
  },
  siteName: {
    fontSize: 12,
    marginBottom: 2,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  description: {
    fontSize: 12,
  },
});
