import * as React from 'react';
import { StyleSheet, TouchableOpacity, View, Text } from 'react-native';

import type { IMessageType } from '../../interfaces/IMessageType';
import type { ITheme } from '../../interfaces/ITheme';
import { TextMessage } from './TextMessage';
import { ImageMessage } from './ImageMessage';
import { FileMessage } from './FileMessage';
import { CustomMessage } from './CustomMessage';
import { getNodes } from '../../utils/helpers';

interface IBubbleProps {
  message: IMessageType.DerivedAny;
  messageWidth: number;
  roundBorder: boolean;
  theme: ITheme;
  currentUserIsAuthor: boolean;
  onMessagePress?: (message: IMessageType.Any) => void;
  onMessageLongPress?: (message: IMessageType.Any, event: any) => void;
  onPreviewDataFetched?: (messageId: string, previewData: any) => void;
  renderBubble?: any;
  renderCustomMessage?: any;
  renderFileMessage?: any;
  renderImageMessage?: any;
  renderTextMessage?: any;
  usePreviewData?: boolean;
}

interface IBubble
  extends React.FC<React.PropsWithChildren<IBubbleProps>> {
  TextMessage: typeof TextMessage;
  ImageMessage: typeof ImageMessage;
  FileMessage: typeof FileMessage;
  CustomMessage: typeof CustomMessage;
}

export const MainView: React.FC<React.PropsWithChildren<IBubbleProps>> = (props) => {
  const {
    message,
    messageWidth,
    roundBorder,
    theme,
    currentUserIsAuthor,
    onMessagePress,
    onMessageLongPress,
    onPreviewDataFetched,
    renderBubble,
    renderTextMessage,
    children
  } = props;

  const nodes = getNodes(children);
  const textMessageProps = nodes.find(n => n.type === TextMessage)?.props;
  const imageMessageProps = nodes.find(n => n.type === ImageMessage)?.props;
  const fileMessageProps = nodes.find(n => n.type === FileMessage)?.props;
  const customMessageProps = nodes.find(n => n.type === CustomMessage)?.props;

  const defaultMessageElement = React.useMemo(() => {
    return <Text style={[
      styles.text,
      { color: currentUserIsAuthor ? theme.colors.onPrimary : theme.colors.onSurface }
    ]}>
      Unsupported message type
    </Text>;
  }, [currentUserIsAuthor, theme]);

  const messageElement = React.useMemo(() => {
    switch (message.type) {
      case 'text':
        return textMessageProps ? React.cloneElement(textMessageProps.children, props) : null;
      case 'image':
        return imageMessageProps ? React.cloneElement(imageMessageProps.children, props) : null;
      case 'file':
        return fileMessageProps ? React.cloneElement(fileMessageProps.children, props) : null;
      case 'custom':
        return customMessageProps ? React.cloneElement(customMessageProps.children, props) : null;
      default:
        return defaultMessageElement;
    }
  }, [textMessageProps, imageMessageProps, fileMessageProps, customMessageProps, defaultMessageElement, message.type]);

  const handlePress = () => {
    onMessagePress?.(message);
  };

  const handleLongPress = (event: any) => {
    onMessageLongPress?.(message, event);
  };

  // Custom bubble renderer
  if (renderBubble) {
    return renderBubble({
      message,
      messageWidth,
      roundBorder,
      currentUserIsAuthor,
    });
  }

  const bubbleStyle = [
    styles.bubble,
    {
      borderTopLeftRadius: !currentUserIsAuthor && !roundBorder ? 4 : 16,
      borderTopRightRadius: currentUserIsAuthor && !roundBorder ? 4 : 16,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      backgroundColor: currentUserIsAuthor
        ? theme.colors.primary
        : theme.colors.surface,
    },
  ];


  return (
    <TouchableOpacity
      onPress={handlePress}
      onLongPress={handleLongPress}
      delayLongPress={500}
      activeOpacity={0.8}
    >
      <View style={[bubbleStyle, { maxWidth: messageWidth }]}>
        {messageElement}
      </View>
    </TouchableOpacity>
  );
};

export const Bubble = MainView as IBubble;
Bubble.TextMessage = TextMessage;
Bubble.ImageMessage = ImageMessage;
Bubble.FileMessage = FileMessage;
Bubble.CustomMessage = CustomMessage;

const styles = StyleSheet.create({
  bubble: {
    borderRadius: 16,
    padding: 12,
    marginVertical: 2,
  },
  text: {
    fontSize: 16,
    lineHeight: 22,
  },
});
