import * as React from 'react';
import { StyleSheet, Text } from 'react-native';
import { ITheme } from '../../interfaces/ITheme';
import { IUser } from '../../interfaces/IUser';
import { getUserName } from '../../utils/helpers';

interface MessageNameProps {
  author: IUser;
  theme: ITheme;
}

export const MessageName: React.FC<MessageNameProps> = ({ author, theme }) => {
  const name = getUserName(author);
  
  if (!name) return null;
  
  return (
    <Text style={[styles.nameText, { color: theme.colors.secondary }]}>
      {name}
    </Text>
  );
};

const styles = StyleSheet.create({
  nameText: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 2,
    marginLeft: 8,
  },
});
