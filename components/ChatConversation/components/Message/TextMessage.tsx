import * as React from 'react';
import { StyleSheet, Text, View, Linking } from 'react-native';

import { IMessageType } from '../../interfaces/IMessageType';
import { ITheme } from '../../interfaces/ITheme';
import { LinkPreview } from './LinkPreview';
import { detectLinks } from '../../utils/helpers';

interface ITextMessageProps4Kid {
  message: IMessageType.DerivedText;
  theme: ITheme;
  currentUserIsAuthor: boolean;
  usePreviewData?: boolean;
  onPreviewDataFetched?: (messageId: string, previewData: any) => void;
}

export const Kid: React.FC<ITextMessageProps4Kid> = ({
  message,
  theme,
  currentUserIsAuthor,
  usePreviewData = false,
  onPreviewDataFetched,
}) => {
  const [links, setLinks] = React.useState<string[]>([]);
  const [showPreview, setShowPreview] = React.useState(false);

  React.useEffect(() => {
    if (message.text && usePreviewData) {
      const detectedLinks = detectLinks(message.text);
      setLinks(detectedLinks);
      setShowPreview(detectedLinks.length > 0);
    }
  }, [message.text, usePreviewData]);

  const handleUrlPress = (url: string) => {
    Linking.canOpenURL(url).then(supported => {
      if (supported) {
        Linking.openURL(url);
      }
    });
  };

  const textStyle = {
    color: currentUserIsAuthor ? theme.colors.onPrimary : theme.colors.onSurface,
  };

  return (
    <View>
      <Text 
        style={[styles.text, textStyle]}
        selectable={true}
      >
        {message.text}
      </Text>

      {showPreview && links.length > 0 && (
        <LinkPreview 
          url={links[0]}
          messageId={message.id}
          onPreviewDataFetched={onPreviewDataFetched}
          theme={theme}
        />
      )}
    </View>
  );
};

interface ITextMessageProps {
  children: React.ReactElement;
}

export const TextMessage: React.FC<ITextMessageProps> = ({
  children,
}) => {
  return children;
};

const styles = StyleSheet.create({
  text: {
    fontSize: 16,
    lineHeight: 22,
  },
});
