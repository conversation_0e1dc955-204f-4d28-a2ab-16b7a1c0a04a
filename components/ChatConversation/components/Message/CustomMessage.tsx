import * as React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { IMessageType } from '../../interfaces/IMessageType';
import { ITheme } from '../../interfaces/ITheme';

interface CustomMessageProps {
  message: IMessageType.DerivedCustom;
  theme: ITheme;
}

export const CustomMessage: React.FC<CustomMessageProps> = ({ message, theme }) => {
  // Fallback rendering for custom messages
  // Typically, custom messages would be handled by a custom renderer provided by the parent component
  
  return (
    <View style={styles.container}>
      <Text style={[styles.text, { color: theme.colors.onSurface }]}>
        {message.type === 'custom'
          ? 'Custom Message'
          : 'Not a Custom Message'}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 8,
  },
  text: {
    fontSize: 14,
  },
});
