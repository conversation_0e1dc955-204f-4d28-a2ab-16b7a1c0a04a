import { StyleSheet } from 'react-native';
import { MessageType, Theme } from '../../utils/types';

interface StyleProps {
  currentUserIsAuthor: boolean;
  message: MessageType.DerivedAny;
  messageWidth: number;
  roundBorder: boolean;
  theme: Theme;
}

const createStyles = ({
  currentUserIsAuthor,
  message,
  messageWidth,
  roundBorder,
  theme,
}: StyleProps) =>
  StyleSheet.create({
    container: {
      alignItems: 'flex-end',
      flexDirection: currentUserIsAuthor ? 'row-reverse' : 'row',
      marginVertical: 4,
      paddingHorizontal: 8,
    },
    contentContainer: {
      backgroundColor: currentUserIsAuthor 
        ? theme.colors.primary 
        : theme.colors.surface,
      borderBottomLeftRadius: !currentUserIsAuthor || roundBorder ? 20 : 4,
      borderBottomRightRadius: currentUserIsAuthor || roundBorder ? 20 : 4,
      borderTopLeftRadius: !currentUserIsAuthor ? 4 : 20,
      borderTopRightRadius: currentUserIsAuthor ? 4 : 20,
      overflow: 'hidden',
      width: messageWidth,
    },
    dateHeader: {
      alignItems: 'center',
      marginVertical: 16,
      width: '100%',
    },
    pressable: {
      maxWidth: '80%',
    },
  });

export default createStyles;
