import React from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { useTheme } from '../hooks';

export const LoadingBubble: React.FC = () => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      alignSelf: 'flex-start',
      backgroundColor: theme.colors.surface,
      borderRadius: 20,
      marginBottom: 8,
      marginLeft: 8,
      marginRight: 40,
      marginTop: 8,
      padding: 16,
    },
  });

  return (
    <View style={styles.container}>
      <ActivityIndicator color={theme.colors.primary} size="small" />
    </View>
  );
};
