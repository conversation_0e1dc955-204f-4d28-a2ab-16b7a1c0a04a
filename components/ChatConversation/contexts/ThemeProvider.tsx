import React, { createContext, useState, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import { lightTheme, darkTheme } from '../utils/theme';
import { ITheme } from '../interfaces/ITheme';

type ThemeContextType = {
  theme: ITheme;
  isDark: boolean;
  toggleTheme: () => void;
  setTheme: (isDark: boolean) => void;
};

// Create context with default values
export const ThemeContext = createContext<ThemeContextType>({
  theme: lightTheme,
  isDark: false,
  toggleTheme: () => {},
  setTheme: () => {},
});

interface ThemeProviderProps {
  children: React.ReactNode;
  initialTheme?: 'light' | 'dark' | 'system';
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  initialTheme = 'system',
}) => {
  const colorScheme = useColorScheme();
  const [isDark, setIsDark] = useState<boolean>(() => {
    if (initialTheme === 'system') {
      return colorScheme === 'dark';
    }
    return initialTheme === 'dark';
  });

  // Update theme when system theme changes if using system setting
  useEffect(() => {
    if (initialTheme === 'system') {
      setIsDark(colorScheme === 'dark');
    }
  }, [colorScheme, initialTheme]);

  const theme = isDark ? darkTheme : lightTheme;

  const toggleTheme = () => {
    setIsDark(prev => !prev);
  };

  const setTheme = (dark: boolean) => {
    setIsDark(dark);
  };

  return (
    <ThemeContext.Provider value={{ theme, isDark, toggleTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

