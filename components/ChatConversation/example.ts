import { ChatConversation } from './ChatConversation';

/**
 * Example AI service implementation (OpenAI)
 * Replace with actual implementation for your project
 */
class ExampleOpenAIService {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async getCompletion(messages: any[], config: any): Promise<string> {
    try {
      // Example implementation - replace with actual OpenAI API call
      console.log('Sending to OpenAI:', { messages, config });
      
      // Simulated API call
      return "This is a simulated response. Replace with actual API integration.";
    } catch (error) {
      console.error('OpenAI API error:', error);
      throw new Error('Failed to communicate with AI service');
    }
  }
}

// Example usage
async function example() {
  // Create the AI service with your API key
  const aiService = new ExampleOpenAIService('your-api-key');
  
  // Create the conversation module
  const conversation = new ChatConversation(aiService, {
    initialSystemPrompt: "You are a helpful assistant.",
    maxHistoryLength: 10
  });
  
  // Example conversation
  try {
    const response1 = await conversation.generateResponse("Hello, who are you?");
    console.log("Assistant:", response1);
    
    const response2 = await conversation.generateResponse("What can you help me with?");
    console.log("Assistant:", response2);
    
    // View history
    console.log("Conversation history:", conversation.getHistory());
  } catch (error) {
    console.error("Error in conversation:", error);
  }
}

// Run the example (uncomment to execute)
// example();
