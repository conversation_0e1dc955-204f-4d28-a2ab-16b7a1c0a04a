# ChatConversation Module

A reusable module for managing AI chat conversations with history tracking.

## Features

- Maintains conversation history with configurable length
- Supports system prompts
- Configurable model parameters
- Clean separation from specific AI implementations

## Usage

```typescript
import { ChatConversation } from './modules/ChatConversation';
import { YourAIService } from './your-ai-service';

// Create an AI service implementation
const aiService = new YourAIService();

// Initialize the conversation module
const conversation = new ChatConversation(aiService, {
  initialSystemPrompt: "You are a friendly assistant named <PERSON>.",
  maxHistoryLength: 15,
  modelConfig: {
    temperature: 0.8,
    maxTokens: 1500
  }
});

// Use the conversation
async function chat() {
  const response = await conversation.generateResponse("Hello, how are you today?");
  console.log(response);
  
  // Get the current conversation history
  const history = conversation.getHistory();
  
  // Clear the conversation if needed
  // conversation.clearHistory();
}
```

## AI Service Interface

Your AI service must implement a `getCompletion` method that takes the message history and model configuration:

```typescript
interface AIService {
  getCompletion(
    messages: Array<{role: string, content: string}>,
    config: {temperature: number, maxTokens: number}
  ): Promise<string>;
}
```

## Customization

The module is designed to be flexible and work with different AI providers. Simply implement the AI service interface for your specific provider (OpenAI, Anthropic, etc.) and pass it to the ChatConversation constructor.


## Digrams
# ChatConversation Module Structure Diagrams

Here are several Mermaid diagrams to help you understand the overall implementation and design of the `src/modules/ChatConversation` module.

## 1. Component Hierarchy Diagram

```mermaid
graph TD
    A[ChatConversationView] --> B[FlatList of Messages]
    A --> C[Scroll Button]
    B --> D[Message Component]
    B --> E[LoadingBubble]
    B --> F[ChatEmptyPlaceholder]
    D --> G[Avatar]
    D --> H[Bubble]
    D --> I[MessageName]
    D --> J[MessageStatus]
    H --> K1[TextMessage]
    H --> K2[ImageMessage]
    H --> K3[FileMessage]
    H --> K4[CustomMessage]
    K1 --> L[LinkPreview]
```

## 2. Message Type Structure

```mermaid
classDiagram
    class MessageType {
        <<namespace>>
    }
    
    class Any {
        id: string
        text: string
        createdAt: Date
        author: User
        type: string
        status: string
    }
    
    class DerivedAny {
        id: string
        text: string
        createdAt: Date
        author: User
        type: string
        status: string
        showName: boolean
        showStatus: boolean
        nextMessageInGroup: boolean
    }
    
    class DerivedText {
        type: "text"
        text: string
    }
    
    class DerivedImage {
        type: "image"
        image: ImageData
        caption?: string
    }
    
    class DerivedFile {
        type: "file"
        file: FileData
    }
    
    class DerivedCustom {
        type: "custom"
        customData: any
    }
    
    MessageType --> Any
    MessageType --> DerivedAny
    DerivedAny <|-- DerivedText
    DerivedAny <|-- DerivedImage
    DerivedAny <|-- DerivedFile
    DerivedAny <|-- DerivedCustom
```

## 3. Data Flow Diagram

```mermaid
flowchart TD
    A[User Input] --> B[Chat Service]
    B --> C[Message Processing]
    C --> D[Update Message State]
    D --> E[ChatConversationView]
    E --> F[Render Messages]
    
    G[API Response] --> H[Message Parser]
    H --> C
    
    subgraph "Message Rendering"
    F --> I[Text/Image/File/Custom]
    I --> J[Link Preview if URL detected]
    end
    
    K[Scroll Events] --> L[UI Updates]
    L --> E
```

## 4. Message Status Flow

```mermaid
stateDiagram-v2
    [*] --> Sending
    Sending --> Sent: Message Delivered to Server
    Sent --> Delivered: Message Delivered to Recipient
    Delivered --> Read: Message Read by Recipient
    Sending --> Error: Failed to Send
    Error --> [*]
    Read --> [*]
```

## 5. ChatConversationView Props Flow

```mermaid
flowchart LR
    A[Parent Component] --> |Props| B[ChatConversationView]
    B --> |Props| C[Message Component]
    
    subgraph "Key Props"
    D[chatMessages]
    E[user]
    F[renderXXX Functions]
    G[onMessage Callbacks]
    H[usePreviewData]
    end
    
    D --> B
    E --> B
    F --> B
    G --> B
    H --> B
    
    B --> |Message Props| C
```

These diagrams should help you understand the component structure, data flow, and message handling in the ChatConversation module. The implementation follows a component-based architecture with clear separation of concerns between different message types and rendering logic.


---
## ChatConversationView

### Component wireframe

┌─────────────────────────────────────────────────────────────────┐
│                     ChatConversationView                        │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                    Animated.FlatList                     │   │
│  │                                                         │   │
│  │  ┌─────────────────────────────────────────────────┐   │   │
│  │  │          ListHeaderComponent                    │   │   │
│  │  │       (renderListHeaderComponent)               │   │   │
│  │  │  ┌───────────────────────────────────────────┐  │   │   │
│  │  │  │         LoadingBubble (if isThinking)     │  │   │   │
│  │  │  └───────────────────────────────────────────┘  │   │   │
│  │  └─────────────────────────────────────────────────┘   │   │
│  │                                                         │   │
│  │  ┌─────────────────────────────────────────────────┐   │   │
│  │  │          ListEmptyComponent                     │   │   │
│  │  │      (renderListEmptyComponent)                 │   │   │
│  │  │  ┌───────────────────────────────────────────┐  │   │   │
│  │  │  │         ChatEmptyPlaceholder              │  │   │   │
│  │  │  └───────────────────────────────────────────┘  │   │   │
│  │  └─────────────────────────────────────────────────┘   │   │
│  │                                                         │   │
│  │  ┌─────────────────────────────────────────────────┐   │   │
│  │  │          Message Items (renderMessage)          │   │   │
│  │  │  ┌───────────────────────────────────────────┐  │   │   │
│  │  │  │ Message Component                         │  │   │   │
│  │  │  │                                           │  │   │   │
│  │  │  │ ├─ TextMessage (if type === 'text')       │  │   │   │
│  │  │  │ ├─ ImageMessage (if type === 'image')     │  │   │   │
│  │  │  │ ├─ FileMessage (if type === 'file')       │  │   │   │
│  │  │  │ └─ CustomMessage (if type === 'custom')   │  │   │   │
│  │  │  └───────────────────────────────────────────┘  │   │   │
│  │  └─────────────────────────────────────────────────┘   │   │
│  │                                                         │   │
│  │  ┌─────────────────────────────────────────────────┐   │   │
│  │  │          ListFooterComponent                    │   │   │
│  │  │      (renderListFooterComponent)                │   │   │
│  │  │                                                 │   │   │
│  │  │  ┌───────────────────────────────────────────┐  │   │   │
│  │  │  │ CircularActivityIndicator (if loading)    │  │   │   │
│  │  │  │               OR                          │  │   │   │
│  │  │  │ Empty Footer View                         │  │   │   │
│  │  │  └───────────────────────────────────────────┘  │   │   │
│  │  └─────────────────────────────────────────────────┘   │   │
│  │                                                         │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │          Scroll To Bottom Button                        │   │
│  │        (Visible when not at bottom)                     │   │
│  │  ┌───────────────────────────────────────────────────┐  │   │
│  │  │         Down Arrow Icon                           │  │   │
│  │  └───────────────────────────────────────────────────┘  │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## Component Renderers and Their Responsibilities

1. **renderListHeaderComponent**
   - Renders the `LoadingBubble` when `isThinking` is true
   - Shown at the top of the inverted list (actually bottom of the chat)

2. **renderListEmptyComponent**
   - Renders `ChatEmptyPlaceholder` when there are no messages
   - Provides UI for when the chat is empty

3. **renderMessage**
   - Handles individual message rendering
   - Configures props like message width, border style, etc.
   - Passes the message to the `Message` component

4. **renderListFooterComponent**
   - Shows loading indicator when fetching more messages (`isNextPageLoading`)
   - Otherwise shows an empty footer view

5. **Message Component Renderers**
   - Can use custom renderers passed through props:
     - `renderBubble`
     - `renderTextMessage`
     - `renderImageMessage`
     - `renderFileMessage`
     - `renderCustomMessage`
   - Falls back to default components if custom renderers aren't provided

The entire structure creates a chat interface with support for different message types, loading states, and custom rendering capabilities.┌─────────────────────────────────────────────────────────────────┐
│                     ChatConversationView                        │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                    Animated.FlatList                     │   │
│  │                                                         │   │
│  │  ┌─────────────────────────────────────────────────┐   │   │
│  │  │          ListHeaderComponent                    │   │   │
│  │  │       (renderListHeaderComponent)               │   │   │
│  │  │  ┌───────────────────────────────────────────┐  │   │   │
│  │  │  │         LoadingBubble (if isThinking)     │  │   │   │
│  │  │  └───────────────────────────────────────────┘  │   │   │
│  │  └─────────────────────────────────────────────────┘   │   │
│  │                                                         │   │
│  │  ┌─────────────────────────────────────────────────┐   │   │
│  │  │          ListEmptyComponent                     │   │   │
│  │  │      (renderListEmptyComponent)                 │   │   │
│  │  │  ┌───────────────────────────────────────────┐  │   │   │
│  │  │  │         ChatEmptyPlaceholder              │  │   │   │
│  │  │  └───────────────────────────────────────────┘  │   │   │
│  │  └─────────────────────────────────────────────────┘   │   │
│  │                                                         │   │
│  │  ┌─────────────────────────────────────────────────┐   │   │
│  │  │          Message Items (renderMessage)          │   │   │
│  │  │  ┌───────────────────────────────────────────┐  │   │   │
│  │  │  │ Message Component                         │  │   │   │
│  │  │  │                                           │  │   │   │
│  │  │  │ ├─ TextMessage (if type === 'text')       │  │   │   │
│  │  │  │ ├─ ImageMessage (if type === 'image')     │  │   │   │
│  │  │  │ ├─ FileMessage (if type === 'file')       │  │   │   │
│  │  │  │ └─ CustomMessage (if type === 'custom')   │  │   │   │
│  │  │  └───────────────────────────────────────────┘  │   │   │
│  │  └─────────────────────────────────────────────────┘   │   │
│  │                                                         │   │
│  │  ┌─────────────────────────────────────────────────┐   │   │
│  │  │          ListFooterComponent                    │   │   │
│  │  │      (renderListFooterComponent)                │   │   │
│  │  │                                                 │   │   │
│  │  │  ┌───────────────────────────────────────────┐  │   │   │
│  │  │  │ CircularActivityIndicator (if loading)    │  │   │   │
│  │  │  │               OR                          │  │   │   │
│  │  │  │ Empty Footer View                         │  │   │   │
│  │  │  └───────────────────────────────────────────┘  │   │   │
│  │  └─────────────────────────────────────────────────┘   │   │
│  │                                                         │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │          Scroll To Bottom Button                        │   │
│  │        (Visible when not at bottom)                     │   │
│  │  ┌───────────────────────────────────────────────────┐  │   │
│  │  │         Down Arrow Icon                           │  │   │
│  │  └───────────────────────────────────────────────────┘  │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## Component Renderers and Their Responsibilities

1. **renderListHeaderComponent**
   - Renders the `LoadingBubble` when `isThinking` is true
   - Shown at the top of the inverted list (actually bottom of the chat)

2. **renderListEmptyComponent**
   - Renders `ChatEmptyPlaceholder` when there are no messages
   - Provides UI for when the chat is empty

3. **renderMessage**
   - Handles individual message rendering
   - Configures props like message width, border style, etc.
   - Passes the message to the `Message` component

4. **renderListFooterComponent**
   - Shows loading indicator when fetching more messages (`isNextPageLoading`)
   - Otherwise shows an empty footer view

5. **Message Component Renderers**
   - Can use custom renderers passed through props:
     - `renderBubble`
     - `renderTextMessage`
     - `renderImageMessage`
     - `renderFileMessage`
     - `renderCustomMessage`
   - Falls back to default components if custom renderers aren't provided

The entire structure creates a chat interface with support for different message types, loading states, and custom rendering capabilities.
