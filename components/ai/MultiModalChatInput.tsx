import React, { useState, useRef } from 'react';
import { View, TextInput, Pressable, StyleSheet, Alert } from 'react-native';
import { SendIcon, ImageIcon, CameraIcon, MicIcon } from 'lucide-react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import * as ImagePicker from 'expo-image-picker';
import { Text } from '@/components/ui/text';
import VoiceInputButton from '@/components/voice/VoiceInputButton';
import { voiceInputService, type VoiceResult } from '@/lib/services/voice-input.service';

interface MultiModalInput {
  text?: string;
  image?: string; // Base64 or URI
  voice?: VoiceResult;
  type: 'text' | 'voice' | 'image' | 'multimodal';
}

interface MultiModalChatInputProps {
  onSendMessage: (input: MultiModalInput) => void;
  placeholder?: string;
  disabled?: boolean;
  maxLength?: number;
  showVoiceInput?: boolean;
  showImageInput?: boolean;
  className?: string;
}

/**
 * Multi-Modal AI Chat Input Component
 * Supports text, voice, and image inputs with smooth animations
 * Following Rule 8: Animation Standards and Rule 6: UI Design Consistency
 */
export default function MultiModalChatInput({
  onSendMessage,
  placeholder = "Ask me anything...",
  disabled = false,
  maxLength = 1000,
  showVoiceInput = true,
  showImageInput = true,
  className = '',
}: MultiModalChatInputProps) {
  const [text, setText] = useState('');
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isVoiceMode, setIsVoiceMode] = useState(false);
  const [currentVoiceResult, setCurrentVoiceResult] = useState<VoiceResult | null>(null);

  // Animation values
  const inputHeight = useSharedValue(50);
  const sendButtonScale = useSharedValue(1);
  const voiceModeOpacity = useSharedValue(0);
  const imagePreviewScale = useSharedValue(0);

  // Refs
  const textInputRef = useRef<TextInput>(null);

  // Check if we can send a message
  const canSend = text.trim().length > 0 || selectedImage || currentVoiceResult;

  const handleTextChange = (newText: string) => {
    setText(newText);
    
    // Animate input height based on content
    const lines = newText.split('\n').length;
    const newHeight = Math.min(Math.max(50, lines * 20 + 30), 120);
    inputHeight.value = withSpring(newHeight);
  };

  const handleVoiceResult = (result: VoiceResult) => {
    setCurrentVoiceResult(result);
    
    if (result.isFinal) {
      // Auto-send voice messages or combine with text
      if (text.trim().length === 0) {
        // Send voice-only message
        const input: MultiModalInput = {
          voice: result,
          type: 'voice',
        };
        onSendMessage(input);
        setCurrentVoiceResult(null);
      } else {
        // Append to text input
        setText(prev => prev + (prev ? ' ' : '') + result.transcript);
        setCurrentVoiceResult(null);
      }
    }
  };

  const handleVoiceError = (error: string) => {
    Alert.alert('Voice Input Error', error);
    setCurrentVoiceResult(null);
  };

  const toggleVoiceMode = () => {
    const newVoiceMode = !isVoiceMode;
    setIsVoiceMode(newVoiceMode);
    
    voiceModeOpacity.value = withTiming(newVoiceMode ? 1 : 0, { duration: 300 });
    
    if (!newVoiceMode) {
      textInputRef.current?.focus();
    }
  };

  const handleImagePicker = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant photo library access to select images.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setSelectedImage(imageUri);
        imagePreviewScale.value = withSpring(1);
      }
    } catch (error) {
      console.error('Image picker error:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const handleCameraCapture = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera access to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setSelectedImage(imageUri);
        imagePreviewScale.value = withSpring(1);
      }
    } catch (error) {
      console.error('Camera capture error:', error);
      Alert.alert('Error', 'Failed to capture image');
    }
  };

  const removeSelectedImage = () => {
    imagePreviewScale.value = withTiming(0, { duration: 200 }, () => {
      setSelectedImage(null);
    });
  };

  const handleSend = () => {
    if (!canSend || disabled) return;

    // Determine input type
    let inputType: MultiModalInput['type'] = 'text';
    if (selectedImage && text.trim().length > 0) {
      inputType = 'multimodal';
    } else if (selectedImage) {
      inputType = 'image';
    } else if (currentVoiceResult) {
      inputType = 'voice';
    }

    const input: MultiModalInput = {
      text: text.trim() || undefined,
      image: selectedImage || undefined,
      voice: currentVoiceResult || undefined,
      type: inputType,
    };

    onSendMessage(input);

    // Reset state
    setText('');
    setSelectedImage(null);
    setCurrentVoiceResult(null);
    inputHeight.value = withSpring(50);
    imagePreviewScale.value = withSpring(0);
    sendButtonScale.value = withSpring(0.9, {}, () => {
      sendButtonScale.value = withSpring(1);
    });
  };

  // Animated styles
  const inputContainerStyle = useAnimatedStyle(() => ({
    height: inputHeight.value,
  }));

  const sendButtonStyle = useAnimatedStyle(() => ({
    transform: [{ scale: sendButtonScale.value }],
    opacity: canSend ? 1 : 0.5,
  }));

  const voiceModeStyle = useAnimatedStyle(() => ({
    opacity: voiceModeOpacity.value,
    transform: [
      { scale: interpolate(voiceModeOpacity.value, [0, 1], [0.8, 1]) },
    ],
  }));

  const imagePreviewStyle = useAnimatedStyle(() => ({
    transform: [{ scale: imagePreviewScale.value }],
    opacity: imagePreviewScale.value,
  }));

  return (
    <View style={[styles.container, className && { className }]}>
      {/* Image Preview */}
      {selectedImage && (
        <Animated.View style={[styles.imagePreview, imagePreviewStyle]}>
          <LinearGradient
            colors={['rgba(255, 107, 157, 0.1)', 'rgba(168, 85, 247, 0.1)']}
            style={styles.imagePreviewGradient}
          >
            <Animated.Image
              source={{ uri: selectedImage }}
              style={styles.previewImage}
              resizeMode="cover"
            />
            <Pressable
              onPress={removeSelectedImage}
              style={styles.removeImageButton}
            >
              <Text className="text-white text-xs font-bold">✕</Text>
            </Pressable>
          </LinearGradient>
        </Animated.View>
      )}

      {/* Voice Mode Overlay */}
      {isVoiceMode && (
        <Animated.View style={[styles.voiceModeOverlay, voiceModeStyle]}>
          <LinearGradient
            colors={['rgba(255, 107, 157, 0.2)', 'rgba(168, 85, 247, 0.2)']}
            style={styles.voiceModeGradient}
          >
            <VoiceInputButton
              onVoiceResult={handleVoiceResult}
              onVoiceError={handleVoiceError}
              size="large"
              showTranscript={true}
            />
            <Text className="text-white text-sm font-medium mt-4 text-center">
              Speak your message
            </Text>
            <Pressable
              onPress={toggleVoiceMode}
              style={styles.voiceModeCloseButton}
            >
              <Text className="text-white/70 text-sm">Cancel</Text>
            </Pressable>
          </LinearGradient>
        </Animated.View>
      )}

      {/* Main Input Container */}
      <Animated.View style={[styles.inputContainer, inputContainerStyle]}>
        <LinearGradient
          colors={['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']}
          style={styles.inputGradient}
        >
          {/* Text Input */}
          <TextInput
            ref={textInputRef}
            style={styles.textInput}
            value={text}
            onChangeText={handleTextChange}
            placeholder={placeholder}
            placeholderTextColor="rgba(255, 255, 255, 0.5)"
            multiline
            maxLength={maxLength}
            editable={!disabled && !isVoiceMode}
          />

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            {/* Image Input Buttons */}
            {showImageInput && (
              <>
                <Pressable
                  onPress={handleImagePicker}
                  style={styles.actionButton}
                  disabled={disabled}
                >
                  <ImageIcon size={20} color="rgba(255, 255, 255, 0.7)" />
                </Pressable>
                
                <Pressable
                  onPress={handleCameraCapture}
                  style={styles.actionButton}
                  disabled={disabled}
                >
                  <CameraIcon size={20} color="rgba(255, 255, 255, 0.7)" />
                </Pressable>
              </>
            )}

            {/* Voice Input Button */}
            {showVoiceInput && (
              <Pressable
                onPress={toggleVoiceMode}
                style={styles.actionButton}
                disabled={disabled}
              >
                <MicIcon size={20} color="rgba(255, 255, 255, 0.7)" />
              </Pressable>
            )}

            {/* Send Button */}
            <Animated.View style={sendButtonStyle}>
              <Pressable
                onPress={handleSend}
                style={styles.sendButton}
                disabled={!canSend || disabled}
              >
                <LinearGradient
                  colors={canSend ? ['#FF6B9D', '#A855F7'] : ['#6B7280', '#9CA3AF']}
                  style={styles.sendButtonGradient}
                >
                  <SendIcon size={20} color="white" />
                </LinearGradient>
              </Pressable>
            </Animated.View>
          </View>
        </LinearGradient>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  imagePreview: {
    marginBottom: 12,
    alignSelf: 'flex-start',
  },
  imagePreviewGradient: {
    borderRadius: 12,
    padding: 8,
    position: 'relative',
  },
  previewImage: {
    width: 80,
    height: 60,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceModeOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceModeGradient: {
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    minWidth: 200,
  },
  voiceModeCloseButton: {
    marginTop: 16,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  inputContainer: {
    borderRadius: 25,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  inputGradient: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  textInput: {
    flex: 1,
    color: 'white',
    fontSize: 16,
    maxHeight: 100,
    textAlignVertical: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  actionButton: {
    padding: 8,
    marginHorizontal: 2,
  },
  sendButton: {
    marginLeft: 4,
  },
  sendButtonGradient: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
