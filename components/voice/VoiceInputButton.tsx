import React, { useState, useEffect, useRef } from 'react';
import { Pressable, StyleSheet, Alert } from 'react-native';
import { MicIcon, MicOffIcon, Volume2Icon } from 'lucide-react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withRepeat,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { Text } from '@/components/ui/text';
import { voiceInputService, type VoiceResult, type VoiceState } from '@/lib/services/voice-input.service';

interface VoiceInputButtonProps {
  onVoiceResult: (result: VoiceResult) => void;
  onVoiceError?: (error: string) => void;
  language?: string;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  showTranscript?: boolean;
  className?: string;
}

/**
 * Voice Input Button Component
 * Following Rule 6: UI Design Consistency with Candy Color Theme
 */
export default function VoiceInputButton({
  onVoiceResult,
  onVoiceError,
  language = 'en-US',
  disabled = false,
  size = 'medium',
  showTranscript = true,
  className = '',
}: VoiceInputButtonProps) {
  const [voiceState, setVoiceState] = useState<VoiceState>(voiceInputService.getState());
  const [currentTranscript, setCurrentTranscript] = useState<string>('');
  const [isPressed, setIsPressed] = useState(false);

  // Animation values
  const scale = useSharedValue(1);
  const pulseScale = useSharedValue(1);
  const rotation = useSharedValue(0);
  const opacity = useSharedValue(1);

  // Component ID for listeners
  const componentId = useRef(`voice-button-${Date.now()}-${Math.random()}`).current;

  useEffect(() => {
    // Add listeners
    voiceInputService.addStateListener(componentId, handleStateChange);
    voiceInputService.addResultListener(componentId, handleVoiceResult);
    voiceInputService.addErrorListener(componentId, handleVoiceError);

    return () => {
      // Cleanup listeners
      voiceInputService.removeStateListener(componentId);
      voiceInputService.removeResultListener(componentId);
      voiceInputService.removeErrorListener(componentId);
    };
  }, []);

  useEffect(() => {
    // Update animations based on voice state
    if (voiceState.isListening) {
      // Pulsing animation while listening
      pulseScale.value = withRepeat(
        withTiming(1.2, { duration: 800 }),
        -1,
        true
      );
      
      // Subtle rotation animation
      rotation.value = withRepeat(
        withTiming(360, { duration: 2000 }),
        -1,
        false
      );
    } else {
      // Reset animations
      pulseScale.value = withSpring(1);
      rotation.value = withSpring(0);
    }
  }, [voiceState.isListening]);

  const handleStateChange = (newState: VoiceState) => {
    setVoiceState(newState);
    
    if (newState.error && onVoiceError) {
      onVoiceError(newState.error);
    }
  };

  const handleVoiceResult = (result: VoiceResult) => {
    setCurrentTranscript(result.transcript);
    onVoiceResult(result);
    
    if (result.isFinal) {
      // Clear transcript after a delay
      setTimeout(() => {
        setCurrentTranscript('');
      }, 2000);
    }
  };

  const handleVoiceError = (error: string) => {
    console.error('Voice input error:', error);
    if (onVoiceError) {
      onVoiceError(error);
    } else {
      Alert.alert('Voice Input Error', error);
    }
  };

  const handlePressIn = () => {
    if (disabled || !voiceState.isAvailable || !voiceState.hasPermission) return;
    
    setIsPressed(true);
    scale.value = withSpring(0.95);
    startListening();
  };

  const handlePressOut = () => {
    setIsPressed(false);
    scale.value = withSpring(1);
    stopListening();
  };

  const startListening = async () => {
    try {
      await voiceInputService.startListening(language);
    } catch (error) {
      console.error('Failed to start listening:', error);
    }
  };

  const stopListening = async () => {
    try {
      await voiceInputService.stopListening();
    } catch (error) {
      console.error('Failed to stop listening:', error);
    }
  };

  const toggleListening = async () => {
    if (voiceState.isListening) {
      await stopListening();
    } else {
      await startListening();
    }
  };

  // Get size-specific dimensions
  const getSizeDimensions = () => {
    switch (size) {
      case 'small':
        return { width: 40, height: 40, iconSize: 16 };
      case 'large':
        return { width: 80, height: 80, iconSize: 32 };
      default:
        return { width: 56, height: 56, iconSize: 24 };
    }
  };

  const dimensions = getSizeDimensions();

  // Animated styles
  const buttonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: scale.value },
        { rotate: `${rotation.value}deg` },
      ],
    };
  });

  const pulseAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: pulseScale.value }],
      opacity: interpolate(pulseScale.value, [1, 1.2], [0.3, 0.1]),
    };
  });

  const getButtonColors = () => {
    if (disabled || !voiceState.isAvailable || !voiceState.hasPermission) {
      return ['#6B7280', '#9CA3AF']; // Gray colors for disabled state
    }
    
    if (voiceState.isListening) {
      return ['#EF4444', '#F87171']; // Red colors for listening state
    }
    
    return ['#FF6B9D', '#A855F7']; // Candy pink to purple gradient
  };

  const getIcon = () => {
    if (!voiceState.isAvailable || !voiceState.hasPermission) {
      return <MicOffIcon size={dimensions.iconSize} color="white" />;
    }
    
    if (voiceState.isListening) {
      return <Volume2Icon size={dimensions.iconSize} color="white" />;
    }
    
    return <MicIcon size={dimensions.iconSize} color="white" />;
  };

  return (
    <>
      <Animated.View style={[buttonAnimatedStyle, { position: 'relative' }]}>
        {/* Pulse effect background */}
        {voiceState.isListening && (
          <Animated.View
            style={[
              pulseAnimatedStyle,
              {
                position: 'absolute',
                width: dimensions.width,
                height: dimensions.height,
                borderRadius: dimensions.width / 2,
                backgroundColor: '#FF6B9D',
                zIndex: 0,
              },
            ]}
          />
        )}
        
        {/* Main button */}
        <Pressable
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          onPress={toggleListening}
          disabled={disabled || !voiceState.isAvailable || !voiceState.hasPermission}
          style={({ pressed }) => [
            styles.button,
            {
              width: dimensions.width,
              height: dimensions.height,
              borderRadius: dimensions.width / 2,
              opacity: pressed ? 0.8 : 1,
            },
          ]}
        >
          <LinearGradient
            colors={getButtonColors()}
            style={[
              styles.gradient,
              {
                width: dimensions.width,
                height: dimensions.height,
                borderRadius: dimensions.width / 2,
              },
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {getIcon()}
          </LinearGradient>
        </Pressable>
      </Animated.View>

      {/* Transcript display */}
      {showTranscript && currentTranscript && (
        <Animated.View style={styles.transcriptContainer}>
          <LinearGradient
            colors={['rgba(255, 107, 157, 0.1)', 'rgba(168, 85, 247, 0.1)']}
            style={styles.transcriptGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Text className="text-white text-sm font-medium text-center">
              "{currentTranscript}"
            </Text>
          </LinearGradient>
        </Animated.View>
      )}

      {/* Status indicator */}
      {voiceState.error && (
        <Text className="text-red-400 text-xs text-center mt-2">
          {voiceState.error}
        </Text>
      )}
      
      {!voiceState.isAvailable && (
        <Text className="text-gray-400 text-xs text-center mt-2">
          Voice input not available
        </Text>
      )}
      
      {!voiceState.hasPermission && voiceState.isAvailable && (
        <Text className="text-yellow-400 text-xs text-center mt-2">
          Microphone permission required
        </Text>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#FF6B9D',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  gradient: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  transcriptContainer: {
    marginTop: 12,
    maxWidth: 280,
    alignSelf: 'center',
  },
  transcriptGradient: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 107, 157, 0.3)',
  },
});
