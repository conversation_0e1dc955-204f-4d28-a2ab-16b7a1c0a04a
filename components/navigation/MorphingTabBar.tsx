import React, { useEffect } from 'react';
import { Dimensions, StyleSheet, Pressable } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate
} from 'react-native-reanimated';

// Import tab bar visibility context
import { useTabBarVisibility } from '@/contexts/TabBarVisibilityContext';

// Import UI components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';

// Import icons
import { 
  HomeIcon, 
  CameraIcon, 
  SearchIcon, 
  UserIcon 
} from 'lucide-react-native';

const { width: screenWidth } = Dimensions.get('window');

interface TabItem {
  name: string;
  label: string;
  icon: React.ComponentType<any>;
  gradient: string[];
}

const tabItems: TabItem[] = [
  {
    name: 'home',
    label: 'Home',
    icon: HomeIcon,
    gradient: ['#10B981', '#34D399'], // Emerald gradient
  },
  {
    name: 'scan',
    label: 'Scan',
    icon: CameraIcon,
    gradient: ['#FF6B9D', '#F093FB'], // Pink gradient
  },
  {
    name: 'search',
    label: 'Search',
    icon: SearchIcon,
    gradient: ['#3B82F6', '#60A5FA'], // Blue gradient
  },
  {
    name: 'profile',
    label: 'Profile',
    icon: UserIcon,
    gradient: ['#A855F7', '#C084FC'], // Purple gradient
  },
];

/**
 * MorphingTabBar - Custom tab bar with morphing bubble navigation
 * 
 * Features:
 * - Morphing bubble background that slides between tabs
 * - Glass morphism container with backdrop blur
 * - Smooth spring animations with React Native Reanimated
 * - Candy color gradients (emerald, pink, blue, purple)
 * - Safe area handling to prevent content overlap
 * - Touch-optimized button sizes with icons
 */
export const MorphingTabBar: React.FC<BottomTabBarProps> = ({
  state,
  descriptors,
  navigation
}) => {
  const insets = useSafeAreaInsets();
  const { tabBarOpacity, tabBarTranslateY } = useTabBarVisibility();

  // Get current active tab index for morphing animation
  const activeIndex = state.index;

  // Morphing bubble animation value
  const morphValue = useSharedValue(activeIndex);

  // Individual tab scale animations for touch feedback
  const tabScales = tabItems.map(() => useSharedValue(1));

  // Update morphing animation when active tab changes
  useEffect(() => {
    morphValue.value = withSpring(activeIndex, {
      damping: 15,
      stiffness: 150,
    });
  }, [activeIndex, morphValue]);

  // Morphing bubble background animation
  const morphStyle = useAnimatedStyle(() => {
    const containerWidth = screenWidth - 40; // Account for horizontal margins
    const tabWidth = containerWidth / tabItems.length;
    
    const translateX = interpolate(
      morphValue.value,
      [0, 1, 2, 3],
      [4, tabWidth + 4, (tabWidth * 2) + 4, (tabWidth * 3) + 4] // Add padding offset
    );
    
    return {
      transform: [{ translateX }],
    };
  });

  // Handle tab press with touch feedback
  const handleTabPress = (routeName: string, index: number) => {
    // Touch feedback animation
    tabScales[index].value = withSpring(0.95, { duration: 100 }, () => {
      tabScales[index].value = withSpring(1, { duration: 200 });
    });
    
    const event = navigation.emit({
      type: 'tabPress',
      target: state.routes[index].key,
      canPreventDefault: true,
    });

    if (!event.defaultPrevented) {
      navigation.navigate(routeName);
    }
  };

  // Tab bar visibility animation
  const tabBarAnimatedStyle = useAnimatedStyle(() => ({
    opacity: tabBarOpacity.value,
    transform: [{ translateY: tabBarTranslateY.value }],
  }));

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: 50,
          paddingBottom: insets.bottom + 16,
          paddingHorizontal: 20,
        },
        tabBarAnimatedStyle
      ]}
    >
      <Box style={styles.bubbleContainer}>
        <LinearGradient
          colors={['rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.1)']}
          style={styles.bubbleBackground}
        >
          {/* Morphing background indicator */}
          <Animated.View style={[styles.morphingIndicator, morphStyle]}>
            <LinearGradient
              colors={tabItems[activeIndex].gradient}
              style={styles.morphingGradient}
            />
          </Animated.View>
          
          {/* Tab buttons */}
          <HStack className="justify-around items-center py-3 relative z-10">
            {tabItems.map((tab, index) => {
              const Icon = tab.icon;
              const isActive = activeIndex === index;
              const route = state.routes[index];
              
              // Individual tab scale animation
              const tabAnimatedStyle = useAnimatedStyle(() => ({
                transform: [{ scale: tabScales[index].value }],
              }));
              
              return (
                <Animated.View key={tab.name} style={tabAnimatedStyle} className="flex-1">
                  <Pressable
                    onPress={() => handleTabPress(route.name, index)}
                    className="flex-col items-center gap-1 py-2"
                    style={styles.tabButton}
                  >
                    <Icon 
                      size={24} 
                      color={isActive ? '#FFFFFF' : 'rgba(255, 255, 255, 0.6)'} 
                    />
                    <Text 
                      className={`text-xs ${
                        isActive ? 'text-white font-semibold' : 'text-white/50'
                      }`}
                    >
                      {tab.label}
                    </Text>
                  </Pressable>
                </Animated.View>
              );
            })}
          </HStack>
        </LinearGradient>
      </Box>
    </Animated.View>
  );
};

// StyleSheet for morphing bubble navigation
const styles = StyleSheet.create({
  bubbleContainer: {
    borderRadius: 34,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  bubbleBackground: {
    borderRadius: 34,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.25)',
    minHeight: 68, // Match original tab bar height
  },
  morphingIndicator: {
    position: 'absolute',
    top: 4,
    bottom: 4,
    width: (screenWidth - 48) / 4, // Account for margins and padding
    borderRadius: 30,
    zIndex: 1,
  },
  morphingGradient: {
    flex: 1,
    borderRadius: 30,
  },
  tabButton: {
    minHeight: 44, // Accessibility touch target
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
  },
});

export default MorphingTabBar;
