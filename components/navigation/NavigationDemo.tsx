import React, { useState } from 'react';
import { ScrollView, Pressable } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Import UI components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';

// Import navigation variants
import {
  FloatingGlassNavigation,
  MorphingBubbleNavigation,
  ElevatedDockNavigation,
  SpotlightNavigation,
} from './NavigationVariants';

interface NavigationVariant {
  id: string;
  name: string;
  description: string;
  component: React.ComponentType;
  overlapSolution: string;
  features: string[];
}

const navigationVariants: NavigationVariant[] = [
  {
    id: 'floating-glass',
    name: 'Floating Glass Navigation',
    description: 'Glass morphism design with floating appearance and safe area handling',
    component: FloatingGlassNavigation,
    overlapSolution: 'Uses safe area insets + 16px bottom padding to prevent overlap',
    features: [
      'Glass morphism background',
      'Automatic safe area handling',
      'Floating appearance with shadows',
      'Candy color gradients for active states',
      'Touch-optimized button sizes',
    ],
  },
  {
    id: 'morphing-bubble',
    name: 'Morphing Bubble Navigation',
    description: 'Animated background that morphs between navigation items',
    component: MorphingBubbleNavigation,
    overlapSolution: 'Safe area insets + 20px margin with morphing indicator',
    features: [
      'Smooth morphing background animation',
      'Spring-based transitions',
      'Bubble-style container',
      'Dynamic gradient colors',
      'Responsive width calculations',
    ],
  },
  {
    id: 'elevated-dock',
    name: 'Elevated Dock Navigation',
    description: 'macOS-style dock with elevation animations and active indicators',
    component: ElevatedDockNavigation,
    overlapSolution: 'Safe area handling + elevation animations prevent content overlap',
    features: [
      'Dock-style elevation effects',
      'Top indicator for active items',
      'Bounce animation on selection',
      'Enhanced shadow system',
      'Rounded container design',
    ],
  },
  {
    id: 'spotlight',
    name: 'Spotlight Navigation',
    description: 'Spotlight effect with content padding to prevent overlap',
    component: SpotlightNavigation,
    overlapSolution: 'Explicit content padding (100px + safe area) prevents overlap',
    features: [
      'Spotlight indicator animation',
      'Content padding solution',
      'Smooth spring transitions',
      'Gradient spotlight effect',
      'Responsive positioning',
    ],
  },
];

export const NavigationDemo: React.FC = () => {
  const [selectedVariant, setSelectedVariant] = useState(0);
  const insets = useSafeAreaInsets();
  
  const CurrentNavigation = navigationVariants[selectedVariant].component;

  return (
    <Box className="flex-1">
      {/* Background gradient */}
      <LinearGradient
        colors={['#1e1b4b', '#312e81', '#4338ca', '#6366f1']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ flex: 1 }}
      >
        {/* Content area with proper padding */}
        <ScrollView 
          className="flex-1"
          contentContainerStyle={{
            paddingTop: insets.top + 20,
            paddingBottom: selectedVariant === 3 ? 0 : 120 + insets.bottom, // Spotlight variant handles its own padding
            paddingHorizontal: 20,
          }}
          showsVerticalScrollIndicator={false}
        >
          <VStack className="gap-6">
            {/* Header */}
            <VStack className="gap-2">
              <Text className="text-3xl font-bold text-white text-center">
                Navigation Variants
              </Text>
              <Text className="text-white/70 text-center">
                Choose a navigation style to preview
              </Text>
            </VStack>

            {/* Variant selector */}
            <VStack className="gap-3">
              {navigationVariants.map((variant, index) => (
                <Pressable
                  key={variant.id}
                  onPress={() => setSelectedVariant(index)}
                >
                  <Box 
                    className={`p-4 rounded-2xl border ${
                      selectedVariant === index
                        ? 'bg-white/20 border-white/40'
                        : 'bg-white/10 border-white/20'
                    }`}
                  >
                    <VStack className="gap-2">
                      <HStack className="justify-between items-center">
                        <Text 
                          className={`font-semibold ${
                            selectedVariant === index ? 'text-white' : 'text-white/80'
                          }`}
                        >
                          {variant.name}
                        </Text>
                        {selectedVariant === index && (
                          <Box className="w-3 h-3 bg-green-400 rounded-full" />
                        )}
                      </HStack>
                      
                      <Text className="text-white/60 text-sm">
                        {variant.description}
                      </Text>
                      
                      <VStack className="gap-1 mt-2">
                        <Text className="text-white/80 text-xs font-medium">
                          Overlap Solution:
                        </Text>
                        <Text className="text-white/60 text-xs">
                          {variant.overlapSolution}
                        </Text>
                      </VStack>
                      
                      {selectedVariant === index && (
                        <VStack className="gap-1 mt-2">
                          <Text className="text-white/80 text-xs font-medium">
                            Features:
                          </Text>
                          {variant.features.map((feature, featureIndex) => (
                            <Text key={featureIndex} className="text-white/60 text-xs">
                              • {feature}
                            </Text>
                          ))}
                        </VStack>
                      )}
                    </VStack>
                  </Box>
                </Pressable>
              ))}
            </VStack>

            {/* Demo content */}
            <VStack className="gap-4 mt-6">
              <Text className="text-white/80 text-lg font-medium text-center">
                Demo Content
              </Text>
              
              {Array.from({ length: 8 }, (_, index) => (
                <Box 
                  key={index}
                  className="p-4 bg-white/10 rounded-xl border border-white/20"
                >
                  <Text className="text-white/70">
                    Content Block {index + 1} - This content demonstrates how the navigation 
                    handles overlap prevention. Scroll to see more content and test the 
                    navigation behavior.
                  </Text>
                </Box>
              ))}
            </VStack>
          </VStack>
        </ScrollView>

        {/* Current navigation variant */}
        <CurrentNavigation />
      </LinearGradient>
    </Box>
  );
};

export default NavigationDemo;
