import React, { useState } from 'react';
import { Pressable, StyleSheet, Dimensions } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import {
  HomeIcon,
  SearchIcon,
  CameraIcon,
  UserIcon,
  BellIcon,
  SettingsIcon,
} from 'lucide-react-native';

// Import UI components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';

const { width: screenWidth } = Dimensions.get('window');

interface NavItem {
  id: string;
  icon: React.ComponentType<any>;
  label: string;
  gradient: string[];
}

const navItems: NavItem[] = [
  { 
    id: 'home', 
    icon: HomeIcon, 
    label: 'Home', 
    gradient: ['#FF6B9D', '#F093FB'] 
  },
  { 
    id: 'search', 
    icon: SearchIcon, 
    label: 'Search', 
    gradient: ['#A855F7', '#C084FC'] 
  },
  { 
    id: 'camera', 
    icon: CameraIcon, 
    label: 'Scan', 
    gradient: ['#10B981', '#34D399'] 
  },
  { 
    id: 'profile', 
    icon: UserIcon, 
    label: 'Profile', 
    gradient: ['#3B82F6', '#60A5FA'] 
  },
];

// Variant 1: Floating Glass Navigation with Safe Area
export const FloatingGlassNavigation: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const insets = useSafeAreaInsets();
  const scaleValue = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleValue.value }],
  }));

  return (
    <Box 
      className="absolute bottom-0 left-0 right-0 z-50"
      style={{ paddingBottom: insets.bottom + 16 }}
    >
      <Animated.View style={[styles.floatingContainer, animatedStyle]}>
        <LinearGradient
          colors={['rgba(255, 255, 255, 0.25)', 'rgba(255, 255, 255, 0.1)']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.glassBackground}
        >
          <HStack className="justify-around items-center py-4">
            {navItems.map((item, index) => {
              const Icon = item.icon;
              const isActive = activeIndex === index;
              
              return (
                <Pressable
                  key={item.id}
                  onPress={() => setActiveIndex(index)}
                  style={styles.navButton}
                >
                  <Box className="relative">
                    {isActive && (
                      <LinearGradient
                        colors={item.gradient}
                        style={styles.activeBackground}
                      />
                    )}
                    <Icon 
                      size={24} 
                      color={isActive ? '#FFFFFF' : 'rgba(255, 255, 255, 0.7)'} 
                    />
                  </Box>
                  <Text 
                    className={`text-xs mt-1 ${
                      isActive ? 'text-white font-medium' : 'text-white/60'
                    }`}
                  >
                    {item.label}
                  </Text>
                </Pressable>
              );
            })}
          </HStack>
        </LinearGradient>
      </Animated.View>
    </Box>
  );
};

// Variant 2: Morphing Bubble Navigation
export const MorphingBubbleNavigation: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const insets = useSafeAreaInsets();
  const morphValue = useSharedValue(0);

  const morphStyle = useAnimatedStyle(() => {
    const translateX = interpolate(
      morphValue.value,
      [0, 1, 2, 3],
      [0, screenWidth * 0.25, screenWidth * 0.5, screenWidth * 0.75]
    );
    
    return {
      transform: [{ translateX }],
    };
  });

  const handlePress = (index: number) => {
    setActiveIndex(index);
    morphValue.value = withSpring(index, {
      damping: 15,
      stiffness: 150,
    });
  };

  return (
    <Box 
      className="absolute bottom-0 left-0 right-0 z-50"
      style={{ paddingBottom: insets.bottom + 20 }}
    >
      <Box className="mx-4 relative">
        <LinearGradient
          colors={['rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.1)']}
          style={styles.bubbleContainer}
        >
          {/* Morphing background indicator */}
          <Animated.View style={[styles.morphingIndicator, morphStyle]}>
            <LinearGradient
              colors={navItems[activeIndex].gradient}
              style={styles.morphingGradient}
            />
          </Animated.View>
          
          <HStack className="justify-around items-center py-3 relative z-10">
            {navItems.map((item, index) => {
              const Icon = item.icon;
              const isActive = activeIndex === index;
              
              return (
                <Pressable
                  key={item.id}
                  onPress={() => handlePress(index)}
                  style={styles.bubbleButton}
                >
                  <VStack className="items-center">
                    <Icon 
                      size={22} 
                      color={isActive ? '#FFFFFF' : 'rgba(255, 255, 255, 0.6)'} 
                    />
                    <Text 
                      className={`text-xs mt-1 ${
                        isActive ? 'text-white font-semibold' : 'text-white/50'
                      }`}
                    >
                      {item.label}
                    </Text>
                  </VStack>
                </Pressable>
              );
            })}
          </HStack>
        </LinearGradient>
      </Box>
    </Box>
  );
};

// Variant 3: Elevated Dock Navigation
export const ElevatedDockNavigation: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const insets = useSafeAreaInsets();
  const elevationValue = useSharedValue(0);

  const elevatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateY: interpolate(elevationValue.value, [0, 1], [0, -8]) },
      { scale: interpolate(elevationValue.value, [0, 1], [1, 1.05]) },
    ],
    shadowOpacity: interpolate(elevationValue.value, [0, 1], [0.3, 0.6]),
  }));

  const handlePress = (index: number) => {
    setActiveIndex(index);
    elevationValue.value = withTiming(1, { duration: 200 }, () => {
      elevationValue.value = withTiming(0, { duration: 300 });
    });
  };

  return (
    <Box
      className="absolute bottom-0 left-0 right-0 z-50"
      style={{ paddingBottom: insets.bottom + 12 }}
    >
      <Box className="mx-6">
        <Animated.View style={[styles.dockContainer, elevatedStyle]}>
          <LinearGradient
            colors={['rgba(255, 255, 255, 0.3)', 'rgba(255, 255, 255, 0.15)']}
            style={styles.dockBackground}
          >
            <HStack className="justify-around items-center py-4">
              {navItems.map((item, index) => {
                const Icon = item.icon;
                const isActive = activeIndex === index;

                return (
                  <Pressable
                    key={item.id}
                    onPress={() => handlePress(index)}
                    style={styles.dockButton}
                  >
                    <Box className="relative items-center">
                      {isActive && (
                        <Box className="absolute -top-2 w-8 h-1 rounded-full">
                          <LinearGradient
                            colors={item.gradient}
                            style={styles.activeIndicator}
                          />
                        </Box>
                      )}
                      <Box
                        className={`p-3 rounded-2xl ${
                          isActive ? 'bg-white/20' : 'bg-transparent'
                        }`}
                      >
                        <Icon
                          size={24}
                          color={isActive ? '#FFFFFF' : 'rgba(255, 255, 255, 0.7)'}
                        />
                      </Box>
                      <Text
                        className={`text-xs mt-1 ${
                          isActive ? 'text-white font-medium' : 'text-white/60'
                        }`}
                      >
                        {item.label}
                      </Text>
                    </Box>
                  </Pressable>
                );
              })}
            </HStack>
          </LinearGradient>
        </Animated.View>
      </Box>
    </Box>
  );
};

// Variant 4: Spotlight Navigation with Content Padding
export const SpotlightNavigation: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const insets = useSafeAreaInsets();
  const spotlightValue = useSharedValue(0);

  const spotlightStyle = useAnimatedStyle(() => {
    const translateX = interpolate(
      spotlightValue.value,
      [0, 1, 2, 3],
      [0, (screenWidth - 64) / 4, (screenWidth - 64) / 2, ((screenWidth - 64) * 3) / 4]
    );

    return {
      transform: [{ translateX }],
    };
  });

  const handlePress = (index: number) => {
    setActiveIndex(index);
    spotlightValue.value = withSpring(index, {
      damping: 20,
      stiffness: 200,
    });
  };

  return (
    <>
      {/* Content padding to prevent overlap */}
      <Box style={{ height: 100 + insets.bottom }} />

      <Box
        className="absolute bottom-0 left-0 right-0 z-50"
        style={{ paddingBottom: insets.bottom + 16 }}
      >
        <Box className="mx-8 relative">
          <LinearGradient
            colors={['rgba(255, 255, 255, 0.25)', 'rgba(255, 255, 255, 0.1)']}
            style={styles.spotlightContainer}
          >
            {/* Spotlight indicator */}
            <Animated.View style={[styles.spotlightIndicator, spotlightStyle]}>
              <LinearGradient
                colors={navItems[activeIndex].gradient}
                style={styles.spotlightGradient}
              />
            </Animated.View>

            <HStack className="justify-around items-center py-4 relative z-10">
              {navItems.map((item, index) => {
                const Icon = item.icon;
                const isActive = activeIndex === index;

                return (
                  <Pressable
                    key={item.id}
                    onPress={() => handlePress(index)}
                    style={styles.spotlightButton}
                  >
                    <VStack className="items-center">
                      <Icon
                        size={24}
                        color={isActive ? '#FFFFFF' : 'rgba(255, 255, 255, 0.6)'}
                      />
                      <Text
                        className={`text-xs mt-1 ${
                          isActive ? 'text-white font-semibold' : 'text-white/50'
                        }`}
                      >
                        {item.label}
                      </Text>
                    </VStack>
                  </Pressable>
                );
              })}
            </HStack>
          </LinearGradient>
        </Box>
      </Box>
    </>
  );
};

// StyleSheet for all navigation variants
const styles = StyleSheet.create({
  // Floating Glass Navigation Styles
  floatingContainer: {
    marginHorizontal: 16,
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  glassBackground: {
    borderRadius: 24,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  navButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  activeBackground: {
    position: 'absolute',
    top: -8,
    left: -8,
    right: -8,
    bottom: -8,
    borderRadius: 16,
  },

  // Morphing Bubble Navigation Styles
  bubbleContainer: {
    borderRadius: 28,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.25)',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 10,
  },
  morphingIndicator: {
    position: 'absolute',
    top: 8,
    bottom: 8,
    width: screenWidth / 4 - 16,
    borderRadius: 20,
    zIndex: 1,
  },
  morphingGradient: {
    flex: 1,
    borderRadius: 20,
  },
  bubbleButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },

  // Elevated Dock Navigation Styles
  dockContainer: {
    borderRadius: 32,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.4,
    shadowRadius: 20,
    elevation: 15,
  },
  dockBackground: {
    borderRadius: 32,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.35)',
  },
  dockButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
  },
  activeIndicator: {
    width: 32,
    height: 4,
    borderRadius: 2,
  },

  // Spotlight Navigation Styles
  spotlightContainer: {
    borderRadius: 26,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  spotlightIndicator: {
    position: 'absolute',
    top: 6,
    bottom: 6,
    width: (screenWidth - 64) / 4,
    borderRadius: 20,
    zIndex: 1,
  },
  spotlightGradient: {
    flex: 1,
    borderRadius: 20,
    opacity: 0.8,
  },
  spotlightButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
});
