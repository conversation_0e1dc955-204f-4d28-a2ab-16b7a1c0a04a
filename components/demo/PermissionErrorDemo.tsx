/**
 * Permission Error Demo Component
 * 
 * Demonstrates how the ErrorBoundary automatically catches AppWrite permission errors
 * and shows the registration modal for guest users.
 */

import React, { useState } from 'react';
import { Alert } from 'react-native';
import { Button, ButtonText } from '@/components/ui/button';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';

// Simulate different types of AppWrite errors
const simulatePermissionError = () => {
  const error = new Error('Permission denied: Role "user" must have an ID value');
  error.name = 'AppwriteException';
  (error as any).code = 401;
  throw error;
};

const simulateUnauthorizedError = () => {
  const error = new Error('Unauthorized access to resource');
  error.name = 'AppwriteException';
  (error as any).code = 403;
  throw error;
};

const simulateInvalidPermissionsError = () => {
  const error = new Error('Invalid permissions param: insufficient permissions');
  error.name = 'AppwriteException';
  throw error;
};

const simulateGenericError = () => {
  const error = new Error('Network connection failed');
  error.name = 'NetworkError';
  throw error;
};

export const PermissionErrorDemo: React.FC = () => {
  const [errorType, setErrorType] = useState<string>('');

  const handleTriggerError = (type: string) => {
    setErrorType(type);
    
    // Trigger error after a short delay to show the button press
    setTimeout(() => {
      switch (type) {
        case 'permission':
          simulatePermissionError();
          break;
        case 'unauthorized':
          simulateUnauthorizedError();
          break;
        case 'invalid':
          simulateInvalidPermissionsError();
          break;
        case 'generic':
          simulateGenericError();
          break;
        default:
          Alert.alert('Demo', 'Select an error type to test');
      }
    }, 100);
  };

  return (
    <Box className="p-6 bg-white rounded-lg shadow-sm border border-gray-200">
      <VStack space="md">
        <Text size="lg" className="font-semibold text-gray-900 mb-4">
          🔐 Permission Error Boundary Demo
        </Text>
        
        <Text size="sm" className="text-gray-600 mb-4">
          Test how the ErrorBoundary automatically catches different types of AppWrite errors:
        </Text>

        <VStack space="sm">
          <Button
            action="candyPink"
            size="md"
            onPress={() => handleTriggerError('permission')}
            className="w-full"
          >
            <ButtonText>Trigger Permission Error</ButtonText>
          </Button>
          
          <Button
            action="candyPink"
            size="md"
            onPress={() => handleTriggerError('unauthorized')}
            className="w-full"
          >
            <ButtonText>Trigger Unauthorized Error</ButtonText>
          </Button>
          
          <Button
            action="candyPink"
            size="md"
            onPress={() => handleTriggerError('invalid')}
            className="w-full"
          >
            <ButtonText>Trigger Invalid Permissions</ButtonText>
          </Button>
          
          <Button
            action="glass"
            size="md"
            onPress={() => handleTriggerError('generic')}
            className="w-full"
          >
            <ButtonText>Trigger Generic Error</ButtonText>
          </Button>
        </VStack>

        <Box className="bg-blue-50 p-4 rounded-lg mt-4">
          <Text size="sm" className="text-blue-800 font-medium mb-2">
            Expected Behavior:
          </Text>
          <Text size="xs" className="text-blue-700">
            • Permission errors → Registration modal for guest users{'\n'}
            • Generic errors → Error boundary fallback UI{'\n'}
            • All errors → Logged to console for debugging
          </Text>
        </Box>

        {errorType && (
          <Box className="bg-yellow-50 p-4 rounded-lg">
            <Text size="sm" className="text-yellow-800">
              Triggering: {errorType} error...
            </Text>
          </Box>
        )}
      </VStack>
    </Box>
  );
};

export default PermissionErrorDemo;
