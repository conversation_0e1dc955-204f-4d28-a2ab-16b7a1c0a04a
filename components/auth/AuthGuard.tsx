import React, { useEffect } from 'react';
import { useRouter, useSegments } from 'expo-router';
import { View } from 'react-native';
import { useAuth } from '@/lib/contexts/AuthContext';
import { Spinner } from '@/components/ui/spinner';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';

interface AuthGuardProps {
  children: React.ReactNode;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();
  const segments = useSegments();

  useEffect(() => {
    // In development mode, allow access to all routes
    if (__DEV__) {
      console.log('🚧 Development Mode: AuthGuard bypassed, allowing all routes');
      return;
    }

    // Enable guest access for development and discovery
    console.log('🔐 AuthGuard: Checking authentication state...');
    if (isLoading) return; // Don't do anything while loading

    const inAuthGroup = segments[0] === 'auth';
    const inTabsGroup = segments[0] === '(tabs)';
    const inKnowledgeGroup = segments[0] === '(knowledge)';
    const inProtectedRoute = inTabsGroup || inKnowledgeGroup;

    // Allow guest access to home screen and basic features
    const isGuestAllowedRoute = segments.join('/') === '(tabs)/home' || 
                               segments.join('/') === '(tabs)/scan' ||
                               segments.join('/') === '(tabs)/search' ||
                               segments.join('/') === '(tabs)/study';

    if (!isAuthenticated && inProtectedRoute && !isGuestAllowedRoute) {
      // User is not authenticated and trying to access restricted route
      router.replace('/auth/login');
    } else if (isAuthenticated && inAuthGroup) {
      // User is authenticated but on auth screen
      router.replace('/(tabs)/home');
    }
  }, [isAuthenticated, isLoading, segments]);

  // In development mode, skip loading screen
  if (__DEV__) {
    return <>{children}</>;
  }

  // Show loading screen while checking authentication
  if (isLoading) {
    return (
      <Box className="flex-1 justify-center items-center bg-background">
        <VStack space="lg" className="items-center">
          <Spinner size="large" />
          <Text className="text-lg text-typography-600">
            Loading...
          </Text>
        </VStack>
      </Box>
    );
  }

  return <>{children}</>;
};

export default AuthGuard;
