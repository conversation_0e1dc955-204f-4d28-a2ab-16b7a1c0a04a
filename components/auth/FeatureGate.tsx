/**
 * Feature Gate Component - Permission-Based Access Control
 * 
 * Provides declarative permission checking for React components:
 * - Role-based feature gating
 * - Usage limit enforcement
 * - Automatic upgrade prompts
 * - Fallback content rendering
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 6: UI Design Consistency with Gluestack UI
 * - Rule 8: Smooth animations with react-native-reanimated
 * - Rule 11: Zod validation for all state
 * - Rule 12: Comprehensive error handling
 */

import React, { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
} from 'react-native-reanimated';

// UI Components following Rule #6
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';
import { Badge, BadgeText } from '@/components/ui/badge';
import { Spinner } from '@/components/ui/spinner';

// Icons
import { Lock, Crown, Zap, AlertTriangle, Info } from 'lucide-react-native';

// Services and hooks
import { useFeaturePermission, useUsageLimitedFeature, PERMISSIONS } from '@/hooks/usePermissions';
import { UpgradePrompt } from '@/components/learni-scan/UpgradePrompt';

interface FeatureGateProps {
  // Permission configuration
  permission: string;
  feature?: string;
  
  // Content configuration
  children: React.ReactNode;
  fallback?: React.ReactNode;
  loadingComponent?: React.ReactNode;
  
  // Behavior configuration
  showUpgradePrompt?: boolean;
  upgradePromptVariant?: 'modal' | 'banner' | 'fullscreen';
  blockAccess?: boolean; // If false, shows warning but allows access
  
  // Styling
  className?: string;
  
  // Callbacks
  onAccessDenied?: (reason: string) => void;
  onUpgradePrompt?: () => void;
  onFeatureUsed?: () => void;
}

interface FeatureGateState {
  hasAccess: boolean;
  isLoading: boolean;
  error: string | null;
  usageInfo?: {
    current: number;
    limit: number;
    remaining: number;
  };
  upgradeRequired: boolean;
  reason?: string;
}

export const FeatureGate: React.FC<FeatureGateProps> = ({
  permission,
  feature,
  children,
  fallback,
  loadingComponent,
  showUpgradePrompt = true,
  upgradePromptVariant = 'modal',
  blockAccess = true,
  className,
  onAccessDenied,
  onUpgradePrompt,
  onFeatureUsed
}) => {
  const [state, setState] = useState<FeatureGateState>({
    hasAccess: false,
    isLoading: true,
    error: null,
    upgradeRequired: false
  });

  const [showUpgrade, setShowUpgrade] = useState(false);

  // Animation values (Rule 8)
  const fadeValue = useSharedValue(0);
  const scaleValue = useSharedValue(0.95);

  // Use permission hook
  const {
    hasAccess,
    permissionResult,
    isChecking,
    checkAccess,
    useFeature,
    hasBasicPermission
  } = useFeaturePermission(feature || 'default', permission);

  // Use usage tracking if feature is specified
  const usageData = useUsageLimitedFeature(feature || 'default');

  useEffect(() => {
    const updateState = async () => {
      setState(prev => ({ ...prev, isLoading: isChecking }));

      if (permissionResult) {
        const newState: FeatureGateState = {
          hasAccess: permissionResult.hasPermission,
          isLoading: false,
          error: null,
          usageInfo: permissionResult.usageInfo,
          upgradeRequired: permissionResult.upgradeRequired || false,
          reason: permissionResult.reason
        };

        setState(newState);

        // Trigger callbacks
        if (!permissionResult.hasPermission && onAccessDenied) {
          onAccessDenied(permissionResult.reason || 'Access denied');
        }

        // Animate content appearance
        if (permissionResult.hasPermission) {
          fadeValue.value = withTiming(1, { duration: 300 });
          scaleValue.value = withSpring(1, { damping: 15, stiffness: 150 });
        }
      }
    };

    updateState();
  }, [permissionResult, isChecking, onAccessDenied, fadeValue, scaleValue]);

  const handleUpgradePrompt = () => {
    setShowUpgrade(true);
    onUpgradePrompt?.();
  };

  const handleFeatureUse = async () => {
    const success = await useFeature();
    if (success) {
      onFeatureUsed?.();
    }
    return success;
  };

  // Animated styles (Rule 8)
  const contentAnimatedStyle = useAnimatedStyle(() => ({
    opacity: fadeValue.value,
    transform: [{ scale: scaleValue.value }],
  }));

  // Loading state
  if (state.isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }

    return (
      <Box className={`justify-center items-center p-4 ${className}`}>
        <VStack space="md" alignItems="center">
          <Spinner size="small" />
          <Text size="sm" color="$neutral600">
            Checking permissions...
          </Text>
        </VStack>
      </Box>
    );
  }

  // Access denied - show upgrade prompt
  if (!state.hasAccess && showUpgradePrompt && state.upgradeRequired) {
    if (showUpgrade) {
      return (
        <UpgradePrompt
          trigger="feature_locked"
          variant={upgradePromptVariant}
          onDismiss={() => setShowUpgrade(false)}
          onUpgrade={() => {
            setShowUpgrade(false);
            // Handle upgrade logic
          }}
        />
      );
    }

    // Show upgrade trigger
    return (
      <Box className={`p-4 ${className}`}>
        <VStack space="md" alignItems="center">
          <Box className="w-16 h-16 rounded-full bg-candyPink/20 justify-center items-center">
            <Crown size={32} color="#FF6B9D" />
          </Box>
          
          <VStack space="sm" alignItems="center">
            <Text size="lg" fontWeight="$semibold" textAlign="center">
              Premium Feature
            </Text>
            <Text size="sm" color="$neutral600" textAlign="center">
              {state.reason || 'This feature requires a premium subscription'}
            </Text>
            
            {state.usageInfo && (
              <Badge action="warning" variant="outline">
                <BadgeText>
                  {state.usageInfo.current}/{state.usageInfo.limit} used
                </BadgeText>
              </Badge>
            )}
          </VStack>

          <Button
            action="candyPink"
            size="md"
            onPress={handleUpgradePrompt}
          >
            <ButtonIcon as={Crown} />
            <ButtonText>Upgrade to Premium</ButtonText>
          </Button>
        </VStack>
      </Box>
    );
  }

  // Access denied - show fallback or block
  if (!state.hasAccess) {
    if (blockAccess) {
      if (fallback) {
        return <>{fallback}</>;
      }

      return (
        <Box className={`p-4 ${className}`}>
          <VStack space="md" alignItems="center">
            <Box className="w-12 h-12 rounded-full bg-error/20 justify-center items-center">
              <Lock size={24} color="#ef4444" />
            </Box>
            
            <VStack space="sm" alignItems="center">
              <Text size="md" fontWeight="$semibold" textAlign="center">
                Access Restricted
              </Text>
              <Text size="sm" color="$neutral600" textAlign="center">
                {state.reason || 'You do not have permission to access this feature'}
              </Text>
            </VStack>
          </VStack>
        </Box>
      );
    } else {
      // Show warning but allow access
      return (
        <VStack space="md" className={className}>
          <Box className="p-3 bg-warning/10 border border-warning/30 rounded-lg">
            <HStack space="sm" alignItems="center">
              <AlertTriangle size={16} color="#f59e0b" />
              <Text size="sm" color="$warning600" flex={1}>
                Limited access: {state.reason}
              </Text>
            </HStack>
          </Box>
          
          <Animated.View style={contentAnimatedStyle}>
            {children}
          </Animated.View>
        </VStack>
      );
    }
  }

  // Access granted - show content with usage info
  return (
    <VStack space="sm" className={className}>
      {/* Usage indicator for limited features */}
      {state.usageInfo && state.usageInfo.limit > 0 && (
        <Box className="p-2 bg-info/10 border border-info/30 rounded-lg">
          <HStack space="sm" alignItems="center" justifyContent="space-between">
            <HStack space="sm" alignItems="center">
              <Info size={14} color="#3b82f6" />
              <Text size="xs" color="$info600">
                Usage: {state.usageInfo.current}/{state.usageInfo.limit}
              </Text>
            </HStack>
            
            <Badge 
              action={state.usageInfo.remaining > 5 ? 'success' : 'warning'}
              size="sm"
            >
              <BadgeText>
                {state.usageInfo.remaining} remaining
              </BadgeText>
            </Badge>
          </HStack>
        </Box>
      )}

      {/* Feature content */}
      <Animated.View style={contentAnimatedStyle}>
        {React.cloneElement(children as React.ReactElement, {
          onFeatureUse: handleFeatureUse
        })}
      </Animated.View>
    </VStack>
  );
};

/**
 * Higher-order component for feature gating
 */
export const withFeatureGate = <P extends object>(
  Component: React.ComponentType<P>,
  gateProps: Omit<FeatureGateProps, 'children'>
) => {
  return (props: P) => (
    <FeatureGate {...gateProps}>
      <Component {...props} />
    </FeatureGate>
  );
};

/**
 * Simple permission check component
 */
export const PermissionCheck: React.FC<{
  permission: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ permission, children, fallback }) => {
  const { hasBasicPermission } = useFeaturePermission('default', permission);

  if (!hasBasicPermission) {
    return fallback ? <>{fallback}</> : null;
  }

  return <>{children}</>;
};

export default FeatureGate;
