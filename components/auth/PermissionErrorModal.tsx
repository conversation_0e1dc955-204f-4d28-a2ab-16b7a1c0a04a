/**
 * Permission Error Modal Component
 * 
 * Shows a global popup when guest users encounter AppWrite permission errors,
 * encouraging them to register for enhanced features and benefits.
 */

import React from 'react';
import { useRouter } from 'expo-router';
import { Alert } from 'react-native';
import { 
  Modal, 
  ModalBackdrop, 
  ModalContent, 
  ModalHeader, 
  ModalCloseButton, 
  ModalBody, 
  ModalFooter 
} from '@/components/ui/modal';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Box } from '@/components/ui/box';
import { MaterialIcons } from '@expo/vector-icons';

interface PermissionErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  errorMessage?: string;
}

export const PermissionErrorModal: React.FC<PermissionErrorModalProps> = ({
  isOpen,
  onClose,
  errorMessage = "Permission denied"
}) => {
  const router = useRouter();

  const handleGoRegister = () => {
    onClose();
    router.push('/auth/signup');
  };

  const handleContinueAsGuest = () => {
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalBackdrop />
      <ModalContent className="bg-glass-bg-primary border-glass-border-primary backdrop-blur-md">
        <ModalHeader className="border-b border-glass-border-primary">
          <HStack space="md" className="items-center">
            <Box className="w-8 h-8 bg-candyPink-500 rounded-full items-center justify-center">
              <MaterialIcons name="lock" size={20} color="white" />
            </Box>
            <Text size="lg" className="font-semibold text-typography-900">
              Unlock Full Features
            </Text>
          </HStack>
          <ModalCloseButton />
        </ModalHeader>

        <ModalBody className="py-6">
          <VStack space="lg">
            {/* Error Context */}
            <Box className="bg-red-50 border border-red-200 rounded-lg p-4">
              <HStack space="sm" className="items-center mb-2">
                <MaterialIcons name="info" size={20} color="#EF4444" />
                <Text size="sm" className="font-medium text-red-800">
                  Guest User Limitation
                </Text>
              </HStack>
              <Text size="sm" className="text-red-700">
                {errorMessage}. Some features require a registered account for enhanced security and personalization.
              </Text>
            </Box>

            {/* Benefits Section */}
            <VStack space="md">
              <Text size="md" className="font-semibold text-typography-900">
                Create your account to unlock:
              </Text>
              
              <VStack space="sm">
                <HStack space="sm" className="items-center">
                  <MaterialIcons name="cloud-upload" size={20} color="#A855F7" />
                  <Text size="sm" className="text-typography-700 flex-1">
                    Save your knowledge cards permanently
                  </Text>
                </HStack>
                
                <HStack space="sm" className="items-center">
                  <MaterialIcons name="sync" size={20} color="#3B82F6" />
                  <Text size="sm" className="text-typography-700 flex-1">
                    Sync across all your devices
                  </Text>
                </HStack>
                
                <HStack space="sm" className="items-center">
                  <MaterialIcons name="analytics" size={20} color="#10B981" />
                  <Text size="sm" className="text-typography-700 flex-1">
                    Track your learning progress
                  </Text>
                </HStack>
                
                <HStack space="sm" className="items-center">
                  <MaterialIcons name="security" size={20} color="#F59E0B" />
                  <Text size="sm" className="text-typography-700 flex-1">
                    Enhanced privacy and security
                  </Text>
                </HStack>
                
                <HStack space="sm" className="items-center">
                  <MaterialIcons name="star" size={20} color="#FF6B9D" />
                  <Text size="sm" className="text-typography-700 flex-1">
                    Access premium features
                  </Text>
                </HStack>
              </VStack>
            </VStack>

            {/* Trust Indicators */}
            <Box className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <HStack space="sm" className="items-center mb-2">
                <MaterialIcons name="verified" size={20} color="#3B82F6" />
                <Text size="sm" className="font-medium text-blue-800">
                  Quick & Secure Registration
                </Text>
              </HStack>
              <Text size="sm" className="text-blue-700">
                • Free forever • No credit card required • 2-minute setup
              </Text>
            </Box>
          </VStack>
        </ModalBody>

        <ModalFooter className="border-t border-glass-border-primary">
          <VStack space="sm" className="w-full">
            <Button
              action="candyPink"
              size="lg"
              className="w-full"
              onPress={handleGoRegister}
            >
              <HStack space="sm" className="items-center">
                <MaterialIcons name="person-add" size={20} color="white" />
                <ButtonText className="font-semibold">Go Register</ButtonText>
              </HStack>
            </Button>
            
            <Button
              action="glass"
              size="md"
              className="w-full"
              onPress={handleContinueAsGuest}
            >
              <ButtonText className="text-typography-600">
                Continue as Guest
              </ButtonText>
            </Button>
          </VStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default PermissionErrorModal;
