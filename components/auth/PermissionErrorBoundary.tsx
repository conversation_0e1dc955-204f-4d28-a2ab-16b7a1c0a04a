/**
 * Permission Error Boundary Component
 * 
 * Global error boundary that catches AppWrite permission errors and shows
 * the registration encouragement modal for guest users automatically.
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Alert, Text, Pressable, View } from 'react-native';
import { isPermissionError, isGuestUserId, logPermissionError } from '@/lib/utils/permissionErrorUtils';

interface Props {
  children: ReactNode;
  onPermissionError?: (error: Error, userId?: string) => void;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class PermissionErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error, errorInfo: null };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error for debugging
    console.error('🚨 PermissionErrorBoundary caught an error:', error);
    console.error('Error Info:', errorInfo);

    // Update state with error info
    this.setState({ errorInfo });

    // Check if this is a permission error
    if (isPermissionError(error)) {
      logPermissionError(error, undefined, 'ErrorBoundary');
      
      // Call the permission error handler if provided
      if (this.props.onPermissionError) {
        this.props.onPermissionError(error);
      }
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
  };

  render() {
    if (this.state.hasError) {
      const { error } = this.state;
      
      // Check if this is a permission error
      if (error && isPermissionError(error)) {
        // For permission errors, we don't show the error boundary UI
        // Instead, the PermissionErrorContext will handle showing the modal
        // Reset the error boundary and let the app continue
        setTimeout(() => {
          this.handleRetry();
        }, 100);
        
        // Return children to continue normal app flow
        return this.props.children;
      }

      // For non-permission errors, show fallback UI or default error screen
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error boundary UI for non-permission errors
      return (
        <View style={{ 
          padding: 20, 
          backgroundColor: '#f8f9fa',
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <Text style={{ color: '#dc3545', marginBottom: 16, fontSize: 20, fontWeight: 'bold' }}>Something went wrong</Text>
          <Text style={{ color: '#6c757d', marginBottom: 20 }}>
            An unexpected error occurred. Please try again.
          </Text>
          <Pressable 
            onPress={this.handleRetry}
            style={{
              backgroundColor: '#007bff',
              padding: 10,
              borderRadius: 5,
              alignItems: 'center'
            }}
          >
            <Text style={{ color: 'white', fontWeight: 'bold' }}>Try Again</Text>
          </Pressable>
          {__DEV__ && (
            <View style={{ marginTop: 20 }}>
              <Text style={{ color: '#6c757d', marginBottom: 10 }}>
                Error Details (Development)
              </Text>
              <Text style={{ 
                backgroundColor: '#f8f9fa', 
                padding: 10, 
                borderRadius: 5,
                fontSize: 12,
                fontFamily: 'monospace'
              }}>
                {error?.toString()}
                {this.state.errorInfo?.componentStack}
              </Text>
            </View>
          )}
        </View>
      );
    }

    return this.props.children;
  }
}

export default PermissionErrorBoundary;
