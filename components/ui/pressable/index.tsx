'use client';
import React from 'react';
import { createPressable } from '@gluestack-ui/pressable';
import { Pressable as RNPressable } from 'react-native';

import { tva } from '@gluestack-ui/nativewind-utils/tva';
import { withStyleContext } from '@gluestack-ui/nativewind-utils/withStyleContext';
import type { VariantProps } from '@gluestack-ui/nativewind-utils';

const UIPressable = createPressable({
  Root: withStyleContext(RNPressable, 'className'),
});

const pressableStyle = tva({
  base: 'data-[focus-visible=true]:outline-none data-[focus-visible=true]:ring-2 data-[disabled=true]:opacity-40 flex-row items-center justify-center',
  variants: {
    // === ACTION VARIANTS (matching Button design tokens) ===
    action: {
      // === EXISTING ACTIONS ===
      primary:
        'data-[hover=true]:bg-primary-500/10 data-[pressed=true]:bg-primary-500/20 data-[focus-visible=true]:ring-primary-500 border-primary-300',
      secondary:
        'data-[hover=true]:bg-secondary-500/10 data-[pressed=true]:bg-secondary-500/20 data-[focus-visible=true]:ring-secondary-500 border-secondary-300',
      positive:
        'data-[hover=true]:bg-success-500/10 data-[pressed=true]:bg-success-500/20 data-[focus-visible=true]:ring-success-500 border-success-300',
      negative:
        'data-[hover=true]:bg-error-500/10 data-[pressed=true]:bg-error-500/20 data-[focus-visible=true]:ring-error-500 border-error-300',
      default:
        'data-[hover=true]:bg-background-50 data-[pressed=true]:bg-background-100 data-[focus-visible=true]:ring-indicator-info',

      // === NEW LEARNI SCAN CANDY VARIANTS ===
      candyPink:
        'data-[hover=true]:bg-primary-500/15 data-[pressed=true]:bg-primary-500/25 data-[focus-visible=true]:ring-primary-500 border-primary-400',
      candyPurple:
        'data-[hover=true]:bg-secondary-500/15 data-[pressed=true]:bg-secondary-500/25 data-[focus-visible=true]:ring-secondary-500 border-secondary-400',
      candyBlue:
        'data-[hover=true]:bg-tertiary-500/15 data-[pressed=true]:bg-tertiary-500/25 data-[focus-visible=true]:ring-tertiary-500 border-tertiary-400',

      // === GLASS MORPHISM VARIANTS ===
      glass:
        'data-[hover=true]:bg-glass-bg-primary/50 data-[pressed=true]:bg-glass-bg-primary/80 data-[focus-visible=true]:ring-glass-border-accent border-glass-border-primary backdrop-blur-md',
      glassCard:
        'data-[hover=true]:bg-glass-bg-card/50 data-[pressed=true]:bg-glass-bg-card/80 data-[focus-visible=true]:ring-glass-border-secondary border-glass-border-secondary backdrop-blur-md',
    },

    // === VARIANT STYLES ===
    variant: {
      default: '',
      enhanced: 'rounded-lg shadow-sm',
      glass: 'backdrop-blur-md border rounded-xl',
      solid: 'rounded-lg',
      outline: 'bg-transparent border rounded-lg',
      ghost: 'bg-transparent rounded-lg',
    },

    // === SIZE VARIANTS (matching Button) ===
    size: {
      xs: 'px-3.5 h-8 rounded-md',
      sm: 'px-4 h-9 rounded-md',
      md: 'px-5 h-10 rounded-lg',
      lg: 'px-6 h-11 rounded-lg',
      xl: 'px-7 h-12 rounded-xl',
    },
  },

  // === COMPOUND VARIANTS ===
  compoundVariants: [
    // === CANDY COLORS + OUTLINE ===
    {
      action: 'candyPink',
      variant: 'outline',
      class:
        'bg-transparent border-primary-500 data-[hover=true]:bg-primary-50/50 data-[pressed=true]:bg-primary-100/50',
    },
    {
      action: 'candyPurple',
      variant: 'outline',
      class:
        'bg-transparent border-secondary-500 data-[hover=true]:bg-secondary-50/50 data-[pressed=true]:bg-secondary-100/50',
    },
    {
      action: 'candyBlue',
      variant: 'outline',
      class:
        'bg-transparent border-tertiary-500 data-[hover=true]:bg-tertiary-50/50 data-[pressed=true]:bg-tertiary-100/50',
    },

    // === GLASS + OUTLINE ===
    {
      action: 'glass',
      variant: 'outline',
      class:
        'bg-glass-bg-secondary/30 border-glass-border-primary data-[hover=true]:bg-glass-bg-primary/40 data-[pressed=true]:bg-glass-bg-secondary/60',
    },
    {
      action: 'glassCard',
      variant: 'outline',
      class:
        'bg-glass-bg-primary/30 border-glass-border-secondary data-[hover=true]:bg-glass-bg-card/40 data-[pressed=true]:bg-glass-bg-primary/60',
    },

    // === SOLID VARIANTS ===
    {
      action: 'candyPink',
      variant: 'solid',
      class:
        'bg-primary-500/90 data-[hover=true]:bg-primary-600 data-[pressed=true]:bg-primary-700 shadow-lg',
    },
    {
      action: 'candyPurple',
      variant: 'solid',
      class:
        'bg-secondary-500/90 data-[hover=true]:bg-secondary-600 data-[pressed=true]:bg-secondary-700 shadow-lg',
    },
    {
      action: 'candyBlue',
      variant: 'solid',
      class:
        'bg-tertiary-500/90 data-[hover=true]:bg-tertiary-600 data-[pressed=true]:bg-tertiary-700 shadow-lg',
    },

    // === GHOST VARIANTS ===
    {
      action: 'candyPink',
      variant: 'ghost',
      class:
        'bg-transparent data-[hover=true]:bg-primary-500/10 data-[pressed=true]:bg-primary-500/20',
    },
    {
      action: 'candyPurple',
      variant: 'ghost',
      class:
        'bg-transparent data-[hover=true]:bg-secondary-500/10 data-[pressed=true]:bg-secondary-500/20',
    },
    {
      action: 'candyBlue',
      variant: 'ghost',
      class:
        'bg-transparent data-[hover=true]:bg-tertiary-500/10 data-[pressed=true]:bg-tertiary-500/20',
    },
  ],
});

type IPressableProps = Omit<
  React.ComponentProps<typeof UIPressable>,
  'context'
> &
  VariantProps<typeof pressableStyle>;

const Pressable = React.forwardRef<
  React.ComponentRef<typeof UIPressable>,
  IPressableProps
>(function Pressable({ 
  className, 
  variant = 'default', 
  size = 'md', 
  action = 'default',
  ...props 
}, ref) {
  return (
    <UIPressable
      {...props}
      ref={ref}
      className={pressableStyle({
        variant,
        size, 
        action,
        class: className,
      })}
    />
  );
});

Pressable.displayName = 'Pressable';
export { Pressable };