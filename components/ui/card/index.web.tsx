import React from 'react';
import { cardStyle } from './styles';
import type { VariantProps } from '@gluestack-ui/nativewind-utils';

type ICardProps = React.ComponentPropsWithoutRef<'div'> &
  VariantProps<typeof cardStyle>;

const Card = React.forwardRef<HTMLDivElement, ICardProps>(function Card(
  { className, size = 'md', variant = 'elevated', ...props },
  ref
) {
  return (
    <div
      className={cardStyle({ size, variant, class: className })}
      {...props}
      ref={ref}
    />
  );
});

Card.displayName = 'Card';

// CardHeader component for web
type ICardHeaderProps = React.ComponentPropsWithoutRef<'div'> & { className?: string };

const CardHeader = React.forwardRef<HTMLDivElement, ICardHeaderProps>(function CardHeader(
  { className, ...props },
  ref
) {
  return (
    <div
      className={`pb-3 ${className || ''}`}
      {...props}
      ref={ref}
    />
  );
});

CardHeader.displayName = 'CardHeader';

// CardBody component for web
type ICardBodyProps = React.ComponentPropsWithoutRef<'div'> & { className?: string };

const CardBody = React.forwardRef<HTMLDivElement, ICardBodyProps>(function CardBody(
  { className, ...props },
  ref
) {
  return (
    <div
      className={`flex-1 ${className || ''}`}
      {...props}
      ref={ref}
    />
  );
});

CardBody.displayName = 'CardBody';

export { Card, CardHeader, CardBody };
