import React from 'react';
import type { VariantProps } from '@gluestack-ui/nativewind-utils';
import { Box, type IBoxProps } from '@/components/ui/box';
import { cardStyle } from './styles';

type ICardProps = IBoxProps &
  VariantProps<typeof cardStyle> & { className?: string };

export const Card = React.forwardRef<React.ComponentRef<typeof Box>, ICardProps>(
  (
    { className, size = 'md', variant = 'elevated' as const, ...props },
    ref
  ) => {
    return (
      <Box
        className={cardStyle({ size, variant, class: className })}
        {...props}
        ref={ref}
      />
    );
  }
);

Card.displayName = 'Card';
