import { tva } from '@gluestack-ui/nativewind-utils/tva';
import { isWeb } from '@gluestack-ui/nativewind-utils/IsWeb';

const gridBaseStyle = isWeb
  ? 'grid grid-cols-12'
  : 'box-border flex-row flex-wrap justify-start';
const gridItemBaseStyle = isWeb ? 'w-auto col-span-1' : '';

export const gridStyle = tva({
  base: `w-full ${gridBaseStyle}`,
  variants: {
    // === NEW LEARNI SCAN GLASS VARIANTS ===
    variant: {
      default: '',
      glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md rounded-xl p-4',
      glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-2xl p-6',
    }
  },
});

export const gridItemStyle = tva({
  base: `w-full ${gridItemBaseStyle}`,
});
