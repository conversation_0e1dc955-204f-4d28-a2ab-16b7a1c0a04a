import { tva } from '@gluestack-ui/nativewind-utils/tva';
import { isWeb } from '@gluestack-ui/nativewind-utils/IsWeb';

const baseStyle = isWeb ? 'flex flex-col relative z-0' : '';

export const centerStyle = tva({
  base: `justify-center items-center ${baseStyle}`,
  variants: {
    // === NEW LEARNI SCAN VARIANTS ===
    variant: {
      default: '',
      glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md rounded-xl p-4',
      glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-2xl p-6',
      candyBorder: 'border-2 border-primary-500 rounded-xl p-4',
    }
  },
});
