import React from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import { useColorScheme } from 'react-native';
import { cn } from '@/lib/utils';

export interface GradientBackgroundProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'candy' | 'glass';
}

export const GradientBackground: React.FC<GradientBackgroundProps> = ({
  children,
  className,
  variant = 'default'
}) => {
  const colorScheme = useColorScheme();

  const getGradientColors = (): [string, string, ...string[]] => {
    const isDark = colorScheme === 'dark';
    
    switch (variant) {
      case 'candy':
        return isDark 
          ? ['#1e1b4b', '#312e81', '#4338ca', '#6366f1']
          : ['#f0f9ff', '#e0f2fe', '#bae6fd', '#7dd3fc'];
      
      case 'glass':
        return isDark
          ? ['rgba(15, 23, 42, 0.95)', 'rgba(30, 41, 59, 0.9)', 'rgba(51, 65, 85, 0.85)']
          : ['rgba(248, 250, 252, 0.95)', 'rgba(241, 245, 249, 0.9)', 'rgba(226, 232, 240, 0.85)'];
      
      default:
        return isDark
          ? ['#0f172a', '#1e293b', '#334155']
          : ['#f8fafc', '#f1f5f9', '#e2e8f0'];
    }
  };

  return (
    <LinearGradient
      colors={getGradientColors()}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      className={cn('flex-1', className)}
    >
      {children}
    </LinearGradient>
  );
};
