import React from 'react';
import { Box } from '@/components/ui/box';
import { cn } from '@/lib/utils';

export interface GradientBackgroundProps {
  children?: React.ReactNode;
  className?: string;
  variant?: 'candy' | 'glass' | 'pink' | 'purple' | 'blue';
  opacity?: 'light' | 'medium' | 'strong';
}

/**
 * GradientBackground - Reusable gradient background component
 * 
 * Provides beautiful gradient backgrounds using design tokens from the LearniScan design system.
 * Supports various gradient variants and opacity levels for consistent theming.
 */
export const GradientBackground = React.forwardRef<
  React.ElementRef<typeof Box>,
  GradientBackgroundProps
>(({ children, className, variant = 'candy', opacity = 'medium', ...props }, ref) => {
  
  // Map variant and opacity to CSS classes
  const getGradientClass = () => {
    const baseClass = 'gradient-candy'; // Default candy gradient
    
    switch (variant) {
      case 'candy':
        return 'gradient-candy'; // Pink to purple to blue
      case 'glass':
        return 'glass-card'; // Glass morphism gradient
      case 'pink':
        return 'btn-candy-pink'; // Pink gradient
      case 'purple':
        return 'btn-candy-purple'; // Purple gradient
      case 'blue':
        return 'btn-candy-blue'; // Blue gradient
      default:
        return baseClass;
    }
  };

  // Map opacity to Tailwind opacity classes
  const getOpacityClass = () => {
    switch (opacity) {
      case 'light':
        return 'opacity-70';
      case 'medium':
        return 'opacity-85';
      case 'strong':
        return 'opacity-95';
      default:
        return 'opacity-85';
    }
  };

  return (
    <Box
      ref={ref}
      className={cn(
        'flex-1', // Full height by default
        getGradientClass(),
        getOpacityClass(),
        className
      )}
      {...props}
    >
      {children}
    </Box>
  );
});

GradientBackground.displayName = 'GradientBackground';

export default GradientBackground;
