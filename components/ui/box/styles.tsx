import { tva } from '@gluestack-ui/nativewind-utils/tva';
import { isWeb } from '@gluestack-ui/nativewind-utils/IsWeb';

const baseStyle = isWeb
  ? 'flex flex-col relative z-0 box-border border-0 list-none min-w-0 min-h-0 bg-transparent items-stretch m-0 p-0 text-decoration-none'
  : '';

export const boxStyle = tva({
  base: baseStyle,
  variants: {
    // === LEARNI SCAN GLASS VARIANTS ===
    variant: {
      default: '',
      glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md rounded-xl',
      glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-2xl p-6',
      candyBorder: 'border-2 border-primary-500 rounded-xl',
    }
  }
});
