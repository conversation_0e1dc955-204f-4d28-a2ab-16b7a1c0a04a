'use client';
import { ActivityIndicator } from 'react-native';
import React from 'react';
import { tva } from '@gluestack-ui/nativewind-utils/tva';
import { cssInterop } from 'nativewind';

cssInterop(ActivityIndicator, {
  className: { target: 'style', nativeStyleToProp: { color: true } },
});

const spinnerStyle = tva({
  base: '',
  variants: {
    // === LEARNI SCAN CANDY COLOR VARIANTS ===
    color: {
      default: '',
      candyPink: 'text-primary-500',
      candyPurple: 'text-secondary-500', 
      candyBlue: 'text-tertiary-500',
      white: 'text-typography-0',
      muted: 'text-typography-400',
    },
    size: {
      small: 'w-4 h-4',
      medium: 'w-6 h-6', 
      large: 'w-8 h-8',
      xl: 'w-10 h-10',
    }
  },
});

const Spinner = React.forwardRef<
  React.ComponentRef<typeof ActivityIndicator>,
  React.ComponentProps<typeof ActivityIndicator>
>(function Spinner(
  {
    className,
    color,
    focusable = false,
    'aria-label': ariaLabel = 'loading',
    ...props
  },
  ref
) {
  return (
    <ActivityIndicator
      ref={ref}
      focusable={focusable}
      aria-label={ariaLabel}
      {...props}
      color={color}
      className={spinnerStyle({ class: className })}
    />
  );
});

Spinner.displayName = 'Spinner';

export { Spinner };
