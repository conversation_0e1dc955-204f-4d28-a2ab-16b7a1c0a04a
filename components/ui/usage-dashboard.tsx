/**
 * Usage Dashboard Component
 * 
 * Comprehensive usage tracking display for LearniScan's freemium features:
 * - Real-time usage indicators
 * - Progress bars and visual feedback
 * - Upgrade prompts and conversion opportunities
 * - Plan comparison and benefits
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 6: UI Design Consistency with Gluestack UI
 * - Rule 8: Smooth animations with react-native-reanimated
 * - Rule 11: Zod validation for all state
 * - Rule 12: Comprehensive error handling
 */

import React, { useEffect } from 'react';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

// UI Components following Rule #6
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';
import { Badge, BadgeText } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Progress, ProgressFilledTrack } from '@/components/ui/progress';

// Icons
import {
  Crown,
  Zap,
  Search,
  FileText,
  Brain,
  Cloud,
  TrendingUp,
  Infinity,
  AlertTriangle,
  CheckCircle,
} from 'lucide-react-native';

// Permission system integration
import { useUsageLimitedFeature, usePermissions } from '@/hooks/usePermissions';

interface UsageIndicatorProps {
  feature: string;
  label: string;
  icon: React.ComponentType<any>;
  variant?: 'compact' | 'detailed' | 'card';
  showProgress?: boolean;
  className?: string;
}

interface UsageDashboardProps {
  variant?: 'overview' | 'detailed' | 'settings';
  showUpgradePrompt?: boolean;
  onUpgrade?: () => void;
  className?: string;
}

export const UsageIndicator: React.FC<UsageIndicatorProps> = ({
  feature,
  label,
  icon: IconComponent,
  variant = 'compact',
  showProgress = true,
  className,
}) => {
  const { usage, isUnlimited, isLoading, canUseFeature } = useUsageLimitedFeature(feature);
  
  // Animation values (Rule 8)
  const progressValue = useSharedValue(0);
  const scaleValue = useSharedValue(0.95);

  useEffect(() => {
    if (!isLoading && !isUnlimited) {
      const percentage = usage.limit > 0 ? (usage.current / usage.limit) * 100 : 0;
      progressValue.value = withTiming(percentage, { duration: 800 });
    }
    scaleValue.value = withSpring(1, { damping: 15, stiffness: 150 });
  }, [usage, isLoading, isUnlimited]);

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${progressValue.value}%`,
  }));

  const containerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleValue.value }],
  }));

  if (isLoading) {
    return (
      <Box className={`p-3 bg-gray-100 rounded-lg ${className}`}>
        <HStack className="items-center justify-between">
          <HStack className="items-center gap-2">
            <IconComponent size={16} color="#9ca3af" />
            <Text size="sm" className="text-gray-500">Loading...</Text>
          </HStack>
        </HStack>
      </Box>
    );
  }

  if (isUnlimited) {
    return (
      <Animated.View style={containerAnimatedStyle}>
        <Box className={`p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200 ${className}`}>
          <HStack className="items-center justify-between">
            <HStack className="items-center gap-2">
              <IconComponent size={16} color="#8b5cf6" />
              <Text size="sm" className="font-medium text-purple-700">{label}</Text>
            </HStack>
            <Badge action="candyPurple" size="sm">
              <Infinity size={10} />
              <BadgeText>Unlimited</BadgeText>
            </Badge>
          </HStack>
        </Box>
      </Animated.View>
    );
  }

  const percentage = usage.limit > 0 ? (usage.current / usage.limit) * 100 : 0;
  const badgeAction = percentage > 80 ? 'error' : percentage > 60 ? 'warning' : 'success';
  const isNearLimit = usage.remaining <= 2;

  if (variant === 'card') {
    return (
      <Animated.View style={containerAnimatedStyle}>
        <Card className={className}>
          <Box className="p-4">
            <VStack space="md">
              <HStack className="items-center justify-between">
                <HStack className="items-center gap-3">
                  <Box className="p-2 bg-blue-100 rounded-lg">
                    <IconComponent size={20} color="#3b82f6" />
                  </Box>
                  <VStack>
                    <Text className="font-semibold text-gray-900">{label}</Text>
                    <Text size="sm" className="text-gray-600">
                      {usage.current} of {usage.limit} used
                    </Text>
                  </VStack>
                </HStack>
                <Badge action={badgeAction}>
                  <BadgeText>{usage.remaining} left</BadgeText>
                </Badge>
              </HStack>
              
              {showProgress && (
                <VStack space="xs">
                  <Progress value={percentage} size="sm" className="bg-gray-200">
                    <ProgressFilledTrack className={`${badgeAction === 'error' ? 'bg-red-500' : badgeAction === 'warning' ? 'bg-yellow-500' : 'bg-green-500'}`} />
                  </Progress>
                  {isNearLimit && (
                    <HStack className="items-center gap-1">
                      <AlertTriangle size={12} color="#f59e0b" />
                      <Text size="xs" className="text-yellow-600">
                        {usage.remaining} uses remaining
                      </Text>
                    </HStack>
                  )}
                </VStack>
              )}
            </VStack>
          </Box>
        </Card>
      </Animated.View>
    );
  }

  if (variant === 'detailed') {
    return (
      <Animated.View style={containerAnimatedStyle}>
        <VStack space="xs" className={className}>
          <HStack className="items-center justify-between">
            <HStack className="items-center gap-2">
              <IconComponent size={16} color="#6b7280" />
              <Text size="sm" className="font-medium text-gray-700">{label}</Text>
            </HStack>
            <Badge action={badgeAction} size="sm">
              <BadgeText>{usage.current}/{usage.limit}</BadgeText>
            </Badge>
          </HStack>
          
          {showProgress && (
            <VStack space="xs">
              <Progress value={percentage} size="sm" className="bg-gray-200">
                <ProgressFilledTrack className={`${badgeAction === 'error' ? 'bg-red-500' : badgeAction === 'warning' ? 'bg-yellow-500' : 'bg-green-500'}`} />
              </Progress>
              {isNearLimit && (
                <Text size="xs" className="text-yellow-600">
                  {usage.remaining} uses remaining
                </Text>
              )}
            </VStack>
          )}
        </VStack>
      </Animated.View>
    );
  }

  // Compact variant (default)
  return (
    <Animated.View style={containerAnimatedStyle}>
      <HStack className={`items-center justify-between p-2 bg-gray-50 rounded-lg ${className}`}>
        <HStack className="items-center gap-2">
          <IconComponent size={14} color="#6b7280" />
          <Text size="sm" className="text-gray-600">{label}</Text>
        </HStack>
        <Badge action={badgeAction} size="sm">
          <BadgeText>{usage.current}/{usage.limit}</BadgeText>
        </Badge>
      </HStack>
    </Animated.View>
  );
};

export const UsageDashboard: React.FC<UsageDashboardProps> = ({
  variant = 'overview',
  showUpgradePrompt = true,
  onUpgrade,
  className,
}) => {
  const { isPremium, userRole, planInfo } = usePermissions();

  const features = [
    { key: 'daily_scans', label: 'Daily Scans', icon: FileText },
    { key: 'ai_searches', label: 'AI Searches', icon: Search },
    { key: 'ai_generations', label: 'AI Generations', icon: Brain },
    { key: 'total_cards', label: 'Knowledge Cards', icon: Zap },
  ];

  if (variant === 'overview') {
    return (
      <VStack space="md" className={className}>
        {features.map((feature) => (
          <UsageIndicator
            key={feature.key}
            feature={feature.key}
            label={feature.label}
            icon={feature.icon}
            variant="compact"
          />
        ))}
        
        {!isPremium && showUpgradePrompt && (
          <Card className="border-2 border-pink-200 bg-gradient-to-r from-pink-50 to-purple-50">
            <Box className="p-4">
              <VStack space="md" className="items-center">
                <Crown size={32} color="#ec4899" />
                <VStack space="sm" className="items-center">
                  <Text className="font-semibold text-lg text-center">
                    Upgrade to Premium
                  </Text>
                  <Text size="sm" className="text-gray-600 text-center">
                    Unlock unlimited access to all features
                  </Text>
                </VStack>
                <Button action="candyPink" onPress={onUpgrade}>
                  <ButtonIcon as={Crown} />
                  <ButtonText>Upgrade Now</ButtonText>
                </Button>
              </VStack>
            </Box>
          </Card>
        )}
      </VStack>
    );
  }

  if (variant === 'detailed') {
    return (
      <VStack space="lg" className={className}>
        <VStack space="md">
          <Text className="font-semibold text-lg">Usage Overview</Text>
          {features.map((feature) => (
            <UsageIndicator
              key={feature.key}
              feature={feature.key}
              label={feature.label}
              icon={feature.icon}
              variant="detailed"
            />
          ))}
        </VStack>

        {!isPremium && (
          <Card className="border-2 border-purple-200">
            <Box className="p-4">
              <VStack space="md">
                <HStack className="items-center gap-3">
                  <Crown size={24} color="#8b5cf6" />
                  <Text className="font-semibold text-lg">Premium Benefits</Text>
                </HStack>
                <VStack space="sm">
                  {[
                    'Unlimited document scans',
                    'Unlimited AI searches',
                    'Unlimited AI card generation',
                    'Advanced study sessions',
                    'Priority support',
                  ].map((benefit, index) => (
                    <HStack key={index} className="items-center gap-2">
                      <CheckCircle size={16} color="#10b981" />
                      <Text size="sm" className="text-gray-700">{benefit}</Text>
                    </HStack>
                  ))}
                </VStack>
                <Button action="candyPurple" onPress={onUpgrade}>
                  <ButtonIcon as={Crown} />
                  <ButtonText>Upgrade to Premium</ButtonText>
                </Button>
              </VStack>
            </Box>
          </Card>
        )}
      </VStack>
    );
  }

  // Settings variant
  return (
    <VStack space="lg" className={className}>
      <VStack space="md">
        <HStack className="items-center justify-between">
          <Text className="font-semibold text-lg">Current Plan</Text>
          <Badge action={isPremium ? 'candyPurple' : 'success'}>
            <BadgeText>{isPremium ? 'Premium' : 'Free'}</BadgeText>
          </Badge>
        </HStack>
        
        {features.map((feature) => (
          <UsageIndicator
            key={feature.key}
            feature={feature.key}
            label={feature.label}
            icon={feature.icon}
            variant="card"
          />
        ))}
      </VStack>
    </VStack>
  );
};

export default UsageDashboard;
