/**
 * Usage Indicator Component
 * 
 * Simple, reusable usage tracking display for LearniScan's freemium features:
 * - Real-time usage indicators
 * - Progress bars and visual feedback
 * - Multiple display variants
 * - Smooth animations
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 6: UI Design Consistency with Gluestack UI
 * - Rule 8: Smooth animations with react-native-reanimated
 * - Rule 11: Zod validation for all state
 * - Rule 12: Comprehensive error handling
 */

import React, { useEffect } from 'react';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

// UI Components following Rule #6
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Badge, BadgeText } from '@/components/ui/badge';
import { Progress, ProgressFilledTrack } from '@/components/ui/progress';

// Icons
import {
  Infinity,
  AlertTriangle,
} from 'lucide-react-native';

// Permission system integration
import { useUsageLimitedFeature } from '@/hooks/usePermissions';

interface UsageIndicatorProps {
  feature: string;
  label: string;
  icon: React.ComponentType<any>;
  variant?: 'compact' | 'detailed' | 'card';
  showProgress?: boolean;
  className?: string;
}

export const UsageIndicator: React.FC<UsageIndicatorProps> = ({
  feature,
  label,
  icon: IconComponent,
  variant = 'compact',
  showProgress = true,
  className,
}) => {
  const { usage, isUnlimited, isLoading, canUseFeature } = useUsageLimitedFeature(feature);
  
  // Animation values (Rule 8)
  const progressValue = useSharedValue(0);
  const scaleValue = useSharedValue(0.95);

  useEffect(() => {
    if (!isLoading && !isUnlimited) {
      const percentage = usage.limit > 0 ? (usage.current / usage.limit) * 100 : 0;
      progressValue.value = withTiming(percentage, { duration: 800 });
    }
    scaleValue.value = withSpring(1, { damping: 15, stiffness: 150 });
  }, [usage, isLoading, isUnlimited]);

  const containerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleValue.value }],
  }));

  if (isLoading) {
    return (
      <Box className={`p-3 bg-gray-100 rounded-lg ${className}`}>
        <HStack className="items-center justify-between">
          <HStack className="items-center gap-2">
            <IconComponent size={16} color="#9ca3af" />
            <Text size="sm" className="text-gray-500">Loading...</Text>
          </HStack>
        </HStack>
      </Box>
    );
  }

  if (isUnlimited) {
    return (
      <Animated.View style={containerAnimatedStyle}>
        <Box className={`p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200 ${className}`}>
          <HStack className="items-center justify-between">
            <HStack className="items-center gap-2">
              <IconComponent size={16} color="#8b5cf6" />
              <Text size="sm" className="font-medium text-purple-700">{label}</Text>
            </HStack>
            <Badge action="candyPurple" size="sm">
              <Infinity size={10} />
              <BadgeText>Unlimited</BadgeText>
            </Badge>
          </HStack>
        </Box>
      </Animated.View>
    );
  }

  const percentage = usage.limit > 0 ? (usage.current / usage.limit) * 100 : 0;
  const badgeAction = percentage > 80 ? 'error' : percentage > 60 ? 'warning' : 'success';
  const isNearLimit = usage.remaining <= 2;

  if (variant === 'detailed') {
    return (
      <Animated.View style={containerAnimatedStyle}>
        <VStack space="xs" className={className}>
          <HStack className="items-center justify-between">
            <HStack className="items-center gap-2">
              <IconComponent size={16} color="#6b7280" />
              <Text size="sm" className="font-medium text-gray-700">{label}</Text>
            </HStack>
            <Badge action={badgeAction} size="sm">
              <BadgeText>{usage.current}/{usage.limit}</BadgeText>
            </Badge>
          </HStack>
          
          {showProgress && (
            <VStack space="xs">
              <Progress value={percentage} size="sm" className="bg-gray-200">
                <ProgressFilledTrack className={`${badgeAction === 'error' ? 'bg-red-500' : badgeAction === 'warning' ? 'bg-yellow-500' : 'bg-green-500'}`} />
              </Progress>
              {isNearLimit && (
                <HStack className="items-center gap-1">
                  <AlertTriangle size={12} color="#f59e0b" />
                  <Text size="xs" className="text-yellow-600">
                    {usage.remaining} uses remaining
                  </Text>
                </HStack>
              )}
            </VStack>
          )}
        </VStack>
      </Animated.View>
    );
  }

  // Compact variant (default)
  return (
    <Animated.View style={containerAnimatedStyle}>
      <HStack className={`items-center justify-between p-2 bg-gray-50 rounded-lg ${className}`}>
        <HStack className="items-center gap-2">
          <IconComponent size={14} color="#6b7280" />
          <Text size="sm" className="text-gray-600">{label}</Text>
        </HStack>
        <Badge action={badgeAction} size="sm">
          <BadgeText>{usage.current}/{usage.limit}</BadgeText>
        </Badge>
      </HStack>
    </Animated.View>
  );
};

export default UsageIndicator;
