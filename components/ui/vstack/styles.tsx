import { isWeb } from '@gluestack-ui/nativewind-utils/IsWeb';
import { tva } from '@gluestack-ui/nativewind-utils/tva';

const baseStyle = isWeb
  ? 'flex flex-col relative z-0 box-border border-0 list-none min-w-0 min-h-0 bg-transparent items-stretch m-0 p-0 text-decoration-none'
  : '';

export const vstackStyle = tva({
  base: `flex-col ${baseStyle}`,
  variants: {
    space: {
      'xs': 'gap-1',
      'sm': 'gap-2',
      'md': 'gap-3',
      'lg': 'gap-4',
      'xl': 'gap-5',
      '2xl': 'gap-6',
      '3xl': 'gap-7',
      '4xl': 'gap-8',
    },
    reversed: {
      true: 'flex-col-reverse',
    },
    // === NEW LEARNI SCAN VARIANTS ===
    variant: {
      default: '',
      glass: 'bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md rounded-xl p-4',
      glassCard: 'bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-2xl p-6',
      candyBorder: 'border-2 border-primary-500 rounded-xl p-4',
    }
  },
});
