'use client';
import { vars } from 'nativewind';

export const config = {
               light: vars({
                 // === CANDY COLOR PALETTE ===
                 // Primary: Candy Pink (main brand color)
                 '--color-primary-0': '255 245 250',     // Very light pink
                 '--color-primary-50': '255 235 245',    // Light pink
                 '--color-primary-100': '255 225 240',   // Lighter pink
                 '--color-primary-200': '255 205 230',   // Light pink
                 '--color-primary-300': '255 185 220',   // Medium light pink
                 '--color-primary-400': '255 145 200',   // Medium pink
                 '--color-primary-500': '255 107 157',   // Candy Pink (main)
                 '--color-primary-600': '230 96 141',    // Darker pink
                 '--color-primary-700': '204 85 125',    // Dark pink
                 '--color-primary-800': '179 74 109',    // Darker pink
                 '--color-primary-900': '153 64 94',     // Very dark pink
                 '--color-primary-950': '128 53 78',     // Darkest pink
             
                 // Secondary: Candy Purple
                 '--color-secondary-0': '250 245 255',   // Very light purple
                 '--color-secondary-50': '245 235 255',  // Light purple
                 '--color-secondary-100': '240 225 255', // Lighter purple
                 '--color-secondary-200': '230 205 255', // Light purple
                 '--color-secondary-300': '220 185 255', // Medium light purple
                 '--color-secondary-400': '200 145 255', // Medium purple
                 '--color-secondary-500': '168 85 247',  // Candy Purple (main)
                 '--color-secondary-600': '151 76 222',  // Darker purple
                 '--color-secondary-700': '134 68 197',  // Dark purple
                 '--color-secondary-800': '117 59 172',  // Darker purple
                 '--color-secondary-900': '100 51 147',  // Very dark purple
                 '--color-secondary-950': '84 42 123',   // Darkest purple
             
                 // Tertiary: Candy Blue
                 '--color-tertiary-0': '240 248 255',    // Very light blue
                 '--color-tertiary-50': '230 242 255',   // Light blue
                 '--color-tertiary-100': '220 235 255',  // Lighter blue
                 '--color-tertiary-200': '200 220 255',  // Light blue
                 '--color-tertiary-300': '180 205 255',  // Medium light blue
                 '--color-tertiary-400': '140 175 255',  // Medium blue
                 '--color-tertiary-500': '59 130 246',   // Candy Blue (main)
                 '--color-tertiary-600': '53 117 221',   // Darker blue
                 '--color-tertiary-700': '47 104 196',   // Dark blue
                 '--color-tertiary-800': '41 91 171',    // Darker blue
                 '--color-tertiary-900': '35 78 147',    // Very dark blue
                 '--color-tertiary-950': '29 65 122',    // Darkest blue
             
                 // === SEMANTIC COLORS (using candy palette) ===
                 '--color-success-0': '228 255 244',     // Light green
                 '--color-success-50': '202 255 232',    // Lighter green
                 '--color-success-100': '162 241 192',   // Light green
                 '--color-success-200': '132 211 162',   // Medium light green
                 '--color-success-300': '102 181 132',   // Medium green
                 '--color-success-400': '72 151 102',    // Medium dark green
                 '--color-success-500': '16 185 129',    // Candy Green (main)
                 '--color-success-600': '14 166 116',    // Darker green
                 '--color-success-700': '12 147 103',    // Dark green
                 '--color-success-800': '10 128 90',     // Darker green
                 '--color-success-900': '8 109 77',      // Very dark green
                 '--color-success-950': '6 90 64',       // Darkest green
             
                 '--color-warning-0': '255 249 245',     // Light yellow
                 '--color-warning-50': '255 244 236',    // Lighter yellow
                 '--color-warning-100': '255 231 213',   // Light yellow
                 '--color-warning-200': '254 205 170',   // Medium light yellow
                 '--color-warning-300': '253 173 116',   // Medium yellow
                 '--color-warning-400': '251 149 75',    // Medium dark yellow
                 '--color-warning-500': '245 158 11',    // Candy Yellow (main)
                 '--color-warning-600': '220 142 10',    // Darker yellow
                 '--color-warning-700': '195 126 9',     // Dark yellow
                 '--color-warning-800': '170 110 8',     // Darker yellow
                 '--color-warning-900': '145 94 7',      // Very dark yellow
                 '--color-warning-950': '120 78 6',      // Darkest yellow
             
                 '--color-error-0': '254 233 233',       // Light red
                 '--color-error-50': '254 226 226',      // Lighter red
                 '--color-error-100': '254 202 202',     // Light red
                 '--color-error-200': '252 165 165',     // Medium light red
                 '--color-error-300': '248 113 113',     // Medium red
                 '--color-error-400': '239 68 68',       // Medium dark red
                 '--color-error-500': '239 68 68',       // Candy Red (main)
                 '--color-error-600': '215 61 61',       // Darker red
                 '--color-error-700': '191 54 54',       // Dark red
                 '--color-error-800': '167 47 47',       // Darker red
                 '--color-error-900': '143 40 40',       // Very dark red
                 '--color-error-950': '119 33 33',       // Darkest red
             
                 '--color-info-0': '236 248 254',        // Light cyan
                 '--color-info-50': '199 235 252',       // Lighter cyan
                 '--color-info-100': '162 221 250',      // Light cyan
                 '--color-info-200': '124 207 248',      // Medium light cyan
                 '--color-info-300': '87 194 246',       // Medium cyan
                 '--color-info-400': '50 180 244',       // Medium dark cyan
                 '--color-info-500': '6 182 212',        // Candy Cyan (main)
                 '--color-info-600': '5 164 191',        // Darker cyan
                 '--color-info-700': '4 146 170',        // Dark cyan
                 '--color-info-800': '3 128 149',        // Darker cyan
                 '--color-info-900': '2 110 128',        // Very dark cyan
                 '--color-info-950': '1 92 107',         // Darkest cyan
             
                 // === GLASS EFFECT COLORS ===
                 '--color-glass-bg-primary': '255 255 255 / 0.1',      // rgba(255, 255, 255, 0.1)
                 '--color-glass-bg-secondary': '255 255 255 / 0.05',   // rgba(255, 255, 255, 0.05)
                 '--color-glass-bg-card': '255 255 255 / 0.08',        // rgba(255, 255, 255, 0.08)
                 '--color-glass-border-primary': '255 255 255 / 0.2',  // rgba(255, 255, 255, 0.2)
                 '--color-glass-border-secondary': '255 255 255 / 0.1', // rgba(255, 255, 255, 0.1)
                 '--color-glass-border-accent': '255 107 157 / 0.3',   // rgba(255, 107, 157, 0.3)
             
                 // === TYPOGRAPHY COLORS (for dark gradient backgrounds) ===
                 '--color-typography-0': '255 255 255',         // White (100%)
                 '--color-typography-50': '255 255 255 / 0.95', // 95% white
                 '--color-typography-100': '255 255 255 / 0.9', // 90% white
                 '--color-typography-200': '255 255 255 / 0.8', // 80% white
                 '--color-typography-300': '255 255 255 / 0.7', // 70% white
                 '--color-typography-400': '255 255 255 / 0.6', // 60% white
                 '--color-typography-500': '255 255 255 / 0.5', // 50% white
                 '--color-typography-600': '255 255 255 / 0.4', // 40% white
                 '--color-typography-700': '255 255 255 / 0.3', // 30% white
                 '--color-typography-800': '255 255 255 / 0.2', // 20% white
                 '--color-typography-900': '255 255 255 / 0.1', // 10% white
                 '--color-typography-950': '255 255 255 / 0.05', // 5% white
             
                 // === OUTLINE COLORS ===
                 '--color-outline-0': '255 255 255 / 0.3',      // Light outline
                 '--color-outline-50': '255 255 255 / 0.25',    // Lighter outline
                 '--color-outline-100': '255 255 255 / 0.2',    // Light outline
                 '--color-outline-200': '255 255 255 / 0.18',   // Medium light outline
                 '--color-outline-300': '255 255 255 / 0.15',   // Medium outline
                 '--color-outline-400': '255 255 255 / 0.12',   // Medium dark outline
                 '--color-outline-500': '255 255 255 / 0.1',    // Main outline
                 '--color-outline-600': '255 255 255 / 0.08',   // Darker outline
                 '--color-outline-700': '255 255 255 / 0.06',   // Dark outline
                 '--color-outline-800': '255 255 255 / 0.04',   // Darker outline
                 '--color-outline-900': '255 255 255 / 0.02',   // Very dark outline
                 '--color-outline-950': '255 255 255 / 0.01',   // Darkest outline
             
                 // === BACKGROUND COLORS ===
                 '--color-background-0': '0 0 0',               // Black
                 '--color-background-50': '18 18 18',           // Very dark
                 '--color-background-100': '31 31 31',          // Dark
                 '--color-background-200': '45 45 45',          // Medium dark
                 '--color-background-300': '64 64 64',          // Medium
                 '--color-background-400': '82 82 82',          // Medium light
                 '--color-background-500': '115 115 115',       // Light
                 '--color-background-600': '140 140 140',       // Lighter
                 '--color-background-700': '165 165 165',       // Very light
                 '--color-background-800': '190 190 190',       // Lightest
                 '--color-background-900': '215 215 215',       // Almost white
                 '--color-background-950': '240 240 240',       // Near white
             
                 // === BACKGROUND SPECIAL ===
                 '--color-background-error': '239 68 68 / 0.1',     // Error background
                 '--color-background-warning': '245 158 11 / 0.1',  // Warning background
                 '--color-background-success': '16 185 129 / 0.1',  // Success background
                 '--color-background-muted': '255 255 255 / 0.05',  // Muted background
                 '--color-background-info': '6 182 212 / 0.1',      // Info background
             
                 // === FOCUS RING INDICATORS ===
                 '--color-indicator-primary': '255 107 157',    // Candy pink indicator
                 '--color-indicator-info': '6 182 212',         // Candy cyan indicator
                 '--color-indicator-error': '239 68 68',        // Candy red indicator
               }),
               
               dark: vars({
                 // Dark mode uses same candy colors but adjusted for dark backgrounds
                 '--color-primary-0': '255 245 250',
                 '--color-primary-50': '255 235 245',
                 '--color-primary-100': '255 225 240',
                 '--color-primary-200': '255 205 230',
                 '--color-primary-300': '255 185 220',
                 '--color-primary-400': '255 145 200',
                 '--color-primary-500': '255 107 157',   // Candy Pink (same)
                 '--color-primary-600': '230 96 141',
                 '--color-primary-700': '204 85 125',
                 '--color-primary-800': '179 74 109',
                 '--color-primary-900': '153 64 94',
                 '--color-primary-950': '128 53 78',
             
                 '--color-secondary-0': '250 245 255',
                 '--color-secondary-50': '245 235 255',
                 '--color-secondary-100': '240 225 255',
                 '--color-secondary-200': '230 205 255',
                 '--color-secondary-300': '220 185 255',
                 '--color-secondary-400': '200 145 255',
                 '--color-secondary-500': '168 85 247',  // Candy Purple (same)
                 '--color-secondary-600': '151 76 222',
                 '--color-secondary-700': '134 68 197',
                 '--color-secondary-800': '117 59 172',
                 '--color-secondary-900': '100 51 147',
                 '--color-secondary-950': '84 42 123',
             
                 '--color-tertiary-0': '240 248 255',
                 '--color-tertiary-50': '230 242 255',
                 '--color-tertiary-100': '220 235 255',
                 '--color-tertiary-200': '200 220 255',
                 '--color-tertiary-300': '180 205 255',
                 '--color-tertiary-400': '140 175 255',
                 '--color-tertiary-500': '59 130 246',   // Candy Blue (same)
                 '--color-tertiary-600': '53 117 221',
                 '--color-tertiary-700': '47 104 196',
                 '--color-tertiary-800': '41 91 171',
                 '--color-tertiary-900': '35 78 147',
                 '--color-tertiary-950': '29 65 122',
             
                 // Semantic colors (same as light mode)
                 '--color-success-500': '16 185 129',    // Candy Green
                 '--color-warning-500': '245 158 11',    // Candy Yellow
                 '--color-error-500': '239 68 68',       // Candy Red
                 '--color-info-500': '6 182 212',        // Candy Cyan
             
                 // Glass effects for dark mode (slightly more opaque)
                 '--color-glass-bg-primary': '255 255 255 / 0.08',
                 '--color-glass-bg-secondary': '255 255 255 / 0.04',
                 '--color-glass-bg-card': '255 255 255 / 0.06',
                 '--color-glass-border-primary': '255 255 255 / 0.15',
                 '--color-glass-border-secondary': '255 255 255 / 0.08',
                 '--color-glass-border-accent': '255 107 157 / 0.25',
             
                 // Typography for dark mode (same as light)
                 '--color-typography-0': '255 255 255',
                 '--color-typography-50': '255 255 255 / 0.95',
                 '--color-typography-100': '255 255 255 / 0.9',
                 '--color-typography-200': '255 255 255 / 0.8',
                 '--color-typography-300': '255 255 255 / 0.7',
                 '--color-typography-400': '255 255 255 / 0.6',
                 '--color-typography-500': '255 255 255 / 0.5',
                 '--color-typography-600': '255 255 255 / 0.4',
                 '--color-typography-700': '255 255 255 / 0.3',
                 '--color-typography-800': '255 255 255 / 0.2',
                 '--color-typography-900': '255 255 255 / 0.1',
                 '--color-typography-950': '255 255 255 / 0.05',
             
                 // Outline colors for dark mode
                 '--color-outline-0': '255 255 255 / 0.25',
                 '--color-outline-50': '255 255 255 / 0.22',
                 '--color-outline-100': '255 255 255 / 0.18',
                 '--color-outline-200': '255 255 255 / 0.15',
                 '--color-outline-300': '255 255 255 / 0.12',
                 '--color-outline-400': '255 255 255 / 0.1',
                 '--color-outline-500': '255 255 255 / 0.08',
                 '--color-outline-600': '255 255 255 / 0.06',
                 '--color-outline-700': '255 255 255 / 0.04',
                 '--color-outline-800': '255 255 255 / 0.03',
                 '--color-outline-900': '255 255 255 / 0.02',
                 '--color-outline-950': '255 255 255 / 0.01',
             
                 // Background colors for dark mode
                 '--color-background-0': '0 0 0',
                 '--color-background-50': '12 12 12',
                 '--color-background-100': '24 24 24',
                 '--color-background-200': '36 36 36',
                 '--color-background-300': '48 48 48',
                 '--color-background-400': '60 60 60',
                 '--color-background-500': '72 72 72',
                 '--color-background-600': '84 84 84',
                 '--color-background-700': '96 96 96',
                 '--color-background-800': '108 108 108',
                 '--color-background-900': '120 120 120',
                 '--color-background-950': '132 132 132',
             
                 // Background special for dark mode
                 '--color-background-error': '239 68 68 / 0.08',
                 '--color-background-warning': '245 158 11 / 0.08',
                 '--color-background-success': '16 185 129 / 0.08',
                 '--color-background-muted': '255 255 255 / 0.03',
                 '--color-background-info': '6 182 212 / 0.08',
             
                 // Focus indicators for dark mode
                 '--color-indicator-primary': '255 107 157',
                 '--color-indicator-info': '6 182 212',
                 '--color-indicator-error': '239 68 68',
               }),
};
