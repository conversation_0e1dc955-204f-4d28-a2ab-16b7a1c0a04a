import React from 'react';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Lightbulb } from 'lucide-react-native';

interface ScanningTipsProps {
  className?: string;
}

const tips = [
  'Ensure good lighting for clear scans',
  'Place document flat on surface',
  'Multiple pages can be scanned in sequence',
  'Auto-crop will detect document edges'
];

export const ScanningTips: React.FC<ScanningTipsProps> = ({ 
  className = '' 
}) => {
  return (
    <Box 
      className={`bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-4 ${className}`}
    >
      <HStack space="sm" className="items-center mb-3">
        <Lightbulb 
          size={20} 
          color="#FBBF24" 
          fill="#FBBF24"
        />
        <Text className="text-white font-medium text-base">
          Scanning Tips
        </Text>
      </HStack>
      
      <VStack space="xs">
        {tips.map((tip, index) => (
          <HStack key={index} space="sm" className="items-start">
            <Box className="w-1 h-1 bg-white/60 rounded-full mt-2 flex-shrink-0" />
            <Text className="text-white/80 text-sm flex-1 leading-5">
              {tip}
            </Text>
          </HStack>
        ))}
      </VStack>
    </Box>
  );
};

export default ScanningTips;
