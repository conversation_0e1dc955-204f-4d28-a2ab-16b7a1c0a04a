/**
 * Post-Scan Navigation Modal
 * 
 * Modern modal component for guiding users to their newly created knowledge content
 * after a successful scan. Provides multiple navigation options with visual feedback.
 */

import React, { useEffect, useRef } from 'react';
import { Modal, Pressable, Dimensions, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  BrainIcon,
  BookOpenIcon,
  GitBranchIcon,
  XIcon,
  ChevronRightIcon,
  SparklesIcon,
  ArrowRightIcon,
  CheckCircleIcon,
  TrendingUpIcon
} from 'lucide-react-native';

// UI Components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';

// Services
import { PostScanNavigationService, type PostScanNavigationOptions } from '@/lib/services/post-scan-navigation.service';
import type { CompleteKnowledgeCard } from '@/types/appwrite-v2';

const { width: screenWidth } = Dimensions.get('window');

interface PostScanNavigationModalProps {
  /** Whether the modal is visible */
  visible: boolean;
  /** Function to close the modal */
  onClose: () => void;
  /** The newly created knowledge cards */
  createdCards: CompleteKnowledgeCard[];
  /** Original scan data for context */
  scanData: {
    extractedText: string;
    confidence: number;
    language?: string;
  };
  /** Optional callback when navigation is selected */
  onNavigate?: (destination: string) => void;
}

// Progress Flow Component for Enhanced User Journey
interface ProgressFlowProps {
  currentStep: number;
  totalSteps: number;
}

const ProgressFlow: React.FC<ProgressFlowProps> = ({ currentStep, totalSteps }) => {
  const progressAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: currentStep / totalSteps,
      duration: 800,
      useNativeDriver: false,
    }).start();
  }, [currentStep, totalSteps]);

  const steps = [
    { icon: SparklesIcon, label: 'Scan', color: '#10B981' },
    { icon: CheckCircleIcon, label: 'Cards', color: '#FF6B9D' },
    { icon: TrendingUpIcon, label: 'Graph', color: '#8B5CF6' },
  ];

  return (
    <VStack space="sm" className="mb-6">
      <Text className="text-white/70 text-sm text-center">Your Knowledge Journey</Text>
      <HStack className="justify-between items-center px-4">
        {steps.map((step, index) => (
          <HStack key={index} className="items-center flex-1">
            <Box className={`w-10 h-10 rounded-full items-center justify-center ${
              index < currentStep ? 'bg-green-500/20' :
              index === currentStep ? 'bg-white/20' : 'bg-white/10'
            }`}>
              <step.icon
                size={20}
                color={index <= currentStep ? step.color : '#FFFFFF60'}
              />
            </Box>
            <Text className={`ml-2 text-xs ${
              index <= currentStep ? 'text-white' : 'text-white/50'
            }`}>
              {step.label}
            </Text>
            {index < steps.length - 1 && (
              <ArrowRightIcon size={16} color="#FFFFFF40" className="ml-2" />
            )}
          </HStack>
        ))}
      </HStack>

      {/* Animated Progress Bar */}
      <Box className="h-1 bg-white/10 rounded-full mx-4 overflow-hidden">
        <Animated.View
          style={{
            height: '100%',
            backgroundColor: '#10B981',
            borderRadius: 2,
            width: progressAnim.interpolate({
              inputRange: [0, 1],
              outputRange: ['0%', '100%'],
            }),
          }}
        />
      </Box>
    </VStack>
  );
};

interface NavigationOptionProps {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  iconColor: string;
  iconBgColor: string;
  onPress: () => void;
  badge?: string;
  badgeColor?: string;
}

const NavigationOption: React.FC<NavigationOptionProps> = ({
  title,
  description,
  icon: Icon,
  iconColor,
  iconBgColor,
  onPress,
  badge,
  badgeColor = "bg-blue-500"
}) => (
  <Pressable onPress={onPress} className="mb-3">
    <Box className="bg-gradient-to-br from-white/25 to-white/15 backdrop-blur-2xl rounded-2xl p-4 border border-white/40 shadow-xl relative overflow-hidden">
      {/* Background decoration */}
      <Box className="absolute top-0 right-0 w-12 h-12 bg-white/10 rounded-full blur-lg" />
      
      <HStack className="items-center space-x-4">
        <Box 
          className="w-12 h-12 justify-center items-center rounded-xl shadow-lg border border-white/30"
          style={{ backgroundColor: iconBgColor }}
        >
          <Icon size={24} color={iconColor} />
        </Box>
        
        <VStack space="xs" className="flex-1">
          <HStack className="items-center justify-between">
            <Text className="text-white text-lg font-bold">
              {title}
            </Text>
            {badge && (
              <Box className={`${badgeColor} rounded-lg px-2 py-1 shadow-lg border border-white/20`}>
                <Text className="text-white text-xs font-bold">
                  {badge}
                </Text>
              </Box>
            )}
          </HStack>
          <Text className="text-white/90 text-sm leading-relaxed">
            {description}
          </Text>
        </VStack>
        
        <ChevronRightIcon size={20} color="white" />
      </HStack>
    </Box>
  </Pressable>
);

export const PostScanNavigationModal: React.FC<PostScanNavigationModalProps> = ({
  visible,
  onClose,
  createdCards,
  scanData,
  onNavigate
}) => {
  const cardCount = createdCards.length;
  const firstCard = createdCards[0];
  
  const navigationOptions: PostScanNavigationOptions = {
    createdCards,
    scanData,
    preferences: { highlightNew: true }
  };

  const handleNavigateToHub = () => {
    PostScanNavigationService.navigateToKnowledgeHub(navigationOptions);
    onNavigate?.('hub');
    onClose();
  };

  const handleNavigateToCards = () => {
    PostScanNavigationService.navigateToKnowledgeCards(navigationOptions);
    onNavigate?.('cards');
    onClose();
  };

  const handleNavigateToGraph = () => {
    PostScanNavigationService.navigateToKnowledgeGraph(navigationOptions);
    onNavigate?.('graph');
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <Box className="flex-1 bg-black/50 justify-center items-center px-6">
        <LinearGradient
          colors={['rgb(255, 107, 157)', 'rgb(168, 85, 247)', 'rgb(59, 130, 246)']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{ 
            width: screenWidth - 48,
            borderRadius: 24,
            padding: 2
          }}
        >
          <Box className="bg-black/20 backdrop-blur-2xl rounded-3xl p-6 relative">
            {/* Close Button */}
            <Pressable 
              onPress={onClose}
              className="absolute top-4 right-4 w-8 h-8 bg-white/20 rounded-full items-center justify-center z-10"
            >
              <XIcon size={16} color="white" />
            </Pressable>

            <VStack space="lg">
              {/* Success Header */}
              <VStack space="md" className="items-center">
                <Box className="w-16 h-16 bg-green-500/30 rounded-full items-center justify-center mb-2">
                  <SparklesIcon size={32} color="#10B981" />
                </Box>
                <Text className="text-white text-2xl font-bold text-center">
                  🎉 Knowledge Cards Created!
                </Text>
                <Text className="text-white/90 text-center text-base leading-relaxed">
                  Successfully generated {cardCount} AI-enhanced knowledge card{cardCount > 1 ? 's' : ''} from your scan
                </Text>
              </VStack>

              {/* Card Summary */}
              {firstCard && (
                <Box className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 border border-white/30">
                  <VStack space="sm">
                    <Text className="text-white font-bold text-lg">
                      "{firstCard.card.title}"
                    </Text>
                    <HStack className="items-center justify-between">
                      <Text className="text-white/80 text-sm">
                        Category: {firstCard.card.category}
                      </Text>
                      <Text className="text-white/80 text-sm">
                        Difficulty: {firstCard.card.difficulty}/5
                      </Text>
                    </HStack>
                  </VStack>
                </Box>
              )}

              {/* Navigation Options */}
              <VStack space="sm">
                <Text className="text-white text-lg font-bold mb-2">
                  Where would you like to go?
                </Text>
                
                <NavigationOption
                  title="Knowledge Hub"
                  description={`View your ${cardCount} new card${cardCount > 1 ? 's' : ''} in the central hub`}
                  icon={BrainIcon}
                  iconColor="#10B981"
                  iconBgColor="rgba(16, 185, 129, 0.2)"
                  onPress={handleNavigateToHub}
                  badge="Recommended"
                  badgeColor="bg-green-500"
                />
                
                <NavigationOption
                  title="Knowledge Cards"
                  description={`Study your ${cardCount} new card${cardCount > 1 ? 's' : ''} immediately`}
                  icon={BookOpenIcon}
                  iconColor="#FF6B9D"
                  iconBgColor="rgba(255, 107, 157, 0.2)"
                  onPress={handleNavigateToCards}
                  badge="Study Now"
                  badgeColor="bg-pink-500"
                />
                
                <NavigationOption
                  title="Knowledge Graph"
                  description="See how your new knowledge connects to existing content"
                  icon={GitBranchIcon}
                  iconColor="#8B5CF6"
                  iconBgColor="rgba(139, 92, 246, 0.2)"
                  onPress={handleNavigateToGraph}
                  badge="Explore"
                  badgeColor="bg-purple-500"
                />
              </VStack>

              {/* Stay Here Option */}
              <Pressable onPress={onClose}>
                <Text className="text-white/70 text-center text-sm">
                  Stay here to scan more content
                </Text>
              </Pressable>
            </VStack>
          </Box>
        </LinearGradient>
      </Box>
    </Modal>
  );
};

export default PostScanNavigationModal;
