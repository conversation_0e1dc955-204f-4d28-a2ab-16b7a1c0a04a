import React from 'react';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { ChevronLeft, Zap, Settings } from 'lucide-react-native';
import { cn } from '@/lib/utils';
import { useColorScheme } from 'react-native';

export interface HeaderBarProps {
  title?: string;
  onBackPress?: () => void;
  onFlashPress?: () => void;
  onSettingsPress?: () => void;
  showBack?: boolean;
  showFlash?: boolean;
  showSettings?: boolean;
  flashActive?: boolean;
  className?: string;
}

/**
 * HeaderBar - Glass morphism header component for scan screens
 * 
 * Features:
 * - Glass morphism background with backdrop blur
 * - Back button with chevron icon
 * - Dynamic title
 * - Flash toggle button
 * - Settings button
 * - Consistent styling with design tokens
 */
export const HeaderBar = React.forwardRef<
  React.ElementRef<typeof Box>,
  HeaderBarProps
>(({ 
  title = "Scan Document",
  onBackPress,
  onFlashPress,
  onSettingsPress,
  showBack = true,
  showFlash = true,
  showSettings = true,
  flashActive = false,
  className,
  ...props 
}, ref) => {
  const colorScheme = useColorScheme();

  return (
    <Box
      ref={ref}
      className={cn(
        // Glass morphism styling
        'absolute top-0 left-0 right-0 z-50',
        'px-5 pt-12 pb-4', // Safe area padding
        'flex-row items-center justify-between',
        'glass-card', // Glass background from design tokens
        className
      )}
      {...props}
    >
      {/* Left side - Back button */}
      <Box className="flex-1 flex-row justify-start">
        {showBack && (
          <Pressable
            onPress={onBackPress}
            className={cn(
              'w-10 h-10 rounded-full items-center justify-center',
              'glass-button', // Glass button styling
              'active:scale-95'
            )}
          >
            <ChevronLeft
              size={24}
              color={colorScheme === 'dark' ? '#FFFFFF' : '#FFFFFF'}
            />
          </Pressable>
        )}
      </Box>

      {/* Center - Title */}
      <Box className="flex-2 items-center justify-center">
        <Text 
          className={cn(
            'text-lg font-semibold text-white',
            'text-center'
          )}
        >
          {title}
        </Text>
      </Box>

      {/* Right side - Flash and Settings buttons */}
      <Box className="flex-1 flex-row justify-end gap-3">
        {showFlash && (
          <Pressable
            onPress={onFlashPress}
            className={cn(
              'w-10 h-10 rounded-full items-center justify-center',
              'glass-button',
              'active:scale-95',
              // Active state for flash
              flashActive && 'btn-candy-yellow' // Use yellow when flash is active
            )}
          >
            <Zap
              size={20}
              color={flashActive ? '#1F2937' : (colorScheme === 'dark' ? '#FFFFFF' : '#FFFFFF')}
              fill={flashActive ? '#F59E0B' : 'none'}
            />
          </Pressable>
        )}
        
        {showSettings && (
          <Pressable
            onPress={onSettingsPress}
            className={cn(
              'w-10 h-10 rounded-full items-center justify-center',
              'glass-button',
              'active:scale-95'
            )}
          >
            <Settings
              size={20}
              color={colorScheme === 'dark' ? '#FFFFFF' : '#FFFFFF'}
            />
          </Pressable>
        )}
      </Box>
    </Box>
  );
});

HeaderBar.displayName = 'HeaderBar';

export default HeaderBar;
