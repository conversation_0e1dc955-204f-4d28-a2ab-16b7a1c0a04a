import React from 'react';
import { Dimensions, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Camera, Upload, Image } from 'lucide-react-native';
import { cn } from '@/lib/utils';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate
} from 'react-native-reanimated';
import { useEffect } from 'react';

const { width: screenWidth } = Dimensions.get('window');

export type TabType = 'camera' | 'upload' | 'gallery';

export interface TabSelectorProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  className?: string;
}

interface TabItem {
  id: TabType;
  label: string;
  icon: React.ComponentType<any>;
  gradient: string[]; // Candy color gradients for morphing background
}

const tabs: TabItem[] = [
  {
    id: 'camera',
    label: 'Camera',
    icon: Camera,
    gradient: ['#FF6B9D', '#F093FB'], // Candy pink gradient
  },
  {
    id: 'upload',
    label: 'Upload',
    icon: Upload,
    gradient: ['#3B82F6', '#60A5FA'], // Candy blue gradient
  },
  {
    id: 'gallery',
    label: 'Gallery',
    icon: Image,
    gradient: ['#A855F7', '#C084FC'], // Candy purple gradient
  },
];

/**
 * TabSelector - Morphing Bubble Navigation with candy colors
 *
 * Features:
 * - Morphing bubble background that slides between tabs
 * - Glass morphism container with backdrop blur
 * - Smooth spring animations with React Native Reanimated
 * - Candy color gradients (pink, blue, purple)
 * - Safe area handling to prevent content overlap
 * - Touch-optimized button sizes
 */
export const TabSelector = React.forwardRef<
  React.ElementRef<typeof Box>,
  TabSelectorProps
>(({ activeTab, onTabChange, className, ...props }, ref) => {
  const insets = useSafeAreaInsets();

  // Get current active tab index for morphing animation
  const activeIndex = tabs.findIndex(tab => tab.id === activeTab);

  // Morphing bubble animation value
  const morphValue = useSharedValue(activeIndex);

  // Individual tab scale animations for touch feedback
  const tabScales = tabs.map(() => useSharedValue(1));

  // Update morphing animation when active tab changes
  useEffect(() => {
    morphValue.value = withSpring(activeIndex, {
      damping: 15,
      stiffness: 150,
    });
  }, [activeIndex, morphValue]);

  // Morphing bubble background animation
  const morphStyle = useAnimatedStyle(() => {
    const containerWidth = screenWidth - 40; // Account for horizontal margins
    const tabWidth = containerWidth / tabs.length;

    const translateX = interpolate(
      morphValue.value,
      [0, 1, 2],
      [8, tabWidth + 8, (tabWidth * 2) + 8] // Add padding offset
    );

    return {
      transform: [{ translateX }],
    };
  });

  // Handle tab press with touch feedback
  const handleTabPress = (tabId: TabType, index: number) => {
    // Touch feedback animation
    tabScales[index].value = withSpring(0.95, { duration: 100 }, () => {
      tabScales[index].value = withSpring(1, { duration: 200 });
    });

    onTabChange(tabId);
  };

  return (
    <Box
      ref={ref}
      className={cn(
        // Floating container positioning with safe area handling
        'absolute left-5 right-5 z-40',
        className
      )}
      style={{
        top: 120, // Fixed position from top
        paddingBottom: insets.bottom + 20 // Safe area + extra padding
      }}
      {...props}
    >
      <Box style={styles.bubbleContainer}>
        <LinearGradient
          colors={['rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.1)']}
          style={styles.bubbleBackground}
        >
          {/* Morphing background indicator */}
          <Animated.View style={[styles.morphingIndicator, morphStyle]}>
            <LinearGradient
              colors={tabs[activeIndex].gradient}
              style={styles.morphingGradient}
            />
          </Animated.View>

          {/* Tab buttons */}
          <Box className="flex-row justify-around items-center py-3 relative z-10">
            {tabs.map((tab, index) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;

              // Individual tab scale animation
              const tabAnimatedStyle = useAnimatedStyle(() => ({
                transform: [{ scale: tabScales[index].value }],
              }));

              return (
                <Animated.View key={tab.id} style={tabAnimatedStyle} className="flex-1">
                  <Pressable
                    onPress={() => handleTabPress(tab.id, index)}
                    className="flex-col items-center gap-1 py-2"
                    style={styles.tabButton}
                  >
                    <Icon
                      size={22}
                      color={isActive ? '#FFFFFF' : 'rgba(255, 255, 255, 0.6)'}
                    />
                    <Text
                      className={cn(
                        'text-xs',
                        isActive ? 'text-white font-semibold' : 'text-white/50'
                      )}
                    >
                      {tab.label}
                    </Text>
                  </Pressable>
                </Animated.View>
              );
            })}
          </Box>
        </LinearGradient>
      </Box>
    </Box>
  );
});

TabSelector.displayName = 'TabSelector';

// StyleSheet for morphing bubble navigation
const styles = StyleSheet.create({
  bubbleContainer: {
    borderRadius: 28,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 10,
  },
  bubbleBackground: {
    borderRadius: 28,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.25)',
  },
  morphingIndicator: {
    position: 'absolute',
    top: 8,
    bottom: 8,
    width: (screenWidth - 56) / 3, // Account for margins and padding
    borderRadius: 20,
    zIndex: 1,
  },
  morphingGradient: {
    flex: 1,
    borderRadius: 20,
  },
  tabButton: {
    minHeight: 44, // Accessibility touch target
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default TabSelector;
