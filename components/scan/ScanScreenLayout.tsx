import React, { ReactNode } from 'react';
import { ScrollView, StatusBar } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Pressable } from '@/components/ui/pressable';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';
import { ArrowLeft, SettingsIcon } from 'lucide-react-native';
import { TabSelectorButtons, TabType } from '@/components/home/<USER>';

interface ScanScreenLayoutProps {
  /** Screen title */
  title: string;
  /** Currently active tab */
  activeTab: TabType;
  /** Tab change handler */
  onTabChange: (tab: TabType) => void;
  /** Main content */
  children: ReactNode;
  /** Additional header actions */
  headerActions?: ReactNode;
  /** Custom gradient colors */
  gradientColors?: string[];
  /** Whether content should be scrollable */
  scrollable?: boolean;
  /** Custom content container style */
  contentContainerStyle?: any;
}

/**
 * ScanScreenLayout - Reusable layout component for scan screens
 * 
 * Features:
 * - Fixed header with back button and title
 * - Fixed tab selector
 * - Scrollable content area (main improvement)
 * - Consistent gradient background
 * - Safe area handling
 */
export const ScanScreenLayout: React.FC<ScanScreenLayoutProps> = ({
  title,
  activeTab,
  onTabChange,
  children,
  headerActions,
  gradientColors = ['#7c2d92', '#be185d', '#ec4899', '#f472b6'],
  scrollable = true,
  contentContainerStyle
}) => {
  const insets = useSafeAreaInsets();
  const router = useRouter();

  const handleBackPress = () => {
    router.push('/(tabs)/home');
  };

  const ContentWrapper = scrollable ? ScrollView : Box;
  const contentProps = scrollable ? {
    contentContainerStyle: {
      flexGrow: 1,
      paddingBottom: insets.bottom + 20,
      ...contentContainerStyle
    },
    showsVerticalScrollIndicator: false,
    keyboardShouldPersistTaps: 'handled' as const,
    bounces: true
  } : {
    style: { flex: 1, ...contentContainerStyle }
  };

  return (
    <LinearGradient
      colors={gradientColors}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Fixed Header */}
      <Box 
        className="flex-row justify-between items-center px-5 py-4 bg-white/10 backdrop-blur-md border-b border-white/20"
        style={{ paddingTop: insets.top + 16 }}
      >
        <HStack className="items-center gap-3">
          <Pressable onPress={handleBackPress}>
            <HStack className="items-center gap-2">
              <ArrowLeft size={20} color="#FFFFFF" />
              <Text className="text-white font-medium">Home</Text>
            </HStack>
          </Pressable>
        </HStack>
        
        <Text className="text-white text-lg font-bold">{title}</Text>
        
        <HStack className="gap-3">
          {headerActions || (
            <Pressable onPress={() => router.push('/(tabs)/profile')}>
              <Box className="p-2 rounded-full bg-white/10 backdrop-blur-md">
                <SettingsIcon size={20} color="#FFFFFF" />
              </Box>
            </Pressable>
          )}
        </HStack>
      </Box>

      {/* Fixed Tab Navigation */}
      <TabSelectorButtons
        activeTab={activeTab}
        onTabChange={onTabChange}
        activeTabColor={activeTab === 'camera' ? '#FF6B9D' : activeTab === 'upload' ? '#f472b6' : '#10B981'}
      />

      {/* Scrollable Content Area - Main Improvement */}
      <ContentWrapper {...contentProps}>
        {children}
      </ContentWrapper>
    </LinearGradient>
  );
};

export default ScanScreenLayout;
