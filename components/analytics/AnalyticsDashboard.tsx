import React, { useState, useEffect } from 'react';
import { View, ScrollView, RefreshControl, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  BarChart3Icon, 
  TrendingUpIcon, 
  AlertTriangleIcon, 
  ActivityIcon,
  BrainIcon,
  ClockIcon,
  TargetIcon,
  ZapIcon
} from 'lucide-react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { analyticsService, type LearningProgress, type AIUsageAnalytics } from '@/lib/services/analytics.service';
import { performanceMonitoringService, type PerformanceSummary, type PerformanceAlert } from '@/lib/services/performance-monitoring.service';

interface AnalyticsDashboardProps {
  className?: string;
}

/**
 * Comprehensive Analytics Dashboard
 * Following Rule 6: UI Design Consistency with Candy Color Theme
 */
export default function AnalyticsDashboard({ className = '' }: AnalyticsDashboardProps) {
  const [learningProgress, setLearningProgress] = useState<LearningProgress | null>(null);
  const [aiUsageAnalytics, setAIUsageAnalytics] = useState<AIUsageAnalytics | null>(null);
  const [performanceSummary, setPerformanceSummary] = useState<PerformanceSummary | null>(null);
  const [currentAlerts, setCurrentAlerts] = useState<PerformanceAlert[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'day' | 'week' | 'month'>('week');

  // Animation values
  const fadeIn = useSharedValue(0);
  const slideUp = useSharedValue(50);

  useEffect(() => {
    loadAnalyticsData();
    
    // Animate in
    fadeIn.value = withTiming(1, { duration: 800 });
    slideUp.value = withSpring(0, { damping: 15 });
  }, [selectedPeriod]);

  const loadAnalyticsData = async () => {
    try {
      const [progress, aiAnalytics, perfSummary, alerts] = await Promise.all([
        analyticsService.getLearningProgress(),
        analyticsService.getAIUsageAnalytics(),
        performanceMonitoringService.getPerformanceSummary(selectedPeriod),
        performanceMonitoringService.getCurrentAlerts(),
      ]);

      setLearningProgress(progress);
      setAIUsageAnalytics(aiAnalytics);
      setPerformanceSummary(perfSummary);
      setCurrentAlerts(alerts);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadAnalyticsData();
    setIsRefreshing(false);
  };

  // Animated styles
  const containerStyle = useAnimatedStyle(() => ({
    opacity: fadeIn.value,
    transform: [{ translateY: slideUp.value }],
  }));

  const getProgressColor = (value: number, max: number) => {
    const percentage = (value / max) * 100;
    if (percentage >= 80) return ['#10B981', '#34D399']; // Green
    if (percentage >= 60) return ['#F59E0B', '#FBBF24']; // Yellow
    return ['#EF4444', '#F87171']; // Red
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <Animated.View style={[containerStyle, { flex: 1 }]}>
      <ScrollView
        style={styles.container}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <Box className="p-6 pb-4">
          <Text className="text-white text-2xl font-bold mb-2">Analytics Dashboard</Text>
          <Text className="text-white/70 text-sm">
            Track your learning progress and system performance
          </Text>
        </Box>

        {/* Period Selector */}
        <Box className="px-6 mb-6">
          <View style={styles.periodSelector}>
            {(['day', 'week', 'month'] as const).map((period) => (
              <Animated.View key={period} style={styles.periodButton}>
                <LinearGradient
                  colors={selectedPeriod === period 
                    ? ['#FF6B9D', '#A855F7'] 
                    : ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']
                  }
                  style={styles.periodButtonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Text 
                    className={`text-sm font-medium ${
                      selectedPeriod === period ? 'text-white' : 'text-white/70'
                    }`}
                    onPress={() => setSelectedPeriod(period)}
                  >
                    {period.charAt(0).toUpperCase() + period.slice(1)}
                  </Text>
                </LinearGradient>
              </Animated.View>
            ))}
          </View>
        </Box>

        {/* Alerts Section */}
        {currentAlerts.length > 0 && (
          <Box className="px-6 mb-6">
            <View style={styles.alertsContainer}>
              <LinearGradient
                colors={['rgba(239, 68, 68, 0.2)', 'rgba(248, 113, 113, 0.1)']}
                style={styles.alertsGradient}
              >
                <View style={styles.alertsHeader}>
                  <AlertTriangleIcon size={20} color="#EF4444" />
                  <Text className="text-red-400 text-sm font-medium ml-2">
                    {currentAlerts.length} Active Alert{currentAlerts.length > 1 ? 's' : ''}
                  </Text>
                </View>
                {currentAlerts.slice(0, 3).map((alert) => (
                  <View key={alert.id} style={styles.alertItem}>
                    <Text className="text-red-300 text-xs">{alert.message}</Text>
                  </View>
                ))}
              </LinearGradient>
            </View>
          </Box>
        )}

        {/* Learning Progress Cards */}
        {learningProgress && (
          <Box className="px-6 mb-6">
            <Text className="text-white text-lg font-semibold mb-4">Learning Progress</Text>
            <View style={styles.statsGrid}>
              {/* Study Time */}
              <View style={styles.statCard}>
                <LinearGradient
                  colors={['rgba(16, 185, 129, 0.2)', 'rgba(52, 211, 153, 0.1)']}
                  style={styles.statCardGradient}
                >
                  <ClockIcon size={24} color="#10B981" />
                  <Text className="text-white text-xl font-bold mt-2">
                    {formatDuration(learningProgress.totalStudyTime)}
                  </Text>
                  <Text className="text-white/70 text-xs">Total Study Time</Text>
                </LinearGradient>
              </View>

              {/* Total Cards */}
              <View style={styles.statCard}>
                <LinearGradient
                  colors={['rgba(168, 85, 247, 0.2)', 'rgba(196, 181, 253, 0.1)']}
                  style={styles.statCardGradient}
                >
                  <BrainIcon size={24} color="#A855F7" />
                  <Text className="text-white text-xl font-bold mt-2">
                    {learningProgress.totalCards}
                  </Text>
                  <Text className="text-white/70 text-xs">Knowledge Cards</Text>
                </LinearGradient>
              </View>

              {/* Accuracy Rate */}
              <View style={styles.statCard}>
                <LinearGradient
                  colors={['rgba(245, 158, 11, 0.2)', 'rgba(251, 191, 36, 0.1)']}
                  style={styles.statCardGradient}
                >
                  <TargetIcon size={24} color="#F59E0B" />
                  <Text className="text-white text-xl font-bold mt-2">
                    {learningProgress.accuracyRate.toFixed(1)}%
                  </Text>
                  <Text className="text-white/70 text-xs">Accuracy Rate</Text>
                </LinearGradient>
              </View>

              {/* Streak Days */}
              <View style={styles.statCard}>
                <LinearGradient
                  colors={['rgba(255, 107, 157, 0.2)', 'rgba(251, 146, 60, 0.1)']}
                  style={styles.statCardGradient}
                >
                  <ZapIcon size={24} color="#FF6B9D" />
                  <Text className="text-white text-xl font-bold mt-2">
                    {learningProgress.streakDays}
                  </Text>
                  <Text className="text-white/70 text-xs">Day Streak</Text>
                </LinearGradient>
              </View>
            </View>
          </Box>
        )}

        {/* AI Usage Analytics */}
        {aiUsageAnalytics && (
          <Box className="px-6 mb-6">
            <Text className="text-white text-lg font-semibold mb-4">AI Usage Analytics</Text>
            <View style={styles.aiAnalyticsContainer}>
              <LinearGradient
                colors={['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']}
                style={styles.aiAnalyticsGradient}
              >
                <View style={styles.aiStatsRow}>
                  <View style={styles.aiStat}>
                    <Text className="text-white text-lg font-bold">
                      {formatNumber(aiUsageAnalytics.totalRequests)}
                    </Text>
                    <Text className="text-white/70 text-xs">Total Requests</Text>
                  </View>
                  <View style={styles.aiStat}>
                    <Text className="text-white text-lg font-bold">
                      {aiUsageAnalytics.averageResponseTime.toFixed(0)}ms
                    </Text>
                    <Text className="text-white/70 text-xs">Avg Response</Text>
                  </View>
                  <View style={styles.aiStat}>
                    <Text className="text-white text-lg font-bold">
                      {aiUsageAnalytics.successRate.toFixed(1)}%
                    </Text>
                    <Text className="text-white/70 text-xs">Success Rate</Text>
                  </View>
                </View>

                <View style={styles.aiStatsRow}>
                  <View style={styles.aiStat}>
                    <Text className="text-white text-lg font-bold">
                      {aiUsageAnalytics.cacheHitRate.toFixed(1)}%
                    </Text>
                    <Text className="text-white/70 text-xs">Cache Hit Rate</Text>
                  </View>
                  <View style={styles.aiStat}>
                    <Text className="text-white text-lg font-bold">
                      ${aiUsageAnalytics.costEstimate.toFixed(3)}
                    </Text>
                    <Text className="text-white/70 text-xs">Est. Cost</Text>
                  </View>
                  <View style={styles.aiStat}>
                    <Text className="text-white text-lg font-bold">
                      {aiUsageAnalytics.errorRate.toFixed(1)}%
                    </Text>
                    <Text className="text-white/70 text-xs">Error Rate</Text>
                  </View>
                </View>
              </LinearGradient>
            </View>
          </Box>
        )}

        {/* Performance Summary */}
        {performanceSummary && (
          <Box className="px-6 mb-6">
            <Text className="text-white text-lg font-semibold mb-4">Performance Summary</Text>
            <View style={styles.performanceContainer}>
              <LinearGradient
                colors={['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']}
                style={styles.performanceGradient}
              >
                <View style={styles.performanceMetric}>
                  <ActivityIcon size={20} color="#10B981" />
                  <View style={styles.performanceMetricContent}>
                    <Text className="text-white text-sm font-medium">Memory Usage</Text>
                    <Text className="text-white/70 text-xs">
                      Avg: {performanceSummary.metrics.memoryUsage.average.toFixed(1)} MB | 
                      Peak: {performanceSummary.metrics.memoryUsage.peak.toFixed(1)} MB
                    </Text>
                  </View>
                </View>

                <View style={styles.performanceMetric}>
                  <TrendingUpIcon size={20} color="#A855F7" />
                  <View style={styles.performanceMetricContent}>
                    <Text className="text-white text-sm font-medium">Cache Performance</Text>
                    <Text className="text-white/70 text-xs">
                      Hit Rate: {performanceSummary.metrics.cachePerformance.hitRate.toFixed(1)}% | 
                      Requests: {performanceSummary.metrics.cachePerformance.totalRequests}
                    </Text>
                  </View>
                </View>

                <View style={styles.performanceMetric}>
                  <BarChart3Icon size={20} color="#F59E0B" />
                  <View style={styles.performanceMetricContent}>
                    <Text className="text-white text-sm font-medium">User Engagement</Text>
                    <Text className="text-white/70 text-xs">
                      Session: {formatDuration(performanceSummary.metrics.userEngagement.sessionDuration / 60000)} | 
                      Features: {performanceSummary.metrics.userEngagement.featuresUsed}
                    </Text>
                  </View>
                </View>
              </LinearGradient>
            </View>
          </Box>
        )}

        {/* Recommendations */}
        {performanceSummary?.recommendations && performanceSummary.recommendations.length > 0 && (
          <Box className="px-6 mb-8">
            <Text className="text-white text-lg font-semibold mb-4">Recommendations</Text>
            <View style={styles.recommendationsContainer}>
              <LinearGradient
                colors={['rgba(59, 130, 246, 0.2)', 'rgba(147, 197, 253, 0.1)']}
                style={styles.recommendationsGradient}
              >
                {performanceSummary.recommendations.map((recommendation, index) => (
                  <View key={index} style={styles.recommendationItem}>
                    <Text className="text-blue-300 text-sm">• {recommendation}</Text>
                  </View>
                ))}
              </LinearGradient>
            </View>
          </Box>
        )}
      </ScrollView>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  periodSelector: {
    flexDirection: 'row',
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  periodButton: {
    flex: 1,
  },
  periodButtonGradient: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  alertsContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  alertsGradient: {
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
  },
  alertsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  alertItem: {
    marginTop: 4,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    width: '48%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  statCardGradient: {
    padding: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  aiAnalyticsContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  aiAnalyticsGradient: {
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  aiStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  aiStat: {
    alignItems: 'center',
    flex: 1,
  },
  performanceContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  performanceGradient: {
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  performanceMetric: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  performanceMetricContent: {
    marginLeft: 12,
    flex: 1,
  },
  recommendationsContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  recommendationsGradient: {
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.3)',
  },
  recommendationItem: {
    marginBottom: 8,
  },
});
