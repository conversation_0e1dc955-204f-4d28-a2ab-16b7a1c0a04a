/**
 * Smart Camera Interface with AI Enhancement
 * 
 * Provides AI-powered camera features including:
 * - Real-time image quality analysis
 * - Smart document detection with visual feedback
 * - Live camera guidance and suggestions
 * - Auto-capture when optimal conditions are detected
 * - AI-enhanced image processing
 * 
 * Following LearniScan Development Workflow Rules:
 * - Rule 6: UI Design Consistency with Gluestack UI
 * - Rule 8: Smooth animations with react-native-reanimated
 * - Rule 9: Safe Area Management
 * - Rule 11: Zod validation for all state
 */

import React, { useState, useEffect, useRef } from 'react';
import { Dimensions, Alert } from 'react-native';
import { CameraView, CameraType, FlashMode } from 'expo-camera';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withRepeat,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import { z } from 'zod';

// Icons
import {
  Camera,
  FlashOn,
  FlashOff,
  RotateCcw,
  Zap,
  CheckCircle,
  AlertTriangle,
  Target,
  Sparkles,
  Brain,
  Grid3X3,
} from 'lucide-react-native';

// UI Components following Rule #6
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';
import { Badge, BadgeText } from '@/components/ui/badge';
import { Progress, ProgressFilledTrack } from '@/components/ui/progress';

// Services
import { useCamera } from '@/hooks/useCamera';
import { 
  aiCameraEnhancementService,
  type CameraGuidance,
  type ImageQualityAnalysis,
  type DocumentDetection,
  type CameraEnhancementProgress,
} from '@/lib/services/ai-camera-enhancement.service';

// State validation with Zod (Rule 11)
const SmartCameraStateSchema = z.object({
  isAnalyzing: z.boolean(),
  aiGuidance: z.object({
    status: z.enum(['excellent', 'good', 'needs_improvement', 'poor']),
    message: z.string(),
    instructions: z.array(z.string()),
    autoCapture: z.boolean(),
  }).optional(),
  qualityScore: z.number().min(0).max(1).optional(),
  documentDetected: z.boolean(),
  enhancementProgress: z.number().min(0).max(100).optional(),
});

type SmartCameraState = z.infer<typeof SmartCameraStateSchema>;

interface SmartCameraInterfaceProps {
  onCapture: (imageUri: string, enhancedUri?: string) => void;
  onClose: () => void;
  enableAIGuidance?: boolean;
  enableAutoCapture?: boolean;
  enableDocumentDetection?: boolean;
  enableImageEnhancement?: boolean;
  realTimeAnalysis?: boolean;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const SmartCameraInterface: React.FC<SmartCameraInterfaceProps> = ({
  onCapture,
  onClose,
  enableAIGuidance = true,
  enableAutoCapture = false,
  enableDocumentDetection = true,
  enableImageEnhancement = true,
  realTimeAnalysis = true,
}) => {
  const {
    cameraRef,
    facing,
    flash,
    isReady,
    toggleCameraFacing,
    toggleFlash,
    takePicture,
    hasPermissions,
  } = useCamera();

  // State management with Zod validation (Rule 11)
  const [state, setState] = useState<SmartCameraState>({
    isAnalyzing: false,
    documentDetected: false,
  });

  const [currentGuidance, setCurrentGuidance] = useState<CameraGuidance | null>(null);
  const [analysisInterval, setAnalysisInterval] = useState<NodeJS.Timeout | null>(null);

  // Animation values (Rule 8)
  const pulseValue = useSharedValue(1);
  const guidanceOpacity = useSharedValue(0);
  const documentFrameScale = useSharedValue(1);
  const qualityIndicatorRotation = useSharedValue(0);

  // Auto-capture countdown
  const [autoCountdown, setAutoCountdown] = useState<number | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isReady && enableAIGuidance && realTimeAnalysis) {
      startRealTimeAnalysis();
    }

    return () => {
      if (analysisInterval) {
        clearInterval(analysisInterval);
      }
      if (countdownRef.current) {
        clearTimeout(countdownRef.current);
      }
    };
  }, [isReady, enableAIGuidance, realTimeAnalysis]);

  const startRealTimeAnalysis = () => {
    // Analyze every 2 seconds for real-time guidance
    const interval = setInterval(async () => {
      if (!state.isAnalyzing && cameraRef.current) {
        await performRealTimeAnalysis();
      }
    }, 2000);

    setAnalysisInterval(interval);
  };

  const performRealTimeAnalysis = async () => {
    try {
      setState(prev => ({ ...prev, isAnalyzing: true }));

      // Take a quick preview photo for analysis
      const previewPhoto = await cameraRef.current?.takePictureAsync({
        quality: 0.3, // Low quality for fast analysis
        base64: false,
        skipProcessing: true,
      });

      if (!previewPhoto?.uri) return;

      // Perform AI analysis
      const enhancementResult = await aiCameraEnhancementService.enhanceCameraCapture(
        previewPhoto.uri,
        {
          enhanceQuality: true,
          detectDocument: enableDocumentDetection,
          optimizeForOCR: false, // Skip OCR optimization for real-time
        }
      );

      // Update guidance
      setCurrentGuidance(enhancementResult.cameraGuidance);
      setState(prev => ({
        ...prev,
        aiGuidance: {
          status: enhancementResult.cameraGuidance.status,
          message: enhancementResult.cameraGuidance.primaryMessage,
          instructions: enhancementResult.cameraGuidance.instructions,
          autoCapture: enhancementResult.cameraGuidance.autoCapture.ready,
        },
        qualityScore: enhancementResult.qualityAnalysis?.overallScore,
        documentDetected: enhancementResult.documentDetection?.detected || false,
      }));

      // Animate guidance appearance
      guidanceOpacity.value = withTiming(1, { duration: 300 });

      // Handle auto-capture
      if (enableAutoCapture && enhancementResult.cameraGuidance.autoCapture.ready) {
        startAutoCapture();
      }

      // Animate quality indicators
      animateQualityFeedback(enhancementResult.cameraGuidance.status);

    } catch (error) {
      console.error('Real-time analysis error:', error);
    } finally {
      setState(prev => ({ ...prev, isAnalyzing: false }));
    }
  };

  const animateQualityFeedback = (status: CameraGuidance['status']) => {
    // Pulse animation based on quality
    const pulseIntensity = status === 'excellent' ? 1.1 : status === 'good' ? 1.05 : 1.02;
    pulseValue.value = withRepeat(
      withSpring(pulseIntensity, { damping: 15 }),
      2,
      true
    );

    // Rotate quality indicator
    qualityIndicatorRotation.value = withTiming(
      qualityIndicatorRotation.value + 360,
      { duration: 1000 }
    );

    // Document frame animation
    if (state.documentDetected) {
      documentFrameScale.value = withSpring(1.05, { damping: 10 }, () => {
        documentFrameScale.value = withSpring(1);
      });
    }
  };

  const startAutoCapture = () => {
    if (autoCountdown !== null) return; // Already counting down

    let countdown = 3;
    setAutoCountdown(countdown);

    const countdownTimer = setInterval(() => {
      countdown -= 1;
      setAutoCountdown(countdown);

      if (countdown <= 0) {
        clearInterval(countdownTimer);
        setAutoCountdown(null);
        handleCapture(true); // Auto capture
      }
    }, 1000);

    countdownRef.current = countdownTimer as any;
  };

  const cancelAutoCapture = () => {
    if (countdownRef.current) {
      clearTimeout(countdownRef.current);
      countdownRef.current = null;
    }
    setAutoCountdown(null);
  };

  const handleCapture = async (isAutoCapture = false) => {
    try {
      setState(prev => ({ ...prev, isAnalyzing: true }));

      // Capture high-quality photo
      const photo = await takePicture({
        quality: 0.9,
        base64: false,
        exif: true,
      });

      if (!photo) {
        throw new Error('Failed to capture photo');
      }

      let enhancedUri: string | undefined;

      // Apply AI enhancement if enabled
      if (enableImageEnhancement) {
        const enhancementResult = await aiCameraEnhancementService.enhanceImageForOCR(
          photo,
          (progress) => {
            setState(prev => ({ ...prev, enhancementProgress: progress.progress }));
          }
        );
        enhancedUri = enhancementResult.enhancedUri;
      }

      // Call the capture callback
      onCapture(photo, enhancedUri);

    } catch (error) {
      console.error('Capture error:', error);
      Alert.alert(
        'Capture Error',
        'Failed to capture photo. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setState(prev => ({ ...prev, isAnalyzing: false, enhancementProgress: undefined }));
    }
  };

  // Animated styles (Rule 8)
  const cameraContainerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseValue.value }],
  }));

  const guidanceStyle = useAnimatedStyle(() => ({
    opacity: guidanceOpacity.value,
  }));

  const documentFrameStyle = useAnimatedStyle(() => ({
    transform: [{ scale: documentFrameScale.value }],
  }));

  const qualityIndicatorStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${qualityIndicatorRotation.value}deg` }],
  }));

  // Get status colors
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'excellent': return '#10b981';
      case 'good': return '#3b82f6';
      case 'needs_improvement': return '#f59e0b';
      case 'poor': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'excellent': return CheckCircle;
      case 'good': return Target;
      case 'needs_improvement': return AlertTriangle;
      case 'poor': return AlertTriangle;
      default: return Brain;
    }
  };

  if (!hasPermissions) {
    return (
      <Box flex={1} justifyContent="center" alignItems="center" p="$4">
        <VStack space="md" alignItems="center">
          <Camera size={64} color="#6b7280" />
          <Text size="lg" textAlign="center">
            Camera permission required
          </Text>
          <Text size="sm" color="$neutral600" textAlign="center">
            Please grant camera access to use smart capture features
          </Text>
        </VStack>
      </Box>
    );
  }

  return (
    <Box flex={1} bg="$black">
      {/* Camera View */}
      <Animated.View style={[{ flex: 1 }, cameraContainerStyle]}>
        <CameraView
          ref={cameraRef}
          style={{ flex: 1 }}
          facing={facing}
          flash={flash}
        >
          {/* AI Guidance Overlay */}
          {enableAIGuidance && currentGuidance && (
            <Animated.View style={[{ position: 'absolute', top: 60, left: 20, right: 20 }, guidanceStyle]}>
              <LinearGradient
                colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.6)']}
                style={{
                  borderRadius: 16,
                  padding: 16,
                  backdropFilter: 'blur(10px)',
                }}
              >
                <HStack alignItems="center" space="md">
                  <Animated.View style={qualityIndicatorStyle}>
                    {React.createElement(getStatusIcon(currentGuidance.status), {
                      size: 24,
                      color: getStatusColor(currentGuidance.status),
                    })}
                  </Animated.View>
                  
                  <VStack flex={1} space="xs">
                    <Text color="$white" fontWeight="$semibold">
                      {currentGuidance.primaryMessage}
                    </Text>
                    {currentGuidance.instructions.length > 0 && (
                      <Text color="$neutral300" size="sm">
                        {currentGuidance.instructions[0]}
                      </Text>
                    )}
                  </VStack>

                  {state.qualityScore !== undefined && (
                    <Badge
                      action={state.qualityScore > 0.8 ? 'success' : 
                             state.qualityScore > 0.6 ? 'warning' : 'error'}
                    >
                      <BadgeText>{Math.round(state.qualityScore * 100)}%</BadgeText>
                    </Badge>
                  )}
                </HStack>
              </LinearGradient>
            </Animated.View>
          )}

          {/* Document Detection Frame */}
          {enableDocumentDetection && state.documentDetected && (
            <Animated.View 
              style={[
                {
                  position: 'absolute',
                  top: '20%',
                  left: '10%',
                  right: '10%',
                  bottom: '30%',
                  borderWidth: 2,
                  borderColor: '#10b981',
                  borderRadius: 12,
                  borderStyle: 'dashed',
                },
                documentFrameStyle
              ]}
            >
              {/* Corner indicators */}
              <Box
                position="absolute"
                top={-8}
                left={-8}
                w={16}
                h={16}
                borderTopWidth={4}
                borderLeftWidth={4}
                borderColor="$success500"
                borderTopLeftRadius={8}
              />
              <Box
                position="absolute"
                top={-8}
                right={-8}
                w={16}
                h={16}
                borderTopWidth={4}
                borderRightWidth={4}
                borderColor="$success500"
                borderTopRightRadius={8}
              />
              <Box
                position="absolute"
                bottom={-8}
                left={-8}
                w={16}
                h={16}
                borderBottomWidth={4}
                borderLeftWidth={4}
                borderColor="$success500"
                borderBottomLeftRadius={8}
              />
              <Box
                position="absolute"
                bottom={-8}
                right={-8}
                w={16}
                h={16}
                borderBottomWidth={4}
                borderRightWidth={4}
                borderColor="$success500"
                borderBottomRightRadius={8}
              />
            </Animated.View>
          )}

          {/* Auto-capture countdown */}
          {autoCountdown !== null && (
            <Box
              position="absolute"
              top="50%"
              left="50%"
              transform={[{ translateX: -40 }, { translateY: -40 }]}
              w={80}
              h={80}
              borderRadius={40}
              bg="rgba(0,0,0,0.8)"
              justifyContent="center"
              alignItems="center"
            >
              <Text color="$white" size="2xl" fontWeight="$bold">
                {autoCountdown}
              </Text>
            </Box>
          )}

          {/* Enhancement Progress */}
          {state.enhancementProgress !== undefined && (
            <Box
              position="absolute"
              bottom={120}
              left={20}
              right={20}
            >
              <LinearGradient
                colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.6)']}
                style={{
                  borderRadius: 12,
                  padding: 12,
                }}
              >
                <VStack space="sm">
                  <HStack alignItems="center" space="md">
                    <Sparkles size={20} color="#8b5cf6" />
                    <Text color="$white" size="sm">
                      Enhancing image...
                    </Text>
                  </HStack>
                  <Progress value={state.enhancementProgress} size="sm">
                    <ProgressFilledTrack />
                  </Progress>
                </VStack>
              </LinearGradient>
            </Box>
          )}
        </CameraView>
      </Animated.View>

      {/* Camera Controls */}
      <LinearGradient
        colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.9)']}
        style={{
          paddingHorizontal: 20,
          paddingVertical: 30,
          paddingBottom: 40,
        }}
      >
        <HStack justifyContent="space-around" alignItems="center">
          {/* Flash Toggle */}
          <Button
            variant="ghost"
            size="lg"
            onPress={toggleFlash}
          >
            <ButtonIcon as={flash === 'off' ? FlashOff : FlashOn} color="$white" />
          </Button>

          {/* Capture Button */}
          <Button
            size="xl"
            onPress={() => handleCapture()}
            isDisabled={state.isAnalyzing}
            style={{
              width: 80,
              height: 80,
              borderRadius: 40,
              backgroundColor: autoCountdown !== null ? '#ef4444' : '#ffffff',
            }}
            onLongPress={autoCountdown !== null ? cancelAutoCapture : undefined}
          >
            <ButtonIcon 
              as={autoCountdown !== null ? AlertTriangle : Camera} 
              size={32} 
              color={autoCountdown !== null ? '$white' : '$black'} 
            />
          </Button>

          {/* Camera Flip */}
          <Button
            variant="ghost"
            size="lg"
            onPress={toggleCameraFacing}
          >
            <ButtonIcon as={RotateCcw} color="$white" />
          </Button>
        </HStack>

        {/* AI Status Indicator */}
        {enableAIGuidance && (
          <HStack justifyContent="center" alignItems="center" space="sm" mt="$4">
            <Brain size={16} color="#8b5cf6" />
            <Text color="$neutral300" size="xs">
              {state.isAnalyzing ? 'AI Analyzing...' : 'AI Ready'}
            </Text>
            {currentGuidance?.visualIndicators.showGrid && (
              <Grid3X3 size={16} color="#f59e0b" />
            )}
          </HStack>
        )}
      </LinearGradient>
    </Box>
  );
};
