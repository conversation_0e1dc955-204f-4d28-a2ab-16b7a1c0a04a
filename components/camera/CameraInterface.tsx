import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Text } from '../ui/text';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Box } from '../ui/box';
import { Button, ButtonText } from '../ui/button';
import { Badge, BadgeText } from '../ui/badge';
import { CameraView } from 'expo-camera';
import { useCamera } from '../../hooks/useCamera';
import { 
  CameraIcon,
  XIcon,
  ZapIcon as FlashIcon,
  ZapOffIcon as FlashOffIcon,
  RotateCcwIcon,
  ZapIcon
} from 'lucide-react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface CameraInterfaceProps {
  onCapture: (imageUri: string) => void;
  onClose: () => void;
  showDocumentFrame?: boolean;
  frameAspectRatio?: number;
  captureButtonSize?: 'sm' | 'md' | 'lg' | 'xl';
  overlayContent?: React.ReactNode;
}

export const CameraInterface: React.FC<CameraInterfaceProps> = ({
  onCapture,
  onClose,
  showDocumentFrame = true,
  frameAspectRatio = 0.75, // 4:3 aspect ratio
  captureButtonSize = 'xl',
  overlayContent,
}) => {
  const {
    cameraRef,
    cameraPermission,
    requestCameraPermission,
    facing,
    flash,
    isReady,
    toggleCameraFacing,
    toggleFlash,
    takePicture,
    hasPermissions,
    isPermissionDenied,
  } = useCamera();

  const handleCapture = async () => {
    try {
      const imageUri = await takePicture({
        quality: 0.9,
        base64: false,
        exif: true,
      });
      
      if (imageUri) {
        onCapture(imageUri);
      }
    } catch (error) {
      console.error('Failed to capture image:', error);
    }
  };

  // Permission states
  if (!cameraPermission) {
    return (
      <View className="flex-1 justify-center items-center bg-background-0 p-6">
        <VStack space="md" className="items-center">
          <CameraIcon size={48} color="#9CA3AF" />
          <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
            Loading Camera...
          </Text>
        </VStack>
      </View>
    );
  }

  if (isPermissionDenied) {
    return (
      <View className="flex-1 justify-center items-center bg-background-0 p-6">
        <VStack space="lg" className="items-center">
          <Box className="w-24 h-24 justify-center items-center rounded-full bg-glass-bg-secondary">
            <CameraIcon size={48} color="#9CA3AF" />
          </Box>
          
          <VStack space="md" className="items-center">
            <Text color="primary" size="xl" style={{ fontWeight: 'bold' }}>
              Camera Permission Required
            </Text>
            <Text color="secondary" size="md" className="text-center">
              Please grant camera access to scan documents.
            </Text>
          </VStack>
          
          <Button action="candyPink" size="lg" onPress={requestCameraPermission}>
            <ButtonText>Grant Camera Permission</ButtonText>
          </Button>
        </VStack>
      </View>
    );
  }

  const frameWidth = screenWidth * 0.8;
  const frameHeight = frameWidth * frameAspectRatio;

  const getFlashIcon = () => {
    switch (flash) {
      case 'off': return FlashOffIcon;
      case 'on': return FlashIcon;
      case 'auto': return ZapIcon;
      default: return FlashOffIcon;
    }
  };

  const getFlashLabel = () => {
    switch (flash) {
      case 'off': return 'Flash Off';
      case 'on': return 'Flash On';
      case 'auto': return 'Auto Flash';
      default: return 'Flash Off';
    }
  };

  const FlashIconComponent = getFlashIcon();

  return (
    <View className="flex-1 bg-black">
      {/* Camera View */}
      <CameraView
        ref={cameraRef}
        style={StyleSheet.absoluteFillObject}
        facing={facing}
        flash={flash}
        onCameraReady={() => {}}
      />
      
      {/* Camera Overlay */}
      <View style={StyleSheet.absoluteFillObject}>
        {/* Top Controls */}
        <Box className="absolute top-0 left-0 right-0 bg-black/50 pt-16 pb-4 px-4">
          <HStack className="justify-between items-center">
            <Button action="glass" variant="outline" size="sm" onPress={onClose}>
              <XIcon size={20} color="#FFFFFF" />
            </Button>
            
            <Text color="primary" size="lg" style={{ fontWeight: 'bold', color: '#FFFFFF' }}>
              Document Scanner
            </Text>
            
            <HStack className="space-x-2">
              <Button action="glass" variant="outline" size="sm" onPress={toggleFlash}>
                <FlashIconComponent size={20} color="#FFFFFF" />
              </Button>
              
              <Button action="glass" variant="outline" size="sm" onPress={toggleCameraFacing}>
                <RotateCcwIcon size={20} color="#FFFFFF" />
              </Button>
            </HStack>
          </HStack>
        </Box>

        {/* Document Frame Overlay */}
        {showDocumentFrame && (
          <View className="flex-1 justify-center items-center">
            <View 
              style={{
                width: frameWidth,
                height: frameHeight,
                borderWidth: 2,
                borderColor: '#FF6B9D',
                borderRadius: 12,
                backgroundColor: 'transparent',
              }}
            />
            
            {/* Corner Indicators */}
            <View className="absolute" style={{ 
              top: (screenHeight - frameHeight) / 2 - 12, 
              left: (screenWidth - frameWidth) / 2 - 12 
            }}>
              <View className="w-6 h-6 border-l-4 border-t-4 border-candyPink" />
            </View>
            <View className="absolute" style={{ 
              top: (screenHeight - frameHeight) / 2 - 12, 
              right: (screenWidth - frameWidth) / 2 - 12 
            }}>
              <View className="w-6 h-6 border-r-4 border-t-4 border-candyPink" />
            </View>
            <View className="absolute" style={{ 
              bottom: (screenHeight - frameHeight) / 2 - 12, 
              left: (screenWidth - frameWidth) / 2 - 12 
            }}>
              <View className="w-6 h-6 border-l-4 border-b-4 border-candyPink" />
            </View>
            <View className="absolute" style={{ 
              bottom: (screenHeight - frameHeight) / 2 - 12, 
              right: (screenWidth - frameWidth) / 2 - 12 
            }}>
              <View className="w-6 h-6 border-r-4 border-b-4 border-candyPink" />
            </View>
          </View>
        )}

        {/* Custom Overlay Content */}
        {overlayContent}

        {/* Bottom Controls */}
        <Box className="absolute bottom-0 left-0 right-0 bg-black/50 pb-8 pt-4 px-4">
          <HStack className="justify-center items-center">
            <View className="flex-1" />
            
            {/* Capture Button */}
            <Button
              action="candyPink"
              size={captureButtonSize}
              onPress={handleCapture}
              disabled={!isReady}
              style={{
                width: captureButtonSize === 'xl' ? 80 : 60,
                height: captureButtonSize === 'xl' ? 80 : 60,
                borderRadius: captureButtonSize === 'xl' ? 40 : 30,
                borderWidth: 4,
                borderColor: '#FFFFFF',
              }}
            >
              <CameraIcon size={captureButtonSize === 'xl' ? 32 : 24} color="#FFFFFF" />
            </Button>
            
            <View className="flex-1 items-end">
              <Badge action="candyPink" variant="solid" size="md">
                <BadgeText style={{ color: '#FFFFFF' }}>
                  {getFlashLabel()}
                </BadgeText>
              </Badge>
            </View>
          </HStack>
        </Box>
      </View>
    </View>
  );
};

export default CameraInterface;
