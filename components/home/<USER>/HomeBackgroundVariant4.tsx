import React, { useEffect } from 'react';
import { Dimensions, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withDelay,
  interpolate,
  runOnUI,
  Easing,
  withSequence,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Morphing Geometric Shape Component
const MorphingShape: React.FC<{
  size: number;
  top?: number;
  left?: number;
  right?: number;
  bottom?: number;
  color: string;
  animationDuration: number;
  delay?: number;
  shapeType: 'circle' | 'square' | 'triangle';
}> = ({ size, top, left, right, bottom, color, animationDuration, delay = 0, shapeType }) => {
  const morphValue = useSharedValue(0);
  const rotationValue = useSharedValue(0);
  const scaleValue = useSharedValue(0);

  useEffect(() => {
    runOnUI(() => {
      morphValue.value = withDelay(
        delay,
        withRepeat(
          withSequence(
            withTiming(1, { duration: animationDuration / 3, easing: Easing.out(Easing.exp) }),
            withTiming(0, { duration: animationDuration / 3, easing: Easing.in(Easing.exp) }),
            withTiming(1, { duration: animationDuration / 3, easing: Easing.inOut(Easing.quad) })
          ),
          -1,
          false
        )
      );
      
      rotationValue.value = withDelay(
        delay,
        withRepeat(
          withTiming(360, { duration: animationDuration, easing: Easing.linear }),
          -1,
          false
        )
      );

      scaleValue.value = withDelay(
        delay,
        withRepeat(
          withTiming(1, { duration: animationDuration / 2, easing: Easing.inOut(Easing.sin) }),
          -1,
          true
        )
      );
    })();
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    const borderRadius = shapeType === 'circle' 
      ? size / 2 
      : shapeType === 'square' 
        ? interpolate(morphValue.value, [0, 1], [size / 8, size / 2])
        : interpolate(morphValue.value, [0, 1], [0, size / 4]);
    
    const scale = interpolate(scaleValue.value, [0, 1], [0.7, 1.3]);
    const rotation = interpolate(rotationValue.value, [0, 360], [0, 360]);
    const opacity = interpolate(morphValue.value, [0, 0.5, 1], [0.3, 0.8, 0.3]);
    
    const skewX = shapeType === 'triangle' 
      ? interpolate(morphValue.value, [0, 1], [0, 15])
      : 0;

    return {
      borderRadius,
      opacity,
      transform: [
        { scale },
        { rotate: `${rotation}deg` },
        { skewX: `${skewX}deg` },
      ],
    };
  });

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          width: size,
          height: size,
          top,
          left,
          right,
          bottom,
          backgroundColor: color,
          shadowColor: color,
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.6,
          shadowRadius: size / 3,
          elevation: 12,
        },
        animatedStyle,
      ]}
    />
  );
};

// Geometric Pattern Grid
const GeometricPattern: React.FC = () => {
  const patternAnimation = useSharedValue(0);

  useEffect(() => {
    runOnUI(() => {
      patternAnimation.value = withRepeat(
        withTiming(1, { duration: 20000, easing: Easing.inOut(Easing.sin) }),
        -1,
        true
      );
    })();
  }, []);

  const patternStyle = useAnimatedStyle(() => {
    const translateX = interpolate(patternAnimation.value, [0, 1], [0, 20]);
    const translateY = interpolate(patternAnimation.value, [0, 1], [0, -15]);
    const scale = interpolate(patternAnimation.value, [0, 0.5, 1], [1, 1.1, 1]);

    return {
      transform: [{ translateX }, { translateY }, { scale }],
    };
  });

  return (
    <Animated.View style={[styles.patternContainer, patternStyle]}>
      <LinearGradient
        colors={['#FF6B9D', '#A855F7', '#3B82F6', '#10B981']}
        locations={[0, 0.33, 0.66, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      />
    </Animated.View>
  );
};

export const HomeBackgroundVariant4: React.FC = () => {
  const { top: topInset } = useSafeAreaInsets();

  return (
    <>
      <StatusBar style="light" translucent />
      
      {/* Geometric Pattern Background */}
      <GeometricPattern />

      {/* Morphing Geometric Shapes */}
      <MorphingShape
        size={100}
        top={screenHeight * 0.12}
        left={screenWidth * 0.08}
        color="rgba(255, 107, 157, 0.4)"
        animationDuration={8000}
        delay={0}
        shapeType="circle"
      />
      
      <MorphingShape
        size={80}
        top={screenHeight * 0.35}
        right={screenWidth * 0.12}
        color="rgba(168, 85, 247, 0.45)"
        animationDuration={6000}
        delay={1000}
        shapeType="square"
      />
      
      <MorphingShape
        size={120}
        bottom={screenHeight * 0.28}
        left={screenWidth * 0.18}
        color="rgba(59, 130, 246, 0.4)"
        animationDuration={7000}
        delay={2000}
        shapeType="triangle"
      />
      
      <MorphingShape
        size={90}
        top={screenHeight * 0.6}
        right={screenWidth * 0.2}
        color="rgba(16, 185, 129, 0.45)"
        animationDuration={9000}
        delay={3000}
        shapeType="circle"
      />
      
      <MorphingShape
        size={70}
        top={screenHeight * 0.08}
        right={screenWidth * 0.35}
        color="rgba(255, 107, 157, 0.35)"
        animationDuration={5000}
        delay={1500}
        shapeType="square"
      />

      {/* Additional Gradient Overlays for Depth */}
      <LinearGradient
        colors={['transparent', 'rgba(168, 85, 247, 0.15)', 'transparent']}
        locations={[0, 0.5, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.depthOverlay}
      />
    </>
  );
};

const styles = StyleSheet.create({
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  patternContainer: {
    position: 'absolute',
    left: -30,
    right: -30,
    top: -30,
    bottom: -30,
  },
  depthOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
});

export default HomeBackgroundVariant4;
