import React, { useEffect } from 'react';
import { Dimensions, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { Box } from '@/components/ui/box';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withDelay,
  interpolate,
  runOnUI,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Enhanced Animated Orb Component
const AnimatedOrb: React.FC<{
  size: number;
  top?: number;
  left?: number;
  right?: number;
  bottom?: number;
  color: string;
  animationDuration: number;
  delay?: number;
}> = ({ size, top, left, right, bottom, color, animationDuration, delay = 0 }) => {
  const animatedValue = useSharedValue(0);
  const rotationValue = useSharedValue(0);

  useEffect(() => {
    runOnUI(() => {
      animatedValue.value = withDelay(
        delay,
        withRepeat(
          withTiming(1, { duration: animationDuration }),
          -1,
          true
        )
      );
      rotationValue.value = withDelay(
        delay,
        withRepeat(
          withTiming(360, { duration: animationDuration * 2 }),
          -1,
          false
        )
      );
    })();
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    const translateY = interpolate(animatedValue.value, [0, 1], [0, -20]);
    const translateX = interpolate(animatedValue.value, [0, 0.5, 1], [0, 10, 0]);
    const scale = interpolate(animatedValue.value, [0, 0.5, 1], [0.8, 1.2, 0.8]);
    const opacity = interpolate(animatedValue.value, [0, 0.5, 1], [0.3, 0.7, 0.3]);
    const rotation = interpolate(rotationValue.value, [0, 360], [0, 360]);

    return {
      transform: [
        { translateY },
        { translateX },
        { scale },
        { rotate: `${rotation}deg` },
      ],
      opacity,
    };
  });

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          width: size,
          height: size,
          top,
          left,
          right,
          bottom,
          borderRadius: size / 2,
          backgroundColor: color,
          shadowColor: color,
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.6,
          shadowRadius: size / 4,
          elevation: 10,
        },
        animatedStyle,
      ]}
    />
  );
};

export const HomeBackgroundVariant1: React.FC = () => {
  const { top: topInset } = useSafeAreaInsets();

  return (
    <>
      <StatusBar style="light" translucent />
      
      {/* Primary Radial Gradient Layer */}
      <LinearGradient
        colors={['#FF6B9D', '#A855F7', '#3B82F6']}
        locations={[0, 0.5, 1]}
        start={{ x: 0.5, y: 0 }}
        end={{ x: 0.5, y: 1 }}
        style={styles.gradient}
      />
      
      {/* Secondary Diagonal Gradient Overlay */}
      <LinearGradient
        colors={['rgba(168, 85, 247, 0.4)', 'transparent', 'rgba(16, 185, 129, 0.4)']}
        locations={[0, 0.5, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientOverlay}
      />

      {/* Animated Orbs with Enhanced Effects */}
      <AnimatedOrb
        size={120}
        top={screenHeight * 0.15}
        left={screenWidth * 0.05}
        color="rgba(255, 107, 157, 0.4)"
        animationDuration={8000}
        delay={0}
      />
      <AnimatedOrb
        size={80}
        top={screenHeight * 0.4}
        right={screenWidth * 0.1}
        color="rgba(168, 85, 247, 0.3)"
        animationDuration={6000}
        delay={1000}
      />
      <AnimatedOrb
        size={100}
        bottom={screenHeight * 0.25}
        left={screenWidth * 0.15}
        color="rgba(59, 130, 246, 0.35)"
        animationDuration={7000}
        delay={2000}
      />
      <AnimatedOrb
        size={60}
        top={screenHeight * 0.7}
        right={screenWidth * 0.25}
        color="rgba(16, 185, 129, 0.4)"
        animationDuration={9000}
        delay={3000}
      />
      <AnimatedOrb
        size={140}
        top={screenHeight * 0.05}
        right={screenWidth * 0.4}
        color="rgba(255, 107, 157, 0.25)"
        animationDuration={10000}
        delay={1500}
      />
    </>
  );
};

const styles = StyleSheet.create({
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  gradientOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
});

export default HomeBackgroundVariant1;
