import React, { useEffect } from 'react';
import { Dimensions, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withDelay,
  interpolate,
  runOnUI,
  withSequence,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Particle Component
const Particle: React.FC<{
  size: number;
  initialX: number;
  initialY: number;
  delay: number;
}> = ({ size, initialX, initialY, delay }) => {
  const animatedValue = useSharedValue(0);
  const floatValue = useSharedValue(0);

  useEffect(() => {
    runOnUI(() => {
      animatedValue.value = withDelay(
        delay,
        withRepeat(
          withSequence(
            withTiming(1, { duration: 3000 }),
            withTiming(0, { duration: 2000 })
          ),
          -1,
          false
        )
      );
      floatValue.value = withDelay(
        delay,
        withRepeat(
          withTiming(1, { duration: 4000 }),
          -1,
          true
        )
      );
    })();
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    const translateY = interpolate(floatValue.value, [0, 1], [0, -30]);
    const translateX = interpolate(floatValue.value, [0, 0.5, 1], [0, 15, 0]);
    const opacity = interpolate(animatedValue.value, [0, 0.5, 1], [0, 0.8, 0]);
    const scale = interpolate(animatedValue.value, [0, 0.5, 1], [0.5, 1, 0.5]);

    return {
      transform: [{ translateY }, { translateX }, { scale }],
      opacity,
    };
  });

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          left: initialX,
          top: initialY,
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: 'rgba(255, 255, 255, 0.6)',
          shadowColor: '#FFFFFF',
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.8,
          shadowRadius: size / 2,
          elevation: 5,
        },
        animatedStyle,
      ]}
    />
  );
};

// Animated Mesh Background
const AnimatedMeshBackground: React.FC = () => {
  const meshAnimation = useSharedValue(0);

  useEffect(() => {
    runOnUI(() => {
      meshAnimation.value = withRepeat(
        withTiming(1, { duration: 15000 }),
        -1,
        true
      );
    })();
  }, []);

  const meshStyle = useAnimatedStyle(() => {
    const rotation = interpolate(meshAnimation.value, [0, 1], [0, 10]);
    const scaleX = interpolate(meshAnimation.value, [0, 0.5, 1], [1, 1.05, 1]);
    const scaleY = interpolate(meshAnimation.value, [0, 0.5, 1], [1, 0.95, 1]);

    return {
      transform: [
        { rotate: `${rotation}deg` },
        { scaleX },
        { scaleY },
      ],
    };
  });

  return (
    <Animated.View style={[styles.meshContainer, meshStyle]}>
      <LinearGradient
        colors={['#FF6B9D', '#A855F7', '#3B82F6', '#10B981']}
        locations={[0, 0.33, 0.66, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      />
      <LinearGradient
        colors={['rgba(255, 107, 157, 0.3)', 'transparent', 'rgba(59, 130, 246, 0.3)']}
        locations={[0, 0.5, 1]}
        start={{ x: 1, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={styles.gradientOverlay}
      />
    </Animated.View>
  );
};

export const HomeBackgroundVariant2: React.FC = () => {
  const { top: topInset } = useSafeAreaInsets();

  // Generate particle positions
  const particles = Array.from({ length: 12 }, (_, i) => ({
    id: i,
    size: Math.random() * 8 + 4,
    x: Math.random() * screenWidth,
    y: Math.random() * screenHeight,
    delay: Math.random() * 5000,
  }));

  return (
    <>
      <StatusBar style="light" translucent />
      
      {/* Animated Mesh Background */}
      <AnimatedMeshBackground />

      {/* Particle System */}
      {particles.map((particle) => (
        <Particle
          key={particle.id}
          size={particle.size}
          initialX={particle.x}
          initialY={particle.y}
          delay={particle.delay}
        />
      ))}

      {/* Additional Gradient Layers for Depth */}
      <LinearGradient
        colors={['transparent', 'rgba(168, 85, 247, 0.1)', 'transparent']}
        locations={[0, 0.5, 1]}
        start={{ x: 0, y: 0.3 }}
        end={{ x: 1, y: 0.7 }}
        style={styles.depthLayer}
      />
    </>
  );
};

const styles = StyleSheet.create({
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  gradientOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  meshContainer: {
    position: 'absolute',
    left: -50,
    right: -50,
    top: -50,
    bottom: -50,
  },
  depthLayer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
});

export default HomeBackgroundVariant2;
