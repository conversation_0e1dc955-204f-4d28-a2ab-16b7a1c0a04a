import React, { useEffect } from 'react';
import { Dimensions, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withDelay,
  interpolate,
  runOnUI,
  Easing,
  interpolateColor,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// 3D Floating Element Component
const Floating3DElement: React.FC<{
  size: number;
  top?: number;
  left?: number;
  right?: number;
  bottom?: number;
  animationDuration: number;
  delay?: number;
  depth: number;
}> = ({ size, top, left, right, bottom, animationDuration, delay = 0, depth }) => {
  const animatedValue = useSharedValue(0);
  const colorShift = useSharedValue(0);

  useEffect(() => {
    runOnUI(() => {
      animatedValue.value = withDelay(
        delay,
        withRepeat(
          withTiming(1, { 
            duration: animationDuration,
            easing: Easing.inOut(Easing.sin)
          }),
          -1,
          true
        )
      );
      
      colorShift.value = withDelay(
        delay,
        withRepeat(
          withTiming(1, { 
            duration: animationDuration * 1.5,
            easing: Easing.linear
          }),
          -1,
          false
        )
      );
    })();
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    const translateY = interpolate(animatedValue.value, [0, 1], [0, -depth]);
    const translateX = interpolate(animatedValue.value, [0, 0.5, 1], [0, depth / 2, 0]);
    const scale = interpolate(animatedValue.value, [0, 0.5, 1], [0.8, 1.2, 0.8]);
    const rotateY = interpolate(animatedValue.value, [0, 1], [0, 180]);
    const rotateX = interpolate(animatedValue.value, [0, 0.5, 1], [0, 15, 0]);
    
    // Holographic color shifting
    const backgroundColor = interpolateColor(
      colorShift.value,
      [0, 0.25, 0.5, 0.75, 1],
      [
        'rgba(255, 107, 157, 0.4)', // Pink
        'rgba(168, 85, 247, 0.4)',  // Purple
        'rgba(59, 130, 246, 0.4)',  // Blue
        'rgba(16, 185, 129, 0.4)',  // Green
        'rgba(255, 107, 157, 0.4)'  // Back to Pink
      ]
    );

    const shadowColor = interpolateColor(
      colorShift.value,
      [0, 0.25, 0.5, 0.75, 1],
      ['#FF6B9D', '#A855F7', '#3B82F6', '#10B981', '#FF6B9D']
    );

    return {
      backgroundColor,
      shadowColor,
      transform: [
        { translateY },
        { translateX },
        { scale },
        { rotateY: `${rotateY}deg` },
        { rotateX: `${rotateX}deg` },
        { perspective: 1000 },
      ],
    };
  });

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          width: size,
          height: size,
          top,
          left,
          right,
          bottom,
          borderRadius: size / 2,
          borderWidth: 2,
          borderColor: 'rgba(255, 255, 255, 0.3)',
          shadowOffset: { width: 0, height: depth / 2 },
          shadowOpacity: 0.8,
          shadowRadius: size / 2,
          elevation: depth,
        },
        animatedStyle,
      ]}
    />
  );
};

// Holographic Background Layer
const HolographicBackground: React.FC = () => {
  const hologramShift = useSharedValue(0);

  useEffect(() => {
    runOnUI(() => {
      hologramShift.value = withRepeat(
        withTiming(1, { 
          duration: 12000,
          easing: Easing.linear
        }),
        -1,
        false
      );
    })();
  }, []);

  const hologramStyle = useAnimatedStyle(() => {
    const rotateZ = interpolate(hologramShift.value, [0, 1], [0, 5]);
    const scaleX = interpolate(hologramShift.value, [0, 0.5, 1], [1, 1.02, 1]);
    const scaleY = interpolate(hologramShift.value, [0, 0.5, 1], [1, 0.98, 1]);

    return {
      transform: [
        { rotateZ: `${rotateZ}deg` },
        { scaleX },
        { scaleY },
      ],
    };
  });

  return (
    <Animated.View style={[styles.hologramContainer, hologramStyle]}>
      {/* Primary Holographic Gradient */}
      <LinearGradient
        colors={['#FF6B9D', '#A855F7', '#3B82F6', '#10B981', '#FF6B9D']}
        locations={[0, 0.25, 0.5, 0.75, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      />
      
      {/* Holographic Overlay Layers */}
      <LinearGradient
        colors={['rgba(255, 255, 255, 0.1)', 'transparent', 'rgba(255, 255, 255, 0.05)']}
        locations={[0, 0.5, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.hologramOverlay}
      />
      
      <LinearGradient
        colors={['transparent', 'rgba(168, 85, 247, 0.2)', 'transparent']}
        locations={[0, 0.5, 1]}
        start={{ x: 0, y: 1 }}
        end={{ x: 1, y: 0 }}
        style={styles.hologramOverlay}
      />
    </Animated.View>
  );
};

export const HomeBackgroundVariant5: React.FC = () => {
  const { top: topInset } = useSafeAreaInsets();

  return (
    <>
      <StatusBar style="light" translucent />
      
      {/* Holographic Background */}
      <HolographicBackground />

      {/* 3D Floating Elements with Different Depths */}
      <Floating3DElement
        size={120}
        top={screenHeight * 0.15}
        left={screenWidth * 0.1}
        animationDuration={8000}
        delay={0}
        depth={25}
      />
      
      <Floating3DElement
        size={90}
        top={screenHeight * 0.4}
        right={screenWidth * 0.15}
        animationDuration={6000}
        delay={1000}
        depth={35}
      />
      
      <Floating3DElement
        size={110}
        bottom={screenHeight * 0.25}
        left={screenWidth * 0.2}
        animationDuration={7000}
        delay={2000}
        depth={20}
      />
      
      <Floating3DElement
        size={80}
        top={screenHeight * 0.65}
        right={screenWidth * 0.25}
        animationDuration={9000}
        delay={3000}
        depth={30}
      />
      
      <Floating3DElement
        size={70}
        top={screenHeight * 0.08}
        right={screenWidth * 0.4}
        animationDuration={5000}
        delay={1500}
        depth={15}
      />

      {/* Additional Depth Layers */}
      <LinearGradient
        colors={['transparent', 'rgba(255, 255, 255, 0.05)', 'transparent']}
        locations={[0, 0.5, 1]}
        start={{ x: 0, y: 0.2 }}
        end={{ x: 1, y: 0.8 }}
        style={styles.depthLayer}
      />
    </>
  );
};

const styles = StyleSheet.create({
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  hologramContainer: {
    position: 'absolute',
    left: -20,
    right: -20,
    top: -20,
    bottom: -20,
  },
  hologramOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  depthLayer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
});

export default HomeBackgroundVariant5;
