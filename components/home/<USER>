import React from 'react';
import { StyleSheet } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { Button, ButtonIcon, ButtonText } from '@/components/ui/button';
import { ArrowRightIcon, PlayIcon } from 'lucide-react-native';
import { useRouter } from 'expo-router';

interface CTAButtonsProps {
  onGetStarted?: () => void;
  onWatchDemo?: () => void;
}

export const CTAButtons: React.FC<CTAButtonsProps> = ({
  onGetStarted,
  onWatchDemo
}) => {
  const router = useRouter();

  const handleGetStarted = () => {
    if (onGetStarted) {
      onGetStarted();
    } else {
      router.push('/(tabs)/scan');
    }
  };

  return (
    <Box className="w-full mb-16 px-8">
      <VStack space="lg" className="sm:flex-row sm:space-x-6 items-center justify-center">
        <Button
          size="xl"
          variant="glass"
          action="candyPink"
          onPress={handleGetStarted}
          className="w-full sm:w-auto min-w-0 rounded-xl"
        >
          <ButtonText
            className="font-bold text-white"
          >
            Get Started
          </ButtonText>
          <ButtonIcon as={ArrowRightIcon} className="text-tertiary-100" />
        </Button>

        <Button
          size="xl"
          variant="glass"
          action="candyPurple"
          onPress={onWatchDemo}
          className="w-full sm:w-auto min-w-0 rounded-xl"
        >
          <ButtonText
            className="font-bold text-white"
          >
            Watch Demo
          </ButtonText>
          <ButtonIcon as={PlayIcon} className="text-tertiary-100" />
        </Button>
      </VStack>
    </Box>
  );
};

const styles = StyleSheet.create({
  buttonText: {
    fontSize: 18,
    fontWeight: '600',
  },
});

export default CTAButtons;
