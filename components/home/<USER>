import React from 'react';
// Fixed syntax error - export moved to correct position
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withRepeat, 
  withTiming,
  Easing
} from 'react-native-reanimated';
import { VStack } from '@/components/ui/vstack';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { FeatureCard } from './FeatureCard';
import { CameraIcon, LightbulbIcon, ShareIcon } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { CTAButtons } from './CTAButtons';

export const FeatureGrid: React.FC = () => {
  const router = useRouter();

  // Floating animation for feature cards (up and down)
  const translateY = useSharedValue(0);

  React.useEffect(() => {
    translateY.value = withRepeat(
      withTiming(-8, {
        duration: 2000,
        easing: Easing.inOut(Easing.ease),
      }),
      -1,
      true
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  // Guest-friendly features that showcase the app's capabilities
  const features = [
    {
      title: "Smart Scanning",
      description: "Try our OCR technology - 5 free scans daily for guests",
      icon: CameraIcon,
      onPress: () => router.push('/(tabs)/scan'),
      badge: "5 Free Daily",
      badgeColor: "bg-green-500"
    },
    {
      title: "Knowledge Cards",
      description: "Create up to 10 knowledge cards to organize your learning",
      icon: LightbulbIcon,
      onPress: () => router.push('/(tabs)/study'),
      badge: "10 Free Cards",
      badgeColor: "bg-blue-500"
    },
    {
      title: "Discover Features",
      description: "Explore AI search, study sessions, and premium features",
      icon: ShareIcon,
      onPress: () => router.push('/(tabs)/search'),
      badge: "Explore",
      badgeColor: "bg-purple-500"
    }
  ];

  return (
    <Box className="w-full mb-12">
      <VStack space="lg" className="items-center w-full">
        <Text 
          size="3xl" 
          className="text-white text-center font-bold mb-2 opacity-95"
        >
          Discover LearniScan
        </Text>
        <Text 
          size="lg" 
          className="text-white/80 text-center mb-8 px-4"
        >
          Start your learning journey with our free guest access
        </Text>
        <VStack space="md" className="px-2 w-full max-w-md">
          {features.map((feature, index) => (
            <Animated.View key={index} style={animatedStyle}>
              <FeatureCard
                className="h-[190px] w-full"
                title={feature.title}
                description={feature.description}
                icon={feature.icon}
                onPress={feature.onPress}
                badge={feature.badge}
                badgeColor={feature.badgeColor}
              />
            </Animated.View>
          ))}
        </VStack>
        
        {/* Guest upgrade prompt */}
        <Box className="mt-8 px-6 py-4 bg-white/10 rounded-2xl backdrop-blur-md border border-white/20">
          <VStack space="sm" className="items-center">
            <Text className="text-white font-semibold text-lg">
              Want More?
            </Text>
            <Text className="text-white/80 text-center text-sm">
              Sign up for unlimited scans, AI features, and cloud sync
            </Text>
            <Box className="flex-row space-x-3 mt-3">
              <Box 
                className="px-4 py-2 bg-primary-500 rounded-full"
                onTouchEnd={() => router.push('/auth/signup')}
              >
                <Text className="text-white font-medium">Sign Up Free</Text>
              </Box>
              <Box 
                className="px-4 py-2 bg-white/20 rounded-full border border-white/30"
                onTouchEnd={() => router.push('/auth/login')}
              >
                <Text className="text-white font-medium">Sign In</Text>
              </Box>
            </Box>
          </VStack>
        </Box>
      </VStack>
    </Box>
  );
}

export default FeatureGrid;