import React from 'react';
import { StyleSheet } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withRepeat, 
  withTiming,
  Easing
} from 'react-native-reanimated';
import { Box } from '@/components/ui/box';
import { LightbulbIcon } from 'lucide-react-native';

interface HeroLogoProps {
  size?: number;
  iconSize?: number;
}

export const HeroLogo: React.FC<HeroLogoProps> = ({
  size = 128,
  iconSize = 64
}) => {
  // Brightness/opacity animation (dim to bright)
  const opacity = useSharedValue(0.6);

  React.useEffect(() => {
    opacity.value = withRepeat(
      withTiming(1, {
        duration: 2000,
        easing: Easing.inOut(Easing.ease),
      }),
      -1,
      true
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  return (
    <Box className="mb-8 items-center">
      <Animated.View style={animatedStyle}>
        <Box
          className="rounded-full justify-center items-center"
          style={[
            styles.logoContainer,
            { width: size, height: size }
          ]}
        >
          <LightbulbIcon size={iconSize} color="#FFFFFF" />
        </Box>
      </Animated.View>
    </Box>
  );
};

const styles = StyleSheet.create({
  logoContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#FF6B9D',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
});

export default HeroLogo;