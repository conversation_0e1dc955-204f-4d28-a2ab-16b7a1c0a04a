import React from 'react';
import { StyleSheet } from 'react-native';
import { Card } from '@/components/ui/card';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { BlurView } from 'expo-blur';
import type { LucideIcon } from 'lucide-react-native';

interface FeatureCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  onPress?: () => void;
  className?: string;
  children?: React.ReactNode;
  badge?: string;
  badgeColor?: string;
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon: Icon,
  onPress,
  className,
  children,
  badge,
  badgeColor = "bg-primary-500",
}) => {
  return (
    <Pressable onPress={onPress} className={`w-full relative ${className}`}>
      <Card
        className="overflow-hidden rounded-3xl p-0 border-white/10 flex-1 min-w-[320px]"
        variant="glass"
      >
        {/* Background Effects */}
        <BlurView intensity={25} tint="dark" style={styles.absBackground} />

        {/* Badge */}
        {badge && (
          <Box className={`absolute top-4 right-4 px-3 py-1 rounded-full ${badgeColor} z-10`}>
            <Text className="text-white text-xs font-semibold">
              {badge}
            </Text>
          </Box>
        )}

        {/* Content Container */}
        <Box className="p-6 justify-start items-center flex-1 pt-8">
          <VStack space="lg" className="items-center w-full">
            <Box className="p-3 rounded-full mb-2 bg-white/10">
              <Icon size={28} className="text-white" />
            </Box>
            <VStack space="xs" className="items-center">
              <Text className="text-white font-bold text-lg text-center">
                {title}
              </Text>
              <Text className="text-white/80 text-sm text-center leading-5">
                {description}
              </Text>
            </VStack>

            {/* CTA Buttons rendered in the normal layout flow */}
            {children && <Box className="w-full pt-6">{children}</Box>}
          </VStack>
        </Box>
      </Card>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  absBackground: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  }
});

export default FeatureCard;
