import React from 'react';
import { Box } from '@/components/ui/box';
import { HStack } from '@/components/ui/hstack';
import { Badge, BadgeText } from '@/components/ui/badge';

const stats = [
  { label: 'Users', value: '10K+' },
  { label: 'Languages', value: '50+' },
  { label: 'Technology', value: 'AI Powered' },
];

export const StatsSection = () => {
  return (
    <Box className="mt-8 w-full items-center">
      <HStack space="md" className="justify-center">
        {stats.map((stat) => (
          <Badge
            key={stat.label}
            action="muted"
            variant="outline"
            size="lg"
            className="py-2 px-5 border-white/20 bg-white/10 rounded-lg"
          >
            <BadgeText className="text-white font-semibold">
              {stat.value}
            </BadgeText>
          </Badge>
        ))}
      </HStack>
    </Box>
  );
};

export default StatsSection;
