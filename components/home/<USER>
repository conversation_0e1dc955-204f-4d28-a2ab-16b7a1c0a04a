import React, { useEffect } from 'react';
import { Dimensions, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withDelay,
  interpolate,
  runOnUI,
  Easing,
  interpolateColor,
} from 'react-native-reanimated';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Floating Particle Component - Variant 4: Subtle Drift Animation
const FloatingParticle: React.FC<{
  size: number;
  top: number;
  left: number;
  animationDuration: number;
  delay?: number;
  color: string;
  opacity: number;
}> = ({ size, top, left, animationDuration, delay = 0, color, opacity }) => {
  const animatedValue = useSharedValue(0);

  useEffect(() => {
    runOnUI(() => {
      animatedValue.value = withDelay(
        delay,
        withRepeat(
          withTiming(1, {
            duration: animationDuration,
            easing: Easing.inOut(Easing.ease)
          }),
          -1,
          true // Gentle back and forth motion
        )
      );
    })();
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    // Subtle drift motion
    const translateY = interpolate(animatedValue.value, [0, 1], [0, -30]);
    const translateX = interpolate(animatedValue.value, [0, 0.5, 1], [0, 15, 0]);
    const scale = interpolate(animatedValue.value, [0, 0.5, 1], [0.8, 1.0, 0.8]);
    const rotate = interpolate(animatedValue.value, [0, 1], [0, 10]);

    return {
      transform: [
        { translateX },
        { translateY },
        { scale },
        { rotate: `${rotate}deg` },
      ],
    };
  });

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          top,
          left,
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: color,
          opacity,
        },
        animatedStyle,
      ]}
    />
  );
};

// Holographic Background Layer
const HolographicBackground: React.FC = () => {
  const hologramShift = useSharedValue(0);

  useEffect(() => {
    runOnUI(() => {
      hologramShift.value = withRepeat(
        withTiming(1, {
          duration: 12000,
          easing: Easing.linear
        }),
        -1,
        false
      );
    })();
  }, []);

  const hologramStyle = useAnimatedStyle(() => {
    const rotateZ = interpolate(hologramShift.value, [0, 1], [0, 5]);
    const scaleX = interpolate(hologramShift.value, [0, 0.5, 1], [1, 1.02, 1]);
    const scaleY = interpolate(hologramShift.value, [0, 0.5, 1], [1, 0.98, 1]);

    return {
      transform: [
        { rotateZ: `${rotateZ}deg` },
        { scaleX },
        { scaleY },
      ],
    };
  });

  return (
    <Animated.View style={[styles.hologramContainer, hologramStyle]}>
      {/* Primary Holographic Gradient */}
      <LinearGradient
        colors={['#FF6B9D', '#A855F7', '#3B82F6', '#10B981', '#FF6B9D']}
        locations={[0, 0.25, 0.5, 0.75, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      />

      {/* Holographic Overlay Layers */}
      <LinearGradient
        colors={['rgba(255, 255, 255, 0.1)', 'transparent', 'rgba(255, 255, 255, 0.05)']}
        locations={[0, 0.5, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.hologramOverlay}
      />

      <LinearGradient
        colors={['transparent', 'rgba(168, 85, 247, 0.2)', 'transparent']}
        locations={[0, 0.5, 1]}
        start={{ x: 0, y: 1 }}
        end={{ x: 1, y: 0 }}
        style={styles.hologramOverlay}
      />
    </Animated.View>
  );
};

export const HomeBackground: React.FC = () => {

  return (
    <>
      <StatusBar style="light" translucent />

      {/* Holographic Background */}
      <HolographicBackground />

      {/* Floating Particles - Bigger but smaller than center lamp */}
      <FloatingParticle
        size={45}
        top={screenHeight * 0.2}
        left={screenWidth * 0.15}
        animationDuration={8000}
        delay={0}
        color="rgba(255, 107, 157, 0.6)"
        opacity={0.7}
      />

      <FloatingParticle
        size={38}
        top={screenHeight * 0.4}
        left={screenWidth * 0.8}
        animationDuration={6000}
        delay={1000}
        color="rgba(168, 85, 247, 0.6)"
        opacity={0.6}
      />

      <FloatingParticle
        size={52}
        top={screenHeight * 0.6}
        left={screenWidth * 0.25}
        animationDuration={7000}
        delay={2000}
        color="rgba(59, 130, 246, 0.6)"
        opacity={0.8}
      />

      <FloatingParticle
        size={42}
        top={screenHeight * 0.75}
        left={screenWidth * 0.7}
        animationDuration={9000}
        delay={3000}
        color="rgba(16, 185, 129, 0.6)"
        opacity={0.5}
      />

      <FloatingParticle
        size={35}
        top={screenHeight * 0.15}
        left={screenWidth * 0.6}
        animationDuration={5000}
        delay={1500}
        color="rgba(245, 158, 11, 0.6)"
        opacity={0.7}
      />

      <FloatingParticle
        size={48}
        top={screenHeight * 0.35}
        left={screenWidth * 0.45}
        animationDuration={7500}
        delay={2500}
        color="rgba(236, 72, 153, 0.6)"
        opacity={0.6}
      />

      <FloatingParticle
        size={32}
        top={screenHeight * 0.85}
        left={screenWidth * 0.35}
        animationDuration={6500}
        delay={500}
        color="rgba(139, 92, 246, 0.6)"
        opacity={0.8}
      />

      <FloatingParticle
        size={55}
        top={screenHeight * 0.55}
        left={screenWidth * 0.9}
        animationDuration={8500}
        delay={3500}
        color="rgba(6, 182, 212, 0.6)"
        opacity={0.5}
      />

      {/* Additional Depth Layers */}
      <LinearGradient
        colors={['transparent', 'rgba(255, 255, 255, 0.05)', 'transparent']}
        locations={[0, 0.5, 1]}
        start={{ x: 0, y: 0.2 }}
        end={{ x: 1, y: 0.8 }}
        style={styles.depthLayer}
      />
    </>
  );
};


const styles = StyleSheet.create({
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  hologramContainer: {
    position: 'absolute',
    left: -50,
    right: -50,
    top: -50,
    bottom: -50,
  },
  hologramOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  depthLayer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
});

export default HomeBackground;
