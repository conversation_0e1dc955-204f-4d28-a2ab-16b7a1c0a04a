import React, { useEffect } from 'react';
import { Dimensions, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withDelay,
  interpolate,
  runOnUI,
  Easing,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Glass Morphism Shape Component
const GlassMorphShape: React.FC<{
  width: number;
  height: number;
  top?: number;
  left?: number;
  right?: number;
  bottom?: number;
  borderRadius: number;
  animationDuration: number;
  delay?: number;
}> = ({ width, height, top, left, right, bottom, borderRadius, animationDuration, delay = 0 }) => {
  const animatedValue = useSharedValue(0);

  useEffect(() => {
    runOnUI(() => {
      animatedValue.value = withDelay(
        delay,
        withRepeat(
          withTiming(1, { 
            duration: animationDuration,
            easing: Easing.inOut(Easing.sin)
          }),
          -1,
          true
        )
      );
    })();
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    const translateY = interpolate(animatedValue.value, [0, 1], [0, -15]);
    const translateX = interpolate(animatedValue.value, [0, 0.5, 1], [0, 8, 0]);
    const scale = interpolate(animatedValue.value, [0, 0.5, 1], [0.95, 1.05, 0.95]);
    const opacity = interpolate(animatedValue.value, [0, 0.5, 1], [0.4, 0.7, 0.4]);

    return {
      transform: [{ translateY }, { translateX }, { scale }],
      opacity,
    };
  });

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          width,
          height,
          top,
          left,
          right,
          bottom,
          borderRadius,
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          borderColor: 'rgba(255, 255, 255, 0.2)',
          shadowColor: 'rgba(255, 255, 255, 0.5)',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 10,
          elevation: 8,
        },
        animatedStyle,
      ]}
    />
  );
};

// Animated Wave Component
const AnimatedWave: React.FC<{
  colors: string[];
  waveHeight: number;
  animationDuration: number;
  delay?: number;
}> = ({ colors, waveHeight, animationDuration, delay = 0 }) => {
  const waveAnimation = useSharedValue(0);

  useEffect(() => {
    runOnUI(() => {
      waveAnimation.value = withDelay(
        delay,
        withRepeat(
          withTiming(1, { 
            duration: animationDuration,
            easing: Easing.inOut(Easing.sin)
          }),
          -1,
          false
        )
      );
    })();
  }, []);

  const waveStyle = useAnimatedStyle(() => {
    const translateX = interpolate(waveAnimation.value, [0, 1], [0, screenWidth]);
    const scaleY = interpolate(waveAnimation.value, [0, 0.5, 1], [0.8, 1.2, 0.8]);

    return {
      transform: [{ translateX }, { scaleY }],
    };
  });

  return (
    <Animated.View style={[styles.waveContainer, waveStyle]}>
      <LinearGradient
        colors={colors}
        locations={[0, 0.5, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={[styles.wave, { height: waveHeight }]}
      />
    </Animated.View>
  );
};

export const HomeBackgroundVariant3: React.FC = () => {
  const { top: topInset } = useSafeAreaInsets();

  return (
    <>
      <StatusBar style="light" translucent />
      
      {/* Base Gradient */}
      <LinearGradient
        colors={['#FF6B9D', '#A855F7', '#3B82F6', '#10B981']}
        locations={[0, 0.3, 0.7, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      />

      {/* Animated Wave Layers */}
      <AnimatedWave
        colors={['rgba(255, 107, 157, 0.3)', 'rgba(168, 85, 247, 0.2)', 'transparent']}
        waveHeight={screenHeight * 0.4}
        animationDuration={12000}
        delay={0}
      />
      <AnimatedWave
        colors={['transparent', 'rgba(59, 130, 246, 0.25)', 'rgba(16, 185, 129, 0.3)']}
        waveHeight={screenHeight * 0.3}
        animationDuration={15000}
        delay={2000}
      />

      {/* Glass Morphism Shapes */}
      <GlassMorphShape
        width={200}
        height={120}
        top={screenHeight * 0.1}
        left={screenWidth * 0.05}
        borderRadius={60}
        animationDuration={8000}
        delay={0}
      />
      <GlassMorphShape
        width={150}
        height={150}
        top={screenHeight * 0.45}
        right={screenWidth * 0.1}
        borderRadius={75}
        animationDuration={10000}
        delay={1500}
      />
      <GlassMorphShape
        width={180}
        height={100}
        bottom={screenHeight * 0.2}
        left={screenWidth * 0.15}
        borderRadius={50}
        animationDuration={9000}
        delay={3000}
      />
      <GlassMorphShape
        width={120}
        height={200}
        top={screenHeight * 0.25}
        right={screenWidth * 0.25}
        borderRadius={60}
        animationDuration={7000}
        delay={4500}
      />

      {/* Blur Overlay for Enhanced Glass Effect */}
      <BlurView
        intensity={20}
        style={styles.blurOverlay}
        tint="light"
      />
    </>
  );
};

const styles = StyleSheet.create({
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  waveContainer: {
    position: 'absolute',
    left: -screenWidth,
    top: 0,
    width: screenWidth * 2,
    height: '100%',
  },
  wave: {
    width: '100%',
    borderRadius: 50,
  },
  blurOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    opacity: 0.1,
  },
});

export default HomeBackgroundVariant3;
