import React, { useCallback, useMemo } from 'react'
import {
  View,
  StyleSheet,
  StyleProp,
  ViewStyle,
} from 'react-native'

import { Avatar, Day, utils } from 'react-native-gifted-chat'
import type { DayProps, BubbleProps, AvatarProps, IMessage } from 'react-native-gifted-chat'
import Bubble from './SlackBubble'

const { isSameUser, isSameDay } = utils

interface Props {
  renderAvatar?: (props: AvatarProps<IMessage>) => React.ReactElement | null,
  renderBubble?: (props: BubbleProps<IMessage>) => React.ReactElement | null,
  renderDay?: (props: DayProps) => React.ReactElement | null,
  currentMessage: IMessage,
  nextMessage: IMessage,
  previousMessage?: IMessage | null,
  containerStyle: any
  // {
  //   left: StyleProp<ViewStyle>,
  //   right: StyleProp<ViewStyle>,
  // },
}

const Message = (props: Props) => {
  const {
    currentMessage,
    nextMessage,
    previousMessage,
    containerStyle,
  } = props

  const getInnerComponentProps = useCallback(() => {
    return {
      ...props,
      position: 'left' as const,
      isSameUser,
      isSameDay,
      containerStyle: props.containerStyle?.left,
      messageTextStyle: {
        fontSize: 15,
      },
      imageStyle: {
        borderRadius: 3,
      }
    }
  }, [props])

  const renderDay = useCallback(() => {
    if (!currentMessage.createdAt) {
      return null;
    }

    const dayProps = {
      ...getInnerComponentProps(),
      createdAt: currentMessage.createdAt
    }

    if (props.renderDay) {
      return props.renderDay(dayProps)
    }

    return <Day {...dayProps} />
  }, [currentMessage.createdAt, getInnerComponentProps, props.renderDay])

  const renderBubble = useCallback(() => {
    const bubbleProps = getInnerComponentProps()

    if (props.renderBubble)
      return props.renderBubble(bubbleProps as BubbleProps<IMessage>)

    return <Bubble 
      {...bubbleProps}    />
  }, [getInnerComponentProps, props.renderBubble])

  const renderAvatar = useCallback(() => {
    let extraStyle: StyleProp<ViewStyle> | undefined;
    if (
      previousMessage &&
      isSameUser(currentMessage, previousMessage) &&
      isSameDay(currentMessage, previousMessage)
    )
      // Set the invisible avatar height to 0, but keep the width, padding, etc.
      extraStyle = { height: 0 }

    const avatarProps = getInnerComponentProps()

    if (props.renderAvatar)
      return props.renderAvatar(avatarProps as AvatarProps<IMessage>)

    return (
      <Avatar
        {...avatarProps as AvatarProps<IMessage>}
        imageStyle={{
          left: [
            styles.slackAvatar, 
            // Fixed: Properly handle potentially undefined imageStyle
            avatarProps.imageStyle && typeof avatarProps.imageStyle === 'object' 
              ? (avatarProps.imageStyle as any).left 
              : undefined, 
            extraStyle
          ],
        }}
      />
    )
  }, [currentMessage, previousMessage, getInnerComponentProps, props.renderAvatar])

  const marginBottom = useMemo(() =>
    nextMessage &&
    isSameUser(
      currentMessage,
      nextMessage
    )
      ? 2
      : 10
  , [currentMessage, nextMessage])

  return (
    <View>
      {renderDay()}
      <View
        style={[
          styles.container,
          { marginBottom },
          containerStyle,
        ]}
      >
        {renderAvatar()}
        {renderBubble()}
      </View>
    </View>
  )
}

export default Message

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'flex-start',
    marginLeft: 8,
    marginRight: 0,
  },
  slackAvatar: {
    // The bottom should roughly line up with the first line of message text.
    height: 40,
    width: 40,
    borderRadius: 3,
  },
})